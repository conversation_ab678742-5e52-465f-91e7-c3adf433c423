<div class="modal-header">
  {{ isOverlay ? 'Overlay Progress' : 'Import Progress' }}
  <button
    type="button"
    class="close pull-right"
    aria-label="Close"
    (click)="bsModalRef.hide()"
  >
    <span aria-hidden="true" class="fa fa-times"></span>
  </button>
</div>
<div class="modal-body">
  <div class="container">
    <div class="row row-grid">
      <div class="col d-flex justify-content-center">
        <circle-progress
          class="align-self-auto"
          [percent]="progress"
          [subtitle]="progressTitle"
          [backgroundColor]="colorVenioSecondary"
          [backgroundOpacity]="0"
          [backgroundPadding]="-18"
          [outerStrokeColor]="colorOuterStroke"
          [innerStrokeColor]="colorVenioSecondary"
          [innerStrokeWidth]="0"
          [outerStrokeWidth]="6"
          [titleColor]="colorVenioSecondary"
          [titleFontSize]="'32'"
          [unitsColor]="colorVenioSecondary"
          [unitsFontSize]="'18'"
          [subtitleColor]="colorVenioSecondary"
          [subtitleFontSize]="'16'"
          [animation]="false"
        >
        </circle-progress>
      </div>
    </div>
    <div class="row row-grid" *ngIf="phaseToShow === 1 || phaseToShow === 2">
      <table class="table table-bordered definition-table">
        <tbody>
          <tr>
            <th scope="row" class="fit">Detail</th>
            <td class="text-right">
              {{
                importProgress?.analysisProgressRemark
                  ? importProgress.analysisProgressRemark
                  : '-'
              }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="row row-grid" *ngIf="phaseToShow === 3">
      <table class="table table-bordered definition-table">
        <tbody>
          <tr>
            <th scope="row" class="fit">Detail</th>
            <td>
              {{
                importProgress?.validationRemark
                  ? importProgress.validationRemark
                  : '-'
              }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="row row-grid" *ngIf="phaseToShow === 4">
      <table class="table table-bordered definition-table">
        <tbody>
          <tr>
            <th scope="row" class="fit">Detail</th>
            <td>
              {{
                importProgress?.importProgressRemark
                  ? importProgress.importProgressRemark
                  : '-'
              }}
            </td>
          </tr>
          <tr>
            <th scope="row" class="fit">Compute Duplicate Media Summary</th>
            <td>
              {{
                importProgress?.duplicateMediaSummaryRemark
                  ? importProgress.duplicateMediaSummaryRemark
                  : '-'
              }}
            </td>
          </tr>
          <tr>
            <th scope="row" class="fit">Indexing</th>
            <td>
              {{
                importProgress?.queueIndexingRemark
                  ? importProgress.queueIndexingRemark
                  : '-'
              }}
            </td>
          </tr>
          <tr *ngIf="!isOverlay">
            <th scope="row" class="fit">Email Analysis</th>
            <td>
              {{
                importProgress?.queueEmailAnalysisRemark
                  ? importProgress.queueEmailAnalysisRemark
                  : '-'
              }}
            </td>
          </tr>
          <tr>
            <th scope="row" class="fit">Language Identification</th>
            <td>
              {{
                importProgress?.queueLanguageIdentificationRemark
                  ? importProgress.queueLanguageIdentificationRemark
                  : '-'
              }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="row" *ngIf="status !== 2 && status != -1 && status !== -2">
      <div class="isa_info w-100">
        Can't wait? You don't have to. You'll receive an email notification
        after the job is done.
      </div>
    </div>
    <div class="row" *ngIf="status === -1">
      <div class="isa_warning w-100">Looks like the job is paused.</div>
    </div>
    <div class="row" *ngIf="status === 2 && phaseToShow === 2">
      <div class="isa_success w-100">
        {{
          isOverlay
            ? 'Overlay analysis is complete. Please continue with field mapping and validation.'
            : 'Import analysis is complete. Please continue with field mapping and validation.'
        }}
      </div>
    </div>
    <div class="row" *ngIf="status === 2 && phaseToShow === 3">
      <div class="isa_success w-100">
        {{
          isOverlay
            ? 'Overlay validation is complete. Please start overlay.'
            : 'Import validation is complete. Please start import.'
        }}
      </div>
    </div>
    <div class="row" *ngIf="status === 2 && phase === 5">
      <div class="isa_success w-100">
        {{ isOverlay ? 'Overlay' : 'Import' }} is complete. However, there might
        be some post-processing job still running.
      </div>
    </div>
    <div class="row" *ngIf="status === -2">
      <accordion class="w-100 error">
        <accordion-group
          heading="{{
            'There was an error completing the ' +
              progressTitle +
              ' job. Click to download error logs.'
          }}"
          (isOpenChange)="download()"
        >
        </accordion-group>
      </accordion>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button
    type="button"
    class="btn btn-{{ client }}-primary"
    (click)="goToLaunchPad()"
    *ngIf="!isExternalUser"
  >
    <fa-icon [icon]="['fas', 'home']"></fa-icon>
    Case Launchpad
  </button>
  <button
    *ngIf="status === 2 && phaseToShow === 3 && !isOverlay"
    type="button"
    class="btn btn-{{ client }}-primary btn-job"
    (click)="import()"
  >
    <span
      *ngIf="importSpinner$ | async"
      class="spinner-border spinner-border-sm"
      role="status"
      aria-hidden="true"
    >
    </span>
    <span *ngIf="!(importSpinner$ | async)">Start Import</span>
  </button>
  <button
    *ngIf="(status == -2 || status === 2) && phase !== 5"
    type="button"
    class="btn btn-grey"
    (click)="doHide()"
  >
    <i class="fa fa-times"></i>
    Close
  </button>
</div>
