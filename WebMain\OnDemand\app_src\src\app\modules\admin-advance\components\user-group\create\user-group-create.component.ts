import {
  AddUpdateGroupModel,
  GridRightModel,
  Project,
  RightsModel,
  UserGroupRightModel
} from '@admin-advance/models'
import {
  AddUpdateUserGroupsAction,
  ClearResponse,
  GetUserGroupAssociation,
  GetUserGroupAssociationByGroupId,
  GetUserGroupRights,
  GetUserGroupsAction,
  GetUserGroupsByIdAction,
  UserGroupStateSelector
} from '@admin-advance/store'
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  QueryList,
  TemplateRef,
  ViewChild,
  ViewChildren
} from '@angular/core'
import {
  FormBuilder,
  FormControl,
  FormControlName,
  FormGroup,
  Validators
} from '@angular/forms'
import { MatCheckboxChange } from '@angular/material/checkbox'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute } from '@angular/router'
import { UsersModel } from '@auth/models/user.model'
import { ConfigService } from '@config/services/config.service'
import { Navigate } from '@ngxs/router-plugin'
import { Select, Store } from '@ngxs/store'
import { animateHeight, fadeInUpDown, fadeInX } from '@shared/animation'
import useMatSelectFilter from '@shared/searchable-select-option/searchable-select-option'
import {
  GenericValidator,
  MessageModel,
  validateBeforeSubmit
} from '@shared/validators'
import { SetSelectedProjectIdAction } from '@stores/actions'
import { CaseSelectors, StartupStateSelector } from '@stores/selectors'
import { DxTreeListComponent } from 'devextreme-angular'
import { cloneDeep, union } from 'lodash'
import { ToastrService } from 'ngx-toastr'
import { EMPTY, fromEvent, Observable, ReplaySubject, Subject } from 'rxjs'
import {
  catchError,
  debounceTime,
  filter,
  switchMap,
  takeUntil,
  tap
} from 'rxjs/operators'

type ModeTypes = 'CREATE' | 'EDIT' | 'CLONE'

@Component({
  selector: 'app-user',
  templateUrl: './user-group-create.component.html',
  styleUrls: ['./user-group-create.component.scss'],
  animations: [fadeInX, fadeInUpDown, animateHeight]
})
export class UserGroupCreateComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  /** Cleanup placeholder for the observers when the component get destroyed. */
  private readonly toDestroy$ = new Subject<void>()

  /**  Static service of app config. */
  config = ConfigService

  /** Enable content placeholder to indicate we're working. */
  isWorking = true

  /**  loading while making the server request */
  isLoading = false

  /** Whether the form is submitting.  */
  isSubmitting: boolean

  /** Create mode by default.  */
  currentMode: ModeTypes = 'CREATE'

  /** Whether the current mode is clone. */
  isClone: boolean

  /** usergroup title form control */
  usergroupForm: FormGroup

  usergroupCtrl = new FormControl(null)

  private genericValidator: GenericValidator

  @ViewChildren(FormControlName, { read: ElementRef })
  private readonly formInputElements: QueryList<ElementRef>

  displayMessage: MessageModel

  /** Form error messages */
  formErrorMessage: string

  /** list of assigned rights */
  assignedRights: number[] = []

  /** all rights */
  @Select(UserGroupStateSelector.SliceOf('allRights'))
  allRights$: Observable<number[]>

  /** list of rights */
  rights: RightsModel[] = []

  /** project control */
  readonly projectCtrl = new FormControl()

  /** show and hide the cases for checkbox */
  showCaseRole = false

  /** List of all the projects */
  users: UsersModel[] = []

  /** selected project */
  selectedUsers: number[] = []

  /** Datasource for the project dropdown list. */
  projects: Project[]

  /** control for the project filter keyword */
  public projectFilterCtrl: FormControl = new FormControl()

  /** list of projects filtered by search keyword */
  public filteredProjects: ReplaySubject<Project[]> = new ReplaySubject<
    Project[]
  >()

  /** query params for edit or clone */
  groupId: number

  /** clone form control select option */
  userGroups: UserGroupRightModel[]

  /** selected rights list */
  selectedRights: number[] = []

  /** auto expand all */
  expanded = true

  @Select(UserGroupStateSelector.SliceOf('userGroupRights'))
  userGroupDataSource$: Observable<GridRightModel[]>

  @ViewChild('treeListContainer', { static: false })
  treeView: DxTreeListComponent

  constructor(
    private store: Store,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog,
    private fb: FormBuilder,
    private toast: ToastrService,
    private route: ActivatedRoute
  ) {
    this.groupId = +this.route.snapshot.queryParams['groupId']
    this.isClone =
      this.route.parent.snapshot.routeConfig.path.includes('/user-group/clone')
    this.currentMode = this.isClone
      ? 'CLONE'
      : this.groupId > 0
      ? 'EDIT'
      : 'CREATE'
  }

  ngOnInit(): void {
    this.initForm()
  }

  ngAfterViewInit(): void {
    this.initSlice()
    this.initValidationRules()

    this.validationWatcher()

    this.projectValueChanged()
    this.projectFilterChange()
  }

  initForm(): void {
    this.usergroupForm = this.fb.group({
      groupName: [
        { value: '', disabled: this.currentMode === 'EDIT' },
        Validators.required
      ],
      isGroupDeactivated: [false],
      isInternalProjectUserGroup: [false]
    })
  }

  onSelectionChanged(e): void {
    const selectedRows = this.treeView.instance.getSelectedRowsData(
      'all'
    ) as GridRightModel[]
    this.assignedRights = selectedRows
      ?.filter((c) => c.rightId > 0)
      .map((c) => c.rightId)
  }

  private getUserGroups(projectId) {
    this.store
      .dispatch(new GetUserGroupsAction(projectId))
      .pipe(
        switchMap(() =>
          this.store.select(UserGroupStateSelector.SliceOf('userGroups'))
        ),
        filter((c) => c?.length >= 0),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res: UserGroupRightModel[]) => {
        this.userGroups = cloneDeep(res)
        if (this.isClone) {
          this.usergroupCtrl.setValue(this.groupId)
        }
        this.isWorking = false
      })
  }

  someUsersSelected(): boolean {
    return this.selectedUsers.length < this.users.length
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  onToolbarPreparing(e): void {
    e.toolbarOptions.items.unshift(
      {
        location: 'before',
        widget: 'dxButton',
        options: {
          icon: 'plus',
          hint: 'Expand All',
          onClick: this.expandAll.bind(this)
        }
      },
      {
        location: 'before',
        widget: 'dxButton',
        options: {
          icon: 'minus',
          hint: 'Collapse All',
          onClick: this.expandAll.bind(this)
        }
      }
    )
  }

  optionChanged(e): void {
    if (e.fullName === 'searchPanel.text') {
      if (e.value) {
        this.expanded = true
      } else {
        this.expanded = false
      }
    }
  }

  expandAll(e): void {
    const parentNodes = this.treeView.instance
      .getDataSource()
      ?.items()
      ?.filter((n) => n.level == 0)
    if (e.component._getContentData()?.icon === 'minus') {
      parentNodes.forEach((node) => {
        this.treeView.instance.collapseRow(node?.key)
      })
      this.expanded = false
    } else {
      this.expandRow(parentNodes)
      this.expanded = true
    }
  }

  expandRow(nodes): void {
    nodes.forEach((node) => {
      this.treeView.instance.expandRow(node?.key)
      if (node?.hasChildren) {
        this.expandRow(node?.children)
      }
    })
  }

  initSlice(): void {
    this.store.dispatch(new GetUserGroupRights())
    this.store
      .select(CaseSelectors.projects)
      .pipe(
        filter((p) => p.length > 0),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (projects) => {
          this.projects = projects
          this.isLoading = false
          // load the initial project list
          this.filteredProjects.next(this.projects.slice())
        }
      })

    /*
     *  Selects persisted project Id
     */
    this.store
      .select(StartupStateSelector.SliceOf('selectedProjectId'))
      .pipe(
        filter((id) => id > 0),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (id) => {
          this.projectCtrl.setValue(id)
          this.getUsers(id)
          this.getUserGroups(id)
          if (this.groupId > 0) {
            this.getUserGroupAssociation(id)
            this.getGroupById(id)
          }
        }
      })
  }

  /**
   * Perform default fetching task when project selected get changed. These are the default derived data from top level
   */
  private projectValueChanged(): void {
    this.projectCtrl.valueChanges
      .pipe(
        // Do not emit if we have the same value after previous emission        distinctUntilChanged(),
        tap((projectId) => [
          (this.users = []),
          (this.isLoading = true),
          this.store.dispatch([new SetSelectedProjectIdAction(projectId)])
        ]),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.defaultFormValue()
        this.isLoading = false
      })
  }

  getUserGroupAssociation(projectId: number): void {
    this.store
      .dispatch(new GetUserGroupAssociationByGroupId(projectId, this.groupId))
      .pipe(
        switchMap(() =>
          this.store.select(
            UserGroupStateSelector.SliceOf('groupUserAssocations')
          )
        ),
        filter((c) => !!c),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res: UsersModel[]) => {
        if (!this.isClone) {
          this.users = union(this.users, res)
        }
      })
  }

  getGroupById(projectId: number): void {
    this.store
      .dispatch(new GetUserGroupsByIdAction(projectId, this.groupId))
      .pipe(
        switchMap(() =>
          this.store.select(UserGroupStateSelector.SliceOf('userGroupById'))
        ),
        filter((c) => !!c),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res: UserGroupRightModel) => {
        this.patchFormValue(res)
      })
  }

  patchFormValue(model): void {
    if (!this.isClone) {
      this.usergroupForm.patchValue({
        groupName: model.groupName,
        isGroupDeactivated: model.isGroupDeactivated,
        isInternalProjectUserGroup: model.isInternalProjectUserGroup
      })
      this.selectedUsers = model.groupUserIds
    }
    this.assignedRights = model.groupRightIds
    this.selectedRights = this.assignedRights
    this.isLoading = false
  }

  getUsers(projectId: number): void {
    this.store
      .dispatch([new GetUserGroupAssociation(projectId)])
      .pipe(
        switchMap(() =>
          this.store.select(
            UserGroupStateSelector.SliceOf('userGroupAssociationList')
          )
        ),
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (res: UsersModel[]) => {
          this.users = union(this.users, res)
        }
      })
  }

  /**
   * change of role id for clone from
   */
  changeCloneValue() {
    const userGroupId = this.usergroupCtrl.value
    if (userGroupId) {
      this.groupId = userGroupId
      this.isClone = true
      this.getGroupById(this.projectCtrl.value)
    }
  }

  setAll(checked: boolean): void {
    if (checked) {
      this.selectedUsers = this.users.map((c) => c.userID)
    } else {
      this.selectedUsers = []
    }
  }

  chooseUser(e: MatCheckboxChange): void {
    this.formErrorMessage = null
    const userId = +e.source.value
    if (e.checked) {
      this.selectedUsers = this.selectedUsers.concat(userId)
    } else {
      this.selectedUsers = this.selectedUsers.filter((c) => c !== userId)
    }
  }

  resetForm(tpl: TemplateRef<HTMLDialogElement>): void {
    const dRef = this.dialog.open(tpl, {
      autoFocus: false,
      closeOnNavigation: true,
      width: '380px'
    })
    setTimeout(() => {
      const actions = document.querySelectorAll('.close-confirm')
      fromEvent(actions, 'click')
        .pipe(takeUntil(this.toDestroy$))
        .subscribe({
          next: (e) => {
            const isReset = (e.currentTarget as HTMLButtonElement).getAttribute(
              'data-reset'
            )
            if (isReset === 'true') {
              this.defaultFormValue()
            }
            dRef.close()
          }
        })
    }, 1000)
  }

  private defaultFormValue(): void {
    const name = this.usergroupForm.get('groupName').value
    this.usergroupForm.reset()
    if (this.currentMode === 'EDIT') {
      this.usergroupForm.get('groupName').setValue(name)
    }
    this.usergroupCtrl.reset()
    this.formErrorMessage = null
    this.assignedRights = []
    this.selectedRights = []
    this.selectedUsers = []
  }

  /** listens to the project filter control change */
  private projectFilterChange(): void {
    // listen for search field value changes
    this.projectFilterCtrl.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        const { filterProjects, filteredProjects } = useMatSelectFilter()
        filterProjects(this.projectFilterCtrl, this.projects)
        this.filteredProjects = filteredProjects
      })
  }

  private initValidationRules() {
    this.genericValidator = new GenericValidator({
      // the parent properties are same as the form group property and
      // the child properties are the either of angular validator or custom property
      name: {
        required: 'Name is a required field.',
        exist: 'Group name with same name already exists. Please try another.'
      }
    })
  }

  private validationWatcher() {
    this.genericValidator
      .initValidationProcess(this.usergroupForm, this.formInputElements)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({
        next: (m: MessageModel) => [
          this.cdr.markForCheck(),
          (this.displayMessage = m)
        ]
      })
  }

  saveChanges(): void {
    this.cdr.markForCheck()
    this.formErrorMessage = null

    if (!this.assignedRights?.length) {
      this.formErrorMessage = 'Please select at least one right.'
      return
    }

    const errorMessage = validateBeforeSubmit(this.usergroupForm)
    if (errorMessage) {
      this.formErrorMessage = errorMessage
      return
    }

    const payload: AddUpdateGroupModel = this.usergroupForm.getRawValue()
    if (this.currentMode !== 'CREATE') {
      payload.groupId = this.groupId
    }
    payload.description = this.usergroupForm.getRawValue().groupName
    payload.groupRightIds = this.assignedRights
    payload.groupUserIds = this.selectedUsers
    payload.isInternalProjectUserGroup = !this.usergroupForm.getRawValue()
      .isInternalProjectUserGroup
      ? false
      : this.usergroupForm.getRawValue().isInternalProjectUserGroup
    payload.isGroupDeactivated = !this.usergroupForm.getRawValue()
      .isGroupDeactivated
      ? false
      : this.usergroupForm.value.isGroupDeactivated

    this.isSubmitting = true
    this.store
      .dispatch(
        new AddUpdateUserGroupsAction(
          this.projectCtrl.value,
          payload,
          this.currentMode === 'EDIT'
        )
      )
      .pipe(
        switchMap(() =>
          this.store.select(UserGroupStateSelector.SliceOf('addUpdateResponse'))
        ),
        takeUntil(this.toDestroy$),
        catchError((res) => {
          this.cdr.markForCheck()
          this.formErrorMessage = res?.error?.message
          this.toast.error(res?.error)
          this.isLoading = false
          this.isSubmitting = false
          return EMPTY
        })
      )
      .subscribe(() => {
        this.isSubmitting = false
        this.cdr.markForCheck()
        this.toast.success('User Group successfully saved.')
        this.store.dispatch(new ClearResponse())
        this.store.dispatch(new Navigate(['/admin/case/user-group/manage']))
      })
  }
}
