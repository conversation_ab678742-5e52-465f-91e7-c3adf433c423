import { Action } from '@ngrx/store'
import { SettingsInfo } from '@stores/models'
import { ProjectSetupInfo } from '../../models/case-template-settings.model'

export enum CaseActionTypes {
  FetchCases = '[Case] Fetch cases',
  FetchCasesSuccess = '[Case] Fetch cases Successful',
  FetchCaseInfo = '[Case] Fetch case Info',
  FetchCaseInfoSuccess = '[Case] Fetch cases Info Successful',
  FetchProjectTemplates = '[Case] Fetch Project Template List',
  FetchProjectTemplatesSuccess = '[Case] Fetch Project Template List Successful',
  ShowCaseListLoader = '[Case] Show loading cases',
  ClearCaseList = '[Clear Case] Clear case list',
  CreateCase = '[Create Case] Create case',
  CreateCaseSuccess = '[Create Success] Create case successful',
  CreateCaseError = '[Create Error] Create case unsuccessful',
  CreateCaseServiceType = '[Create Case] Create case service type',
  CreateCaseServiceTypeSuccess = '[Create Success] Create case service type successful',
  CreateCaseServiceTypeError = '[Create Error] Create case service type unsuccessful',
  UpdateCase = '[Update Case] Create case',
  UpdateCaseSuccess = '[Update Success] Create case successful',
  UpdateCaseError = '[Update Error] Create case unsuccessful',
  GetTimeZones = '[Time Zone] Get time zones',
  GetTimeZonesSuccessful = '[Time Zone] Get time zones successful',
  GetProjectMediaStatus = '[Case Media Status] Project Media Status',
  GetProjectMediaStatusSuccessful = '[Case Media Status] Fetch status successful',
  GetProjectRightList = '[Right List] Get Case Right List',
  GetProjectRightListSuccessful = '[Right List] Getting Right List Successful',
  ResetProjectRightList = '[Right List] Reset Right List State',
  ResetCachedProjectRightList = '[Right List] Reset Cached Right List State',
  GetUsersListToInvite = '[User List] Get user  list',
  FetchUsersGroupListSuccess = '[User List] Get user  list successful',
  GetExtUsersListToInvite = '[Ext User List] Get external user  list',
  FetchExtUsersGroupListSuccess = '[User List] Get external user  list successful',
  SendInvitation = '[Invite User] Send invitation',
  SendInvitationSuccess = '[Invite User] Send invitation successful',
  ClearStoreProperty = '[Clear Property] Clear store property',
  FetchCaseTemplateSettings = '[Case Template Settings] Fetch Template Settings',
  FetchCaseTemplateSettingsSuccess = '[Case Template Settings] Get Project Template Setting Successful',
  FetchCaseTemplateSettingsError = '[Case Template Settings] Get Project Template Settings failed',
  FetchCaseInfoEdit = '[Case] Get Project For Edit',
  FetchCaseInfoEditSuccess = '[Case] Get Project For Edit Success',
  GetTranscriptStatus = '[Case Transcript Status]  Transcript Status ',
  GetTranscriptStatusSuccessful = '[Case Transcript Status] Fetch Transcript Status Successful',
  GetSupportedFileTypesForTranscribing = '[Case Get Supported Files Transcribe]  Fetch supported files for transcribing ',
  GetSupportedFileTypesForTranscribingSuccessful = '[Case Get Supported Files Transcribe] Fetch supported files for transcribing Successful',
  GetTranscribeAccessKeys = '[Case Get Transcribe Access Keys ] Fetch transcribe access keys',
  GetTranscribeAccessKeysSuccessful = '[Case Get Transcribe Access Keys ] Fetch transcribe access keys Successful',
  ResetCaseInfoEdit = '[Reset Case Info] Reset project to initial state for edit',
  FetchFileTypePageLimit = '[Case] Fetch File Type Page Limit',
  FetchFileTypePageLimitSuccess = '[Case] Fetch File Type Page Limit Successful',
  GetImageFileExtensions = '[Image File Extensions] Get image file extensions',
  GetImageFileExtensionsSuccessful = '[Image File Extensions] Get image file extensions successful',
  CreateCaseTemplate = '[Create Case] Create case template',
  CreateCaseTemplateSuccess = '[Create Success] Create case template successful',
  CreateCaseTemplateError = '[Create Error] Create case template unsuccessful',
  GetDefaultProjectTemplate = '[Default Project Template] Default Project Template Settings',
  GetDefaultProjectTemplateSuccess = '[Default Project Template] Default Project Template Settings Successful',
  GetDefaultProjectTemplateError = '[Default Project Template] Default Project Template Settings failed',
  ResetTemplateInfoEdit = '[Reset Template Info] Reset template to initial state for edit',
  FetchTemplateInfoEdit = '[Case Template Info] Get template for edit',
  FetchTemplateInfoEditSuccess = '[Case Template Info] Get template For edit success',
  UpdateCaseTemplate = '[Update Case Template] Update case template',
  UpdateCaseTemplateSuccess = '[Update Case Template Success] Update Case Template successful',
  UpdateCaseTemplateError = '[Update Case Template Error] Update Case Template unsuccessful',
  FetchUnIndexMediaStatus = '[Project] Fetch Project Media Status',
  FetchUnIndexMediaStatusSuccess = '[Project] Fetch Project Media Status: Success',
  FetchUnIndexMediaStatusFailure = '[Project] Fetch Project Media Status: Failure',
  SetCanFetchNewCase = '[Project] Set Can Fetch New Case'
}

export class FetchCases implements Action {
  readonly type = CaseActionTypes.FetchCases

  constructor(readonly payload: any) {}
}

export class FetchCasesSuccess implements Action {
  readonly type = CaseActionTypes.FetchCasesSuccess

  constructor(readonly response: any) {}
}

export class FetchCaseInfo implements Action {
  readonly type = CaseActionTypes.FetchCaseInfo

  constructor(readonly projectId) {}
}

export class FetchCaseInfoSuccess implements Action {
  readonly type = CaseActionTypes.FetchCaseInfoSuccess

  constructor(readonly response: any) {}
}

export class FetchCaseInfoEdit implements Action {
  readonly type = CaseActionTypes.FetchCaseInfoEdit

  constructor(readonly projectId: number) {}
}

export class ResetCaseInfoEdit implements Action {
  readonly type = CaseActionTypes.ResetCaseInfoEdit

  constructor() {}
}

export class FetchCaseInfoEditSuccess implements Action {
  readonly type = CaseActionTypes.FetchCaseInfoEditSuccess

  constructor(readonly response: any) {}
}

export class FetchProjectTemplates implements Action {
  readonly type = CaseActionTypes.FetchProjectTemplates
}

export class FetchProjectTemplatesSuccess implements Action {
  readonly type = CaseActionTypes.FetchProjectTemplatesSuccess

  constructor(readonly payload: any) {}
}

export class ClearCaseList implements Action {
  readonly type = CaseActionTypes.ClearCaseList

  constructor() {}
}

export class CreateCase implements Action {
  readonly type = CaseActionTypes.CreateCase

  // TODO Change this to only SettingsInfo after removing all usages of CreateCaseReqModel
  //constructor(readonly payload: CreateCaseReqModel | SettingsInfo) {}
  constructor(readonly payload: ProjectSetupInfo) {}
}

export class CreateCaseSuccess implements Action {
  readonly type = CaseActionTypes.CreateCaseSuccess

  constructor(readonly payload: any) {}
}

export class CreateCaseError implements Action {
  readonly type = CaseActionTypes.CreateCaseError

  constructor(readonly payload: any) {}
}

export class CreateCaseServiceType implements Action {
  readonly type = CaseActionTypes.CreateCaseServiceType

  constructor(readonly payload: SettingsInfo) {}
}

export class CreateCaseServiceTypeSuccess implements Action {
  readonly type = CaseActionTypes.CreateCaseServiceTypeSuccess

  constructor(readonly payload: any) {}
}

export class CreateCaseServiceTypeError implements Action {
  readonly type = CaseActionTypes.CreateCaseServiceTypeError

  constructor(readonly payload: any) {}
}

export class UpdateCase implements Action {
  readonly type = CaseActionTypes.UpdateCase

  constructor(
    readonly projectId: number,
    // readonly payload: CreateCaseReqModel | SettingsInfo
    readonly payload: ProjectSetupInfo
  ) {}
}

export class UpdateCaseSuccess implements Action {
  readonly type = CaseActionTypes.UpdateCaseSuccess

  constructor(readonly payload: any) {}
}

export class UpdateCaseError implements Action {
  readonly type = CaseActionTypes.UpdateCaseError

  constructor(readonly payload: any) {}
}

export class UpdateCaseTemplate implements Action {
  readonly type = CaseActionTypes.UpdateCaseTemplate

  constructor(
    readonly templateId: number,
    readonly payload: ProjectSetupInfo
  ) {}
}

export class UpdateCaseTemplateSuccess implements Action {
  readonly type = CaseActionTypes.UpdateCaseTemplateSuccess

  constructor(readonly payload: any) {}
}

export class UpdateCaseTemplateError implements Action {
  readonly type = CaseActionTypes.UpdateCaseTemplateError

  constructor(readonly payload: any) {}
}

export class GetTimeZones implements Action {
  readonly type = CaseActionTypes.GetTimeZones
}

export class GetTimeZonesSuccessful implements Action {
  readonly type = CaseActionTypes.GetTimeZonesSuccessful

  constructor(readonly payload: any) {}
}

export class GetProjectMediaStatus implements Action {
  readonly type = CaseActionTypes.GetProjectMediaStatus

  constructor(readonly projectId: number) {}
}

export class GetProjectMediaStatusSuccessful implements Action {
  readonly type = CaseActionTypes.GetProjectMediaStatusSuccessful

  constructor(readonly res: any) {}
}

export class GetProjectRightList implements Action {
  readonly type = CaseActionTypes.GetProjectRightList

  constructor(readonly projectId: number) {}
}

export class GetProjectRightListSuccessful implements Action {
  readonly type = CaseActionTypes.GetProjectRightListSuccessful

  constructor(readonly res: any, readonly isVodrEnabled: boolean) {}
}

export class ResetProjectRightListAction implements Action {
  readonly type = CaseActionTypes.ResetProjectRightList
}

export class ResetCachedProjectRightListAction implements Action {
  readonly type = CaseActionTypes.ResetCachedProjectRightList
}

export class GetUsersListToInvite implements Action {
  readonly type = CaseActionTypes.GetUsersListToInvite

  constructor(readonly ProjectId: number) {}
}

export class FetchUsersGroupListSuccess implements Action {
  readonly type = CaseActionTypes.FetchUsersGroupListSuccess

  constructor(readonly res: any) {}
}

export class GetExtUsersListToInvite implements Action {
  readonly type = CaseActionTypes.GetExtUsersListToInvite

  constructor() {}
}

export class FetchExtUsersGroupListSuccess implements Action {
  readonly type = CaseActionTypes.FetchExtUsersGroupListSuccess

  constructor(readonly res: any) {}
}

export class SendInvitation implements Action {
  readonly type = CaseActionTypes.SendInvitation

  constructor(readonly payload: any) {}
}

export class SendInvitationSuccess implements Action {
  readonly type = CaseActionTypes.SendInvitationSuccess

  constructor(readonly res: any) {}
}

export class ClearStoreProperty implements Action {
  readonly type = CaseActionTypes.ClearStoreProperty

  constructor(readonly propertyName: string) {}
}

export class FetchCaseTemplateSettings implements Action {
  readonly type = CaseActionTypes.FetchCaseTemplateSettings

  constructor(readonly templateId: number) {}
}

export class FetchCaseTemplateSettingsSuccess implements Action {
  readonly type = CaseActionTypes.FetchCaseTemplateSettingsSuccess

  constructor(readonly res: ProjectSetupInfo) {} // CaseTemplateSettings
}

export class FetchCaseTemplateSettingError implements Action {
  readonly type = CaseActionTypes.FetchCaseTemplateSettingsError

  constructor() {}
}

export class ShowCaseLoader implements Action {
  readonly type = CaseActionTypes.ShowCaseListLoader

  constructor(readonly isShow) {}
}

export class GetTranscriptStatus implements Action {
  readonly type = CaseActionTypes.GetTranscriptStatus

  constructor(readonly projectId: number) {}
}

export class GetTranscriptStatusSuccessful implements Action {
  readonly type = CaseActionTypes.GetTranscriptStatusSuccessful

  constructor(readonly res: boolean) {}
}
export class GetSupportedFileTypesForTranscribing implements Action {
  readonly type = CaseActionTypes.GetSupportedFileTypesForTranscribing

  constructor() {}
}

export class GetSupportedFileTypesForTranscribingSuccessful implements Action {
  readonly type = CaseActionTypes.GetSupportedFileTypesForTranscribingSuccessful

  constructor(readonly res) {}
}
export class GetTranscribeAccessKeys implements Action {
  readonly type = CaseActionTypes.GetTranscribeAccessKeys

  constructor() {}
}

export class GetTranscribeAccessKeysSuccessful implements Action {
  readonly type = CaseActionTypes.GetTranscribeAccessKeysSuccessful

  constructor(readonly res) {}
}

export class GetImageFileExtensions implements Action {
  readonly type = CaseActionTypes.GetImageFileExtensions
}

export class GetImageFileExtensionsSuccessful implements Action {
  readonly type = CaseActionTypes.GetImageFileExtensionsSuccessful

  constructor(readonly payload: any) {}
}

export class CreateCaseTemplate implements Action {
  readonly type = CaseActionTypes.CreateCaseTemplate

  constructor(readonly payload: ProjectSetupInfo) {}
}

export class CreateCaseTemplateSuccess implements Action {
  readonly type = CaseActionTypes.CreateCaseTemplateSuccess

  constructor(readonly payload: any) {}
}

export class CreateCaseTemplateError implements Action {
  readonly type = CaseActionTypes.CreateCaseTemplateError

  constructor(readonly payload: any) {}
}

export class GetDefaultProjectTemplate implements Action {
  readonly type = CaseActionTypes.GetDefaultProjectTemplate
}

export class GetDefaultProjectTemplateSuccess implements Action {
  readonly type = CaseActionTypes.GetDefaultProjectTemplateSuccess

  constructor(readonly res: ProjectSetupInfo) {} // CaseTemplateSettings
}

export class GetDefaultProjectTemplateError implements Action {
  readonly type = CaseActionTypes.GetDefaultProjectTemplateError

  constructor() {}
}

export class ResetTemplateInfoEdit implements Action {
  readonly type = CaseActionTypes.ResetTemplateInfoEdit

  constructor() {}
}

export class FetchTemplateInfoEdit implements Action {
  readonly type = CaseActionTypes.FetchTemplateInfoEdit

  constructor(readonly templateId: number) {}
}

//
export class FetchTemplateInfoEditSuccess implements Action {
  readonly type = CaseActionTypes.FetchTemplateInfoEditSuccess

  constructor(readonly response: any) {}
}

export class FetchUnIndexMediaStatusSuccess implements Action {
  readonly type = CaseActionTypes.FetchUnIndexMediaStatusSuccess

  constructor(readonly unIndexMediaSuccessResponse: any) {}
}

export class FetchUnIndexMediaStatusFailure implements Action {
  readonly type = CaseActionTypes.FetchUnIndexMediaStatusFailure

  constructor(readonly unIndexMediaErrorResponse: any) {}
}

export class FetchUnIndexMediaStatus implements Action {
  readonly type = CaseActionTypes.FetchUnIndexMediaStatus

  constructor(readonly projectId: number) {}
}

export class SetCanFetchNewCase implements Action {
  readonly type = CaseActionTypes.SetCanFetchNewCase

  constructor(readonly canFetchNewCase: boolean) {}
}

export type CaseActions =
  | FetchCases
  | FetchCasesSuccess
  | FetchCaseInfo
  | FetchCaseInfoSuccess
  | FetchProjectTemplates
  | FetchProjectTemplatesSuccess
  | CreateCase
  | ShowCaseLoader
  | CreateCaseSuccess
  | CreateCaseError
  | UpdateCase
  | UpdateCaseSuccess
  | UpdateCaseError
  | ClearCaseList
  | GetTimeZones
  | GetTimeZonesSuccessful
  | GetProjectMediaStatus
  | GetProjectMediaStatusSuccessful
  | GetProjectRightList
  | GetProjectRightListSuccessful
  | GetUsersListToInvite
  | FetchUsersGroupListSuccess
  | GetExtUsersListToInvite
  | FetchExtUsersGroupListSuccess
  | SendInvitation
  | SendInvitationSuccess
  | ClearStoreProperty
  | FetchCaseTemplateSettingsSuccess
  | FetchCaseTemplateSettingError
  | FetchCaseInfoEdit
  | FetchCaseInfoEditSuccess
  | GetTranscriptStatus
  | GetTranscriptStatusSuccessful
  | GetSupportedFileTypesForTranscribing
  | GetSupportedFileTypesForTranscribingSuccessful
  | GetTranscribeAccessKeys
  | GetTranscribeAccessKeysSuccessful
  | ResetCaseInfoEdit
  | GetImageFileExtensions
  | GetImageFileExtensionsSuccessful
  | CreateCaseTemplate
  | CreateCaseTemplateSuccess
  | CreateCaseTemplateError
  | GetDefaultProjectTemplate
  | GetDefaultProjectTemplateSuccess
  | GetDefaultProjectTemplateError
  | ResetTemplateInfoEdit
  | FetchTemplateInfoEdit
  | FetchTemplateInfoEditSuccess
  | UpdateCaseTemplate
  | UpdateCaseTemplateSuccess
  | UpdateCaseTemplateError
  | ResetProjectRightListAction
  | ResetCachedProjectRightListAction
  | FetchUnIndexMediaStatus
  | FetchUnIndexMediaStatusSuccess
  | FetchUnIndexMediaStatusFailure
  | SetCanFetchNewCase
