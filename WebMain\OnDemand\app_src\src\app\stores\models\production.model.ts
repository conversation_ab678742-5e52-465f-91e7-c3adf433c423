import { TimeZone, TimeZoneEnumConverter } from '@shared/models/timezone.model'
import { JsonObject, JsonProperty } from 'json2typescript'
import { ResponseModel } from '../../modules/shared/models/response.model'
import { UploadFileDetails } from '../../modules/upload/models'
import {
  BinderColor,
  BinderColorEnumConverter,
  BinderSize,
  BinderSizeEnumConverter,
  BindingType,
  BindingTypeEnumConverter,
  ConversionType,
  ConversionTypeEnumConverter,
  CSVExcelHandling,
  CSVExcelHandlingEnumConverter,
  DataRetentionType,
  DataRetentionTypeEnumConverter,
  DeduplicationOption,
  DeduplicationOptionEnumConverter,
  DirectExportImageType,
  DirectExportImageTypeEnumConverter,
  DocumentSeparator,
  DocumentSeparatorEnumConverter,
  EndorsementLocation,
  EndorsementLocationEnumConverter,
  ExportLocation,
  ExportLocationEnumConverter,
  FamilyFileHandling,
  FamilyFileHandlingEnumConverter,
  PaperSide,
  PaperSideEnumConverter,
  PaperType,
  PaperTypeEnumConverter,
  PDFFamilyFileHandling,
  PDFFamilyFileHandlingEnumConverter,
  PDFFileNamingConvention,
  PDFFileNamingConventionEnumConverter,
  PDFType,
  PDFTypeEnumConverter,
  PrefixDelimiter,
  PrefixDelimiterEnumConverter,
  PrintSetOption,
  PrintSetOptionEnumConverter,
  SelectiveEndorsementType,
  SelectiveEndorsementTypeEnumConverter,
  ServiceType,
  ServiceTypeEnumConverter
} from './service-request-enums.model'

/** ---------------------- Service Request Models : Start ---------------------- **/

@JsonObject('ColorConversion')
export class ColorConversion {
  @JsonProperty('ImageFileType', ConversionTypeEnumConverter, true)
  imageFileType: ConversionType = ConversionType.BLACK_AND_WHITE

  @JsonProperty('PDFFiles', ConversionTypeEnumConverter, true)
  pdfFiles: ConversionType = ConversionType.BLACK_AND_WHITE

  @JsonProperty('Powerpoint', ConversionTypeEnumConverter, true)
  powerpoint: ConversionType = ConversionType.BLACK_AND_WHITE
}

@JsonObject('ImageConversionOption')
export class ImageConversionOption {
  @JsonProperty('ImageType', DirectExportImageTypeEnumConverter, true)
  imageType: DirectExportImageType = DirectExportImageType.PDF

  @JsonProperty('ImageColorConversion', ColorConversion, true)
  imageColorConversion: ColorConversion = new ColorConversion()

  @JsonProperty('PasswordList', [String], true) passwordList: string[] = []

  @JsonProperty('DeduplicationOption', DeduplicationOptionEnumConverter, true)
  deduplicationOption: DeduplicationOption = DeduplicationOption.NONE

  @JsonProperty('TimeZone', TimeZoneEnumConverter, true) timeZone: TimeZone =
    TimeZone.GREEN_WICH_MEANTIME

  @JsonProperty('CSV_Excel_option', CSVExcelHandlingEnumConverter, true)
  csvExcelOption: CSVExcelHandling = CSVExcelHandling.PLACEHOLDER_ONLY

  @JsonProperty('CSV_Excel_option', CSVExcelHandlingEnumConverter, true)
  csV_Excel_option: CSVExcelHandling = CSVExcelHandling.PLACEHOLDER_ONLY

  @JsonProperty('AutoGenerateImagesAfterIngestion', Boolean, true)
  autoGenerateImagesAfterIngestion = true

  @JsonProperty('IgnoreAutoTiffJobsForMediaProcessingStatus', Boolean, true)
  ignoreAutoTiffJobsForMediaProcessingStatus = false
}

@JsonObject('ServiceRequestType')
export class ServiceRequestType {
  @JsonProperty('ExportTemplateName', String, true)
  exportTemplateName: string = null

  @JsonProperty('ServiceType', String, true) serviceType: string = null
}

@JsonObject('EditableCustomField')
export class EditableCustomField {
  @JsonProperty('FieldId', Number, true) fieldId: number = null

  @JsonProperty('FieldName', String, true) fieldName: string = null

  @JsonProperty('Description', String, true) description: string = null

  @JsonProperty('FieldValue', String, true) fieldValue: string = null
}

@JsonObject('SelectiveEndorsementSetting')
export class SelectiveEndorsementSetting {
  @JsonProperty('TagName', String, true) tagName: string = null

  @JsonProperty('TagId', Number, true) tagId: number = null

  @JsonProperty('EndorsementType', SelectiveEndorsementTypeEnumConverter, true)
  endorsementType: SelectiveEndorsementType = null

  @JsonProperty('EndorsementText', String, true) endorsementText: string = null

  @JsonProperty('EndorsementLocation', EndorsementLocationEnumConverter, true)
  endorsementLocation: EndorsementLocation = EndorsementLocation.LC
}

@JsonObject('ControlNumberSetting')
export class ControlNumberSetting {
  @JsonProperty('Prefix', String, true) prefix = ''

  @JsonProperty('PrefixDelimiter', PrefixDelimiterEnumConverter, true)
  prefixDelimiter: PrefixDelimiter = PrefixDelimiter.NONE

  @JsonProperty('StartNumber', Number, true) startNumber = 1

  @JsonProperty('EndorseControlNumber', Boolean, true) endorseControlNumber =
    false

  @JsonProperty('ControlNumberLocation', EndorsementLocationEnumConverter, true)
  controlNumberLocation: EndorsementLocation = EndorsementLocation.LR

  @JsonProperty('EndorseOptionalMessage', Boolean, true)
  endorseOptionalMessage = false

  @JsonProperty('MessageText', String, true) messageText = ''

  @JsonProperty(
    'OptionalMessageLocation',
    EndorsementLocationEnumConverter,
    true
  )
  optionalMessageLocation: EndorsementLocation = EndorsementLocation.LL

  @JsonProperty('VolumnId', String, true) volumeId?: string = ''

  @JsonProperty('VolumnId', String, true) volumnId?: string = ''

  @JsonProperty('PaddingLength', Number, true) paddingLength = 8

  @JsonProperty('ContinueFromPreviousControlNumber', Boolean, true)
  continueFromPreviousControlNumber = false

  @JsonProperty(
    'AdvancedEndorsementSetting',
    [SelectiveEndorsementSetting],
    true
  )
  advancedEndorsementSetting: SelectiveEndorsementSetting[] = null

  @JsonProperty('PrefixDelimiterValue', String, true) prefixDelimiterValue = ''
}

@JsonObject('ControlNumberEndorsement')
export class ControlNumberEndorsement {
  @JsonProperty('SortOrder', String, true) sortOrder = 'RELATIVE_FILE_PATH'

  @JsonProperty('ControlNumberSetting', ControlNumberSetting, true)
  controlNumberSetting: ControlNumberSetting = new ControlNumberSetting()

  @JsonProperty('ExportLocation', ExportLocationEnumConverter, true)
  exportLocation: ExportLocation = ExportLocation.VoDR_HOME
}

@JsonObject('FilterOptions')
export class FilterOptions {
  @JsonProperty('ExcludeProducedDocuments', Boolean, true)
  excludeProducedDocuments = false

  @JsonProperty('ExcludeNativeForRedactedDocuments', Boolean, true)
  excludeNativeForRedactedDocuments = false
}
@JsonObject('SavedSearchItem')
export class SavedSearchItem {
  @JsonProperty('SearchId', Number, true) searchId: number = null

  @JsonProperty('SearchName', String, true) searchName: string = null

  @JsonProperty('CustomFieldId', Number, true) customFieldId: number = null

  @JsonProperty('CustomFieldName', String, true) customFieldName: string = null
}

@JsonObject('Connector')
export class Connector {
  @JsonProperty('Id', Number, true) id: number = null

  @JsonProperty('Name', String, true) name: string = null

  @JsonProperty('ConnectorPlatform', String, true) connectorPlatform: string =
    null

  @JsonProperty('WorkspaceId', Number, true) workspaceId: number = null

  @JsonProperty('WorkspaceName', String, true) workspaceName: string = null

  @JsonProperty('UserEnvironmentId', Number, true) userEnvironmentId: number =
    null

  @JsonProperty('ConnectorFileSharePath', String, true)
  connectorFileSharePath: string = null
}

@JsonObject('ProductionOptions')
export class ProductionOptions {
  @JsonProperty('FilterOptions', FilterOptions, true)
  filterOptions: FilterOptions = new FilterOptions()

  @JsonProperty('FieldTemplateID', Number, true) fieldTemplateId = 10

  @JsonProperty('FieldTemplateID', Number, true) fieldTemplateID = 10

  @JsonProperty('SavedSearchesForExpressions', [SavedSearchItem], true)
  savedSearchesForExpressions: SavedSearchItem[] = null

  @JsonProperty('RelativityFieldMappingTemplateId', Number, true)
  relativityFieldMappingTemplateId = null

  @JsonProperty('Connector', Connector, true)
  connector: Connector = null
}

@JsonObject('PrintBinding')
export class PrintBinding {
  @JsonProperty('BindingType', BindingTypeEnumConverter, true)
  bindingType: BindingType = BindingType.NONE

  @JsonProperty('BinderSize', BinderSizeEnumConverter, true)
  binderSize: BinderSize = BinderSize.ONE

  @JsonProperty('BinderColor', BinderColorEnumConverter, true)
  binderColor: BinderColor = BinderColor.BLACK
}

@JsonObject('PrintSet')
export class PrintSet {
  @JsonProperty('PrintSetOption', PrintSetOptionEnumConverter, true)
  printSetOption: PrintSetOption = PrintSetOption.SINGLE_COPY_SET

  @JsonProperty('NumberOfSetValue', Number, true) numberOfSetValue = 1
}

@JsonObject('PrintServiceOption')
export class PrintServiceOption {
  @JsonProperty('PrintBinding', PrintBinding, true)
  printBinding: PrintBinding = new PrintBinding()

  @JsonProperty('PrintSet', PrintSet, true) printSet: PrintSet = new PrintSet()

  @JsonProperty('PaperType', PaperTypeEnumConverter, true)
  paperType: PaperType = PaperType.REGULAR

  @JsonProperty('FamilyFileHandling', FamilyFileHandlingEnumConverter, true)
  familyFileHandling: FamilyFileHandling =
    FamilyFileHandling.PARENTCHILD_AS_SEPARATE_DOC

  @JsonProperty('DocumentSeparator', DocumentSeparatorEnumConverter, true)
  documentSeparator: DocumentSeparator = DocumentSeparator.BLANK_BLUE_SLIPSHEET

  @JsonProperty('PaperSide', PaperSideEnumConverter, true)
  paperSide: PaperSide = PaperSide.SINGLE_SIDED
}

@JsonObject('PDFServiceOption')
export class PDFServiceOption {
  @JsonProperty('PDFType', PDFTypeEnumConverter, true) pdfType: PDFType =
    PDFType.SEARCHABLE

  @JsonProperty(
    'PDFFamilyFileHandling',
    PDFFamilyFileHandlingEnumConverter,
    true
  )
  pdfFamilyFileHandling: PDFFamilyFileHandling =
    PDFFamilyFileHandling.PARENTCHILD_AS_ONE_DOC

  @JsonProperty(
    'PDFFileNamingConvention',
    PDFFileNamingConventionEnumConverter,
    true
  )
  pdfFileNamingConvention: PDFFileNamingConvention =
    PDFFileNamingConvention.NAME_AFTER_CONTROL_NUMBER
}

@JsonObject('ThirdPartyBillingInformation')
export class ThirdPartyBillingInformation {
  @JsonProperty('ThirdPartyBillingEnabled', Boolean, true)
  thirdPartyBillingEnabled = false

  @JsonProperty('Company', String, true) company: string = null

  @JsonProperty('BillingAddress', String, true) billingAddress: string = null

  @JsonProperty('BillingCaseName', String, true) billingCaseName: string = null

  @JsonProperty('ContactPerson', String, true) contactPerson: string = null

  @JsonProperty('ContactPhone', String, true) contactPhone: string = null

  @JsonProperty('ContactEmail', String, true) contactEmail: string = null
}

@JsonObject('SettingsInfo')
export class SettingsInfo {
  @JsonProperty('NotifyAfterImgGenComplete', Boolean, true)
  notifyAfterImgGenComplete = false

  @JsonProperty('GenerateColorImage', Boolean, true) generateColorImage = false

  @JsonProperty('DefaultTiffColorOption', ConversionTypeEnumConverter, true)
  defaultTiffColorOption: ConversionType = ConversionType.BLACK_AND_WHITE

  @JsonProperty('SearchDupOption', DeduplicationOptionEnumConverter, true)
  searchDupOption: DeduplicationOption = DeduplicationOption.NONE

  @JsonProperty('SearchTerm', String, true) searchTerm: string = null

  @JsonProperty('TimeZone', EndorsementLocationEnumConverter, true)
  timeZone: EndorsementLocation = EndorsementLocation.LR

  @JsonProperty('TzTimeZone', String, true) tzTimeZone = 'Etc/GMT'

  @JsonProperty('EnableNativeFileHandling', Boolean, true)
  enableNativeFileHandling = false

  @JsonProperty('ServiceRequestType', ServiceTypeEnumConverter, true)
  serviceRequestType?: ServiceType = ServiceType.PDF_SERVICE

  @JsonProperty('ServiceRequestType', ServiceTypeEnumConverter, true)
  serviceType?: ServiceType = ServiceType.PRINT_SERVICE

  @JsonProperty('ExportTemplateName', String, true) exportTemplateName =
    'PDF Service'

  @JsonProperty('ApprovePreProcessPage_CostEstimate', Boolean, true)
  approvePreProcessPageCostEstimate = false

  @JsonProperty('ApprovePreProcessPage_CostEstimate', Boolean, true)
  approvePreProcessPage_CostEstimate = false

  @JsonProperty('ImageConversionOption', ImageConversionOption, true)
  imageConversionOption: ImageConversionOption = new ImageConversionOption()

  @JsonProperty('ControlNumber_Endorsement', ControlNumberEndorsement, true)
  controlNumber_Endorsement?: ControlNumberEndorsement = new ControlNumberEndorsement()

  @JsonProperty('ControlNumberEndorsement', ControlNumberEndorsement, true)
  controlNumberEndorsement?: ControlNumberEndorsement = new ControlNumberEndorsement()

  @JsonProperty('ProductionOptions', ProductionOptions, true)
  productionOptions: ProductionOptions = new ProductionOptions()

  @JsonProperty('PrintServiceOption', PrintServiceOption, true)
  printServiceOption: PrintServiceOption = new PrintServiceOption()

  @JsonProperty('PDFServiceOption', PDFServiceOption, true)
  pdfServiceOption: PDFServiceOption = new PDFServiceOption()

  @JsonProperty('ThirdPartyBillingOption', ThirdPartyBillingInformation, true)
  thirdPartyBillingOption: ThirdPartyBillingInformation = new ThirdPartyBillingInformation()

  @JsonProperty('WebURL', String, true) webURL = 'http://localhost/VenioWeb'

  @JsonProperty('ClientMatterNo', String, true) clientMatterNo: string = null

  @JsonProperty('CaseName', String, true) caseName = ''

  @JsonProperty('CreateImage', Boolean, true) createImage = true

  @JsonProperty('DataRetentionRequest', DataRetentionTypeEnumConverter, true)
  dataRetentionRequest: DataRetentionType =
    DataRetentionType.RETAIN_30DAYS_THEN_REMOVE

  @JsonProperty('EditableCustomFieldList', [EditableCustomField], true)
  editableCustomFieldList: EditableCustomField[] = null

  @JsonProperty('EditableCustomFieldList', [EditableCustomField], true)
  editableCustomFields: EditableCustomField[] = []

  @JsonProperty('ProductionSourceId', String, true) productionSourceId = ''

  @JsonProperty('EnableDiscoveryExceptionHandling', Boolean, true)
  enableDiscoveryExceptionHandling = true

  @JsonProperty('IndexOnlyCase', Boolean, true)
  indexOnlyCase = true

  @JsonProperty('AutoQueueForEntityExtraction', Boolean, true)
  autoQueueForEntityExtraction = false

  @JsonProperty('OcrLanguages', [String], true) ocrLanguages: string[] = null

  @JsonProperty('ProjectTemplateId', Number, true) projectTemplateId = 0

  @JsonProperty('TranscribeEngine', String, true) transcribeEngine: string =
    null

  @JsonProperty('AutoQueueTranscribe', Boolean, true) autoQueueTranscribe =
    false

  @JsonProperty('SupportedFiles', [String], true) supportedFiles: string[] =
    null

  @JsonProperty('AutoFolderRelativePathDuringIngestion', Boolean, true)
  autoFolderRelativePathDuringIngestion = false

  @JsonProperty('IngestionEngine', Number, true) ingestionEngine = 0
}

@JsonObject('VODRSettings')
export class VODRSettings {
  @JsonProperty('UploadedFileList', [UploadFileDetails], true)
  uploadedFileList: UploadFileDetails[] = null

  @JsonProperty('SettingsInfo', SettingsInfo, true)
  settingsInfo: SettingsInfo = null

  @JsonProperty('addDataToExistingCase', Boolean, true)
  addDataToExistingCase: boolean = null

  @JsonProperty('overrideSettingsInfo', Boolean, true)
  overrideSettingsInfo: boolean = null

  @JsonProperty('IsContinuedAfterControlNumberConflict', Boolean, true)
  isContinuedAfterControlNumberConflict: boolean = null
}

@JsonObject('ExportFieldTemplate')
export class ExportFieldTemplate {
  @JsonProperty('Id', Number, true) id: number = null

  @JsonProperty('IsDefaultTemplate', Boolean, true)
  isDefaultTemplate: boolean = null

  @JsonProperty('IsEditable', Boolean, true) isEditable: boolean = null

  @JsonProperty('Name', String, true) name: string = null

  @JsonProperty('TemplateProjectId', Number, true)
  templateProjectId: number = null
}

@JsonObject('ExportFieldDetails')
export class ExportFieldDetails {
  @JsonProperty('DESCRIPTION', String, true) description: string = null

  @JsonProperty('FIELD NAME', String, true) fieldName: string = null

  @JsonProperty('GROUP NAME', String, true) groupName: string = null

  @JsonProperty('PRODUCTION FIELD NAME', String, true)
  productionFieldName: string = null
}

/**
 * State model for Production.
 */
export interface ProductionStateModel {
  settingsInfo: SettingsInfo
  exportFieldTemplates: ExportFieldTemplate[]
  exportFieldDetails: ExportFieldDetails[]
  savedSearches: SavedSearchItem[]
  controlNumberConflictResponse: ResponseModel | undefined
}
