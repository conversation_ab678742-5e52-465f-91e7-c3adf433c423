import * as _ from 'lodash'
import {
  CustodianConfig,
  DataOverlayConfig,
  FullTextConfig,
  ImageConfig,
  ImageLoadFileConfig,
  ImageOverlayConfig,
  ImportConfig,
  ImportTemplateConfig,
  LoadFileConfig,
  <PERSON>er,
  MediaConfig,
  NativeConfig
} from '../../models/import-config'
import { ImportProgress } from '../../models/import-progress'
import { ImportStatus } from '../../models/import-status'
import { ImportConfigActions, ImportConfigActionTypes } from '../actions'

export interface ImportConfigState {
  importConfig: ImportConfig
  loadFileToVenioMapping: any
  analyzeSpinner: boolean
  validateSpinner: boolean
  validateFilePathsSpinner: boolean
  importSpinner: boolean
  customFieldsCreationErrorMessage: string
  customFieldsCreationError: any
  customFieldsCreated: any
  customFieldsSpinner: boolean
  importStatus: ImportStatus
  importProgress: ImportProgress
  importTemplates: ImportTemplateConfig[]
  importTemplate: ImportTemplateConfig
  importTemplateAlreadyExist: boolean
  delimiters: string[]
  processCheckboxValueChange: boolean
  validateFileExistence: boolean
}

export const initialState: ImportConfigState = {
  importConfig: {
    loadFile: {} as LoadFileConfig,
    imageLoadFile: {} as ImageLoadFileConfig,
    native: {} as NativeConfig,
    image: {} as ImageConfig,
    fullText: {} as FullTextConfig,
    venioToLoadFileMapping: {} as any,
    custodian: {} as CustodianConfig,
    media: {} as MediaConfig,
    dataOverlay: {
      isOverlayLoadFileData: false,
      isReplaceOverlay: false,
      overlayFieldIdentifier: '',
      overlayDelimiter: ''
    } as DataOverlayConfig,
    imageOverlay: {
      processImage: false,
      overlayReferenceFieldName: Matcher.BatesNumber,
      customFieldName: '',
      copyImageToProjectLocation: false,
      autoTiffOcr: false,
      pageLevelReplacement: false
    } as ImageOverlayConfig
  },
  loadFileToVenioMapping: {},
  analyzeSpinner: false,
  validateSpinner: false,
  validateFilePathsSpinner: false,
  importSpinner: false,
  customFieldsCreationErrorMessage: null,
  customFieldsCreationError: null,
  customFieldsCreated: null,
  customFieldsSpinner: false,
  importStatus: null,
  importProgress: null,
  importTemplates: [],
  importTemplate: null,
  importTemplateAlreadyExist: false,
  delimiters: null,
  processCheckboxValueChange: false,
  validateFileExistence: false
}

export const progressInitialState: ImportProgress = {
  extractionProgress: 0,
  analysisProgress: 0,
  analysisProgressRemark: '-',
  validationLineNumber: 0,
  validationProgress: 0,
  validationRemark: '-',
  importLineNumber: 0,
  importProgress: 0,
  duplicateMediaSummaryStatus: 0,
  importProgressRemark: '-',
  duplicateMediaSummaryRemark: '-',
  queueEmailAnalysisStatus: 0,
  queueEmailAnalysisRemark: '-',
  queueLanguageIdentificationStatus: 0,
  queueLanguageIdentificationRemark: '-',
  queueIndexingStatus: 0,
  queueIndexingRemark: '-'
}

export function reducer(
  state = initialState,
  action: ImportConfigActions
): ImportConfigState {
  let newState

  switch (action.type) {
    case ImportConfigActionTypes.ConfigureLoadFilePath:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          loadFile: {
            ...state.importConfig.loadFile,
            filePath: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureLoadFileFormat:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          loadFile: {
            ...state.importConfig.loadFile,
            format: {
              type: action.payload.type,
              fieldSeparator: action.payload.fieldSeparator,
              lineBreak: action.payload.lineBreak,
              textQualifier: action.payload.textQualifier
            }
          }
        }
      }

    case ImportConfigActionTypes.ConfigureLoadFileProcessOption:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          loadFile: {
            ...state.importConfig.loadFile,
            processLoadFile: action.processLoadFile
          },
          native: {
            ...state.importConfig.native,
            processNative: action.processLoadFile
          },
          fullText: {
            ...state.importConfig.fullText,
            processFullText: action.processLoadFile
          }
        }
      }

    case ImportConfigActionTypes.ConfigureImageLoadFilePath:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          imageLoadFile: {
            ...state.importConfig.imageLoadFile,
            filePath: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureNativeProcessOption:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          native: {
            ...state.importConfig.native,
            processNative: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureNativeFilePathField:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          native: {
            ...state.importConfig.native,
            fieldWithPath: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureNativeFolderPath:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          native: {
            ...state.importConfig.native,
            folderPath: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureNativeExtractFulltext:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          native: {
            ...state.importConfig.native,
            extractFulltextFromNative: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureAutoComputeFileSize:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          native: {
            ...state.importConfig.native,
            autoComputeFileSize: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureAutoComputeFileExtension:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          native: {
            ...state.importConfig.native,
            autoComputeFileExtension: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureAutoComputeFileType:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          native: {
            ...state.importConfig.native,
            autoComputeFileType: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureFullTextProcessOption:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          fullText: {
            ...state.importConfig.fullText,
            processFullText: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureFullTextFilePathField:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          fullText: {
            ...state.importConfig.fullText,
            fieldWithPath: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureFullTextFolderPath:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          fullText: {
            ...state.importConfig.fullText,
            folderPath: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureFulltextFromLoadFile:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          fullText: {
            ...state.importConfig.fullText,
            hasExtractedTextInLoadFile: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureExtractedTextFields:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          fullText: {
            ...state.importConfig.fullText,
            extractedTextFields: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureImageProcessOption:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          image: {
            ...state.importConfig.image,
            processImage: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureImageMappingField:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          image: {
            ...state.importConfig.image,
            imageMappingField: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureImageFolderPath:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          image: {
            ...state.importConfig.image,
            folderPath: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureDateFormat:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          loadFile: {
            ...state.importConfig.loadFile,
            dateFormat: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureTimeZone:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          loadFile: {
            ...state.importConfig.loadFile,
            timeZone: action.payload
          }
        }
      }

    case ImportConfigActionTypes.ConfigureCustodian:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          custodian: action.custodian
        }
      }

    case ImportConfigActionTypes.ClearMapping:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          venioToLoadFileMapping: {},
          native: {
            ...state.importConfig.native,
            fieldWithPath: ''
          },
          fullText: {
            ...state.importConfig.fullText,
            fieldWithPath: ''
          },
          image: {
            ...state.importConfig.image,
            imageMappingField: ''
          }
        },
        loadFileToVenioMapping: {}
      }

    case ImportConfigActionTypes.AddLoadFileToVenioMapping:
      newState = _.cloneDeep(state)
      setLoadFileToVenioMapping(
        newState,
        action.loadFileField,
        action.venioField
      )
      setVenioToLoadFileMapping(
        newState,
        action.venioField,
        action.loadFileField
      )
      return newState

    case ImportConfigActionTypes.RemoveLoadFileToVenioMapping:
      newState = _.cloneDeep(state)
      if (action.venioField !== null) {
        unsetLoadFileToVenioMapping(
          newState,
          action.loadFileField,
          action.venioField
        )
        unsetVenioToLoadFileMapping(newState, action.venioField)
      } else {
        const mappedVenioFields = getMappedVenioFields(
          newState,
          action.loadFileField
        )
        if (mappedVenioFields != null) {
          for (const venioField of mappedVenioFields) {
            unsetVenioToLoadFileMapping(newState, venioField)
          }
        }
        delete newState.loadFileToVenioMapping[action.loadFileField]
      }
      return newState

    case ImportConfigActionTypes.AddVenioToLoadFileMapping:
      newState = _.cloneDeep(state)
      setVenioToLoadFileMapping(
        newState,
        action.venioField,
        action.loadFileField
      )
      setLoadFileToVenioMapping(
        newState,
        action.loadFileField,
        action.venioField
      )
      return newState

    case ImportConfigActionTypes.RemoveVenioToLoadFileMapping: {
      newState = _.cloneDeep(state)
      const loadFileField = getMappedLoadFileField(newState, action.venioField)
      unsetVenioToLoadFileMapping(newState, action.venioField)
      unsetLoadFileToVenioMapping(newState, loadFileField, action.venioField)
      return newState
    }

    case ImportConfigActionTypes.FetchImportConfigSuccess:
      return {
        ...state,
        importConfig: action.payload
      }

    case ImportConfigActionTypes.FetchImportTemplatesSuccess:
      return {
        ...state,
        importTemplates: action.payload
      }

    case ImportConfigActionTypes.SetImportTemplate:
      return {
        ...state,
        importTemplate: action.importTemplate
      }

    case ImportConfigActionTypes.SetImportTemplateExistance:
      return {
        ...state,
        importTemplateAlreadyExist: action.importTemplateAlreadyExist
      }

    case ImportConfigActionTypes.ShowAnalyzeSpinner:
      return {
        ...state,
        analyzeSpinner: true
      }

    case ImportConfigActionTypes.HideAnalyzeSpinner:
      return {
        ...state,
        analyzeSpinner: false
      }

    case ImportConfigActionTypes.ShowValidateSpinner:
      return {
        ...state,
        validateSpinner: true
      }

    case ImportConfigActionTypes.HideValidateSpinner:
      return {
        ...state,
        validateSpinner: false
      }

    case ImportConfigActionTypes.ShowValidateFilePathsSpinner:
      return {
        ...state,
        validateFilePathsSpinner: true
      }

    case ImportConfigActionTypes.HideValidateFilePathsSpinner:
      return {
        ...state,
        validateFilePathsSpinner: false
      }

    case ImportConfigActionTypes.ShowImportSpinner:
      return {
        ...state,
        importSpinner: true
      }

    case ImportConfigActionTypes.HideImportSpinner:
      return {
        ...state,
        importSpinner: false
      }

    case ImportConfigActionTypes.AddCustomFieldsError: {
      let customFieldsCreationError
      if (typeof action.errorData === 'string') {
        try {
          customFieldsCreationError = JSON.parse(action.errorData)
        } catch (e) {
          customFieldsCreationError = action.errorData
        }
      } else if (typeof action.errorData === 'object') {
        customFieldsCreationError = action.errorData
      } else {
        customFieldsCreationError = null
      }
      return {
        ...state,
        customFieldsCreationError: customFieldsCreationError,
        customFieldsCreationErrorMessage: action.errorMessage,
        customFieldsCreated: null
      }
    }

    case ImportConfigActionTypes.AddCustomFieldsSuccess:
      return {
        ...state,
        customFieldsCreated: action.customFields,
        customFieldsCreationError: null,
        customFieldsCreationErrorMessage: null
      }

    case ImportConfigActionTypes.ShowCustomFieldsSpinner:
      return {
        ...state,
        customFieldsSpinner: true
      }

    case ImportConfigActionTypes.HideCustomFieldsSpinner:
      return {
        ...state,
        customFieldsSpinner: false
      }

    case ImportConfigActionTypes.ClearCustomFieldsState:
      return {
        ...state,
        customFieldsCreationError: null,
        customFieldsCreationErrorMessage: null,
        customFieldsCreated: null
      }

    case ImportConfigActionTypes.FetchImportStatusSuccess:
      return {
        ...state,
        importStatus: action.importStatus
      }

    case ImportConfigActionTypes.FetchImportProgressSuccess:
      return {
        ...state,
        importProgress: action.importProgress
      }

    case ImportConfigActionTypes.ResetImportProgress:
      return {
        ...state,
        importProgress: progressInitialState
      }

    case ImportConfigActionTypes.SetCustomDelimiterAction:
      return {
        ...state,
        delimiters: action.delimiters
      }

    case ImportConfigActionTypes.SetDataOverlayConfigAction:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          dataOverlay:
            action.dataOverlayConfigs ?? initialState.importConfig.dataOverlay
        }
      }
    case ImportConfigActionTypes.SetDataOverlayFlagAction:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          dataOverlay: {
            ...initialState.importConfig.dataOverlay,
            ...state.importConfig.dataOverlay,
            isOverlayLoadFileData: action.flag
          }
        }
      }
    case ImportConfigActionTypes.SetImageOverlayFlagAction:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          imageOverlay: {
            ...initialState.importConfig.imageOverlay,
            ...state.importConfig.imageOverlay,
            processImage: action.flag
          }
        }
      }
    case ImportConfigActionTypes.SetImageOverlayConfigAction:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          imageOverlay:
            action.imageOverlayConfig ?? initialState.importConfig.imageOverlay
        }
      }

    case ImportConfigActionTypes.SetDataCustodianMediaConfigAction:
      return {
        ...state,
        importConfig: {
          ...state.importConfig,
          custodian: action.custodian ?? initialState.importConfig.custodian,
          media: action.media ?? initialState.importConfig.media
        }
      }

    case ImportConfigActionTypes.SetValidateFileExistence:
      return {
        ...state,
        validateFileExistence: action.payload
      }

    case ImportConfigActionTypes.ClearUI:
      return initialState

    default:
      return state
  }
}

function setLoadFileToVenioMapping(
  state,
  loadFileField: string,
  venioField: string
) {
  const mapping = state.loadFileToVenioMapping
  let venioFields = mapping[loadFileField]
  if (venioFields != null) {
    if (venioFields.indexOf(venioField) === -1) {
      venioFields.push(venioField)
    }
  } else {
    venioFields = [venioField]
    mapping[loadFileField] = venioFields
  }
}

function unsetLoadFileToVenioMapping(
  state,
  loadFileField: string,
  venioField: string
) {
  const mapping = state.loadFileToVenioMapping
  const venioFields = mapping[loadFileField]
  if (venioFields != null) {
    const index = venioFields.indexOf(venioField)
    if (index !== -1) {
      venioFields.splice(index, 1)
    }
    if (venioFields.length === 0 || !venioField) {
      delete mapping[loadFileField]
    }
  }
}

function setVenioToLoadFileMapping(
  state,
  venioField: string,
  loadFileField: string
) {
  const mapping = state.importConfig.venioToLoadFileMapping
  mapping[venioField] = loadFileField

  // This is a unique case, need to set image mapping field the same as DOCUMENT_UNIQUE_IDENTIFIER mapped field
  if (venioField === 'DOCUMENT_UNIQUE_IDENTIFIER') {
    state.importConfig.image.imageMappingField = loadFileField
  }

  // This is a unique case, need to set custodian config if CUSTODIAN field mapped
  if (venioField === 'CUSTODIAN') {
    state.importConfig.custodian = {
      custodianFromLoadFileField: true,
      custodianLoadFileField: loadFileField
    } as CustodianConfig
  }
}

function unsetVenioToLoadFileMapping(state, venioField: string) {
  const mapping = state.importConfig.venioToLoadFileMapping
  delete mapping[venioField]

  // This is a unique case, unset image mapping field if clearing field mapping to DOCUMENT_UNIQUE_IDENTIFIER
  if (venioField === 'DOCUMENT_UNIQUE_IDENTIFIER') {
    state.importConfig.image.imageMappingField = ''
  }

  // This is a unique case, unset custodian config when CUSTODIAN field is unmapped
  if (venioField === 'CUSTODIAN') {
    state.importConfig.custodian = initialState.importConfig.custodian
  }
}

function getMappedLoadFileField(state, venioField: string): string {
  const mapping = state.importConfig.venioToLoadFileMapping
  return mapping[venioField]
}

function getMappedVenioFields(state, loadFileField: string): string[] {
  const mapping = state.loadFileToVenioMapping
  return mapping[loadFileField]
}
