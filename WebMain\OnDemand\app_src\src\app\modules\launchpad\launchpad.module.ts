import { AdminAccessGuard } from '@admin-advance/guards/admin-access-guard'
import {
  CaseService,
  ClientMgmtService,
  CustomFieldService
} from '@admin-advance/services'
import { CaseStates } from '@admin-advance/store/case/case.states'
import { ClientMgmtState } from '@admin-advance/store/client-management/client-management.state'
import { CustomFieldStates } from '@admin-advance/store/custom-field/custom-field.states'
import { DragDropModule } from '@angular/cdk/drag-drop'
import { OverlayModule } from '@angular/cdk/overlay'
import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatAutocompleteModule } from '@angular/material/autocomplete'
import { MatBadgeModule } from '@angular/material/badge'
import { MatButtonModule } from '@angular/material/button'
import { MatCardModule } from '@angular/material/card'
import { MatCheckboxModule } from '@angular/material/checkbox'
import { MatChipsModule } from '@angular/material/chips'
import { MatNativeDateModule, MAT_DATE_LOCALE } from '@angular/material/core'
import { MatDatepickerModule } from '@angular/material/datepicker'
import { MatDialogModule } from '@angular/material/dialog'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatFormFieldModule } from '@angular/material/form-field'
import { MatIconModule } from '@angular/material/icon'
import { MatInputModule } from '@angular/material/input'
import { MatMenuModule } from '@angular/material/menu'
import { MatProgressBarModule } from '@angular/material/progress-bar'
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'
import { MatRadioModule } from '@angular/material/radio'
import { MatSelectModule } from '@angular/material/select'
import { MatStepperModule } from '@angular/material/stepper'
import { MatTabsModule } from '@angular/material/tabs'
import { MatTooltipModule } from '@angular/material/tooltip'
import { AccessEffects } from '@auth/store/effects/access.effects'
import { ConfigModule } from '@config/config.module'
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome'
import { NgSelectModule } from '@ng-select/ng-select'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { StoreDevtoolsModule } from '@ngrx/store-devtools'
import { NgxsModule } from '@ngxs/store'
import { CaseLaunchpadContainerComponent } from '@root/modules/micro-apps/case-launchpad/components/case-launchpad-container/case-launchpad-container.component'
import { ReportsContainerComponent } from '@root/modules/micro-apps/review-next/components/reports-container/reports-container.component'
import { CardModule } from '@shared/card/card.module'
import { SwitchModule } from '@shared/components/switch/switch.module'
import { SharedDirectivesModule } from '@shared/directives/shared-directives.module'
import { DynamicFormModule } from '@shared/dynamic-form'
import { ContentPlaceholderModule } from '@shared/placeholder/content-placeholder.module'
import { SharedModule } from '@shared/shared.module'
import { DocumentsEffects } from '@shared/store/effects/document.effects'
import { SharedEffects } from '@shared/store/effects/shared.effects'
import { AgGridModule } from 'ag-grid-angular'
import {
  DxButtonGroupModule,
  DxButtonModule,
  DxDataGridModule,
  DxDropDownBoxModule,
  DxHtmlEditorModule,
  DxLoadPanelModule,
  DxPopoverModule,
  DxTagBoxModule,
  DxTemplateModule,
  DxTreeListModule
} from 'devextreme-angular'
import { RouterEffects } from 'ngrx-router'
import { AccordionModule } from 'ngx-bootstrap/accordion'
import { PopoverModule } from 'ngx-bootstrap/popover'
import { InfiniteScrollModule } from 'ngx-infinite-scroll'
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search'
import { NgxSpinnerModule } from 'ngx-spinner'
import { environment } from 'src/environments/environment'
import { ServiceUploadModule } from './../service-upload/service-upload.module'
import { AdGroupMappingComponent } from './components/ad-group-mapping/ad-group-mapping.component'
import { IdpGroupMappingComponent } from './components/idp-group-mapping/idp-group-mapping.component'
import { SettingsContainerComponent } from './components/settings-container/settings-container.component'
import { LegalHoldGuard } from './guards/legal-hold.guard'
import { LaunchpadRoutingModule } from './launchpad-routing.module'
import { ActiveDirectorySettingComponent } from './pages/active-directory-setting/active-directory-setting.component'
import { CaseSettingComponent } from './pages/case-setting/case-setting.component'
import { AdvanceIngestionSettingComponent } from './pages/case-setting/components/advance-ingestion-setting/advance-ingestion-setting.component'
import { ConversionSettingComponent } from './pages/case-setting/components/conversion-setting/conversion-setting.component'
import { DateFilterComponent } from './pages/case-setting/components/date-filter/date-filter.component'
import { EdaiEcaComponent } from './pages/case-setting/components/edai-eca/edai-eca.component'
import { EmbeddedItemFilterComponent } from './pages/case-setting/components/embedded-item-filter/embedded-item-filter.component'
import { FileExtensionFilterComponent } from './pages/case-setting/components/file-extension-filter/file-extension-filter.component'
import { FileIdentificationComponent } from './pages/case-setting/components/file-identification/file-identification.component'
import { ImageFileExtensionComponent } from './pages/case-setting/components/image-file-extension/image-file-extension.component'
import { ImportFileExtensionsComponent } from './pages/case-setting/components/import-file-extensions/import-file-extensions.component'
import { FiletypeGroupTimeoutComponent } from './pages/case-setting/components/ingestion-settings/child/filetype-group-timeout/filetype-group-timeout.component'
import { FiletypeTimeoutSettingComponent } from './pages/case-setting/components/ingestion-settings/child/filetype-timeout-setting/filetype-timeout-setting.component'
import { IngestionSettingsComponent } from './pages/case-setting/components/ingestion-settings/ingestion-settings.component'
import { NearDuplicateSettingComponent } from './pages/case-setting/components/near-duplicate-setting/near-duplicate-setting.component'
import { SocialMediaSettingComponent } from './pages/case-setting/components/social-media-setting/social-media-setting.component'
import { StopWordsComponent } from './pages/case-setting/components/stop-words/stop-words.component'
import { CustodianPortalComponent } from './pages/custodian-portal/custodian-portal.component'
import { DashboardComponent } from './pages/dashboard/dashboard.component'
import { DraftComponent } from './pages/draft/draft.component'
import { NoticeManageComponent } from './pages/draft/notice-manage/notice-manage.component'
import { CaseLaunchpadFbiComponent } from './pages/launchpad/case-launchpad-fbi/case-launchpad-fbi.component'
import { CaseLaunchpadComponent } from './pages/launchpad/case-launchpad.component'
import { AddCustodianComponent } from './pages/launchpad/modal/add-custodian/add-custodian.component'
import { CreateHoldComponent } from './pages/launchpad/modal/create-hold/create-hold.component'
import { ImportCustodianComponent } from './pages/launchpad/modal/import-custodian/import-custodian.component'
import { PickCustodianComponent } from './pages/launchpad/modal/pick-custodian/pick-custodian.component'
import { ScheduleNoticeComponent } from './pages/launchpad/modal/schedule-notice/schedule-notice.component'
import { StateErrorDetailComponent } from './pages/launchpad/modal/state-error-detail/state-error-detail.component'
import { TemplateComponent } from './pages/launchpad/modal/template/template.component'
import { ViewResponseComponent } from './pages/launchpad/modal/view-response/view-response.component'
import { CaseSettingService } from './services/case-setting.service'
import { CaseSettingsFormHandlerService } from './services/case-settings-form-handler.service'
import { LaunchpadService } from './services/launchpad.service'
import { reducers } from './store'
import { CaseSettingFacade } from './store/case-setting/case-setting.facade'
import { CaseSettingState } from './store/case-setting/case-setting.state'
import { CaseEffects } from './store/effects/case.effects'
import { LegalHoldStates } from './store/legal-hold/legal-hold.states'
import { NotificationTemplateStates } from './store/notification-template/notification-template.states'
import { StateErrorState } from './store/state-error/state-error.state'

@NgModule({
  declarations: [
    CaseLaunchpadComponent,
    CaseLaunchpadFbiComponent,
    CaseSettingComponent,
    DashboardComponent,
    CreateHoldComponent,
    DraftComponent,
    TemplateComponent,
    AddCustodianComponent,
    CustodianPortalComponent,
    ImportCustodianComponent,
    ViewResponseComponent,
    ScheduleNoticeComponent,
    PickCustodianComponent,
    NoticeManageComponent,
    SettingsContainerComponent,
    ImageFileExtensionComponent,
    ConversionSettingComponent,
    NearDuplicateSettingComponent,
    EmbeddedItemFilterComponent,
    ActiveDirectorySettingComponent,
    IngestionSettingsComponent,
    AdvanceIngestionSettingComponent,
    DateFilterComponent,
    FileExtensionFilterComponent,
    ImportFileExtensionsComponent,
    FileIdentificationComponent,
    SocialMediaSettingComponent,
    FiletypeTimeoutSettingComponent,
    AdGroupMappingComponent,
    FiletypeGroupTimeoutComponent,
    IdpGroupMappingComponent,
    StateErrorDetailComponent,
    ReportsContainerComponent,
    StopWordsComponent,
    CaseLaunchpadContainerComponent,
    EdaiEcaComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    LaunchpadRoutingModule,
    StoreModule.forFeature('launchpad', reducers),
    EffectsModule.forFeature([
      CaseEffects,
      RouterEffects,
      SharedEffects,
      DocumentsEffects,
      AccessEffects
    ]),
    !environment.production ? StoreDevtoolsModule.instrument() : [],
    InfiniteScrollModule,
    NgxSpinnerModule,
    AccordionModule.forRoot(),
    AgGridModule.withComponents([]),
    NgxsModule.forFeature([
      LegalHoldStates,
      NotificationTemplateStates,
      CaseSettingState,
      CustomFieldStates,
      StateErrorState,
      CaseStates,
      ClientMgmtState
    ]),
    ConfigModule,
    SharedModule,
    NgSelectModule,
    ContentPlaceholderModule,
    MatStepperModule,
    MatCardModule,
    MatDialogModule,
    MatIconModule,
    MatButtonModule,
    MatAutocompleteModule,
    CardModule,
    MatExpansionModule,
    DxDataGridModule,
    MatSelectModule,
    MatTabsModule,
    MatTooltipModule,
    MatFormFieldModule,
    NgxMatSelectSearchModule,
    MatInputModule,
    DxLoadPanelModule,
    DragDropModule,
    DxDropDownBoxModule,
    DxHtmlEditorModule,
    DxButtonGroupModule,
    MatProgressSpinnerModule,
    DynamicFormModule,
    SwitchModule,
    MatCheckboxModule,
    MatRadioModule,
    PopoverModule,
    OverlayModule,
    MatChipsModule,
    MatProgressBarModule,
    DxTreeListModule,
    DxPopoverModule,
    DxButtonModule,
    DxTemplateModule,
    DxTagBoxModule,
    MatMenuModule,
    MatBadgeModule,
    SharedDirectivesModule,
    ServiceUploadModule,
    MatDatepickerModule,
    MatInputModule,
    MatNativeDateModule
  ],
  exports: [MatFormFieldModule, MatInputModule],
  entryComponents: [],
  providers: [
    LaunchpadService,
    AdminAccessGuard,
    LegalHoldGuard,
    CaseSettingsFormHandlerService,
    CaseSettingService,
    CustomFieldService,
    CaseSettingFacade,
    CaseService,
    ClientMgmtService,
    { provide: MAT_DATE_LOCALE, useValue: 'en-US' }
  ]
})
export class LaunchpadModule {}
