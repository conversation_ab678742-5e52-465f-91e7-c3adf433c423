import {
  FetchReviewSetReviewerAction,
  ReviewSetStateSelector
} from '@admin-advance/store'
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild
} from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { Store } from '@ngxs/store'
import { DxChartComponent } from 'devextreme-angular'
import * as moment from 'moment'
import { Subject } from 'rxjs'
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil,
  tap
} from 'rxjs/operators'
import { ReviewStatusModel } from '../../../../models/review-set/review-set.model'

@Component({
  templateUrl: './reviewer.component.html',
  styleUrls: ['../child/data-container.component.scss']
})
export class ReviewerComponent implements AfterViewInit, OnInit, OnDestroy {
  /**
   * Cleanup placeholder for the observers when the component get destroyed.
   */
  private readonly toDestroy$ = new Subject<void>()

  /**
   * Bar chart widget component
   */
  @ViewChild(DxChartComponent)
  private readonly barChart: DxChartComponent

  /**
   * Chart data
   */
  data: ReviewStatusModel[] = []

  isReviewerLoading = true

  constructor(
    private cdr: ChangeDetectorRef,
    private router: Router,
    private store: Store,
    private route: ActivatedRoute
  ) {}

  /**
   * Customized tooltip text of chart series
   */
  customizeTooltip(arg: any): unknown {
    return {
      text: `${arg.point.data.userName} - ${
        arg.point.data.reviewedDocCount
      } documents
       <span style="text-align: left">
             Date: ${moment(new Date(arg.point.data.reviewedDate)).format(
               'MMM DD, YYYY'
             )}
       </span>`
    }
  }

  ngOnInit(): void {
    this.initEvent()
  }

  ngAfterViewInit(): void {
    this.initSlices()
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Init router event and load progress data on the widget.
   */
  private readonly initEvent = (): void => {
    const qp = this.route.snapshot.queryParams
    if (!!qp && qp['projectId'] > 0 && qp['reviewSetId'] > 0)
      this.store.dispatch(
        new FetchReviewSetReviewerAction(+qp['projectId'], +qp['reviewSetId'])
      )
    else this.data = []
  }

  /**
   * Init slices from store
   */
  private readonly initSlices = (): void => {
    if (this.barChart) {
      this.barChart.instance.refresh()
    }
    this.cdr.detectChanges()
    this.store
      .select(ReviewSetStateSelector.SliceOf('reviewSetReviewers'))
      .pipe(
        distinctUntilChanged(),
        debounceTime(600),
        tap((d) => {
          this.cdr.markForCheck()
          this.data = d
            .map((p) => ({ ...p, date: new Date(p.reviewedDate) }))
            .sort((a: any, b: any) => a.date - b.date)
            .map((p) => ({
              ...p,
              // format the date to display in chart series
              day: moment(p.date).format('MMM DD')
            }))
          this.isReviewerLoading = false
        }),
        debounceTime(100),
        filter(() => !!this.barChart),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: () => {
          this.barChart.instance.refresh()
          this.cdr.detectChanges()
        }
      })
  }

  // TODO: we'll replace this with date range filter later
  randomSeries(): void {
    // below code is for demo only
    // function randoms(min, max) {
    //   min = Math.ceil(min)
    //   max = Math.floor(max)
    //   return Math.floor(Math.random() * (max - min + 1)) + min
    // }

    // this.data = this.data.slice().map((el, index) => ({
    //   ...el,
    //   number: randoms(1000, 9000),
    //   day: `${randoms(index, 30)}`
    // }))

    this.barChart.instance.refresh()
  }
}
