@import '~src/variables';

.navbar {
  padding: 0;
  -webkit-box-shadow: 0 6px 7px -7px #999;
  -moz-box-shadow: 0 6px 7px -7px #999;
  box-shadow: 0 6px 7px -7px #999;
}

.case-select-span {
  display: flex;

  .case-label {
    padding-top: 0.5rem;
    padding-right: 0.5rem;
  }
}

.review2-nav {
  ul {
    &.navbar-nav {
      width: 100%;
      justify-content: flex-end;
      position: relative;

      .case-select {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}

.nav-item {
  font-size: 16px;
  display: flex;
  align-items: center;
  padding: 0 6px;
  color: v(black-text);

  &.bg-grey {
    background-color: #f3f3f3;
    display: flex;
    align-items: center;
    padding: 2.1px 15px;

    .fa {
      font-size: 32px;
      color: v(dark-grey);
    }
  }

  span {
    padding: 0 8px;
    font-size: 14px;
  }
}

.title-venio {
  color: v(venio-secondary-color);
  padding-right: 2px;
}

.title-one {
  background-color: v(venio-primary-color);
  color: white;
  font-weight: bold;
  padding: 2px;
  border-radius: 5px;
}

.box {
  font-size: medium;
  border-radius: 4px;
}

.box.active {
  transform: perspective(1px) translateZ(0);
  backface-visibility: hidden;
  border-top: 2px solid v(venio-theme-color-main-extra-dark);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.box:hover {
  transform: perspective(1px) translateZ(0);
  backface-visibility: hidden;
  border-top: 2px solid v(venio-theme-color-main-extra-dark);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.dropdown-menu {
  border: 0;
  -webkit-box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.4);
  -moz-box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.4);
}

dx-menu {
  height: 100%;
  font-size: medium;
}

.dx-menu .dx-menu-item-content {
  padding: 0;
}

.fa-bell {
  color: #ffe529;
  font-size: 26px;
}

.notification-icon {
  &:after {
    display: none;
  }
}

.notification-counter {
  position: absolute;
  top: 5px;
  left: 23px;
  background-color: v(venio-red);
  color: #fff;
  border-radius: 7px;
  padding: 1px 6px;
  font: 8px Verdana !important;
  &.v-notification-counter{
    background-color: v(venio-nxt-error-color);
    padding: 2px !important;
    border-radius: 100px;
    display: grid;
    place-content: center;
    left: 25px;
    top: 9px;
    width: 17px;
    height: 16px;
  }
}

.notifications-scroll {
  width: 40vw;
  height: 15rem !important;
  overflow-y: auto;
}

#notification-menu {
  padding: 15px;
}

.notification-dropdown-content {
  padding: 0;
}

.notification-dropdown-content .dropdown-item {
  border-left: 5px solid v(border-color);
  border-bottom: 1px solid v(venio-secondary-color);
}

.notification-not-viewed {
  border-left: 5px solid v(venio-secondary-color) !important;
  background: v(venio-theme-color-main-light) !important;
}

.notification-title {
  color: black;
}

.notification-time {
  color: gray;
  font-size: small;
}

#notification-content-container {
  white-space: normal;
}

/*-- rules to manage top right case text in the `li` element ---*/

.case-text-wrap {
  margin-right: 15px;
  font-size: 14px;
  color: #255985;
  cursor: pointer;
  position: relative;
  top: 5px;
}

.case-text-wrap:hover {
  color: #09579b;
}

/*-- rules:  make left li element to pointer when user hovers to it. ---*/
.left-inlined-nav li:hover {
  cursor: pointer;
}

/*-- rules to manage top right case text in the `li` element ---*/

.case-text-wrap {
  margin-right: 15px;
  font-size: 14px;
  color: #255985;
  cursor: pointer;
  position: relative;
  top: 5px;
}

.case-text-wrap:hover {
  color: #09579b;
}

// do not overlap everything on the screen.
.fixed-top {
  z-index: 1000 !important;
}

::ng-deep {

  // Overriding the dialog box default rules.
  .mat-dialog-container {
    padding: 0 !important;
  }

  .mat-dialog-content {
    margin: 7px;
  }

  .mat-dialog-actions {
    margin-bottom: 0px;
  }
}

.help-url-header {
  background-color: #607929;
  height: 44px;
  color: white;
}

.help-url-fx-spacer {
  flex: 1 1 auto;
}

.no-url-label {
  top: 50%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  .mat-icon {
    color: #607929;
  }
}

.navbar-nav {
  li {
    mat-select {
      min-width: 200px;

      &+span {
        min-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// side nav
.side-nav {
  background-color: v(side-nav);
  width: 70px;
  position: fixed;
  height: calc(100vh - 52px);
  border-radius: 0 15px 15px 0;
  margin-top: 52px;
  z-index: 9;
  padding-top: 20px;

  li {
    min-height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;

    a {
      color: #fff;
      display: flex;
      font-size: 20px;
      text-align: center;
      padding: 0 10px;
      position: relative;
      z-index: 3;

      fa-icon {
        transition: 0.2s font-size ease-in-out;
      }

      &:after {
        position: absolute;
        top: 10px;
        right: 3px;
        margin: 0 !important;
      }

      span.custom-tooltip {
        display: none;
        position: absolute;
        color: #fff !important;
        background-color: v(venio-secondary-color);
        font-size: 12px;
        padding: 5px;
        border-radius: 3px;
        left: 78px;
        top: 50%;
        transform: translateY(-50%);
        box-shadow: 0 0 3px #000;

        &:before {
          top: -7px;
          z-index: 99;
          content: '\f0d9';
          font-size: 26px;
          font-family: 'Font Awesome 5 Free';
          font-weight: 900;
          position: absolute;
          left: -8px;
          color: v(venio-secondary-color);
        }
      }

      &:hover {
        span.custom-tooltip {
          display: block;
        }

        fa-icon {
          font-size: 25px;
        }
      }
    }

    &.active {
      position: relative;

      &:before {
        position: absolute;
        content: '';
        top: -24px;
        background: url(../../../../../assets/images/nav-active.png);
        background-size: contain;
        background-repeat: no-repeat;
        height: 125px;
        width: 70px;
        left: 3px;
      }

      // padding: 51px 0;
      a {
        color: v(venio-secondary-color);

        &:hover {
          fa-icon {
            font-size: 20px;
          }
        }
      }
    }
  }

  ::ng-deep {
    .dx-menu .dx-menu-item-expanded {
      background-color: transparent !important;
      border: 0 !important;
    }
  }
}

::ng-deep {
  .dx-menu-item-content {
    padding: 3px 5px !important;
  }
}

.review-set-name {
  border-left: 1px #757575 solid;
}

::ng-deep {
  .mat-tooltip {
    font-size: 13px;
    max-width: unset !important;
    white-space: pre-wrap;
  }
}

.menu-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 5px;
}

::ng-deep {
  .custom-menu {
    text-align: left;
    color: v(dark-grey);
    background-color: transparent !important;
    border: 0 !important;
    text-transform: uppercase;
    opacity: 1;
    font-size: 1rem;
    padding: 5px !important;
  }

  .custom-menu.active {
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 5px;
    text-decoration-color: v(title-color);
    color: v(title-color);
  }

  .side-nav-collapse {
    visibility: collapse !important;
  }
}

/* Rules for flag value 3 in the control setting e.g. `VOD_VERSION = 3` */
.v3 {
  .nav-item.bg-grey {
    background-color: transparent;
  }

  .navbar {
    box-shadow: 0 6px 3px -7px #999;
  }

  .side-nav {
    background-color: #f7fbff;
    box-shadow: 1px 3px 5px -3px #999999;
    border-radius: 0;
    margin-top: 49px;
    padding-top: 0;
    z-index: 1000;

    li {
      a {
        color: black;

        span.custom-tooltip {
          left: 45px;
        }

        &:after {
          right: -5px;
        }

        span.custom-tooltip {
          z-index: 1001;
          left: 45px;
        }
      }

      &.active {
        &:before {
          top: 0px !important;
          background: none !important;
          height: 70px !important;
          width: 70px !important;
          left: 3px !important;
        }
      }
    }

    ::ng-deep {
      .dx-menu .dx-menu-item-expanded {
        background-color: transparent !important;
        border: 0 !important;
      }
    }
  }

  ::ng-deep {
    .custom-menu.active {
      text-decoration: underline;
      text-decoration-thickness: 2px;
      text-underline-offset: 5px;
    }

    .side-nav-collapse {
      visibility: collapse !important;
    }
  }
}

.expiration-warning {
  background-color: #E5C20073;
  color: #7A6803;
  padding: 10px;
  padding-right: 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.expiration-warning a {
  color: #3C3613;
  text-decoration: none;
  font-weight: 600;
}

.expiration-warning .close-expiration-warning-button {
  position: absolute;
  top: 10px;
  right: 7px;
  width: 20px;
  height: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
}

.expiration-warning .close-expiration-warning-button:before,
.expiration-warning .close-expiration-warning-button:after {
  content: '';
  position: absolute;
  width: 1px;
  height: 60%;
  background-color: #7A6803;
  top: 0;
  left: 50%;
  margin-top: 2px;
  transform: translateX(-50%);
}

.expiration-warning .close-expiration-warning-button:before {
  transform: translateX(-50%) rotate(45deg);
}

.expiration-warning .close-expiration-warning-button:after {
  transform: translateX(-50%) rotate(-45deg);
}

// @media (max-width: 850px) {
//   .expiration-warning {
//     left: 45%; /* Adjusted position for narrower screens so that the notification icon and warning close button doesn't overlap */
//   }
// }

// @media (max-width: 767px) {
//   .expiration-warning {
//     left: 50%; /* at 767px width the navigation collapses so there is more space so bring warning message back at the center. */
//   }
// }

.center-row {
  gap: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.relative-position {
  position: relative;
}
