<ng-template [ngIf]="isWorking">
  <app-content-placeholder [linesOnly]="true"></app-content-placeholder>
</ng-template>
<ng-template [ngIf]="!isWorking">
  <app-route-breadcrumb>
    <div class="row">
      <div class="col-md-6 offset-md-6">
        <div class="row"></div>
      </div>
    </div>
  </app-route-breadcrumb>
  <mat-accordion hideToggle="true" displayMode="flat" #acr multi="true">
    <div class="row" [formGroup]="slipsheetTemplateForm">
      <div class="col-md-12">
        <mat-card class="block-shadow pt-0 mb-2">
          <mat-card-content>
            <div class="row heading mb-15">
              <div class="section-title font-16">
                {{ qParams?.slipsheetTemplateId > 0 ? 'Edit' : 'Create' }}
                Slipsheet Template
              </div>
            </div>
            <div
              #messageWrap
              class="row mt-1 mb-1"
              [@animateHeight]="formErrorMessage"
            >
              <div class="col-md-7" *ngIf="formErrorMessage">
                <div class="alert alert-danger">
                  {{ formErrorMessage }}
                  <button
                    type="button"
                    (click)="formErrorMessage = null"
                    class="close"
                  >
                    <span aria-hidden="true" class="fa fa-times"></span>
                  </button>
                </div>
              </div>
            </div>
            <div class="row default-font-size">
              <div class="col-md-12">
                <div class="row form-group">
                  <label for="field-name" class="col-sm-3"
                    >Template Name:<span class="text-danger">*</span></label
                  >
                  <div
                    class="col-md-5"
                    [@animateHeight]="displayMessage?.TemplateName"
                  >
                    <input
                      formControlName="slipsheetTemplateName"
                      id="slipsheetTemplateName"
                      type="text"
                      autocomplete="off"
                      class="form-control"
                      placeholder="Template Name"
                      [ngClass]="{
                        'is-invalid':
                          displayMessage?.TemplateName || isTemplateNameExists
                      }"
                    />
                    <span
                      *ngIf="displayMessage?.slipsheetTemplateName"
                      class="invalid-feedback"
                    >
                      {{ displayMessage?.slipsheetTemplateName }}
                    </span>
                  </div>
                </div>
                <div class="form-group">
                  <div class="row">
                    <div class="col-md-5">
                      <div
                        class="
                          sampling-control
                          sampling-control-inline
                          sampling-radio
                        "
                      >
                        <mat-radio-button
                          class="sampling-method-rad-button"
                          id="rdPlaceholderText"
                          [checked]="isTextType"
                          (change)="onrdPlaceholderTextClick($event)"
                        >
                          Slipsheet Text:
                        </mat-radio-button>
                      </div>
                    </div>
                  </div>
                  <div class="row default-font-size">
                    <div class="col-md-12">
                      <div class="row form-group">
                        <label class="col-md-3 pl-50"
                          >Slipsheet Text Location
                        </label>
                        <div
                          class="col-md-5"
                          [@animateHeight]="displayMessage?.placeHolderPosition"
                        >
                          <mat-select
                            formControlName="placeHolderPosition"
                            class="form-control"
                            [ngClass]="{
                              'is-invalid': displayMessage?.placeHolderPosition
                            }"
                            placeholder="Select text location"
                            [disabled]="!isTextType"
                          >
                            <mat-option
                              *ngFor="let t of locationList"
                              [value]="t"
                            >
                              {{ t }}
                            </mat-option>
                          </mat-select>
                          <span
                            *ngIf="displayMessage?.placeHolderPosition"
                            class="invalid-feedback"
                          >
                            {{ displayMessage?.placeHolderPosition }}</span
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row align-items-center">
                    <div
                      class="col-md-8 pl-50"
                      [@animateHeight]="displayMessage?.placeHolderText"
                    >
                      <textarea
                        class="form-control"
                        matInput
                        id="placeHolderText"
                        rows="3"
                        formControlName="placeHolderText"
                      ></textarea>
                      <div
                        class="alert alert-danger mt-2"
                        *ngIf="displayMessage?.placeHolderText"
                      >
                        {{ displayMessage?.placeHolderText }}
                      </div>
                    </div>
                    <div
                      class="
                        col-md-4
                        action-btn
                        d-flex
                        flex-direction-column
                        pl-0
                      "
                    >
                      <button
                        id="btnFont"
                        aria-hidden="true"
                        [disabled]="!isTextType"
                        (click)="openFontStyle()"
                      >
                        <span
                          ><img src="assets/images/font.png" alt="" />Font</span
                        >
                      </button>
                      <button
                        id="btnField"
                        aria-hidden="true"
                        (click)="openField()"
                        [disabled]="!isTextType"
                      >
                        <span>
                          <img src="assets/images/add.png" alt="" /> Add
                          Fields</span
                        >
                      </button>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <div class="row">
                    <div class="col-md-12">
                      <div
                        class="
                          sampling-control
                          sampling-control-inline
                          sampling-radio
                        "
                      >
                        <mat-radio-button
                          class="sampling-method-rad-button"
                          id="rdPlaceholderTiffFile"
                          (change)="onrdPlaceholderTextClick($event)"
                          [checked]="!isTextType"
                        >
                          Select a Slipsheet image file:
                        </mat-radio-button>
                      </div>
                    </div>

                    <div class="col-md-8 pl-50">
                      <div class="input-file-wrap position-relative">
                        <input
                          #fileInput
                          type="file"
                          id="placeHolderFile"
                          style="display: none"
                          (change)="handleFileInput($event)"
                          name="placeHolderFile"
                          accept="image/*"
                          [disabled]="isTextType"
                          class="inputfile"
                        />
                        <!-- <label for="placeHolderFile ">
                                                  <span *ngIf="!isCreateMode">{{selectedFile}}</span> -->
                        <!-- <span>Choose a file</span></label> -->
                        <textarea rows="1" class="form-control pl-3" disabled>{{
                          selectedFile
                        }}</textarea>
                        <div
                          class="alert alert-danger mt-2"
                          *ngIf="displayMessage?.placeHolderFile"
                        >
                          {{ displayMessage?.placeHolderFile }}
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 pl-0">
                      <button
                        id="btnFont"
                        class="btn btn-secondary"
                        aria-hidden="true"
                        [disabled]="isTextType"
                        (click)="selectFile()"
                      >
                        <i class="fa fa-upload"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12 text-right">
                <button
                  id="btnPreview "
                  class="btn btn-outline-{{
                    config.themeClient
                  }}-primary mr-2  btn-md actions "
                  aria-hidden="true "
                  (click)="previewImage()"
                >
                  <i class="fa fa-search"> Preview </i>
                </button>
                <button
                  [disabled]="isSubmitting || isEditRestricted"
                  (click)="saveChanges()"
                  type="button "
                  class="btn btn-{{ config.themeClient }}-{{
                    isSubmitting ? ' disabled border' : 'primary'
                  }} mr-2 "
                >
                  <mat-spinner
                    *ngIf="isSubmitting"
                    [strokeWidth]="2"
                    [diameter]="20"
                    mode="indeterminate "
                    class="mr-1"
                  >
                  </mat-spinner>
                  {{ isCreateMode ? 'Create' : 'Update' }}
                </button>
                <button
                  (click)="resetForm(confirmReset)"
                  type="button "
                  *ngIf="slipsheetTemplateForm.dirty"
                  class="btn btn-outline-{{
                    config.themeClient
                  }}-secondary float-right mx-2 "
                >
                  Reset
                </button>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </mat-accordion>
</ng-template>

<ng-template #confirmReset>
  <div
    class="row mx-0 background p-3"
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper "
    cdkDragRootElement=".cdk-overlay-pane "
  >
    <div class="modal-header d-flex col-12" cdkDragHandle>
      <h4 class="modal-title pull-left">Confirm!</h4>
      <button
        type="button "
        class="close pull-right close-confirm"
        data-reset="false "
      >
        <span aria-hidden="true " class="fa fa-times"></span>
      </button>
    </div>
    <div class="modal-body h5 text-center mb-0">
      Are you sure you want to reset form?
    </div>
    <div class="modal-footer w-100 text-right">
      <button
        type="button "
        class="btn btn-venio-primary float-right close-confirm"
        data-reset="true"
      >
        YES
      </button>
      <button
        data-reset="false"
        type="button "
        class="btn btn-grey close-confirm"
      >
        NO
      </button>
    </div>
  </div>
</ng-template>
<ng-template #slipsheetField>
  <div [formGroup]="fieldForm">
    <div
      class="row mx-0"
      cdkDrag
      cdkDragBoundary=".cdk-global-overlay-wrapper "
      cdkDragRootElement=".cdk-overlay-pane "
    >
      <div class="modal-header d-flex col-12" cdkDragHandle>
        <h4 class="modal-title pull-left">SlipSheetField</h4>
        <button
          type="button "
          class="close pull-right close-confirm"
          [mat-dialog-close]="false"
        >
          <span aria-hidden="true " class="fa fa-times"></span>
        </button>
      </div>
      <div class="modal-body h5 text-center mb-0">
        <mat-select class="form-control" formControlName="fieldType">
          <mat-option *ngFor="let t of fieldTypesData" [value]="t">
            {{ t }}
          </mat-option>
        </mat-select>
      </div>
      <div class="modal-footer w-100 text-right">
        <button
          type="button "
          class="btn btn-{{
            config.themeClient
          }}-primary float-right close-confirm "
          [mat-dialog-close]="true"
          (click)="AddFieldType()"
        >
          Add
        </button>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #font>
  <div [formGroup]="fontForm">
    <div
      class="mx-0"
      cdkDrag
      cdkDragBoundary=".cdk-global-overlay-wrapper "
      cdkDragRootElement=".cdk-overlay-pane "
    >
      <div class="modal-header d-flex col-12" cdkDragHandle>
        <h4 class="modal-title pull-left">font</h4>
        <button
          type="button "
          class="close pull-right close-confirm"
          [mat-dialog-close]="false"
        >
          <span aria-hidden="true " class="fa fa-times"></span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row font-style">
          <div class="col-md-5">
            Font:
            <mat-select
              formControlName="fontFamily"
              class="form-control"
              (selectionChange)="fontFamilyChanged($event)"
            >
              <mat-option *ngFor="let t of fontNameList" [value]="t">
                {{ t }}
              </mat-option>
            </mat-select>
          </div>
          <div class="col-md-4">
            Font Style:
            <mat-select
              formControlName="fontWeight"
              (selectionChange)="fontWeightChanged($event)"
              class="form-control"
            >
              <mat-option *ngFor="let t of fontStyleList" [value]="t.value">
                {{ t.name }}
              </mat-option>
            </mat-select>
          </div>
          <div class="col-md-3">
            Size:
            <mat-select
              formControlName="fontSize"
              (selectionChange)="fontSizeChanged($event)"
              class="form-control"
            >
              <mat-option *ngFor="let t of fontSizeList" [value]="t.value">
                {{ t.name }}
              </mat-option>
            </mat-select>
          </div>
        </div>

        <div class="row font-sample mx-0">
          <div class="col-md-7 border">
            <label>Sample:</label>
            <input
              class="form-control text-center"
              [style.font-style]="selectedFontStyle"
              [style.font-weight]="selectedFontWeight"
              [style.font-family]="selectedFont"
              [style.font-size]="selectedFontSize"
              type="text"
              value="AaBaYyZz"
              disabled
            />
          </div>
          <!-- <p [style.font-family]="defaultFont ">Test</p> -->
        </div>
      </div>
      <div class="modal-footer w-100 text-right">
        <button
          type="button "
          class="btn btn-{{
            config.themeClient
          }}-primary float-right close-confirm "
          [mat-dialog-close]="true"
        >
          Ok
        </button>
        <button
          type="button "
          class="btn btn-grey float-right close-confirm"
          [mat-dialog-close]="true"
        >
          <i class="fa fa-times mt-2"></i>
          Cancel
        </button>
      </div>
    </div>
  </div>
</ng-template>
<!-- <div *appSpinner="(showSpinner$ | async); message: 'Loading Image...'" id="document-notes-container" class="container-fluid h-100 d-flex flex-column"> -->
<ng-template #imagePreview>
  <div class="modal-header d-flex mb-0" mat-dialog-title cdkDragHandle>
    <h4 class="modal-title pull-left">Image Preview</h4>
    <button type="button" class="close pull-right" mat-dialog-close>
      <span aria-hidden="true" class="fa fa-times"></span>
    </button>
  </div>
  <div class="modal-body preview-img">
    <img
      [src]="imageToShow"
      alt="Place image title"
      [style.width]="imgWidth"
      [style.height]="imgHeight"
      style="border: 0.1rem solid; object-fit: fill"
      (load)="loadImage($event)"
    />
    <!-- <iframe #tiffViewer [src]="(tiffUrl ? tiffUrl : 'about:blank') | sanitize: 'resourceUrl'" class="h-100 w-100" id="tiffViewer"></iframe> -->
  </div>
  <div class="modal-footer w-100 text-left img-size">
    <mat-radio-group
      name="imageSize"
      value="'test'"
      aria-labelledby="example-radio-group-label"
      class="modal-footer w-100 text-left"
    >
      <mat-radio-button
        name="imageSize"
        [checked]="true"
        value="isBestFit"
        (change)="radioChange($event)"
        >Best Fit
      </mat-radio-button>
      <mat-radio-button
        name="imageSize"
        [checked]="false"
        value="isActualSize"
        (change)="radioChange($event)"
        >ActualSize
      </mat-radio-button>
    </mat-radio-group>
  </div>
  <!-- <div class="modal-footer w-100 text-right ">
        <button type="button " class="btn btn-grey float-right close-confirm col-md-5" [mat-dialog-close]="true " (click)="AddFieldType() ">
            <i class="fa fa-times mt-2"></i>
            Cancel
        </button>
    </div> -->
</ng-template>
<!-- </div> -->
