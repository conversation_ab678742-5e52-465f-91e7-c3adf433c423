<div class="btn-group v-action-group">
  <button
    type="button"
    class="btn btn-{{
      config.themeClient
    }}-primary d-flex justify-content-between align-items-center"
    (click)="addQuery.emit(_isStatus ? 'QUERY_MODE' : selectedOperator)"
    [disabled]="isDisabled || isLoading"
  >
    <mat-spinner
      [diameter]="18"
      [strokeWidth]="1.5"
      color="primary"
      *ngIf="isLoading"
    ></mat-spinner>
    <span class="ml-2"
      >{{ 'Add' + (isLoading ? 'ing..' : '') }}
      {{ _isStatus ? '' : '(' + selectedOperator + ')' }}</span
    >
  </button>
  <mat-select
    [(ngModel)]="selectedOperator"
    *ngIf="!_isStatus"
    class="btn btn-{{ config.themeClient }}-primary v-action-select"
    matTooltip="Change Query Appending Operator"
    matTooltipPosition="above"
    matTooltipClass="bg-white text-dark mx-1 mt-1 mb-1"
  >
    <mat-option value="OR">OR</mat-option>
    <mat-option value="AND">AND</mat-option>
    <mat-option value="NOT">NOT</mat-option>
  </mat-select>
</div>
<div *ngIf="cachedQuery" class="ml-2">
  <button
    type="button"
    class="btn btn-grey"
    cdkOverlayOrigin
    #trigger="cdkOverlayOrigin"
    (click)="openConfirmClear = true"
  >
    clear
  </button>
  <ng-template
    cdkConnectedOverlay
    [cdkConnectedOverlayOrigin]="trigger"
    [cdkConnectedOverlayOpen]="openConfirmClear"
    (overlayOutsideClick)="openConfirmClear = false"
  >
    <div class="row v-confirm-clear">
      <div class="col-12 mb-4">Are you sure?</div>
      <div class="col-12">
        <button
          class="btn btn-sm btn-{{ config.themeClient }}-primary mr-1"
          (click)="clearAndClose()"
        >
          YES
        </button>
        <button
          class="btn btn-sm btn-grey ml-1"
          (click)="openConfirmClear = false"
        >
          NO
        </button>
      </div>
    </div>
  </ng-template>
</div>
