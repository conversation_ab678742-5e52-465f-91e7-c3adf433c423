import {
  After<PERSON>iewInit,
  Component,
  <PERSON>ement<PERSON>ef,
  EventEmitter,
  Injector,
  NgModuleFactory,
  OnDestroy,
  OnInit,
  TemplateRef,
  Type,
  ViewChild
} from '@angular/core'
import { FormControl } from '@angular/forms'
import { MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { ProjectInfo, UserLocalStorageModel } from '@config/models'
import { ConfigService } from '@config/services/config.service'
import { getProjectInfo } from '@config/store/selectors'
import { Action, select, Store as RxStore } from '@ngrx/store'
import { Select, Store } from '@ngxs/store'
import * as fromSearchActions from '@review/store/actions/search.actions'
import { folderScopes } from '@review2/models'
import { animateHeight, fadeInOut, inOut } from '@shared/animation'
import { CustodianMediaComponent } from '@shared/components/custodian-media/custodian-media.component'
import { HighlightNavigationDirective } from '@shared/directives/highlight-navigation/highlight-navigation.directive'
import {
  Project,
  SaveTagForProductionRequestModel,
  SearchExpressionCount,
  SearchQueryModel,
  SearchQueryModule
} from '@shared/models'
import { BreadCrumb } from '@shared/models/breadcrumb.model'
import {
  SearchInputParams,
  SearchResponseModel
} from '@shared/models/search.model'
import { SyntaxParseTypes } from '@shared/search-builder-simple/simple-search.model'
import { SimpleQueryBuilderSelector } from '@shared/search-builder-simple/store/simple-query-builder.selector'
import {
  FetchSimpleQueryBuilderDataSourceAction,
  QuerySyntaxToFormAction,
  SetDisplayableSearchNameOrQueryAction,
  SimpleQueryBuilderStateResetAction,
  StartedSimpleQueryBuilderSearchAction,
  ToggleSimpleQueryBuilderUiAction
} from '@shared/search-builder-simple/store/simple-search-query-builder.actions'
import useMatSelectFilter from '@shared/searchable-select-option/searchable-select-option'
import { HighlightFieldService } from '@shared/services/highlight-field.service'
import {
  saveTagsForProduction,
  setSearchExpressionListTemp,
  setSearchQuery
} from '@shared/store/actions/document.actions'
import { clearSearchResponse } from '@shared/store/actions/search.actions'
import {
  getSavedSearchId,
  getSearchQuery
} from '@shared/store/selectors/document.selectors'
import { StringUtils } from '@shared/utils/string-utils'
import { ClearIndexedDb, InsertProjectLoginDetails } from '@shared/xsStore'
import {
  FetchUserLocalStorageInfo,
  UploadStateResetAction
} from '@stores/actions'
import {
  CaseSelectors,
  StartupStateSelector,
  UploadStateSelector
} from '@stores/selectors'
import * as moment from 'moment'
import { combineLatest, Observable, ReplaySubject, Subject } from 'rxjs'
import { debounceTime, filter, switchMap, takeUntil, tap } from 'rxjs/operators'
import { UserRights } from 'src/app/helpers/user-rights'
import { SearchInputs } from 'src/app/modules/analyze/models'
import { SearchHelpComponent } from 'src/app/modules/review/components/search-help/search-help.component'
import { SearchHelpModule } from 'src/app/modules/review/components/search-help/search-help.module'
import { SaveSearchRequestModel } from 'src/app/modules/review/models/search.model'
import { ReviewStateSelector } from 'src/app/modules/review2/store/review.selectors'
import { getControlSetting } from '../../../config/store/selectors'
import {
  DebounceTimer,
  transformInlinedTextToQuery
} from '../../../shared/utils'
import {
  FetchSearchResultData,
  SearchAction,
  SetFolderAction,
  SetFolderScopeAction,
  SetFolderScopeQueryAction,
  SetIsSearchTriggeredFromHistoryAction,
  UpdateBreadcrumbAction,
  UpdateSearchResponse
} from '../../store/review.actions'
import { DocumentSelectionComponent } from '../document-selection/document-selection.component'
import { DocumentSelectionModule } from '../document-selection/document-selection.module'
import { SetIncludeFamilySearch } from './../../store/review.actions'

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss'],
  animations: [inOut, fadeInOut, animateHeight]
})
export class SearchBarComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('searchText') searchText: ElementRef

  unsubscribed$: Subject<void> = new Subject<void>()

  mediaList: number[] = []

  searchQuery: string

  DEFAULT_SEARCH_EXPRESSION = 'FileId>0'

  searchTextAreaExpanded: boolean

  includeFamilySearch: boolean

  isSearched: boolean

  config = ConfigService

  projectId: number

  /** project ctrl for selection of project list */
  readonly projectCtrl = new FormControl()

  /** control for the project filter keyword */
  public projectFilterCtrl: FormControl = new FormControl()

  /** list of projects filtered by search keyword */
  public filteredProjects: ReplaySubject<Project[]> = new ReplaySubject<
    Project[]
  >()

  @ViewChild('searchHelpTemplate')
  private searchHelpTemplate: TemplateRef<any>

  searchHelpComponent: Promise<Type<SearchHelpComponent>>

  searchHelpModule: NgModuleFactory<SearchHelpModule>

  searchHelpInjector: Injector

  @ViewChild('documentSelectionTemplate')
  private documentSelectionTemplateRef: TemplateRef<any>

  documentSelectionComponent: Promise<Type<DocumentSelectionComponent>>

  documentSelectionModule: NgModuleFactory<DocumentSelectionModule>

  documentSelectionInjector: Injector

  /**
   * Lazy component to load on project scope (custodian/media)
   */
  custodianMediaComp: Promise<Type<CustodianMediaComponent>>

  /**
   *  Injector provider for lazy component
   */
  custodianMediaInjector: Injector

  /**
   * template ref of remove view all fields by template Id
   */
  @ViewChild('custodianMedia')
  private readonly tplCustodianMedia: TemplateRef<any>

  isSearchFromOtherModule: boolean

  @Select(ReviewStateSelector.allDocumentCount)
  allDocCount: Observable<number>

  breadCrumbs: BreadCrumb[]

  showAdvanceFilterUi: boolean

  isInvalidQueryValue: boolean

  /**
   * Whether user should be able to write on search input when query builder UI is shown
   */
  toggleInputWritable: boolean

  /**
   * handles text input event to perform UI toggling task or  element manipulation.
   */
  readonly forQueryBuilder = new EventEmitter()

  projects: Project[] = []

  /** login detail id of the user */
  detailId: number

  /**
   * Checks Whether the search input has length by removing leading,trailing spaces  expression text.
   */
  get isEmptyInput(): boolean {
    return !(this.searchText?.nativeElement?.value?.trim().length > 0)
  }

  /**
   * User right to send the documents to analyze
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_VIEW_DASHBOARD)
  )
  allowAnalyze$: Observable<boolean>

  /**
   * List of all fields that user have permissions to view in search grid
   */
  allFields: string[] = []

  /**
   * Fields to show in grid by default when loading search result
   */
  visibleFields: string[] = []

  projectInfo: ProjectInfo

  /**
   * User right to produce the documents
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_EXPORT))
  allowProduction$: Observable<boolean>

  searchResponse: any

  /**
   * Boolean flag that indicates the send to production action is triggered.
   */
  navigateToProduction: boolean

  searchTerm$ = new Subject<string>()

  multiLineSearchTerm$ = new Subject<boolean>()

  numberOfLines: number

  clearSearchText$ = new Subject<boolean>()

  lazySearchBuilderUiComp: Promise<Type<unknown>>

  selectedScope: string

  folderScopes = folderScopes

  vodVersion: number

  constructor(
    private store: Store,
    private dialog: MatDialog,
    private injector: Injector,
    private rxStore: RxStore<Action>,
    private router: Router,
    private configService: ConfigService,
    private highlightFieldService: HighlightFieldService
  ) {}

  ngOnInit() {
    this.projectId = this.store.selectSnapshot(
      StartupStateSelector.SliceOf('selectedProjectId')
    )
    this.getProjects()

    this.initSlices()

    this.searchWhenQueryReceivedFromOtherModule()
    this.getSearchQueryFromAnalyze()

    this.rxStore
      .pipe(select(getSavedSearchId), takeUntil(this.unsubscribed$))
      .subscribe((savedSearchId: number) => {
        if (savedSearchId && this.navigateToProduction) {
          this.router.navigate(['/production'], {
            queryParams: { projectId: this.projectId }
          })
        }
      })

    this.getLoginDetails()

    this.toggleAdvanceSearchUiVisibility()
    this.store
      .select(getControlSetting('VOD_VERSION'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((vodVersion) => {
        this.vodVersion = vodVersion
      })
  }

  public getLoginDetails(): void {
    this.store
      .select(StartupStateSelector.SliceOf('selectedProjectId'))
      .pipe(
        tap((id) => this.store.dispatch(new FetchUserLocalStorageInfo(id))),
        switchMap(() =>
          this.store.select(
            StartupStateSelector.SliceOf('userLocalStorageInfo')
          )
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((res: UserLocalStorageModel) => {
        this.detailId = res?.DetailID
      })
  }

  getProjects(): void {
    this.store
      .select(CaseSelectors.projects)
      .pipe(
        filter((p) => p.length > 0),
        debounceTime(100),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({
        next: (projects) => {
          this.projects = projects
          // load the initial project list
          this.filteredProjects.next(this.projects.slice())
          // project list control should be disabled if we are editing an item
          if (+this.projectId > 0) {
            // we're setting selected project Id from query string param if supplied.
            this.projectCtrl.setValue(+this.projectId)
          }
        }
      })
  }

  getSelectedFolderScope(): void {
    combineLatest([
      this.store.select(ReviewStateSelector.SliceOf('selectedFolderScope')),
      this.store.select(ReviewStateSelector.SliceOf('selectedFolder'))
    ])
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe(([folderScope, folderName]) => {
        let scope = 'All Document'
        if (folderScope && folderScopes?.length) {
          scope =
            folderScopes?.find((c) => c.value === folderScope)?.displayName ??
            'All Document'
        }
        this.selectedScope = folderName ? folderName : scope
      })
  }

  /**
   * Perform default fetching task when project selected get changed. These are the default derived data from top level
   */
  projectValueChanged(event): void {
    this.store.dispatch([
      new ClearIndexedDb('HtmlParts'),
      new ClearIndexedDb('NativeRedaction')
    ])
    this.store.dispatch(clearSearchResponse())

    const projectId = event.value
    const currentRoute = this.router.url.split('projectId')
    const navUrl = `${this.configService.getWebBaseUrl()}/ondemand/appplus/#${
      currentRoute[0]
    }projectId=${projectId}`

    if (this.vodVersion.toString() !== '3') {
      this.store.dispatch(
        new InsertProjectLoginDetails(projectId, this.detailId)
      )
    }
    window.location.replace(navUrl)
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }

  /** listens to the project filter control change */
  private projectFilterChange(): void {
    // listen for search field value changes
    this.projectFilterCtrl.valueChanges
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe(() => {
        const { filterProjects, filteredProjects } = useMatSelectFilter()
        filterProjects(this.projectFilterCtrl, this.projects)
        this.filteredProjects = filteredProjects
      })
  }

  /**
   * There are some scenarios that needs to preload the form data sources which then being used
   * during parsing the query for form data and prefilling it based on logics.
   * @see parseTermAndFolderQuerySyntax
   * @see QueryParserWorkerFacade
   * @see SimpleSearchQueryBuilderStates
   */
  private initActionsOfAdvancedSearch(): void {
    this.store.dispatch([
      new FetchSimpleQueryBuilderDataSourceAction({
        option: 'FIELD SEARCH',
        projectId: this.projectId,
        userId: this.config.userId
      }),
      new FetchSimpleQueryBuilderDataSourceAction({
        option: 'FOLDER',
        projectId: this.projectId,
        userId: this.config.userId
      }),
      new FetchSimpleQueryBuilderDataSourceAction({
        option: 'TAG',
        projectId: this.projectId,
        userId: this.config.userId
      })
    ])
  }

  /**
   * It will set the displayable search name or query in the advanced search UI
   * @param expression - search expression. Default value is empty string
   * @see resetAdvancedSearch
   * @see reset
   */
  #setDisplayableSearchInAdvancedSearchUi(expression = ''): void {
    this.store.dispatch(
      new SetDisplayableSearchNameOrQueryAction({
        name: '',
        searchExpression:
          expression.trim().toLowerCase() !==
          this.DEFAULT_SEARCH_EXPRESSION.toLowerCase()
            ? expression
            : ''
      })
    )
  }

  /**
   * We have to parse the search expression for prefilling the form criteria
   * that was previously searched.Here, we indicate that only parse simple syntax that has term e.g. `term1 OR term2
   * @param searchExpression - syntax to parse and set in the keyword section of advanced search UI
   */
  #setQuerySyntaxToAdvancedForm(searchExpression: string): void {
    if (this.isEmptyInput) return

    this.store.dispatch(
      new QuerySyntaxToFormAction({
        searchExpression,
        parseType: SyntaxParseTypes.Terms
      })
    )
  }

  ngAfterViewInit(): void {
    this.initActionsOfAdvancedSearch()

    this.store
      .select(ReviewStateSelector.SliceOf('breadCrumbs'))
      .pipe(
        filter((result) => !!result && result.length > 0),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((breadCrumbs) => {
        this.breadCrumbs = breadCrumbs.map((item, index) => ({
          ...item,
          itemIndex: index
        }))
      })

    this.handleAdvanceFilterSearch()
    this.getSelectedFolderScope()
    this.initAdvancedSearchLazyComponent()

    if (this.isSearchFromOtherModule) return
    this.initAction()

    this.rxStore
      .select(getProjectInfo)
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        this.projectInfo = res
      })
    this.listenElementRefChanges()
    this.projectFilterChange()
    this.store
      .select(ReviewStateSelector.SliceOf('searchResponse'))
      .pipe(
        filter((response) => !!response?.searchResultIntialParameters),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((response) => {
        const {
          searchResultIntialParameters: { searchExpression }
        } = response
        this.searchResponse = response

        this.isSearched =
          searchExpression?.toLowerCase() !=
            this.DEFAULT_SEARCH_EXPRESSION.toLowerCase() || this.isSearched

        this.#setDisplayableSearchInAdvancedSearchUi(searchExpression)
        this.#setQuerySyntaxToAdvancedForm(searchExpression)
      })

    this.store
      .select(ReviewStateSelector.SliceOf('currentSearchExpression'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((expression: string) => {
        this.clearSearchText$.next(
          expression.length > 0 &&
            this.searchQuery?.trim()?.toLowerCase() !==
              this.DEFAULT_SEARCH_EXPRESSION.toLowerCase()
        )
      })
    this.#selectIsSearchTriggeredFromHistory()
  }

  listenElementRefChanges() {
    this.searchTerm$
      .pipe(
        debounceTime(1000),
        switchMap((term) => [
          (this.searchQuery = term),
          (this.numberOfLines = term?.match(/\n/g)?.length + 1 || 0),
          this.multiLineSearchTerm$.next(this.numberOfLines > 2),
          this.clearSearchText$.next(term?.length > 0)
        ]),
        takeUntil(this.unsubscribed$)
      )
      .subscribe()
  }

  initAction(): void {
    this.store.dispatch(
      new SearchAction({
        searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
        medialist: this.mediaList,
        isResetBaseGuid: true
      })
    )
  }

  initSlices(): void {
    this.store
      .select(ReviewStateSelector.SliceOf('includeFamilySearch'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        this.includeFamilySearch = res
      })
  }

  openSearchGuide() {
    const modalRef = this.dialog.open(this.searchHelpTemplate)

    // Create or update the injector with the new modalRef
    this.searchHelpInjector = Injector.create({
      providers: [
        {
          provide: 'searchHelpData',
          useValue: {
            modalRef: modalRef
          }
        }
      ],
      parent: this.injector
    })

    if (!this.searchHelpComponent) {
      this.searchHelpComponent = import(
        '../../../review/components/search-help/search-help.component'
      ).then(({ SearchHelpComponent }) => SearchHelpComponent)
    }
  }

  openDocumentSelectionModal(): void {
    const modalRef = this.dialog.open(this.documentSelectionTemplateRef, {
      closeOnNavigation: true,
      width: '50vw',
      height: '70vh'
    })
    if (!this.documentSelectionComponent) {
      this.documentSelectionInjector = Injector.create({
        providers: [
          {
            provide: 'documentSelectionData',
            useValue: {
              modalRef: modalRef
            }
          }
        ],
        parent: this.injector
      })

      this.documentSelectionComponent = import(
        './../document-selection/document-selection.component'
      ).then(({ DocumentSelectionComponent }) => DocumentSelectionComponent)
    }
  }

  /**
   * Resets Simple Query Builder form and its slices.
   */
  private resetAdvancedSearch(): void {
    /**
     * We provide what slices we want to reset.
     */
    this.store.dispatch([
      new SimpleQueryBuilderStateResetAction([
        // entire form criteria
        'searchForm',
        // when a user clicks on save button from the advanced search
        'saveAdvancedSearchFormCriteria',
        // when a user clicks on a search log link like history or saved searches, default value set to `none`
        'searchLogLinkType',
        // We need to reset the displayable search name or query when the user clicks on the reset button
        'displayableSearchNameOrQuery',
        // as we reset the persisted search criteria, we need to reset the expanded panels as collapsed
        'uuidOfExpandedPanels',
        'uiTypeOfExpandedPanels',
        // reset the event payload that was passed from the history panel each time a task is performed.
        'searchHistoryActionEvent'
      ])
    ])
  }

  reset() {
    this.searchQuery = this.DEFAULT_SEARCH_EXPRESSION
    this.highlightFieldService.resetHightlightedField()
    // reset include family option
    this.store.dispatch([
      new SetFolderScopeQueryAction(''),
      new SetIncludeFamilySearch(false)
    ])
    this.store.dispatch(
      new SearchAction({
        searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
        isResetBaseGuid: true
      })
    )
    this.isSearched = false
    this.searchTextAreaExpanded = false
    this.resetFlagsToDefault()
    this.resetAdvancedSearch()
    this.store.dispatch([
      new SetFolderScopeAction('allDocuments'),
      new SetFolderAction(''),
      new SetIsSearchTriggeredFromHistoryAction(false)
    ])
  }

  toggleSearchTextArea() {
    this.searchTextAreaExpanded = !this.searchTextAreaExpanded
  }

  onKeyDown(event: KeyboardEvent): void {
    // Check if the Enter key is pressed without the Shift key
    if (event.key === 'Enter' && !event.shiftKey) {
      // Prevent default behavior (perform search)
      event.preventDefault()
      this.searchClicked()
    }

    // Allow Shift + Enter to create a new line
  }

  searchClicked() {
    if (
      this.isEmptyInput ||
      (!this.isEmptyInput &&
        this.isInvalidQueryValue &&
        this.showAdvanceFilterUi) ||
      (this.isEmptyInput &&
        this.isInvalidQueryValue &&
        this.showAdvanceFilterUi)
    ) {
      return
    }
    this.showAdvanceFilterUi = false
    this.isInvalidQueryValue = false
    const query = StringUtils.isNullOrEmpty(
      this.searchText.nativeElement.value?.trim()
    )
      ? this.DEFAULT_SEARCH_EXPRESSION
      : this.searchText.nativeElement.value?.trim()

    this.store.dispatch(
      new SearchAction({
        searchExpression: query,
        isResetBaseGuid: true
      })
    )
    this.isSearched = true
    HighlightNavigationDirective.resetValuesOnPageSearch(1)
    this.resetAdvancedSearch()
  }

  private readonly toggleAdvanceSearchUiVisibility = (): void => {
    this.store
      .select(SimpleQueryBuilderSelector.sliceOf('isClose'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe({
        next: (isClose) => {
          this.showAdvanceFilterUi = !isClose
        }
      })
  }

  /**
   * Load it eagerly to initialize the component and wait for the user to click on the load UI or
   * might be triggered by some other component.
   */
  private initAdvancedSearchLazyComponent(): void {
    this.lazySearchBuilderUiComp = import(
      '@shared/search-builder-simple/simple-query-builder-container.component'
    ).then(({ SimpleQueryBuilderContainerComponent }) => {
      return SimpleQueryBuilderContainerComponent
    })
  }

  /**
   * Keeps on track whether the search is triggered from history or not which needs to update the search query.
   * If a user performed a search from another section, the terms in the input must be removed.
   */
  #selectIsSearchTriggeredFromHistory(): void {
    this.store
      .select(ReviewStateSelector.SliceOf('isSearchTriggeredFromHistory'))
      .pipe(
        filter((IsSearchTriggeredFromHistory) => IsSearchTriggeredFromHistory),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.searchQuery = ''
      })
  }

  readonly actionClicked = (a: 'LAUNCH' | 'CLOSE'): void => {
    this.store.dispatch(new ToggleSimpleQueryBuilderUiAction(a === 'CLOSE'))
  }

  /**
   * Handles search from advance search UI. Passes generated query along with other parameters
   * and emits event of `generatedQuery` slice where we can take that value to  perform search.
   * @see SimpleSearchQueryBuilderStates
   */
  private readonly handleAdvanceFilterSearch = (): void => {
    this.store
      .select(SimpleQueryBuilderSelector.sliceOf('generatedQuery'))
      .pipe(
        filter((q) => !!q?.expression),
        debounceTime(400),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({
        next: (model) => {
          this.isSearched = true
          this.store.dispatch([
            new SearchAction({
              searchExpression: model.expression,
              includePC: model.includeFamily,
              formState: model.formState,
              isResetBaseGuid: true
            }),
            // once we get that generated query, we need to close the UI.
            new ToggleSimpleQueryBuilderUiAction(true),
            // and update the status of query builder UI
            new StartedSimpleQueryBuilderSearchAction(false),
            // if a search is triggered from advanced search, we need
            // to set falsy value to `isSearchTriggeredFromHistory`
            // slice
            new SetIsSearchTriggeredFromHistoryAction(false)
          ])
        }
      })
  }

  search() {
    const searchRequest: SearchInputParams = {
      searchExpression: StringUtils.isNullOrEmpty(this.searchQuery)
        ? this.DEFAULT_SEARCH_EXPRESSION
        : this.searchQuery,
      isForwardFilter: false,
      medialist: this.mediaList //set medialist if original scope need to be used when query sent from search history.
    }
    this.store.dispatch(new SearchAction(searchRequest))
  }

  clearSearchText() {
    this.searchQuery = ''
    this.resetFlagsToDefault()
  }

  private searchWhenQueryReceivedFromOtherModule() {
    this.store
      .select(UploadStateSelector.SliceOf('initHistoryQuery'))
      .pipe(
        filter((value) => !!value && !Array.isArray(value?.query)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({
        next: (searchOption) => {
          this.isSearchFromOtherModule = true
          this.searchQuery = searchOption.query as string
          const enableMultiLine: boolean =
            this.searchText?.nativeElement?.offsetHeight <=
            this.searchText?.nativeElement?.scrollHeight
          this.multiLineSearchTerm$.next(enableMultiLine)
          this.clearSearchText$.next(this.searchQuery?.length > 0)
          this.isSearched = true
          if (!enableMultiLine && this.searchTextAreaExpanded) {
            this.toggleSearchTextArea()
          }

          this.store.dispatch(
            new SetIncludeFamilySearch(searchOption?.includePc)
          )

          //searchOption.medialist is available when original scope is selected by user when searched from search history.
          this.mediaList = searchOption?.mediaList?.length
            ? searchOption?.mediaList?.map((id) => Number(id))
            : this.mediaList
          this.store.dispatch(
            new SearchAction({
              searchExpression: searchOption?.query as string,
              medialist: this.mediaList,
              isResetBaseGuid: true
            })
          )
          // so after we done, lets reset to its initial value
          this.store.dispatch(new UploadStateResetAction('initHistoryQuery'))
        }
      })
  }

  getSearchQueryFromAnalyze() {
    this.rxStore
      .pipe(select(getSearchQuery), takeUntil(this.unsubscribed$))
      .subscribe((searchQueryModel: SearchQueryModel) => {
        //searchQueryModel is not null or undefined when query is sent from another module like analyse, production, document share etc.
        if (searchQueryModel) {
          this.isSearchFromOtherModule = true
          const {
            totalHitCount,
            searchScopeCount,
            searchedDocCount,
            filteredDocCount
          } = searchQueryModel?.searchParameters?.searchResultIntialParameters
          const searchResponseData: SearchResponseModel = {
            error: null,
            searchResultIntialParameters: {
              totalHitCount: totalHitCount,
              searchScopeCount: searchScopeCount,
              searchedDocCount: searchedDocCount,
              filteredDocCount: filteredDocCount
            },
            tempTables: searchQueryModel.searchParameters?.tempTables
          }
          const breadCrumbs: BreadCrumb[] = searchQueryModel.searchInputs?.map(
            (input) => ({
              query: input.expression,
              filterText: input.displayText,
              itemIndex: input.currentIndex,
              isFilterSearch: input.isFilterSearch
            })
          )
          this.store
            .dispatch([
              new UpdateBreadcrumbAction(breadCrumbs),
              new UpdateSearchResponse(searchResponseData)
            ])
            .pipe(takeUntil(this.unsubscribed$))
            .subscribe(() => {
              this.store.dispatch(new FetchSearchResultData(this.projectId))
            })
        }
      })
  }

  sendToAnalyze() {
    this.rxStore.dispatch(
      setSearchQuery({
        payload: {
          searchQueryModel: {
            searchQuery: StringUtils.isNullOrEmpty(
              this.searchText.nativeElement.value
            )
              ? this.DEFAULT_SEARCH_EXPRESSION
              : this.searchText.nativeElement.value,
            sourceModule: SearchQueryModule.Review,
            searchInputs: this.breadCrumbs
              .filter((q) => q.filterText !== 'Home')
              .map(
                (q) =>
                  ({
                    expression: q.query,
                    displayText: q.filterText ?? q.query,
                    documentCounts: q.docCount,
                    isFilterSearch: q.isFilterSearch
                  } as SearchInputs)
              ),
            searchParameters: {
              error: null,
              searchResultIntialParameters: null,
              tempTables: this.store.selectSnapshot(
                ReviewStateSelector.tempTableResponseModel
              )
            },
            mediaList: this.mediaList.map((m) => `${m}`),
            includeFamilySearch: this.includeFamilySearch
          }
        }
      })
    )
    this.router.navigate(['/analyze'], {
      queryParams: { projectId: this.projectId }
    })
  }

  sendToProduction() {
    if (
      !this.configService.isVodEnabled &&
      this.projectInfo?.isFilteringServiceCase
    ) {
      const searchExpressionList = []
      const searchExpressionCount = new SearchExpressionCount()
      searchExpressionCount.searchExpression =
        this.searchResponse?.searchExpression
      searchExpressionCount.totalHitCount = this.searchResponse?.totalHitCount
      searchExpressionList.push(searchExpressionCount)
      this.rxStore.dispatch(
        setSearchExpressionListTemp({
          payload: { searchExpressionList }
        })
      )

      //This is the Ricoh case
      const productionTagModel = new SaveTagForProductionRequestModel()
      productionTagModel.moduleName = 'REVIEW'
      productionTagModel.productionTagName =
        'Production_' + moment().format('YYYYMMDD_HHmmss')
      productionTagModel.projectId = this.projectId
      productionTagModel.userId = +localStorage.getItem('UserId')
      productionTagModel.searchTempTableName =
        this.searchResponse?.tempTables.searchResultTempTable
      this.rxStore.dispatch(
        saveTagsForProduction({
          payload: { saveTagRequestModel: productionTagModel }
        })
      )
    } else {
      this.navigateToProduction = true

      const saveSearchRequest: SaveSearchRequestModel = {
        searchGuid: this.searchResponse?.tempTables
          ? this.searchResponse?.tempTables?.searchGuid
          : null,
        searchName: 'ProductionSave_' + moment().format('YYYYMMDD_HHmmss'),
        saveOnCustomField: true,
        isNewCustomField: true,
        customFieldId: null,
        customFieldName: null,
        applyAutoTagBasedOnSearchTerm: false,
        useExistingTag: false,
        tagGroupIdOfExistingSavedSearch: null
      }

      this.rxStore.dispatch(
        fromSearchActions.saveSearch({
          payload: {
            projectId: this.projectId,
            saveSearchRequest,
            saveSearchForProduction: true
          }
        })
      )
    }
  }

  // resets toolbar search actions to default state
  resetFlagsToDefault() {
    this.clearSearchText$.next(false)
    this.multiLineSearchTerm$.next(false)
  }

  ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
    // either clear specific or all at once.
    // to clear specific, pass arg or args in an array
    this.store.dispatch(new SimpleQueryBuilderStateResetAction())
  }

  /**
   * Generates query out of pasted text
   * @param e clipboard event to capture value
   */
  @DebounceTimer(200)
  readonly handleTextPaste = (e: ClipboardEvent): void => {
    const input = this.searchText.nativeElement
    const value = (e.target['value'] || '').trim() as string
    if (!input) return
    const terms = value.split(/\r\n/g).filter((t) => !!t.trim())
    if (!(value || terms.some((t) => !!t))) return
    // create query using util
    let query = terms.reduce((accum, text, index, arr) => {
      const qText = transformInlinedTextToQuery(text)
      return (accum += `
        ${qText ? `(${qText})` : ''} ${
        index >= 0 && index < arr.length - 1 && qText ? 'OR' : ''
      }`)
    }, '')
    // some cleanup for multi-spaces.
    query = query.replace(/\s+/g, ' ')
    // so if we have paste value, make query out of it and insert it,
    // otherwise, whatever the input have is it's onw.
    input.value = query ? `(${query})` : input.value

    // if we have more terms, maybe expand the box a little so
    // user can have more space to see their text being generated as query
    this.searchTextAreaExpanded = terms.length > 10
  }

  includeFamChange(event: HTMLInputElement): void {
    this.store.dispatch(new SetIncludeFamilySearch(event.checked))
  }
}
