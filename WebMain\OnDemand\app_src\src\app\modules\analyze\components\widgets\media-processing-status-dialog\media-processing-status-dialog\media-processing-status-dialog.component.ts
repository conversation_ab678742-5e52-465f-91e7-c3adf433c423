import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  Output
} from '@angular/core'
import { select, Store as RxStore } from '@ngrx/store'
import { Subject } from 'rxjs'
import { filter, takeUntil } from 'rxjs/operators'
import { SetCanFetchNewCase } from '../../../../../launchpad/store'
import { unIndexMedia } from '../../../../../launchpad/store/selectors/case.selectors'
import { UnIndexMediaModel } from '../../../../models'

@Component({
  selector: 'app-media-processing-status-dialog',
  templateUrl: './media-processing-status-dialog.component.html',
  styleUrls: ['./media-processing-status-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MediaProcessingStatusDialogComponent implements OnInit, OnDestroy {
  @Output() readonly cancel = new EventEmitter()

  public readonly toDestroy$ = new Subject<void>()

  public unIndexMediaList: UnIndexMediaModel[] = []

  constructor(private rxStore: RxStore, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.#selectMediaStaus()
  }

  #selectMediaStaus(): void {
    this.rxStore
      .pipe(
        select(unIndexMedia),
        filter((result) => Boolean(result)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((result) => {
        this.unIndexMediaList = (result?.data || []).map((item, index) => ({
          ...item,
          sn: index + 1
        }))
        this.cdr.detectChanges()
      })
  }

  proceed(): void {
    this.rxStore.dispatch(new SetCanFetchNewCase(true))
  }

  onCancel(): void {
    this.cancel.emit()
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
