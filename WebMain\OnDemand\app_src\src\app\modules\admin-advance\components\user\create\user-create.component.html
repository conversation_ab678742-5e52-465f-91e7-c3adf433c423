<ng-template [ngIf]="isWorking">
  <app-content-placeholder [linesOnly]="true"></app-content-placeholder>
</ng-template>
<ng-template [ngIf]="!isWorking">
  <app-route-breadcrumb>
    <div class="row">
      <div class="col-md-6 offset-md-6">
        <div class="row"></div>
      </div>
    </div>
  </app-route-breadcrumb>
  <mat-accordion hideToggle="true" displayMode="flat" #acr multi="true">
    <div class="row" [formGroup]="addUserForm">
      <div class="col-md-12">
        <mat-card class="block-shadow pt-0">
          <mat-card-content>
            <div class="row heading mb-15">
              <div class="section-title font-16">{{ mode }} User</div>
            </div>
            <div class="row mt-1 mb-1" [@animateHeight]="formErrorMessage">
              <div class="col-md-7" *ngIf="formErrorMessage">
                <div class="alert alert-danger">
                  {{ formErrorMessage }}
                  <button
                    type="button"
                    (click)="formErrorMessage = null"
                    class="close"
                  >
                    <span aria-hidden="true" class="fa fa-times"></span>
                  </button>
                </div>
              </div>
            </div>
            <div class="row default-font-size">
              <div class="col-md-12">
                <div class="row form-group">
                  <label for="field-name" class="col-sm-2"
                    >Email Address<span class="text-danger">*</span></label
                  >
                  <div
                    class="col-md-5"
                    [@animateHeight]="displayMessage?.EmailAddress"
                  >
                    <input
                      formControlName="EmailAddress"
                      id="EmailAddress"
                      type="email"
                      autocomplete="off"
                      class="form-control"
                      placeholder="Email Address"
                      [ngClass]="{
                        'is-invalid':
                          displayMessage?.EmailAddress || isEmailAlreadyInUse
                      }"
                    />
                    <span
                      *ngIf="displayMessage?.EmailAddress"
                      class="invalid-feedback"
                    >
                      {{ displayMessage?.EmailAddress }}
                    </span>
                    <span *ngIf="isEmailAlreadyInUse" class="invalid-feedback">
                      The email address already exists
                    </span>
                  </div>
                  <div
                    class="col-md-4 import-ad"
                    *ngIf="
                      ((isADEnabled && !allGroupsMapped) ||
                        (isIDPEnabled && showImportUser)) &&
                      mode === 'Create'
                    "
                  >
                    <a
                      href="javascript:void(0)"
                      (click)="onImportUser()"
                      class="pl-1"
                      >Import User From
                      {{ isADEnabled ? 'Active Directory' : 'IDP' }}</a
                    >
                  </div>
                </div>
                <div class="row form-group">
                  <label for="display-name" class="col-sm-2">
                    Full Name <span class="text-danger">*</span></label
                  >
                  <div
                    class="col-md-5"
                    [@animateHeight]="displayMessage?.FullName"
                  >
                    <input
                      formControlName="FullName"
                      id="FullName"
                      type="text"
                      class="form-control"
                      placeholder="Full Name"
                      autocomplete="off"
                      [ngClass]="{ 'is-invalid': displayMessage?.FullName }"
                    />
                    <span
                      *ngIf="displayMessage?.FullName"
                      class="invalid-feedback"
                    >
                      {{ displayMessage?.FullName }}
                    </span>
                  </div>
                </div>
                <div class="row form-group" *ngIf="mode === 'Edit'">
                  <label class="col-sm-2"> Password </label>
                  <div
                    class="col-md-5"
                    [@animateHeight]="displayMessage?.Password"
                  >
                    <input
                      formControlName="Password"
                      id="Password"
                      type="password"
                      class="form-control"
                      placeholder="Password"
                      autocomplete="off"
                      [ngClass]="{ 'is-invalid': displayMessage?.Password }"
                    />
                    <span
                      *ngIf="displayMessage?.Password"
                      class="invalid-feedback"
                    >
                      {{ passwordValidationMessage }}
                    </span>
                  </div>
                </div>
                <div class="row form-group" *ngIf="mode === 'Edit'">
                  <label class="col-sm-2"> Confirm Password</label>
                  <div
                    class="col-md-5"
                    [@animateHeight]="displayMessage?.ConfirmPassword"
                  >
                    <input
                      formControlName="ConfirmPassword"
                      id="ConfirmPassword"
                      type="password"
                      class="form-control"
                      placeholder="Confirm Password"
                      autocomplete="off"
                      [ngClass]="{
                        'is-invalid': displayMessage?.ConfirmPassword
                      }"
                    />
                    <span
                      *ngIf="displayMessage?.ConfirmPassword"
                      class="invalid-feedback"
                    >
                      {{ displayMessage?.ConfirmPassword }}
                    </span>
                  </div>
                </div>
                <div class="row form-group">
                  <label class="col-sm-2">Phone</label>
                  <div
                    class="col-md-5"
                    [@animateHeight]="displayMessage?.Phone"
                  >
                    <input
                      formControlName="Phone"
                      id="Phone"
                      type="text"
                      class="form-control"
                      autocomplete="off"
                      placeholder="Phone"
                      [ngClass]="{ 'is-invalid': displayMessage?.Phone }"
                    />
                    <span
                      *ngIf="displayMessage?.Phone"
                      class="invalid-feedback"
                    >
                      {{ displayMessage?.Phone }}
                    </span>
                  </div>
                </div>

                <div
                  class="row form-group"
                  *ngIf="
                    userDetails.clientId == 1 && userDetails.globalRoleId == 1
                  "
                >
                  <label for="field-desc" class="col-sm-2"
                    >Client Name<span class="text-danger">*</span></label
                  >
                  <div
                    class="col-md-5"
                    [@animateHeight]="displayMessage?.ClientName"
                  >
                    <mat-select
                      formControlName="ClientName"
                      class="form-control"
                      disableRipple
                      [ngClass]="{ 'is-invalid': displayMessage?.ClientName }"
                      placeholder="Select Client"
                    >
                      <mat-option
                        class="wrap-text"
                        *ngFor="let t of clientData"
                        [value]="t.clientId"
                      >
                        {{ t.clientName }}
                      </mat-option>
                    </mat-select>
                    <span
                      *ngIf="displayMessage?.ClientName"
                      class="invalid-feedback"
                    >
                      {{ displayMessage?.ClientName }}</span
                    >
                  </div>
                </div>

                <div class="row form-group">
                  <label for="field-desc" class="col-sm-2"
                    >User Role<span class="text-danger">*</span></label
                  >
                  <div
                    class="col-md-5"
                    [@animateHeight]="displayMessage?.UserRole"
                  >
                    <mat-select
                      formControlName="UserRole"
                      class="form-control w-100"
                      disableRipple
                      [ngClass]="{ 'is-invalid': displayMessage?.UserRole }"
                      placeholder="Select User Role"
                      (selectionChange)="onUserRoleSelectionChange($event)"
                    >
                      <mat-option
                        class="wrap-text"
                        *ngFor="let t of userRoleList"
                        [value]="t.globalRoleId"
                      >
                        {{ t.globalRoleName }}
                      </mat-option>
                    </mat-select>
                    <span
                      *ngIf="displayMessage?.UserRole"
                      class="invalid-feedback"
                    >
                      {{ displayMessage?.UserRole }}</span
                    >
                  </div>
                  <div
                    *ngIf="isCreateMode() && userRoleList.length > 0"
                    class="col-sm-1 mt-2"
                  >
                    <a (click)="loadUserRole()">
                      <i class="fas fa-lg fa-eye"></i>
                    </a>
                  </div>
                </div>
                <div
                  class="row form-group"
                  *ngIf="selectedUserRoleName !== 'Legal Admin'"
                >
                  <label for="UserLayout" class="col-sm-2"
                    >Layout<span class="text-danger">*</span></label
                  >
                  <div
                    class="col-md-5"
                    [@animateHeight]="displayMessage?.UserLayout"
                  >
                    <ng-select
                      id="saved-search-selection"
                      formControlName="UserLayout"
                      [items]="layouts"
                      [loading]="isLayoutLoading"
                      [ngStyle]="{ width: '100%' }"
                      [multiple]="false"
                      [clearable]="false"
                      [searchable]="true"
                      [closeOnSelect]="true"
                      bindLabel="name"
                      [bindValue]="'reviewLayoutId'"
                      placeholder="Select Layout"
                    >
                      <ng-template
                        ng-label-tmp
                        let-item="item"
                        let-clear="clear"
                      >
                        <span class="ng-value-label">{{ item.name }}</span>
                      </ng-template>
                    </ng-select>

                    <span
                      *ngIf="displayMessage?.UserLayout"
                      class="invalid-feedback"
                    >
                      {{ displayMessage?.UserLayout }}</span
                    >
                  </div>
                  <div class="col-md-1 pl-md-0 v-options">
                    <a
                      href="javascript:;"
                      (click)="createLayout()"
                      class="btn btn-link v-link px-0 pb-0 pt-0"
                      matTooltip="Create Layout"
                      matTooltipPosition="above"
                      >Create</a
                    >
                  </div>
                </div>
                <div class="row form-group" *ngIf="mode === 'Edit'">
                  <label for="field-desc" class="col-sm-2 mr-1">Account</label>
                  <div
                    class="custom-control custom-checkbox ml-3 mr-5"
                    *ngIf="hasLockRight$ | async"
                  >
                    <input
                      formControlName="IsUserLocked"
                      id="l-user"
                      type="checkbox"
                      class="custom-control-input"
                    />
                    <label for="l-user" class="custom-control-label">
                      Lock User</label
                    >
                  </div>
                  <div class="custom-control custom-checkbox ml-3 mr-5">
                    <input
                      formControlName="IsUserDeactivated"
                      id="d-user"
                      type="checkbox"
                      class="custom-control-input"
                    />
                    <label for="d-user" class="custom-control-label">
                      Deactivate User</label
                    >
                  </div>
                  <div class="custom-control custom-checkbox ml-3 mr-5">
                    <input
                      formControlName="ForceUserToChangePassword"
                      id="p-user"
                      type="checkbox"
                      class="custom-control-input"
                      [attr.disabled]="disableForcePasswordChange ? true : null"
                    />
                    <label for="p-user" class="custom-control-label">
                      User must change password in next login</label
                    >
                  </div>
                  <div
                    class="custom-control custom-checkbox ml-3 mr-5"
                    *ngIf="showOptionForDisablePasswordReset"
                  >
                    <input
                      formControlName="DisablePasswordReset"
                      id="resetpwd-user"
                      type="checkbox"
                      class="custom-control-input"
                    />
                    <label for="resetpwd-user" class="custom-control-label">
                      Disable password reset</label
                    >
                  </div>
                </div>
                <div
                  class="row form-group"
                  *ngIf="addUserForm.controls.IsUserDeactivated.value"
                >
                  <label class="col-sm-2 mr-1"></label>
                  <label
                    style="width: 104px"
                    *ngIf="hasLockRight$ | async"
                  ></label>
                  <div
                    class="d-flex custom-control custom-checkbox col-md-4 ml-3"
                  >
                    <label for="reason" class="form-label mr-3">Reason</label>
                    <input
                      formControlName="Reason"
                      id="reason"
                      type="text"
                      class="form-control"
                    />
                  </div>
                </div>
                <div
                  class="row form-group"
                  *ngIf="selectedUserRoleName !== 'Legal Admin'"
                >
                  <div class="col-12 mt-1 mb-2 block-title-text">
                    Assign Cases
                    <span
                      *ngIf="selectedRows?.length"
                      class="badge badge__tip pb-1"
                    >
                      <i class="far fa-lightbulb mx-1 text-warning"></i>
                      {{ selectedRows?.length }}
                      case(s) selected.
                    </span>
                  </div>
                  <div class="col-md-12">
                    <dx-data-grid
                      #dxGridCase
                      [showRowLines]="true"
                      id="gridContainer"
                      [dataSource]="casesDatasource"
                      [showBorders]="true"
                      keyExpr="ProjectId"
                      (onRowUpdated)="onRowUpdated($event)"
                      [(selectedRowKeys)]="selectedRows"
                      [allowColumnResizing]="true"
                      [columnMinWidth]="100"
                      [columnAutoWidth]="true"
                      [disabled]="disableGrid"
                    >
                      <dxo-selection
                        showCheckBoxesMode="always"
                        [allowSelectAll]="true"
                        mode="multiple"
                      >
                      </dxo-selection>
                      <dxo-paging [pageSize]="5"></dxo-paging>
                      <dxo-pager
                        [showPageSizeSelector]="true"
                        [allowedPageSizes]="[5, 10, 20]"
                        [showInfo]="true"
                      >
                      </dxo-pager>
                      <!-- dblClick | click -->
                      <dxo-editing
                        mode="cell"
                        [allowUpdating]="true"
                        [startEditAction]="'click'"
                        [allowAdding]="false"
                        [useIcons]="false"
                        [texts]="null"
                      >
                      </dxo-editing>
                      <dxo-header-filter
                        [searchMode]
                        [allowSearch]="true"
                        [visible]="true"
                      >
                      </dxo-header-filter>
                      <dxi-column
                        [allowEditing]="false"
                        dataField="ProjectName"
                        caption="Case"
                        [width]="300"
                      ></dxi-column>
                      <dxi-column
                        [allowEditing]="false"
                        dataField="ClientMatterNumber"
                        [width]="250"
                        caption="Client Matter Number"
                      >
                      </dxi-column>
                      <dxi-column
                        dataField="GroupId"
                        caption="User Group"
                        [showEditorAlways]="true"
                        calculateDisplayValue="GroupName"
                        [allowFiltering]="false"
                        [allowHeaderFiltering]="false"
                        [allowSorting]="false"
                        [width]="425"
                      >
                        <dxo-lookup
                          [allowClearing]="false"
                          [dataSource]="prepareSelection"
                          displayExpr="GroupName"
                          valueExpr="GroupId"
                        >
                        </dxo-lookup>
                      </dxi-column>
                      <dxo-load-panel [enabled]="true"></dxo-load-panel>
                      <!-- <dxo-scrolling showScrollbar="always" mode="virtual"></dxo-scrolling> -->
                      <!-- or "virtual" | "infinite" -->
                    </dx-data-grid>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12 text-right">
                <button
                  [disabled]="
                    isSubmitting ||
                    isWorking ||
                    isLayoutLoading ||
                    disableCreateUser
                  "
                  (click)="updateUser()"
                  type="button"
                  class="btn btn-{{ config.themeClient }}-{{
                    isSubmitting ? ' disabled border' : 'primary'
                  }} mr-2 "
                >
                  <mat-spinner
                    *ngIf="isSubmitting"
                    [strokeWidth]="2"
                    [diameter]="20"
                    mode="indeterminate"
                    class="mr-1"
                  >
                  </mat-spinner>
                  {{
                    mode === 'Create'
                      ? 'Create'
                      : mode === 'Edit'
                      ? 'Update'
                      : 'Clone'
                  }}
                </button>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </mat-accordion>
</ng-template>

<ng-template #confirmReset>
  <div
    class="row mx-0"
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div class="modal-header d-flex col-12" cdkDragHandle>
      <h4 class="modal-title pull-left">Confirm!</h4>
      <button
        type="button"
        class="close pull-right close-confirm"
        data-reset="false"
      >
        <span aria-hidden="true" class="fa fa-times"></span>
      </button>
    </div>
    <div class="modal-body h5 text-center mb-0">
      Are you sure you want to reset form?
    </div>
    <div class="modal-footer w-100 text-right">
      <button
        type="button"
        class="btn btn-venio-primary float-right close-confirm"
        data-reset="true"
      >
        YES
      </button>
      <button
        data-reset="false"
        type="button"
        class="btn btn-grey close-confirm"
      >
        NO
      </button>
    </div>
  </div>
</ng-template>

<ng-template #updateEmailConfirm>
  <div
    class=""
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div class="modal-header d-flex col-12" cdkDragHandle>
      <h4 class="modal-title pull-left">VERIFICATION</h4>
      <button
        type="button"
        class="close pull-right close-confirm"
        [mat-dialog-close]="false"
      >
        <span aria-hidden="true" class="fa fa-times"></span>
      </button>
    </div>
    <div class="modal-body h5 text-center mb-0">
      An email will be sent to the user to verify this email address.
      <div class="custom-control custom-checkbox mt-3">
        <input
          [formControl]="generatePw"
          id="notify"
          type="checkbox"
          class="custom-control-input"
        />
        <label for="notify" class="custom-control-label">
          Notify user and generate a new password</label
        >
      </div>
    </div>
    <div class="modal-footer text-right">
      <button
        type="button"
        class="btn btn-{{
          config.themeClient
        }}-primary float-right close-confirm"
        [mat-dialog-close]="true"
      >
        SEND
      </button>
      <button
        [mat-dialog-close]="false"
        type="button"
        class="btn btn-grey close-confirm"
      >
        CANCEL
      </button>
    </div>
  </div>
</ng-template>
