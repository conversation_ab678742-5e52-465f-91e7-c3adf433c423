import { DOCUMENT } from '@angular/common'
import {
  AfterViewInit,
  Component,
  ElementRef,
  Inject,
  OnDestroy,
  OnInit,
  Renderer2,
  TemplateRef,
  ViewChild
} from '@angular/core'
import { FormControl } from '@angular/forms'
import { MatDialog, MatDialogConfig } from '@angular/material/dialog'
import {
  ActivatedRoute,
  NavigationEnd,
  NavigationStart,
  RouteConfigLoadEnd,
  Router
} from '@angular/router'
import { ChangePasswordComponent } from '@auth/components/change-password/change-password.component'
import { ProjectInfo } from '@config/models'
import { ConfigService } from '@config/services/config.service'
import { select, Store } from '@ngrx/store'
import { Select, Store as xsStore } from '@ngxs/store'
import { GeneralService } from '@review2/services/general.service'
import {
  NavigationQuickLinkDataModel,
  NavigationQuickLinkTypes,
  quickLinkNavigationData
} from '@root/modules/application-nav/models/navigation-quick-link-data'
import { VenMatDialogUtilityService } from '@shared/components/ven-mat-confirm-dialog/ven-mat-dialog-utility.service'
import { IVenMatConfirmDialog, Project, ReviewSetInfo } from '@shared/models'
import useMatSelectFilter from '@shared/searchable-select-option/searchable-select-option'
import { ModuleLoginService } from '@shared/services/module-login.service'
import { clearSearchResponse } from '@shared/store/actions/search.actions'
import {
  ClearIndexedDb,
  ModuleLoginStateSelector,
  UpdateModuleLogin
} from '@shared/xsStore'
import { FetchProjectList } from '@stores/actions'
import { CaseSelectors, StartupStateSelector } from '@stores/selectors'
import { BsDropdownDirective } from 'ngx-bootstrap/dropdown'
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal'
import { CookieService } from 'ngx-cookie-service'
import { combineLatest, Observable, of, ReplaySubject, Subject } from 'rxjs'
import {
  debounceTime,
  filter,
  map,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs/operators'
import { UserSettingsComponent } from 'src/app/modules/application-nav/components/user-settings/user-settings.component'
import { CaseState } from 'src/app/modules/launchpad/store/reducers/case.reducers'
import { ReviewSetStateSelector } from 'src/app/modules/review/xs-store'
import { EulaService } from 'src/app/services/eula.service'
import { environment } from '../../../../../environments/environment'
import { NotificationsComponent } from '../../../../components/notifications/notifications.component'
import { UserRights } from '../../../../helpers/user-rights'
import { RouteRightsMappingService } from '../../../../services/route-rights-mapping.service'
import { GlobalLogOutAction } from '../../../../store/actions'
import { User, UserModel } from '../../../auth/models/user.model'
import { AuthService } from '../../../auth/services/auth.service'
import {
  getUser,
  getUserDetails
} from '../../../auth/store/selectors/access.selectors'
import { LicenseValidityModel } from '../../../config/models'
import { FetchLicenseValidity } from '../../../config/store/actions'
import { ConfigState } from '../../../config/store/reducers'
import {
  getBaseSetting,
  getControlSetting,
  getCssThemeVariables,
  getHelpLinks,
  getLicenseValidity,
  getProjectInfo,
  getThemeClient
} from '../../../config/store/selectors'
import { ConfirmationDialogComponent } from '../../../launchpad/components/confirmation-dialog/confirmation-dialog.component'
import { MessagePopUpComponent } from '../../../launchpad/components/message-pop-up/message-pop-up.component'
import {
  FetchUnIndexMediaStatus,
  GetProjectMediaStatus,
  GetTranscriptStatus,
  ResetCachedProjectRightListAction
} from '../../../launchpad/store/actions'
import {
  canFetchNewCase,
  projectMediaStatus,
  transcriptStatus
} from '../../../launchpad/store/selectors/case.selectors'
import { InAppNotification } from '../../models/notification.model'
import { NavSharedService } from '../../services/nav-shared.service'
import { ReportsMenuBuilderService } from '../../services/reports-menu-builder.service'
import {
  FetchInAppNotifications,
  FetchInAppNotificationsCount,
  GetUserPreference,
  SetInAppNotificationAsViewed,
  StartPollingForNotificationsCount,
  StopPollingForNotificationsCount,
  UpdateUserLastViewedNotificationEvent
} from '../../store/actions'
import {
  getInAppNotifications,
  getInAppNotificationsCount
} from '../../store/selectors/application-nav.selectors'
import { QuickLinksContainerComponent } from '../quick-links-container/quick-links-container.component'
import { fadeInOut } from './../../../shared/animation/animations'
import {
  FetchManualLinkAvailabilityAction,
  FetchMediaStatusByProjectIdAction,
  SetShowJobStatusTemplate
} from './../../stores/application-nav.actions'
import { ApplicationNavStateSelector } from './../../stores/application-nav.selectors'

@Component({
  selector: 'app-navigation-bar',
  templateUrl: './navigation-bar.component.html',
  styleUrls: ['./navigation-bar.component.scss'],
  animations: [fadeInOut]
})
export class NavigationBarComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild('helpUrlTmplt', { static: false })
  helpUrlTmpltRef: TemplateRef<any>

  @ViewChild('dropdown') dropdown: BsDropdownDirective

  private blurListener: () => void

  client: string

  iconBackgroundColor = '#FFFFFF'

  projectInfo: ProjectInfo

  userDetails: User

  userRights: any

  mediaStatus: any

  companyName: string

  isVodrEnabled: boolean

  processedMediaExists = true

  isAllMediaProcessed = true

  navModule: string

  reports: any[]

  review2MenuItems = []

  quickLinkNavigationData: NavigationQuickLinkDataModel[]

  notificationsToShow = 5

  notificationsCount: number

  notifications: InAppNotification[]

  enableLimitedOnDemandAccess: boolean

  bsModalRef: BsModalRef

  notifModalRef: BsModalRef

  unsubscribe$ = new Subject<void>()

  /**
   * Display new admin link only if the env is development.
   */
  enableNewAdminUiLink = environment.enableNewAdminUi

  vodHelpURL = ''

  /**
   * logo full path
   */
  logoFullPath: string

  /**
   * Check if the user has Admin access
   */
  isAdminSettingsInvalid = false

  userDetails$: Observable<User>

  /**
   * User right to upload data
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ADD_CUSTODIAN_MEDIA))
  allowToUpload$: Observable<boolean>

  /**
   * User right to produce data
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_SEARCH))
  allowToReview$: Observable<boolean>

  /**
   * User right to access analyze page
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_VIEW_DASHBOARD)
  )
  allowToAnalyze$: Observable<boolean>

  /**
   * User right to produce data
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_EXPORT))
  allowProduction$: Observable<boolean>

  /**
   * User right to view production status
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_VIEW_EXPORT_STATUS)
  )
  allowToViewProductionStatus$: Observable<boolean>

  /**
   * User right to view production status
   */
  @Select(
    StartupStateSelector.hasGlobalRight(UserRights.ALLOW_TO_MANAGE_LEGAL_HOLD)
  )
  manageLegalHold: Observable<boolean>

  /**
   * User right to view job status
   */
  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_TO_VIEW_JOB_STATUS_DASHBOARD
    )
  )
  allowToViewJobStatusDashboard$: Observable<boolean>

  /**
   * flag to identify if user has right for production
   */
  allowProduction = false

  /**
   * flag to identify if user has right to view production status
   */
  allowToViewProductionStatus = false

  /**
   * Cleanup placeholder for the observers when the component get destroyed.
   */
  private readonly toDestroy$ = new Subject<void>()

  /** check whether there are transcript files */
  transcriptStatus: boolean

  /** store the name of the reviewSet */
  reviewSetInformation: ReviewSetInfo

  /** show or hide the review set name */
  isReviewSetView = false

  /** true when the control setting to enable second review set view */
  enableReview2: boolean

  /** list of projects */
  projects: Project[]

  /** project ctrl for selection of project list */
  readonly projectCtrl = new FormControl()

  /** control for the project filter keyword */
  public projectFilterCtrl: FormControl = new FormControl()

  /** list of projects filtered by search keyword */
  public filteredProjects: ReplaySubject<Project[]> = new ReplaySubject<
    Project[]
  >()

  selectedCase = null

  /** get external user id from query string */
  extUserId = -1

  /** hide admin settigns and modules for external user */
  hideForExternal = false

  reviewSetId: number

  isReview2Nav = false

  newProjectId: number

  activeMenuItem: string

  helpLinks: { [key: string]: string }

  isFBIReview = false

  get _isNotificationAllowed(): boolean {
    return this.xsstore?.selectSnapshot(
      StartupStateSelector.hasGlobalRight(
        UserRights.ALLOW_TO_VIEW_INAPP_NOTIFICATION
      )
    )
  }

  legalHoldLicense = false

  hasManualLinks: boolean

  private get projectId(): number {
    return +this.route.snapshot.queryParams['projectId']
  }

  get isQuickLinkNavigationEnabled(): boolean {
    return (
      !this.enableLimitedOnDemandAccess &&
      !this.router.url.startsWith('/launchpad') &&
      !this.router.url.startsWith('/notifications') &&
      this.hasManualLinks &&
      this.configService.isVodEnabled &&
      this.userDetails?.globalRoleName !== 'Reviewer'
    )
  }

  hasMediaScopeId$ = this.xsstore
    .select(ApplicationNavStateSelector.SliceOf('mediaScopeIds'))
    .pipe(map((media) => !!media?.some((id) => id > 0)))

  projectGroupName: string

  logoutRedirect: string

  /**
   * For a certain period, we will redirect between the old UI and new UI.
   * We store the base path for the new UI to redirect when the new UI feature is enabled.
   * Both apps are hosted on the same origin in production,
   * which means that if one app obtains a JWT token and stores it in storage, the other app can easily access it.
   * However, the key used must be the same for both apps.
   * @private
   */
  private newUiBaseurl = ''

  /**
   * Initially, setting 1 would load the default style.
   * If there are conditions explicit to value 1, please make necessary adjustment.
   */
  vodVersion = 1

  passwordExpiryInDays: number

  notifyForPasswordChange: boolean

  notifyForLicenseExpiry: boolean

  licenseExpiryDate: Date

  isIdPUser?: boolean

  isFBIEnabled: boolean

  public formattedLicenseExpiryDate: string

  public licenseRemainingDays: number

  public get hidePasswordExpiryWarning(): boolean {
    return (
      localStorage.getItem('hidePasswordExpiryWarning')?.toLocaleLowerCase() ===
      'true'
    )
  }

  public get hideLicenseExpiryWarning(): boolean {
    return (
      localStorage.getItem('hideLicenseExpiryWarning')?.toLocaleLowerCase() ===
      'true'
    )
  }

  constructor(
    public router: Router,
    private cookieService: CookieService,
    public configService: ConfigService,
    private store: Store<CaseState>,
    private reportsMenuBuilderService: ReportsMenuBuilderService,
    private modalService: BsModalService,
    private appNavService: NavSharedService,
    private dialog: MatDialog,
    private xsstore: xsStore,
    private mlService: ModuleLoginService,
    private rightMappingService: RouteRightsMappingService,
    private authenticationService: AuthService,
    @Inject(DOCUMENT)
    private _document: HTMLDocument,
    private vcds: VenMatDialogUtilityService,
    private eulaService: EulaService,
    private route: ActivatedRoute,
    private gservice: GeneralService,
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private configStore: Store<ConfigState>
  ) {
    route.queryParamMap
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((param) => {
        this.reviewSetId = +param.get('reviewSetId') ?? -1
      })
    this.extUserId = route.snapshot.queryParams.extUserId ?? -1
    this.enableLimitedOnDemandAccess = true
    this.isVodrEnabled = !this.configService.isVodEnabled
    this.companyName = this.configService.companyName
    this.isReview2Nav = location.href.includes('review2')
    this.subscribeToRouterEvents()
    //this.reviewSetId = route.snapshot.queryParams.reviewSetId ?? -1

    // if (
    //   this.extUserId != -1 ||
    //   router.url.startsWith('/launchpad/custodian-portal')
    // ) {
    //   this.hideForExternal = true
    // }
    this.GetProjectGroupNameAndSetLinkVisibility()

    //if (!this.manageLegalHold) {
    this.reportsMenuBuilderService.menuData
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((reportsMenu) => {
        this.reports = reportsMenu
      })
    // } else {
    //   this.reportsMenuBuilderService.menuDataForHold
    //     .pipe(takeUntil(this.unsubscribe$))
    //     .subscribe((reportsMenu) => {
    //       this.reports = reportsMenu
    //     })
    // }

    // set favorite icon full path
    let faviconFullPath =
      configService.getWebBaseUrl() + '/' + configService.favIconPath
    if (
      this.configService.favIconPath.startsWith('http://') ||
      this.configService.favIconPath.startsWith('https://')
    ) {
      faviconFullPath = this.configService.favIconPath
    }
    this._document
      .getElementById('appFavicon')
      .setAttribute('href', faviconFullPath)

    // set logo full path data
    if (
      this.configService.logoPath.startsWith('http://') ||
      this.configService.logoPath.startsWith('https://')
    ) {
      this.logoFullPath = this.configService.squareLogoPath
    } else {
      this.logoFullPath =
        this.configService.getWebBaseUrl() +
        '/' +
        this.configService.squareLogoPath
    }
  }

  private selectSelectedProjectId(): void {
    this.xsstore
      .select(StartupStateSelector.SliceOf('selectedProjectId'))
      .pipe(
        filter((id) => id > 0),
        takeUntil(this.toDestroy$)
      )
      .subscribe((id) => {
        this.projectCtrl.setValue(id)
      })
  }

  ngOnInit() {
    this.selectVodVersionAndSetNewUiRedirectionPath()
    // Do not load cases if the VOD version is 3 as we switch to new UI
    if (this.vodVersion !== 3) {
      this.xsstore.dispatch(new FetchProjectList())
    }

    this.store
      .select(getControlSetting('ENABLE_REVIEW_2'))
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isFbiEnabled) => {
        this.isFBIEnabled = Boolean(isFbiEnabled)
      })

    this.selectSelectedProjectId()
    this.#handleNewCaseFetchStatus()
    this.loadManualLinkAvailability()

    this.xsstore
      .select(CaseSelectors.projects)
      .pipe(
        filter((p) => p.length > 0),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (projects) => {
          this.projects = projects
          // load the initial project list
          this.filteredProjects.next(this.projects.slice())
          // project list control should be disabled if we are editing an item
          if (+this.projectId > 0) {
            // we're setting selected project Id from query string param if supplied.
            this.projectCtrl.setValue(+this.projectId)
          }
        }
      })
    this.store.dispatch(new StartPollingForNotificationsCount())

    this.store
      .pipe(select(getThemeClient), takeUntil(this.unsubscribe$))
      .subscribe((client: string) => {
        this.client = client
      })
    this.store
      .pipe(
        select(getControlSetting('ENABLE_REVIEW_2')),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isEnable: boolean) => {
        this.enableReview2 = isEnable
        this.isFBIReview = this.isReview2Nav || this.enableReview2
      })
    this.store
      .pipe(
        select(getCssThemeVariables),
        takeUntil(this.unsubscribe$),
        filter((colors: any) => !!colors)
      )
      .subscribe((colors: any) => {
        this.iconBackgroundColor = colors['--venio-theme-color-main-extra-dark']
      })

    this.store
      .pipe(select(getProjectInfo), takeUntil(this.unsubscribe$))
      .subscribe((projectInfo: ProjectInfo) => {
        this.projectInfo = projectInfo
        if (+this.projectId > 0 || this.projectInfo?.projectId > 0) {
          // we're setting selected project Id from query string param if supplied.
          this.xsstore.dispatch(new FetchProjectList())

          this.projectCtrl.setValue(
            Number.isNaN(+this.projectId)
              ? +this.projectInfo?.projectId
              : +this.projectId
          )
          this.projectCtrl.updateValueAndValidity()
        }
      })

    // this.store
    //   .pipe(
    //     select(getUserDetails),
    //     filter((userDetails) => !!userDetails),
    //     takeUntil(this.unsubscribe$)
    //   )
    //   .subscribe((userDetails: User) => {
    //     this.userDetails = userDetails
    //     this.store.dispatch(new GetUserPreference())
    //   })

    this.userDetails$ = this.store.pipe(
      select(getUserDetails),
      filter((userDetails) => !!userDetails),
      takeUntil(this.unsubscribe$)
    )

    this.userDetails$.subscribe((userDetails: User) => {
      this.userDetails = userDetails
      this.store.dispatch(new GetUserPreference())
      // localStorage 'hidePasswordExpiryWarning' is null when password warning is not shown yet to the user. in that case, we will not show error until user relogins.
      if (localStorage.getItem('hidePasswordExpiryWarning') !== null) {
        this.notifyForPasswordChange = userDetails.notifyForPasswordChange
        this.passwordExpiryInDays = userDetails.passwordExpiryInDays
      }
    })

    combineLatest([this.allowProduction$, this.allowToViewProductionStatus$])
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(([allowProduction, viewProductionStatus]) => {
        this.allowProduction = allowProduction
        this.allowToViewProductionStatus = viewProductionStatus
      })

    this.store
      .pipe(select(getInAppNotificationsCount), takeUntil(this.unsubscribe$))
      .subscribe((notificationsCount: number) => {
        this.notificationsCount = notificationsCount
      })

    this.store
      .pipe(select(getInAppNotifications), takeUntil(this.unsubscribe$))
      .subscribe((notifications: InAppNotification[]) => {
        this.notifications = notifications
      })

    this.store
      .pipe(
        select(getControlSetting('ENABLE_LIMITED_ONDEMAND_ACCESS')),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((isEnabled: boolean) => {
        this.enableLimitedOnDemandAccess = isEnabled
      })

    this.appNavService.userSettingsEventEmitter.subscribe(
      (tabIndex: number) => {
        this.openUserSettings(tabIndex)
      }
    )

    this.store
      .pipe(
        select(getControlSetting('VOD_HELP_URL')),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((value: string) => {
        this.vodHelpURL = value
      })

    // this.store
    //   .pipe(select(transcriptStatus), takeUntil(this.toDestroy$))
    //   .subscribe((res: boolean) => (this.transcriptStatus = res))

    this.eulaService.logOffStatus
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((logOffStatus: boolean) => {
        logOffStatus && this.logOff()
      })

    this.configService
      .fetchLicenseStatus$('FEATURE', 'LEGAL HOLD')
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((isEnabled: boolean) => {
        this.legalHoldLicense = isEnabled
      })

    this.configStore
      .pipe(
        select(getLicenseValidity),
        filter((li) => !!li),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((licenseValidity: LicenseValidityModel) => {
        this.notifyForLicenseExpiry = licenseValidity.notifyForLicenseExpiry
        this.licenseExpiryDate = new Date(licenseValidity.expiryDate)
        this.formattedLicenseExpiryDate = this.getFormattedLicenseExpiryDate()
        this.licenseRemainingDays = this.getLicenseRemainingDays()
      })

    this.store
      .pipe(
        select(getHelpLinks),
        filter((res) => !!res),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((res) => {
        this.helpLinks = res
      })

    this.getReview2MenuItems()

    this.getUserInfoAfterLogin()

    this.getLogoutRedirectUrl()

    this.store.dispatch(new FetchLicenseValidity())
  }

  // gets userinfo from login response
  private getUserInfoAfterLogin() {
    this.store
      .pipe(
        select(getUser),
        takeUntil(this.unsubscribe$),
        map((user: UserModel) => user),
        filter((user: UserModel) => !!user)
      )
      .subscribe((user: UserModel) => {
        this.isIdPUser = user.IsIdPUser
        this.notifyForPasswordChange = user.NotifyForPasswordChange
        this.passwordExpiryInDays = user.PasswordExpiryInDays
        if (user.NotifyForPasswordChange) {
          localStorage.setItem('hidePasswordExpiryWarning', 'false')
          if (user.PasswordExpiryInDays < 2) this.handlePwChange()
        }
      })
  }

  private getLogoutRedirectUrl() {
    this.store
      .pipe(
        select(getBaseSetting('isOktaIdpEnabled')), // Select the setting from the store
        takeUntil(this.unsubscribe$), // Unsubscribe when `unsubscribe$` emits
        switchMap((isOktaIdpEnabled: any) => {
          if (isOktaIdpEnabled) {
            return this.authenticationService
              .fetchLogoutRedirect<any>('OKTA')
              .pipe(
                take(1) // Ensure only one emission
              )
          } else {
            return of(null) // Return an observable that emits `null` if `isOktaIdpEnabled` is false
          }
        })
      )
      .subscribe((response) => {
        if (response) {
          this.logoutRedirect = response.data
        }
      })
  }

  private selectVodVersionAndSetNewUiRedirectionPath(): void {
    this.store
      .pipe(
        select(getControlSetting('VOD_VERSION')),
        takeUntil(this.toDestroy$)
      )
      .subscribe((VOD_VERSION: number) => {
        this.vodVersion = Number(VOD_VERSION)
        /**
         * This method of redirecting users from the old UI to the new one is only temporary.
         * The redirect will be removed once the updated UI is fully functional.
         */
        const isNewUiEnabled = Number(VOD_VERSION) === 3

        if (isNewUiEnabled) {
          this.newUiBaseurl = `/review-next`
        } else {
          this.newUiBaseurl = ''
        }
      })
  }

  private NavigateToUserSelectedRoute() {
    let message = ''
    let navUrl = ''
    if (this.navModule === 'add') {
      if (this.isVodrEnabled) {
        navUrl =
          '/launchpad/service_request/add-data?projectId=' +
          this.projectId +
          '&existingCase=' +
          true
      } else {
        navUrl = '/upload?projectId=' + this.projectId
      }
      this.router.navigateByUrl(navUrl)
    } else if (this.navModule === 'processing') {
      // rediret to processing page even if the media is not processed yet
      // or not added to the case
      navUrl = '/processing?projectId=' + this.projectId
      this.router.navigateByUrl(navUrl)
    } else {
      if (!this.mediaStatus.MediaList) {
        if (
          this.navModule === 'review' &&
          this.projectInfo.enableTranscriptViewer
        )
          this.processedMediaExists = this.transcriptStatus
        else this.processedMediaExists = false
        message =
          'No media has been added to the case. Please use the upload page to add media in the case.'
      } else if (!this.mediaStatus.ProcessedMediaList) {
        this.processedMediaExists = false
        if (this.isVodrEnabled) {
          message = 'The media in the case are being processed.'
        } else {
          message =
            'The media in the case are being processed. Please use the upload page to view the processing status.'
        }
      } else if (!this.mediaStatus.IsAllMediaProcessed) {
        this.isAllMediaProcessed = false
        this.processedMediaExists = true
        message =
          'There are data that are currently being processed.' +
          'Would you like to ' +
          this.navModule +
          ' all the data that has been completed?'
      } else {
        this.processedMediaExists = true
        this.isAllMediaProcessed = true
        message = ''
      }

      //set navigation url
      if (this.navModule === 'review') {
        let reviewSetUrl = ''
        if (this.reviewSetId > 0) {
          reviewSetUrl = `&reviewSetId=${this.reviewSetId}`
        }
        navUrl = `${
          this.enableReview2
            ? '/review2'
            : this.newUiBaseurl
            ? '/review-next'
            : '/review'
        }?media=1&projectId=${this.projectId}${reviewSetUrl}`
      } else if (this.navModule === 'analyze')
        navUrl = '/analyze?media=1&projectId=' + this.projectId
      else if (this.navModule === 'produce') {
        if (this.isVodrEnabled)
          navUrl = '/production/production_status?projectId=' + this.projectId
        else if (
          !this.userRights?.ALLOW_EXPORT &&
          (this.userRights?.ALLOW_TO_VIEW_EXPORT_STATUS ||
            this.userRights?.ALLOW_TO_DOWNLOAD_EXPORT_ARCHIVES)
        )
          navUrl = '/production/production_status?projectId=' + this.projectId
        else navUrl = '/production?projectId=' + this.projectId
      }

      if (!this.processedMediaExists) {
        this.showMessage(message)
      } else if (!this.isAllMediaProcessed) {
        this.showConfirmation(message).subscribe((result) => {
          if (result) {
            this.router.navigateByUrl(navUrl)
          }
        })
      } else {
        this.router.navigateByUrl(navUrl)
      }
      //}
    }
    //this.isFBIReview = this.navModule === 'review' && this.enableReview2
    this.navModule = null
  }

  /**
   * Perform default fetching task when project selected get changed. These are the default derived data from top level
   */
  projectValueChanged(event): void {
    this.newProjectId = event.value
    const isAnalyzePage = this.router.url.includes('analyze')
    if (isAnalyzePage) {
      this.#fetchMediaStatus()
    } else this.#hanldeProjectChange(this.newProjectId)
  }

  #fetchMediaStatus(): void {
    this.store.dispatch(new FetchUnIndexMediaStatus(this.newProjectId))
  }

  #handleNewCaseFetchStatus(): void {
    this.store
      .pipe(select(canFetchNewCase), takeUntil(this.toDestroy$))
      .subscribe((status) => {
        if (status === undefined) return

        status
          ? this.#hanldeProjectChange(this.newProjectId)
          : this.projectCtrl.setValue(this.projectId)
      })
  }

  #hanldeProjectChange(projectId: number): void {
    this.xsstore.dispatch([
      new ClearIndexedDb('HtmlParts'),
      new ClearIndexedDb('NativeRedaction')
    ])
    this.store.dispatch(clearSearchResponse())
    const currentRoute = this.router.url.split('projectId')
    const navUrl = `${this.configService.getWebBaseUrl()}/ondemand/appplus/#${
      currentRoute[0]
    }projectId=${projectId}`
    window.location.replace(navUrl)
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }

  /** listens to the project filter control change */
  private projectFilterChange(): void {
    // listen for search field value changes
    this.projectFilterCtrl.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        const { filterProjects, filteredProjects } = useMatSelectFilter()
        filterProjects(this.projectFilterCtrl, this.projects)
        this.filteredProjects = filteredProjects
      })
  }

  /**
   * Edited: Sanh Huynh; Date: May 07, 2020; Ref #23816 - Able to navigate to the project in RVOD whose services are other than Analyse and Review Service
   * populate the case list.
   */
  ngAfterViewInit() {
    this.CheckAdminSettingsAccess()
    this.reviewSetInfo()
    // this.projectValueChanged()
    this.projectFilterChange()
    this.blurListener = this.renderer.listen('window', 'blur', () => {
      this.handleWindowBlur()
    })
  }

  private handleWindowBlur(): void {
    // Close the dropdown when the document loses focus (iframe clicked)
    if (this.dropdown.isOpen) {
      this.dropdown.hide()
    }
  }

  reviewSetInfo() {
    this.xsstore
      .select(ReviewSetStateSelector.SliceOf('selectedReviewSetInfo'))
      .pipe(
        tap(() => (this.isReviewSetView = false)),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (res: ReviewSetInfo) => {
          if (this.route.snapshot.queryParams.reviewSetId && res) {
            this.isReviewSetView = true
            this.reviewSetInformation = res
          }
        }
      })
  }

  /**
   * Check if the user has Admin access
   */
  CheckAdminSettingsAccess() {
    this.xsstore
      .select(
        StartupStateSelector.hasGlobalRight(
          UserRights.ACCESS_ADMINISTRATIVE_MODE
        )
      )
      .pipe(takeUntil(this.toDestroy$), debounceTime(200))
      .subscribe((access: boolean) => {
        this.isAdminSettingsInvalid = false
        if (!access && access !== undefined) {
          this.isAdminSettingsInvalid = true
        }
      })
  }

  GetProjectGroupNameAndSetLinkVisibility() {
    this.xsstore
      .select(StartupStateSelector.GetProjectGroupName())
      .pipe(takeUntil(this.toDestroy$), debounceTime(200))
      .subscribe((pgname: string) => {
        this.projectGroupName = pgname
        if (
          this.projectGroupName === 'External User Group' ||
          this.router.url.startsWith('/launchpad/custodian-portal')
        ) {
          this.hideForExternal = true
        }
      })
  }

  /**
   * Prepares query param for the case list to get from API
   * @param start Page index begin
   * @param end Page index end
   */
  private prepareParam(start: number, end: number, searchTerm?: string) {
    // we don't have to worry about checking search term value here cause we've validated in service class.
    return {
      userId: this.userDetails.userId,
      start: start,
      end: end,
      sortBy: 'projectId',
      isDesc: true,
      searchTerm: searchTerm
    }
  }

  ngOnDestroy() {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.store.dispatch(new StopPollingForNotificationsCount())
    if (this.notifModalRef) {
      this.notifModalRef.hide()
    }
    if (this.blurListener) {
      this.blurListener()
    }
  }

  itemClick(data) {
    let reportPopupViewer
    if (!data.itemData.items) {
      const reportType = data.itemData.id
      if (reportType === 'NoReportsAvailable') {
        return
      }
      if (
        reportType === 'PrivilegeDetailReport' ||
        reportType === 'RedactionDetailReport' ||
        reportType === 'ReviewSetReport' ||
        reportType === 'TagHistoryDetailReport' ||
        reportType === 'ReviewDocumentNotesReport' ||
        reportType === 'TagCommentDetailReport' ||
        reportType === 'ReviewQcDetailReport'
      ) {
        reportPopupViewer = window.open(
          this.configService.getWebBaseUrl() +
            '/Review/ReviewReport.aspx?Type=' +
            reportType +
            '&HdrDis=1'
        )
        return
      }
      if (reportType === 'UserProjectAssociationReport') {
        reportPopupViewer = window.open(
          this.configService.getWebBaseUrl() +
            '/Reports/Report.aspx?Type=' +
            reportType +
            '&IsUserSelection=1' +
            '&HdrDis=1'
        )
      } else if (reportType === 'HoldDetailReport') {
        reportPopupViewer = window.open(
          this.configService.getWebBaseUrl() +
            '/Reports/Report.aspx?Type=' +
            reportType +
            '&HoldReport=1' +
            '&HdrDis=1'
        )
      } else if (reportType === 'ActivityReport') {
        this.router.navigate(['activity-report'])
      } else {
        reportPopupViewer = window.open(
          this.configService.getWebBaseUrl() +
            '/Reports/Report.aspx?Type=' +
            reportType +
            '&HdrDis=1'
        )
      }
    }
  }

  onNotificationDropdownShown() {
    this.store.dispatch(new UpdateUserLastViewedNotificationEvent())
    this.fetchNotifications(this.notificationsToShow, true)
    this.store.dispatch(new FetchInAppNotificationsCount())
  }

  onNotificationClicked(inAppNotificationId: number | any) {
    this.store.dispatch(new SetInAppNotificationAsViewed(inAppNotificationId))
    this.fetchNotifications(this.notificationsToShow, true)
  }

  onScroll() {
    this.notificationsToShow = this.notificationsToShow + 5
    this.fetchNotifications(this.notificationsToShow, true)
  }

  onViewAllNotifications() {
    // Edited: Sanh Huynh; Date: Feb 04, 2020; Ref #23284 - Notification - Create a popup rather than opening in new page
    this.notifModalRef = this.modalService.show(
      NotificationsComponent,
      Object.assign(
        {},
        {
          class: 'modal-sidnav-options-pages',
          ignoreBackdropClick: true,
          keyboard: false
        }
      )
    )
    this.dropdown.hide()
  }

  fetchNotifications(notificationsToShow: number, forceFetch: boolean) {
    if (
      this._isNotificationAllowed &&
      (this.notificationsCount !== 0 ||
        this.notifications.length === 0 ||
        (forceFetch && this.router.url !== '/notifications'))
    ) {
      this.store.dispatch(new FetchInAppNotifications(notificationsToShow))
    }
  }

  openUserSettings(tabIndex = 0) {
    const initialState = {
      tabIndex: tabIndex
    }
    this.bsModalRef = this.modalService.show(
      UserSettingsComponent,
      Object.assign(
        { initialState },
        {
          class: 'modal-sidnav-options-pages',
          ignoreBackdropClick: true,
          keyboard: false
        }
      )
    )
  }

  logOff() {
    const reviewSetId = this.xsstore.selectSnapshot(
      ReviewSetStateSelector.selectedReviewSetId
    )
    const projectId = this.xsstore.selectSnapshot(
      ModuleLoginStateSelector.SliceOf('projectId')
    )
    const prjLoginId = this.xsstore.selectSnapshot(
      ModuleLoginStateSelector.SliceOf('projectLoginId')
    )
    const moduleLoginId = this.xsstore.selectSnapshot(
      ModuleLoginStateSelector.SliceOf('moduleLoginId')
    )
    //Update the logout datetime of the module.
    if (projectId && prjLoginId && moduleLoginId)
      this.xsstore.dispatch(
        new UpdateModuleLogin(
          projectId,
          moduleLoginId,
          this.mlService.getModuleLoginModel(
            +localStorage.UserId,
            prjLoginId,
            reviewSetId,
            false
          )
        )
      )

    // reset state so will prevent login with difference user
    this.store.dispatch(new GlobalLogOutAction())
    this.store.dispatch(new ResetCachedProjectRightListAction())
    this.configService.clearSession().pipe(take(1)).subscribe()
    localStorage.clear()
    /**
     * When an user logs out by clicking on the button, don't capture the current url as a `returnUrl`.
     * Only capture when the log out happened automatically by session time out or something like (bot).
     */

    this.cookieService.delete('rememberMe', '/')
    this.cookieService.delete('rememberMe', '/ondemand/appplus')

    if (this.isIdPUser && this.logoutRedirect)
      window.location.href = this.logoutRedirect
    else this.router.navigateByUrl('/login')
  }

  goToLaunchpad() {
    this.activeMenuItem = ''
    if (!this.hideForExternal) this.router.navigate(['launchpad'])
  }

  /**
   * navigate to analyze, review or production page
   * @param navModule
   */
  navigateToNextPage(navModule: string, menuName?: string) {
    this.navModule = navModule
    this.activeMenuItem = menuName
    const projectId = +this.route.snapshot.queryParams['projectId']
    this.store.dispatch(new GetTranscriptStatus(this.projectId))
    this.store.dispatch(new GetProjectMediaStatus(projectId))

    combineLatest([
      this.store.pipe(select(projectMediaStatus)),
      this.store.pipe(select(transcriptStatus))
    ])
      .pipe(
        filter(
          ([mediaStatus, transcriptStatus]) =>
            !!this.navModule && !!mediaStatus && transcriptStatus !== undefined
        ),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([caseMediaStatus, transcriptStatus]) => {
        this.mediaStatus = caseMediaStatus
        this.transcriptStatus = transcriptStatus
        this.NavigateToUserSelectedRoute()
      })
  }

  // This function highlights the menu whenever the user refreshes the page.
  highlightReview2Menu() {
    switch (true) {
      case this.router.url.includes('upload'):
        this.activeMenuItem = 'upload'
        break
      case this.router.url.includes('analyze'):
        this.activeMenuItem = 'analyze'
        break
      case this.router.url.includes('review'):
        this.activeMenuItem = 'review'
        break
      case this.router.url.includes('production'):
        this.activeMenuItem = 'produce'
        break
      case this.router.url.includes('processing'):
        this.activeMenuItem = 'processing'
        break
      default:
        this.activeMenuItem = ''
    }
  }

  /**
   * show the confirmation dialog and return the user confirmation result
   * @param message
   */
  showConfirmation(message: string): Subject<boolean> {
    const modal = this.modalService.show(ConfirmationDialogComponent, {
      class: 'modal-dialog-centered'
    })
    ;(<ConfirmationDialogComponent>modal.content).showConfirmationModal(
      this.companyName,
      message
    )
    return (<ConfirmationDialogComponent>modal.content).onClose
  }

  showMessage(message: string) {
    const initialState = {
      list: [message],
      title: this.companyName,
      closeBtnName: 'Close'
    }

    this.bsModalRef = this.modalService.show(
      MessagePopUpComponent,
      Object.assign(
        { initialState },
        { class: 'modal-dialog-centered', ignoreBackdropClick: true }
      )
    )
  }

  openVODHelpURL() {
    if (!this.vodHelpURL) {
      const message =
        'Help URL is not configured. Please contact your administrator.'
      const headerText = 'Help URL'
      const info = <IVenMatConfirmDialog>{
        headerText: headerText,
        confirmationText: message,
        isAlert: true
      }
      this.vcds.openConfirmDialog(info)
      this.vcds.confirmDialogAction
        .pipe(takeUntil(this.toDestroy$))
        .subscribe((action) => {
          if (action == 'yes') {
            this.vcds.closeConfirmDialog()
          }
        })
      return
    }
    const isURLValid = (value: string) => {
      try {
        const pattern = new RegExp(
          '^(https?:\\/\\/)?' + // protocol
            '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z\\d]{2,}|' + // domain name
            '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
            '(\\:\\d+)?(\\/[-a-z\\d%_.~+#=-?]*)*' + // port and path
            '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
            '(\\#[-a-z\\d_]*)?$',
          'i'
        ) // fragment locator
        return !!pattern.test(value)
      } catch (_) {
        return false
      }
    }

    if (isURLValid(this.vodHelpURL)) {
      if (this.helpLinks) {
        const moduleUrl = this.gservice.getRelativeUrl()
        if (this.helpLinks[moduleUrl])
          window.open(this.vodHelpURL + this.helpLinks[moduleUrl], 'user_guide')
        else window.open(this.vodHelpURL, 'user_guide')
      } else window.open(this.vodHelpURL, 'user_guide')
    } else {
      const message = 'Invalid help URL. Please contact your administrator.'
      const headerText = 'Help URL'
      const info = <IVenMatConfirmDialog>{
        headerText: headerText,
        confirmationText: message,
        isAlert: true
      }
      this.vcds.openConfirmDialog(info)
      this.vcds.confirmDialogAction
        .pipe(takeUntil(this.toDestroy$))
        .subscribe((action) => {
          if (action == 'yes') {
            this.vcds.closeConfirmDialog()
          }
        })
      return
    }
  }

  toggleJobStatusView() {
    const showJobStatusView = this.xsstore.selectSnapshot(
      ApplicationNavStateSelector.SliceOf('showJobStatusTemplate')
    )
    this.xsstore.dispatch(new SetShowJobStatusTemplate(!showJobStatusView))
  }

  private calculateLinkAvailabilityAfterLoaded = (data) => {
    this.hasManualLinks = Object.values(data).some((has) => has)
    const mapped = Object.keys(data).reduce((result, key) => {
      if (key === 'emailAnalysisEnabled') {
        result['EMAIL_ANALYSIS'] = data[key]
      }
      if (key === 'indexingEnabled') {
        result['INDEXING'] = data[key]
      }
      if (key === 'languageIdentificationEnabled') {
        result['LANGUAGE_IDENTIFICATION'] = data[key]
      }
      if (key === 'computeNearDuplicatesEnabled') {
        result['COMPUTE_NEAR_DUPLICATE'] = data[key]
      }
      if (key === 'generateMissingEmailsEnabled') {
        result['GENERATE_MISSING_EMAILS'] = data[key]
      }
      if (key === 'prepareEmailThreadingEnabled') {
        result['EMAIL_THREADING'] = data[key]
      }
      if (key === 'populateDuplicateFilePathEnabled') {
        result['POPULATE_DUPLICATE_FILEPATH'] = data[key]
      }
      if (key === 'computeInclusiveEmailEnabled') {
        result['COMPUTE_INCLUSIVE_EMAIL'] = data[key]
      }
      if (key === 'populateCustodianDedupEnabled') {
        result['POPULATE_CUSTODIAN_DEDUP'] = data[key]
      }
      if (key === 'applySpamTagEnabled') {
        result['APPLY_SPAM_TAG'] = data[key]
      }

      return result
    }, {} as { [key in NavigationQuickLinkTypes]: boolean })
    const navData = quickLinkNavigationData.map((parent) => ({
      ...parent,
      items: [...parent?.items]
    }))

    if (!Array.isArray(navData)) return

    navData.forEach((parent) => {
      if (Array.isArray(parent?.items)) {
        parent.items = parent?.items?.filter(
          (child) => mapped[child?.linkType] === true
        )
      }
    })
    this.quickLinkNavigationData = navData
  }

  private fetchRequiredDataForManualLinks = (): void => {
    if (!(this.projectId > 0)) {
      return
    }

    this.xsstore.dispatch([
      new FetchManualLinkAvailabilityAction(this.projectId),
      new FetchMediaStatusByProjectIdAction(this.projectId)
    ])
  }

  private readonly loadManualLinkAvailability = (): void => {
    this.router.events
      .pipe(
        filter(
          (event) =>
            event instanceof NavigationEnd ||
            event instanceof RouteConfigLoadEnd
        ),
        tap(this.fetchRequiredDataForManualLinks),
        switchMap(() =>
          this.xsstore.select(
            ApplicationNavStateSelector.SliceOf('quickLinkAvailability')
          )
        ),
        filter((data) => !!data),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(this.calculateLinkAvailabilityAfterLoaded)
  }

  readonly quickLinkItemClicked = (event: unknown): void => {
    const menu: NavigationQuickLinkDataModel = event['itemData']

    if (menu.linkType === 'NONE') {
      return
    }

    const config: MatDialogConfig<unknown> = {
      maxHeight: '96vh',
      width: 'auto',
      maxWidth: '90vw',
      disableClose: true,
      closeOnNavigation: true,
      ariaLabel: 'quick-links',
      data: { linkType: menu.linkType }
    }
    this.dialog.open(QuickLinksContainerComponent, config)
  }

  // load review2 menu items

  isUserInRole(userDetails: any, roleName: string): boolean {
    return userDetails?.globalRoleName === roleName
  }

  isProjectInFilteringServiceCase(projectInfo: any): boolean {
    return projectInfo?.isFilteringServiceCase
  }

  isReviewer(userDetails: any): boolean {
    return this.isUserInRole(userDetails, 'Reviewer')
  }

  isLimitedOnDemandAccessEnabled(): boolean {
    return !this.enableLimitedOnDemandAccess
  }

  isNotReviewer(): boolean {
    return !this.isReviewer(this.userDetails)
  }

  isUploadMenuItemVisible(allowToUpload: boolean): boolean {
    return allowToUpload && this.isNotReviewer()
  }

  isAnalyzeMenuItemVisible(
    allowToAnalyze: boolean,
    allowToReview: boolean
  ): boolean {
    return (
      this.isLimitedOnDemandAccessEnabled() &&
      allowToAnalyze &&
      allowToReview &&
      this.isNotReviewer()
    )
  }

  isReviewMenuItemVisible(allowToReview: boolean): boolean {
    const isFilteringServiceCase = this.isProjectInFilteringServiceCase(
      this.projectInfo
    )
    return (
      this.isLimitedOnDemandAccessEnabled() &&
      (!this.isVodrEnabled || (this.isVodrEnabled && isFilteringServiceCase)) &&
      allowToReview &&
      this.isNotReviewer()
    )
  }

  isProduceMenuItemVisible(
    allowProduction: boolean,
    allowToViewProductionStatus: boolean
  ): boolean {
    return (
      this.isLimitedOnDemandAccessEnabled() &&
      (allowProduction || allowToViewProductionStatus) &&
      this.isNotReviewer()
    )
  }

  isProessingStatusMenuItemVisible(allowToViewJobStatus: boolean): boolean {
    return this.isLimitedOnDemandAccessEnabled() && allowToViewJobStatus
  }

  getMenu(name) {
    switch (name) {
      case 'Upload':
        return {
          id: 1,
          name,
          icon: 'upload',
          navigateToNextPage: 'add'
        }
      case 'Analyze':
        return {
          id: 2,
          name,
          icon: 'chart-line',
          navigateToNextPage: 'analyze'
        }
      case 'Review':
        return {
          id: 3,
          name,
          icon: 'search',
          navigateToNextPage: 'review'
        }
      case 'Produce':
        return {
          id: 4,
          name,
          icon: 'download',
          navigateToNextPage: 'produce'
        }
      case 'Processing':
        return {
          id: 5,
          name,
          icon: 'cogs',
          navigateToNextPage: 'processing'
        }
      default:
        return null
    }
  }

  addMenu(menuName: string): void {
    const menu = this.getMenu(menuName)
    this.review2MenuItems.push(menu)
  }

  clearMenu(): void {
    this.review2MenuItems = []
  }

  checkMenuVisibility(
    menuName: string,
    observable$: Observable<boolean>,
    userDetails$: Observable<User>,
    optionalObservable$?: Observable<boolean>
  ): void {
    const observables = [observable$, userDetails$]
    if (optionalObservable$) {
      observables.push(optionalObservable$)
    }

    const results = combineLatest(observables).pipe(
      filter(
        ([value, userDetails, optionalValue]) =>
          !!value && !!userDetails && (!optionalObservable$ || !!optionalValue)
      ),
      takeUntil(this.unsubscribe$)
    )

    results.subscribe(
      ([value, userDetails, optionalValue]: [boolean, User, boolean]) => {
        this.userDetails = userDetails
        // Upload
        if (menuName === 'Upload' && this.isUploadMenuItemVisible(value)) {
          this.clearMenu()
          this.addMenu(menuName)
        }

        // Analyze
        if (
          menuName === 'Analyze' &&
          this.isAnalyzeMenuItemVisible(value, optionalValue)
        ) {
          this.addMenu(menuName)
        }

        // Review
        if (menuName === 'Review' && this.isReviewMenuItemVisible(value)) {
          this.addMenu(menuName)
        }

        // Produce
        if (
          menuName === 'Produce' &&
          this.isProduceMenuItemVisible(value, optionalValue)
        ) {
          this.addMenu(menuName)
        }

        // Processing
        if (
          menuName === 'Processing' &&
          this.isProessingStatusMenuItemVisible(value)
        ) {
          this.addMenu(menuName)
        }
      }
    )
  }

  getReview2MenuItems(): void {
    // Call the checkMenuVisibility function for each menu
    this.review2MenuItems = []
    this.checkMenuVisibility(
      'Upload',
      this.allowToUpload$,
      this.userDetails$,
      null
    )
    this.checkMenuVisibility(
      'Analyze',
      this.allowToAnalyze$,
      this.userDetails$,
      this.allowToReview$
    )
    this.checkMenuVisibility(
      'Review',
      this.allowToReview$,
      this.userDetails$,
      null
    )
    this.checkMenuVisibility(
      'Produce',
      this.allowProduction$,
      this.userDetails$,
      this.allowToViewProductionStatus$
    )
    this.checkMenuVisibility(
      'Processing',
      this.allowToViewJobStatusDashboard$,
      this.userDetails$,
      null
    )
    this.highlightReview2Menu()
  }

  onRouteNavigate(event: NavigationStart | NavigationEnd): void {
    if (event instanceof NavigationStart) {
      this.isReview2Nav = event.url.includes('review2')
    }
    if (event instanceof NavigationEnd) {
      this.highlightReview2Menu()
    }
  }

  subscribeToRouterEvents(): void {
    this.router.events
      .pipe(
        filter(
          (event) =>
            event instanceof NavigationStart || event instanceof NavigationEnd
        ),
        takeUntil(this.unsubscribe$)
      )
      .subscribe({
        next: (event: NavigationStart | NavigationEnd) => {
          this.onRouteNavigate(event)
        }
      })
  }

  public resetPassword(): void {
    this.handlePwChange()
  }

  public closePasswordWarning(): void {
    localStorage.setItem('hidePasswordExpiryWarning', 'true')
  }

  public get passwordExpiryMessage(): string {
    if (!this.notifyForPasswordChange) return ''
    if (this.passwordExpiryInDays < 0) return 'Your password has expired'
    if (this.passwordExpiryInDays === 0)
      return 'Your password is expiring today'
    const days =
      this.passwordExpiryInDays > 1
        ? `${this.passwordExpiryInDays} days`
        : `${this.passwordExpiryInDays} day`
    return `Your password will expire in ${days}`
  }

  public getFormattedLicenseExpiryDate(): string {
    if (!this.notifyForLicenseExpiry) return ''
    if (isNaN(this.licenseExpiryDate.getTime())) return '' //check for invalid date

    const day = this.licenseExpiryDate.getDate()

    const month = this.licenseExpiryDate.toLocaleString('default', {
      month: 'long'
    }) // Get full month name
    const year = this.licenseExpiryDate.getFullYear()

    // Function to determine the correct suffix for the day
    function getDaySuffix(day: number): string {
      if (day >= 11 && day <= 13) return 'th' // Special case for 11th, 12th, 13th
      switch (day % 10) {
        case 1:
          return 'st'
        case 2:
          return 'nd'
        case 3:
          return 'rd'
        default:
          return 'th'
      }
    }

    // Construct formatted date
    const formattedExpiryDate = `${day}${getDaySuffix(day)} ${month} ${year}`
    return formattedExpiryDate
  }

  public getLicenseRemainingDays(): number {
    if (!this.notifyForLicenseExpiry) return 0
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Calculate remaining days
    const timeDiff = this.licenseExpiryDate.getTime() - today.getTime()
    const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24)) + 1 // Convert milliseconds to days
    return daysLeft
  }

  public closeLicenseWarning(): void {
    localStorage.setItem('hideLicenseExpiryWarning', 'true')
  }

  private readonly handlePwChange = (): void => {
    const ref = this.dialog.open(ChangePasswordComponent, {
      disableClose: false,
      closeOnNavigation: true,
      width: 'auto',
      height: '480px',
      data: {
        isFBIEnabled: this.isFBIEnabled,
        notifyForPasswordChange: this.notifyForPasswordChange,
        fromNavbarLink: true
      }
    })

    ref
      .afterOpened()
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => {
        const iframeElement = document.getElementById('auth-app-loader')
        if (iframeElement) {
          iframeElement.classList.toggle('in-dialog')
        }
      })

    ref
      .afterClosed()
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((isSuccess) => {
        if (isSuccess) {
          this.notifyForPasswordChange = false
          localStorage.setItem('hidePasswordExpiryWarning', 'true')
        }
      })
  }
}
