import {
  AdminLevelModel,
  GroupRequestPayload,
  GroupResponseModel,
  SamlGridUiDataType,
  SamlGridUiTypes
} from '@admin-advance/models/saml-idp-server/saml-idp-server.model'
import { SamlIdpServerSettingsService } from '@admin-advance/services'
import {
  FetchSamlIdpGroupsAction,
  FetchSamlIdpServerSettingAction,
  FetchSamlIdpServerSettingParseFromXmlMetafileAction,
  initialSamlIdpServerState,
  SamlIdpServerSettingStateCleanupAction,
  samlIdpServerStateFeatureKey,
  SamlIdpServerStateModel,
  SamlServerStateKeys,
  StoreSamlIdpServerSettingAction
} from '@admin-advance/store'
import { getUiGridData } from '@admin-advance/store/saml-idp-server/saml-idp-server-state-testing-data'
import { SamlIdpServerStates } from '@admin-advance/store/saml-idp-server/saml-idp-server.states'
import { HttpErrorResponse } from '@angular/common/http'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { TestBed } from '@angular/core/testing'
import { ConfigService } from '@config/services/config.service'
import { provideMockStore } from '@ngrx/store/testing'
import { NgxsModule, Store } from '@ngxs/store'
import { ResponseModel } from '@shared/models'
import { of, throwError } from 'rxjs'

describe('SamlIdpServerStates', () => {
  let store: Store
  let samlIdpServerSettingsService: SamlIdpServerSettingsService

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      teardown: { destroyAfterEach: true },
      imports: [
        HttpClientTestingModule,
        NgxsModule.forRoot([SamlIdpServerStates])
      ],
      providers: [
        ConfigService,
        provideMockStore({}),
        SamlIdpServerSettingsService
      ]
    })
  })

  beforeEach(() => {
    store = TestBed.inject(Store)
    samlIdpServerSettingsService = TestBed.inject(SamlIdpServerSettingsService)
    store.reset({
      ...store.snapshot(),
      [samlIdpServerStateFeatureKey]: initialSamlIdpServerState
    })
  })
  const levelItem: AdminLevelModel = {
    groupsid: '1',
    venioadminlevel: 'test'
  }
  const samlIdpMockState = () => {
    return {
      [samlIdpServerStateFeatureKey]: {
        applySamlSettingResponse: {
          message: 'success'
        },
        storeSamlIdpServerSetting: {
          formData: {},
          venioAdminLevels: [{ ...levelItem }],
          venioUsers: [{ ...levelItem }],
          venioApplicationAccess: [{ ...levelItem }],
          venioUserGroups: [{ ...levelItem }]
        },
        samlIdpGroupsResponse: {
          data: [{ ...levelItem }]
        },
        fetchSamlSettingResponse: {
          data: [{ ...levelItem }]
        },
        fetchSamlSettingParseXmlMetafileResponse: {
          data: [{ ...levelItem }]
        },
        samlIdpUsersResponse: {}
      } as SamlIdpServerStateModel
    }
  }

  const commonTestSetupForLoadingIdpSettings = () => {
    const payload = { test: 'data' }
    const successResponseModel: ResponseModel = { data: [{ ...levelItem }] }
    const errorResponseModel: ResponseModel = { message: 'fail' }
    const errorResponse = { error: errorResponseModel } as HttpErrorResponse
    return {
      payload,
      successResponseModel,
      errorResponse
    }
  }

  const getNormalizedStateFromStoreAfterChange = (
    dataStoreKey: SamlServerStateKeys,
    type: SamlGridUiTypes
  ) => {
    const getStoredStateData =
      store.snapshot()[samlIdpServerStateFeatureKey][dataStoreKey]
    // values are  store in this format
    // `venioUserGroups: { '0': { groupsid: '1', venioadminlevel: 'test' } }`
    return [getStoredStateData[type]['0']]
  }

  it.each(getUiGridData())(
    `should store '$type' SamlI-dp Server Settings when admin level data grid changes`,
    ({ type, samlIdpSettings }) => {
      // GIVEN groups grid data
      const dataStoreKey: SamlServerStateKeys = 'storeSamlIdpServerSetting'
      const samlGridDataPayload: Partial<SamlGridUiDataType> = {
        type,
        samlIdpSettings
      }

      // WHEN groups grid data changes
      store.dispatch(new StoreSamlIdpServerSettingAction(samlGridDataPayload))

      // THEN store admin level grid data
      const loaded = getNormalizedStateFromStoreAfterChange(dataStoreKey, type)
      expect(loaded.length).toBeGreaterThanOrEqual(1)
      expect(loaded).toEqual(samlIdpSettings)
    }
  )

  it(`should fetch SAML-Idp server data from the API and patch state of success response object`, () => {
    // GIVEN request payload
    const { successResponseModel } = commonTestSetupForLoadingIdpSettings()
    const dataKey: SamlServerStateKeys = 'fetchSamlSettingResponse'
    const fetchSamlIdpServerSettingsSpy = jest
      .spyOn(samlIdpServerSettingsService, 'fetchSamlIdpServerSettings')
      .mockReturnValue(of(successResponseModel))

    // WHEN HTTP get request with payload
    store.dispatch(new FetchSamlIdpServerSettingAction())

    // THEN should return SAML data in response object
    const response: ResponseModel =
      store.snapshot()[samlIdpServerStateFeatureKey][dataKey]

    expect(fetchSamlIdpServerSettingsSpy).toHaveBeenCalledTimes(1)
    expect(response.data).toBeDefined()
    expect(response.data).toStrictEqual(successResponseModel.data)
  })

  it(`should fetch SAML-Idp server data from the API and patch state of error response object`, () => {
    // GIVEN request payload
    const { errorResponse } = commonTestSetupForLoadingIdpSettings()
    const dataKey: SamlServerStateKeys = 'fetchSamlSettingResponse'
    const fetchSamlIdpServerSettingsSpy = jest
      .spyOn(samlIdpServerSettingsService, 'fetchSamlIdpServerSettings')
      .mockReturnValue(throwError(errorResponse))

    // WHEN HTTP get request with payload
    store.dispatch(new FetchSamlIdpServerSettingAction())

    // THEN should return error response object
    const response: ResponseModel =
      store.snapshot()[samlIdpServerStateFeatureKey][dataKey]
    expect(fetchSamlIdpServerSettingsSpy).toHaveBeenCalledTimes(1)
    expect(response).toStrictEqual(errorResponse.error)
  })

  it(`should reset SAML-Idp store to initial state`, () => {
    // GIVEN store states
    store.reset({ ...samlIdpMockState() })
    const stateKeys: SamlServerStateKeys[] = [
      'storeSamlIdpServerSetting',
      'fetchSamlSettingResponse',
      'applySamlSettingResponse',
      'fetchSamlSettingParseXmlMetafileResponse',
      'samlIdpGroupsResponse',
      'samlIdpUsersResponse',
      'samlIdPGroupMappingSettingsStatusResponse'
    ]

    // WHEN reset action is dispatched with state keys
    store.dispatch(new SamlIdpServerSettingStateCleanupAction(stateKeys))

    // THEN should reset store to its initial state
    expect(store.snapshot()[samlIdpServerStateFeatureKey]).toStrictEqual(
      initialSamlIdpServerState
    )
  })

  it(`should fetch SAML-Idp groups from the API when load groups with token and group type was called`, () => {
    // GIVEN SAML token and group type
    const loadGroupPayload: GroupRequestPayload = {
      groupType: 'OKTA_GROUP',
      link: 'http://load-things.com',
      token: 'iamatokenandthanks',
      applicationClientId: '',
      applicationObjectId: '',
      clientId: null,
      providerName: null,
      tenantId: '',
      fetchGroupsFromDatabase: false
    }
    const responseGroupData: GroupResponseModel[] = [
      {
        profile: {
          name: 'text'
        },
        id: '1',
        type: 'OKTA_GROUP'
      }
    ]
    const idpGroupResponse: ResponseModel = {
      data: responseGroupData
    }
    const fetchIdpGroupsSpy = jest
      .spyOn(samlIdpServerSettingsService, 'fetchIdpGroups')
      .mockReturnValue(of(idpGroupResponse))

    // WHEN fetch group is called
    store.dispatch(new FetchSamlIdpGroupsAction(loadGroupPayload))

    // THEN should load SAML groups
    expect(fetchIdpGroupsSpy).toHaveBeenCalledTimes(1)
  })

  it(`should fetch parse SAML-Idp settings from metadata XML file  when metadata XML file is uploaded`, () => {
    // GIVEN valid XML metadata file
    const { successResponseModel } = commonTestSetupForLoadingIdpSettings()
    const xmlMetadataFile = 'xml string file'
    const dataKey: SamlServerStateKeys =
      'fetchSamlSettingParseXmlMetafileResponse'
    const fetchParseFromXmlMetafileSpy = jest
      .spyOn(samlIdpServerSettingsService, 'fetchParseFromXmlMetafile')
      .mockReturnValue(of(successResponseModel))

    // WHEN uploaded
    store.dispatch(
      new FetchSamlIdpServerSettingParseFromXmlMetafileAction(xmlMetadataFile)
    )

    // THEN should parse and return setting information
    const response: ResponseModel =
      store.snapshot()[samlIdpServerStateFeatureKey][dataKey]
    expect(fetchParseFromXmlMetafileSpy).toHaveBeenCalledTimes(1)
    expect(response.data).toBeDefined()
    expect(response.data).toStrictEqual(successResponseModel.data)
  })

  it(`should patch parsing error response object of metadata XML file when invalid metadata XML file is uploaded`, () => {
    // GIVEN invalid XML metadata file
    const { errorResponse } = commonTestSetupForLoadingIdpSettings()
    const xmlMetadataFile = 'invalid file'
    const dataKey: SamlServerStateKeys =
      'fetchSamlSettingParseXmlMetafileResponse'
    const fetchParseFromXmlMetafileSpy = jest
      .spyOn(samlIdpServerSettingsService, 'fetchParseFromXmlMetafile')
      .mockReturnValue(throwError(errorResponse))

    // WHEN uploaded
    store.dispatch(
      new FetchSamlIdpServerSettingParseFromXmlMetafileAction(xmlMetadataFile)
    )

    // THEN should patch parsing error response object
    const response: ResponseModel =
      store.snapshot()[samlIdpServerStateFeatureKey][dataKey]
    expect(fetchParseFromXmlMetafileSpy).toHaveBeenCalledTimes(1)
    expect(response.message).toBeDefined()
    expect(response).toStrictEqual(errorResponse.error)
  })
})
