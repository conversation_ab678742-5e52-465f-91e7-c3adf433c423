import { DragDropModule } from '@angular/cdk/drag-drop'
import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatCardModule } from '@angular/material/card'
import { MatDialogModule } from '@angular/material/dialog'
import { MatDividerModule } from '@angular/material/divider'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatIconModule } from '@angular/material/icon'
import { MatProgressBarModule } from '@angular/material/progress-bar'
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'
import { MatRadioModule } from '@angular/material/radio'
import { MatSelectModule } from '@angular/material/select'
import { MatTooltipModule } from '@angular/material/tooltip'
import { RouterModule } from '@angular/router'
import { ContentPlaceholderModule } from '@shared/placeholder/content-placeholder.module'
import { RouteBreadcrumbModule } from '@shared/route-breadcrumb/route-breadcrumb.module'
import { DxCheckBoxModule, DxDataGridModule } from 'devextreme-angular'
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search'
import { SlipSheetTemplateManageComponent } from './slipsheet-template-manage.component'

@NgModule({
  declarations: [SlipSheetTemplateManageComponent],
  imports: [
    CommonModule,
    RouterModule.forChild([
      { path: '', component: SlipSheetTemplateManageComponent }
    ]),
    FormsModule,
    ReactiveFormsModule,
    MatSelectModule,
    DxDataGridModule,
    MatTooltipModule,
    MatExpansionModule,
    MatDialogModule,
    DragDropModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatCardModule,
    RouteBreadcrumbModule,
    ContentPlaceholderModule,
    MatDividerModule,
    NgxMatSelectSearchModule,
    MatIconModule,
    MatRadioModule,
    DxCheckBoxModule
  ]
})
export class SlipSheetTemplateManageModule {}
