<ng-container [formGroup]="samlIdpForm">
  <div class="col-md-6">
    <div class="row mb-2">
      <label class="col-md-4">IDP Metadata file</label>
      <div class="col-md-8">
        <div class="input-group">
          <div class="custom-file">
            <input
              #file
              accept="text/xml"
              [disabled]="isXmlMetafileParsing"
              (change)="onXmlMetafileChange($event)"
              type="file"
              class="custom-file-input"
            />
            <label class="custom-file-label">
              {{ file.files[0]?.name || 'Choose file' }}
            </label>
          </div>
          <div
            class="input-group-append input-group-text border-0"
            *ngIf="isXmlMetafileParsing"
          >
            <mat-spinner color="primary" diameter="24"></mat-spinner>
          </div>
        </div>
      </div>
    </div>
    <div class="row mb-2">
      <label class="col-md-4">SSO url provided by IDP</label>
      <div class="col-md-8">
        <input formControlName="idpssoUrl" type="text" class="form-control" />
      </div>
    </div>
    <div class="row mb-2">
      <label class="col-md-4">IdP Issuer</label>
      <div class="col-md-8">
        <input formControlName="idpIssuer" type="text" class="form-control" />
      </div>
    </div>
    <div class="row mb-2">
      <label class="col-md-4">IdP API Url</label>
      <div class="col-md-8">
        <input formControlName="groupAPIURL" type="text" class="form-control" />
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="border">
      <p class="text-primary title">Load Groups</p>
      <div class="p-10">
        <!--        <mat-radio-group-->
        <!--          formControlName="loadGroupType"-->
        <!--          aria-label="Select an option"-->
        <!--        >-->
        <!--          <mat-radio-button [value]="loadGroupTypes.TOKEN" class="mb-2">-->
        <div class="d-flex mb-2">
          <label class="form-label col-md-4">IdP Token/Secret Value</label>
          <input
            formControlName="token"
            type="password"
            class="form-control sm-form-control col-md-8"
          />
        </div>
        <ng-container *ngIf="selectedProvider === 'OKTA'">
          <div class="mt-2 d-flex">
            <label class="form-label col-md-4">Group Type</label>
            <mat-select
              formControlName="groupType"
              class="form-control sm-form-control col-md-8"
            >
              <mat-option value="OKTA_GROUP">OKTA_GROUP</mat-option>
              <mat-option value="APP_GROUP">APP_GROUP</mat-option>
              <mat-option value="BUILT_IN">BUILT_IN</mat-option>
            </mat-select>
          </div>
        </ng-container>
        <!-- Show Azure AD fields if provider is AZURE_AD -->
        <ng-container *ngIf="selectedProvider === 'Azure_AD'">
          <div class="d-flex mb-2">
            <label class="form-label col-md-4">Tenant ID</label>
            <input
              formControlName="tenantId"
              type="text"
              class="form-control sm-form-control col-md-8"
            />
          </div>
          <div class="d-flex mb-2">
            <label class="form-label col-md-4">Client ID</label>
            <input
              formControlName="applicationClientId"
              type="text"
              class="form-control sm-form-control col-md-8"
            />
          </div>
          <div class="d-flex mb-2">
            <label class="form-label col-md-4">Application Object ID</label>
            <input
              formControlName="applicationObjectId"
              type="text"
              class="form-control sm-form-control col-md-8"
            />
          </div>
        </ng-container>
        <!--          </mat-radio-button>-->
        <!--          <mat-radio-button [value]="loadGroupTypes.FILE"-->
        <!--          >Select File having group names (one group name per line)-->
        <!--            <div class="mt-2 d-flex">-->
        <!--              <label class="form-label">File Location</label>-->
        <!--              <div class="custom-file sm-form-control">-->
        <!--                <input-->
        <!--                  formControlName="groupFile"-->
        <!--                  type="file"-->
        <!--                  class="custom-file-input"-->
        <!--                />-->
        <!--                <label class="custom-file-label">Choose file</label>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </mat-radio-button>-->
        <!--          <mat-radio-button [value]="loadGroupTypes.PROFILE"-->
        <!--          >Load groups from user profile-->
        <!--          </mat-radio-button>-->
        <!--        </mat-radio-group>-->
        <app-ui-button
          class="d-flex w-100 justify-content-end mt-2"
          (clicked)="loadSamlGroups()"
          [disabled]="
            isLoading || samlIdpForm?.disabled || samlIdpForm?.invalid
          "
          [buttonLabel]="loadButtonLabel"
          [showSpinner]="isLoading"
        ></app-ui-button>
      </div>
    </div>
  </div>
</ng-container>
<ng-template #confirmUpdate>
  <div
    class=""
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div class="modal-header d-flex col-12" cdkDragHandle>
      <h4 class="modal-title pull-left">Confirm!</h4>
      <button
        type="button"
        class="close pull-right close-confirm"
        [mat-dialog-close]="false"
      >
        <span aria-hidden="true" class="fa fa-times"></span>
      </button>
    </div>
    <div class="modal-body h5 text-center mb-0">
      {{ confirmationMessage }}
    </div>
    <div class="modal-footer text-right">
      <button
        type="button"
        class="btn btn-primary float-right close-confirm"
        [mat-dialog-close]="true"
      >
        YES
      </button>
      <button
        [mat-dialog-close]="false"
        type="button"
        class="btn btn-grey close-confirm"
      >
        NO
      </button>
    </div>
  </div>
</ng-template>
