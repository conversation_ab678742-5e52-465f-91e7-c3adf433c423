import { Injectable } from '@angular/core'
import { ProjectInfo } from '@config/models'
import { Actions, Effect, ofType } from '@ngrx/effects'
import { Action, Store as RxStore } from '@ngrx/store'
import { Store } from '@ngxs/store'
import { StringUtils } from '@shared/utils/string-utils'
import { ClearIndexedDb } from '@shared/xsStore'
import cssVars from 'css-vars-ponyfill'
import { JsonConvert } from 'json2typescript'
import { isEmpty } from 'lodash'
import { EMPTY, from, Observable } from 'rxjs'
import { catchError, mergeMap, switchMap, take } from 'rxjs/operators'
import { ServerSessionService } from '../../../../services/server-session.service'
import {
  GlobalErrorAction,
  GlobalSuccessAction
} from '../../../../store/actions'
import * as fromCaseActions from '../../../../stores/actions/case.actions'
import { ConfigService } from '../../services/config.service'
import {
  ConfigActionTypes,
  FetchBaseSettings,
  FetchControlSettings,
  FetchHelpLinks,
  FetchLicenseValidity,
  FetchProjectInfo,
  FetchTheme,
  SetBaseSettings,
  SetControlSettings,
  SetCssThemeVariables,
  SetHelpLinks,
  SetLicenseValidity,
  SetProjectInfo,
  SetServerSideSession
} from '../actions'
import { getControlSettings } from '../selectors'

/**
 * Effects class for handling general Config actions.
 */
@Injectable()
export class ConfigEffects {
  private jsonConvert: JsonConvert

  constructor(
    private store: Store,
    private rxStore: RxStore,
    private actions$: Actions,
    private service: ConfigService,
    private sessionService: ServerSessionService
  ) {
    this.jsonConvert = new JsonConvert()
  }

  /**
   * Fetches base settings from the store.
   */
  @Effect()
  fetchBaseSettings: Observable<Action> = this.actions$.pipe(
    ofType<FetchBaseSettings>(ConfigActionTypes.FetchBaseSettings),
    switchMap(() => {
      return this.service.fetchBaseSettings$().pipe(
        mergeMap((res: unknown) => {
          // These are extremely basic settings which are widely used throughout the application
          // Setting them to service for the quick access
          this.service.favIconPath = res['faviconPath']

          // set login background path
          this.service.loginImageBackgroundPath =
            res['loginImageBackgroundPath']

          Object.keys(res).forEach((key) => {
            this.service[key] = res[key]
          })

          return [
            new SetBaseSettings(res), // Set base settings in the store
            new GlobalSuccessAction(
              'Base settings fetched successfully.',
              res,
              false,
              false
            ),
            new FetchTheme(this.service.themeFilePath) // Fetch theme variables
          ]
        }),
        catchError((err) =>
          from([
            /** in case of error, don't block the whole app as `APP_INITIALIZER`
             *  being used in `ConfigModule` module which will never be completed.
             *  @see ConfigModule
             *  @see loadBaseSettingsFactory
             *  @see resolveBaseSettings
             */
            new SetBaseSettings({ apiUrl: 'error' }),
            new GlobalErrorAction(err, false, true)
          ])
        )
      )
    })
  )

  /**
   * Fetches control settings from the store.
   */
  @Effect()
  fetchControlSettings$: Observable<Action> = this.actions$.pipe(
    ofType<FetchControlSettings>(ConfigActionTypes.FetchControlSettings),
    switchMap(() => this.rxStore.select(getControlSettings)),
    switchMap((setting) => {
      if (isEmpty(setting))
        return this.service.fetchControlSettings$().pipe(
          mergeMap((res: any) => {
            return [
              new GlobalSuccessAction(res.message, res.data, false, false),
              new SetControlSettings(res.data)
            ]
          }),
          catchError((err) => from([new GlobalErrorAction(err, true, true)]))
        )
      else return EMPTY
    })
  )

  /**
   * Fetch user guide help links
   */
  @Effect()
  fetchHelpLinks$: Observable<Action> = this.actions$.pipe(
    ofType<FetchHelpLinks>(ConfigActionTypes.FetchHelpLinks),
    switchMap(() => {
      return this.service.fetchHelpLinks$().pipe(
        mergeMap((res: any) => {
          return [new SetHelpLinks(res.data)]
        }),
        catchError((err) => from([new GlobalErrorAction(err, true, true)]))
      )
    })
  )

  @Effect()
  fetchLicenseValidity$: Observable<Action> = this.actions$.pipe(
    ofType<FetchLicenseValidity>(ConfigActionTypes.FetchLicenseValidity),
    switchMap(() => {
      return this.service.fetchLicenseValidity$().pipe(
        mergeMap((res: any) => {
          return [new SetLicenseValidity(res.data)]
        }),
        catchError((err) => from([new GlobalErrorAction(err, true, true)]))
      )
    })
  )

  /**
   * Fetches theme variables as soon as control settings is set in the store.
   * If there is anything that needs to be done after fetching and setting control settings in the store, this is the place.
   */
  @Effect()
  setControlSettings: Observable<Action> = this.actions$.pipe(
    ofType<SetControlSettings>(ConfigActionTypes.SetControlSettings),
    switchMap((action) => {
      if (action.controlSettings) {
        // Fetching theme file path should be spontaneous,
        // that is why we get the THEME_FILE_PATH setting value from the control setting soon after it is set in the store,
        // and then fetch the theme variables.
        const themeFilePath = action.controlSettings['THEME_FILE_PATH']
        if (!StringUtils.isNullOrEmpty(themeFilePath)) {
          return [new FetchTheme(themeFilePath)] // Return the action that fetches theme variable
        }
      }
      return []
    })
  )

  /**
   * Fetches theme variables.
   */
  @Effect()
  fetchTheme$ = this.actions$.pipe(
    ofType<FetchTheme>(ConfigActionTypes.FetchTheme),
    switchMap((action) => {
      return this.service.fetchTheme$(action.path).pipe(
        mergeMap((res: any) => {
          if (res) {
            // Use the css-vars-ponyfill library to set the CSS custom properties (aka "CSS variables")
            // The library provides the support in legacy and modern browsers
            cssVars({
              onlyLegacy: false,
              variables: res,
              watch: true,
              onComplete: (a, b, variables) => {}
            })

            // Set the theme client tag for static access.
            if (res && res['--client']) {
              ConfigService.themeClient = res['--client']
            }

            return [
              new GlobalSuccessAction(
                'Theme variables fetched successfully.',
                res,
                false,
                false
              ),
              new SetCssThemeVariables(res) // Set CSS variables in the store
            ]
          }
        }),
        catchError((err) => from([new GlobalErrorAction(err, true, true)]))
      )
    })
  )

  /**
   * Fetches project info from the server.
   */
  @Effect()
  fetchProjectInfo$ = this.actions$.pipe(
    ofType<FetchProjectInfo>(ConfigActionTypes.FetchProjectInfo),
    switchMap((action) => {
      return this.service.fetchProjectInfo$(action.projectId).pipe(
        mergeMap((res: any) => {
          if (res.status === 'Success') {
            try {
              const projectInfo = this.jsonConvert.deserializeObject(
                res.data,
                ProjectInfo
              )

              //Clear indexed db for html parts when project is loaded.
              this.store.dispatch(new ClearIndexedDb('HtmlParts'))
              // Fetch media status information for the project (media list, processed media list, processed status)
              this.store
                .dispatch(
                  new fromCaseActions.FetchMediaStatus(action.projectId)
                )
                .pipe(take(1))
                .subscribe(() => {
                  this.sessionService.maintainSession()
                })

              return [
                new GlobalSuccessAction(res.message, projectInfo, false, false),
                new SetProjectInfo(projectInfo),

                // TODO This will not be necessary in the future
                // To maintain the session in the old VOD, as some part of the application still relies on it
                // The other place this is being used is while refreshing token
                new SetServerSideSession(
                  new Map<string, any>().set('projectid', projectInfo.projectId)
                )
              ]
            } catch (e) {
              return [
                new GlobalErrorAction(
                  new Error('Error de-serialising project info.'),
                  true,
                  true
                )
              ]
            }
          } else {
            return [
              new GlobalErrorAction(
                new Error('Error getting project info: ' + res),
                true,
                true
              )
            ]
          }
        }),
        catchError((err) => from([new GlobalErrorAction(err, true, true)]))
      )
    })
  )

  /**
   * Sets the server side session values.
   * This effect is only required as long as we rely on old VOD and a few ASP.NET server side scripts.
   */
  @Effect()
  setServerSideSession: Observable<Action> = this.actions$.pipe(
    ofType<SetServerSideSession>(ConfigActionTypes.SetServerSideSession),
    switchMap((action) => {
      return this.service.setServerSession$(action.session).pipe(
        mergeMap((res: any) => {
          return [
            new GlobalSuccessAction(
              'Server side session set successfully.',
              res,
              false,
              false
            )
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, true)]))
      )
    })
  )
}
