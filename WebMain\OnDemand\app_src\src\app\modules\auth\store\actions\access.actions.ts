import { HttpErrorResponse } from '@angular/common/http'
import { Action } from '@ngrx/store'
import { MaintenanceInfoModel } from '@shared/models'
import { TwoFactorAuthResponseModel } from '../../models/2fa-response.model'
import {
  ExtUserAuthResponseModel,
  HoldUserAuthResponseModel
} from '../../models/external-user-authentication.model'
import { Login } from '../../models/login.model'
import { ResetPasswordModel } from '../../models/reset-password.model'
import { ResponseModel } from '../../models/response.model'
import { Client, User, UserModel } from '../../models/user.model'

export enum AccessActionTypes {
  FetchAuthToken = '[Token] Fetch Authentication Token',
  FetchAuthTokenSuccess = '[Token] Fetch Authentication Token Success',
  AuthError = '[Token] Authentication Error',
  FetchUserDetails = '[User] Fetch User Details',
  FetchUserDetailsSuccess = '[User] Fetch User Details Success',
  FetchUser = '[User] Fetch User',
  FetchUserError = '[User] Fetch User Error',
  SetUser = '[User] Set User',
  SendTwoFactorAuthNotification = '[2FA] Send Two Factor Authentication Notification',
  SendTwoFactorAuthNotificationSuccess = '[2FA] Send Two Factor Authentication Notification Success',
  VerifyAuthenticationCode = '[2FA] Verify Authentication Code',
  VerifyAuthenticationCodeSuccess = '[2FA] Verify Authentication Code Success',
  TwoFactorAuthError = '[2FA] Two Factor Authentication Error',
  SendExtUserAuthNotification = '[External User] Send External User Notification',
  SendExtUserAuthNotificationSuccess = '[External User] Send External User Notification Success',
  VerifyExtUserAuthCode = '[External User] Verify External User Code',
  VerifyExtUserAuthCodeSuccess = '[External User] Verify External User Code Success',
  FetchExtUserAuthToken = '[Token] Fetch External User Authentication Token',
  ExtUserAuthError = '[External User] External User Error',
  FetchExtUserDetails = '[User] Fetch External User Details',
  FetchExtUser = '[User] Fetch External User',
  SetServerSession = '[Session] Set Server Side Session',
  SendResetPasswordLink = '[Reset Password] Sending Password Reset Link',
  SendResetPasswordLinkSuccessful = '[Reset Password] Sending Password Reset Link Successful',
  SendResetPasswordLinkError = '[Change Password] Sending Password Reset Link Failed',
  ChangePassword = '[Change Password] Change Password',
  ClearStoreProperty = '[Auth Store] Clear Store Property Value',
  ClearAuthStore = '[Auth Store] Clear Whole Store',
  SetIsAddOnBoxChecked = '[Addon] Set Install Addon Box Checked',
  ShowLoginSpinner = '[Login] Show Spinner',
  HideLoginSpinner = '[Login] Hide Spinner',
  LogOut = '[Log Out] Log Out',
  PasswordComplexity = '[Password Complexity] Display Required Complexity Options',
  FetchClientInfo = '[Client] Fetch Client Info',
  SetClientInfo = '[Client] Set Client Info',
  FetchMaintenanceSetting = '[Maintenance] FetchSystem Maintenance Info',
  SetMaintenanceSetting = '[Maintenance] Set System Maintenance Info',
  SendHoldUserAuthNotification = '[Legal Hold] Send Legal Hold Notification',
  SendHoldUserAuthNotificationSuccess = '[Legal Hold] Send Legal Hold Notification Success',
  VerifyHoldUserAuthCode = '[Legal Hold] Verify Legal Hold Notification',
  VerifyHoldUserAuthCodeSuccess = '[Legal Hold] Verify Legal Hold Notification Success',
  ChangePasswordSuccess = '[Change Password ] Change Password Success',
  EmailUpdate = 'EmailUpdate'
}

export class FetchAuthToken implements Action {
  readonly type = AccessActionTypes.FetchAuthToken

  constructor(readonly login: Login) {}
}

export class FetchAuthTokenSuccess implements Action {
  readonly type = AccessActionTypes.FetchAuthTokenSuccess

  constructor(readonly tokenResponse: ResponseModel) {}
}

export class AuthError implements Action {
  readonly type = AccessActionTypes.AuthError

  constructor(readonly error: HttpErrorResponse) {}
}

export class FetchUserDetails implements Action {
  readonly type = AccessActionTypes.FetchUserDetails

  constructor(readonly password: string) {}
}

export class FetchUserDetailsSuccess implements Action {
  readonly type = AccessActionTypes.FetchUserDetailsSuccess

  constructor(readonly userDetails: UserModel) {}
}

export class FetchUser implements Action {
  readonly type = AccessActionTypes.FetchUser

  constructor() {}
}

export class FetchUserError implements Action {
  readonly type = AccessActionTypes.FetchUserError

  constructor(readonly error: string) {}
}

export class SetUser implements Action {
  readonly type = AccessActionTypes.SetUser

  constructor(readonly user: User) {}
}

export class SendTwoFactorAuthenticationNotification implements Action {
  readonly type = AccessActionTypes.SendTwoFactorAuthNotification

  constructor(readonly UserId: number, readonly resend: boolean) {}
}

export class SendTwoFactorAuthenticationNotificationSuccess implements Action {
  readonly type = AccessActionTypes.SendTwoFactorAuthNotificationSuccess

  constructor(readonly isNotificationSent: boolean) {}
}

export class VerifyAuthenticationCode implements Action {
  readonly type = AccessActionTypes.VerifyAuthenticationCode

  constructor(
    readonly UserId: number,
    readonly verificationCode,
    readonly rememberUser
  ) {}
}

export class VerifyAuthenticationCodeSuccess implements Action {
  readonly type = AccessActionTypes.VerifyAuthenticationCodeSuccess

  constructor(readonly twoFactorResponse: TwoFactorAuthResponseModel) {}
}

export class TwoFactorAuthenticationError implements Action {
  readonly type = AccessActionTypes.TwoFactorAuthError

  constructor(readonly error: HttpErrorResponse) {}
}

export class SendExtUserAuthNotification implements Action {
  readonly type = AccessActionTypes.SendExtUserAuthNotification

  constructor(readonly UserId: number, readonly resend: boolean) {}
}

export class SendExtUserAuthNotificationSuccess implements Action {
  readonly type = AccessActionTypes.SendExtUserAuthNotificationSuccess

  constructor(readonly notificationSentResponse: ExtUserAuthResponseModel) {}
}

export class VerifyExtUserAuthCode implements Action {
  readonly type = AccessActionTypes.VerifyExtUserAuthCode

  constructor(
    readonly UserId: number,
    readonly verificationCode: string,
    readonly userToken: string,
    readonly tokenType: string,
    readonly projectId: number
  ) {}
}

export class VerifyExtUserAuthCodeSuccess implements Action {
  readonly type = AccessActionTypes.VerifyExtUserAuthCodeSuccess

  constructor(readonly extUserResponse: ExtUserAuthResponseModel) {}
}

export class FetchExtUserAuthToken implements Action {
  readonly type = AccessActionTypes.FetchExtUserAuthToken

  constructor(
    readonly login: Login,
    readonly token: string,
    readonly projectId: number
  ) {}
}

export class ExtUserAuthError implements Action {
  readonly type = AccessActionTypes.ExtUserAuthError

  constructor(readonly error: HttpErrorResponse) {}
}

export class FetchExtUserDetails implements Action {
  readonly type = AccessActionTypes.FetchExtUserDetails

  constructor(readonly token: string, readonly projectId: number) {}
}

export class FetchExtUser implements Action {
  readonly type = AccessActionTypes.FetchExtUser

  constructor(readonly userId: number) {}
}

export class SetServerSideSession implements Action {
  readonly type = AccessActionTypes.SetServerSession

  constructor(
    readonly sessions: any,
    readonly isAddonChkBoxChecked: boolean,
    readonly urlPath: string,
    readonly isInAppNav: boolean,
    readonly shouldRedirect?: boolean
  ) {}
}

export class SendResetPasswordLink implements Action {
  readonly type = AccessActionTypes.SendResetPasswordLink

  constructor(readonly userName: string) {}
}

export class SendResetPasswordLinkSuccessful implements Action {
  readonly type = AccessActionTypes.SendResetPasswordLinkSuccessful

  constructor(readonly response: any) {}
}

export class SendResetPasswordLinkError implements Action {
  readonly type = AccessActionTypes.SendResetPasswordLinkError

  constructor(readonly error: HttpErrorResponse) {}
}

export class ChangePassword implements Action {
  readonly type = AccessActionTypes.ChangePassword

  constructor(
    readonly passwordModel: ResetPasswordModel,
    readonly token?: string
  ) {}
}

export class ClearStoreProperty implements Action {
  readonly type = AccessActionTypes.ClearStoreProperty

  constructor(readonly propertyName: string) {}
}

export class ClearAuthStore implements Action {
  readonly type = AccessActionTypes.ClearAuthStore
}

export class SetIsAddOnBoxChecked implements Action {
  readonly type = AccessActionTypes.SetIsAddOnBoxChecked

  constructor(readonly isChecked: boolean) {}
}

export class ShowLoginSpinner implements Action {
  readonly type = AccessActionTypes.ShowLoginSpinner
}

export class HideLoginSpinner implements Action {
  readonly type = AccessActionTypes.HideLoginSpinner
}

export class LogOut implements Action {
  readonly type = AccessActionTypes.LogOut
}

export class PasswordComplexity implements Action {
  readonly type = AccessActionTypes.PasswordComplexity

  constructor(readonly options: HttpErrorResponse) {}
}

export class FetchClientInfo implements Action {
  readonly type = AccessActionTypes.FetchClientInfo

  constructor() {}
}

export class SetClientInfo implements Action {
  readonly type = AccessActionTypes.SetClientInfo

  constructor(readonly client: Client) {}
}

export class FetchMaintenanceSetting implements Action {
  readonly type = AccessActionTypes.FetchMaintenanceSetting
}

export class SetMaintenanceSetting implements Action {
  readonly type = AccessActionTypes.SetMaintenanceSetting

  constructor(readonly maintenanceSetting: MaintenanceInfoModel) {}
}

export class SendHoldUserAuthNotification implements Action {
  readonly type = AccessActionTypes.SendHoldUserAuthNotification

  constructor(readonly custId: number, readonly resend: boolean) {}
}

export class SendHoldUserAuthNotificationSuccess implements Action {
  readonly type = AccessActionTypes.SendHoldUserAuthNotificationSuccess

  constructor(
    readonly holdnotificationSentResponse: HoldUserAuthResponseModel
  ) {}
}

export class VerifyHoldUserAuthCode implements Action {
  readonly type = AccessActionTypes.VerifyHoldUserAuthCode

  constructor(
    readonly custId: number,
    readonly verificationCode: string,
    readonly userToken: string,
    readonly tokenType: string
  ) {}
}

export class VerifyHoldUserAuthCodeSuccess implements Action {
  readonly type = AccessActionTypes.VerifyHoldUserAuthCodeSuccess

  constructor(readonly holdCustResponse: HoldUserAuthResponseModel) {}
}

export class ChangePasswordSuccess implements Action {
  readonly type = AccessActionTypes.ChangePasswordSuccess

  constructor(readonly res) {}
}

export class EmailUpdate implements Action {
  readonly type = AccessActionTypes.EmailUpdate

  constructor(readonly userId: number, readonly emailAddress?: string) {}
}

export type AccessActions =
  | FetchAuthToken
  | FetchAuthTokenSuccess
  | FetchUserDetails
  | FetchUserDetailsSuccess
  | FetchUser
  | SetUser
  | FetchUserError
  | SendTwoFactorAuthenticationNotification
  | SendTwoFactorAuthenticationNotificationSuccess
  | VerifyAuthenticationCode
  | VerifyAuthenticationCodeSuccess
  | TwoFactorAuthenticationError
  | SendExtUserAuthNotification
  | SendExtUserAuthNotificationSuccess
  | VerifyExtUserAuthCode
  | VerifyExtUserAuthCodeSuccess
  | FetchExtUserAuthToken
  | ExtUserAuthError
  | FetchExtUser
  | FetchExtUserDetails
  | SendResetPasswordLink
  | SendResetPasswordLinkSuccessful
  | SendResetPasswordLinkError
  | ChangePassword
  | SetServerSideSession
  | AuthError
  | ClearStoreProperty
  | ClearAuthStore
  | SetIsAddOnBoxChecked
  | LogOut
  | ShowLoginSpinner
  | HideLoginSpinner
  | PasswordComplexity
  | FetchClientInfo
  | SetClientInfo
  | FetchMaintenanceSetting
  | SetMaintenanceSetting
  | SendHoldUserAuthNotification
  | SendHoldUserAuthNotificationSuccess
  | VerifyHoldUserAuthCode
  | VerifyHoldUserAuthCodeSuccess
  | ChangePasswordSuccess
  | EmailUpdate
