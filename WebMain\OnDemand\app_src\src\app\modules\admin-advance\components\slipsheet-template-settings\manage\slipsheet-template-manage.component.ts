import { ActionTypes, SlipsheetTemplateModel } from '@admin-advance/models'
import {
  DeleteSlipsheetTemplateAction,
  GetAllSlipSheetTemplateSettingsAction,
  SlipsheetTemplateSettingsStateSelector
} from '@admin-advance/store'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ConfigService } from '@config/services/config.service'
import { Navigate } from '@ngxs/router-plugin'
import { Store } from '@ngxs/store'
import { animateHeight, fadeInX } from '@shared/animation'
import { DxDataGridComponent } from 'devextreme-angular'
import { ToastrService } from 'ngx-toastr'
import { Subject, timer } from 'rxjs'
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  switchMap,
  takeUntil,
  tap
} from 'rxjs/operators'

@Component({
  selector: 'app-slipsheet-template-manage',
  templateUrl: './slipsheet-template-manage.component.html',
  styleUrls: ['./slipsheet-template-manage.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [fadeInX, animateHeight]
})
export class SlipSheetTemplateManageComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  /**
   * Cleanup placeholder for the observers when the component get destroyed.
   */
  private readonly toDestroy$ = new Subject<void>()

  /**
   * Static service of app config.
   */
  config = ConfigService

  /**
   * Enable content placeholder to indicate we're working.
   */
  isWorking = true

  /**
   * Types of action
   */
  actionTypes = ActionTypes

  selectedKeys: number[]

  selectedSlipSheetIds: number[] = []

  fieldData: SlipsheetTemplateModel[] = []

  isECALicense = false

  /**
   * Dev-express data grid to represent slipsheet list.
   */
  @ViewChild('dxGridCase')
  private readonly dxDataGrid: DxDataGridComponent

  /**
   * Confirmation wrapper to ensure deletion.
   */
  @ViewChild('removeConfirm')
  private readonly confirmDel: TemplateRef<any>

  constructor(
    private store: Store,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog,
    private toast: ToastrService,
    private service: ConfigService
  ) {}

  ngAfterViewInit(): void {
    this.initIntoView()
    this.initSlices()
  }

  /**
   * Initial process after angular inserts view elements on DOM.
   * All methods call who needs to access view when app starts after loading goes here.
   */
  initIntoView() {
    // once we've reach this hook, should start inserting other stuff on view
    timer(700)
      .pipe(
        tap(() => [(this.isWorking = false), this.cdr.detectChanges()]),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: () => [this.initDxSelectionChanges()]
      })
  }

  /**
   * Since we use `.onPush` strategy with content loading placeholder, We need to call this method after
   * loader finishes and toggles the DOM insertion.
   * @see `initIntoView`
   */
  private readonly initDxSelectionChanges = (): void => {
    this.dxDataGrid?.selectedRowKeysChange
      .pipe(
        distinctUntilChanged(),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (keys) => [this.cdr.markForCheck(), (this.selectedKeys = keys)]
      })
  }

  /**
   * Perform task(s) when an action is clicked from a row.
   * @param action Type of the action
   * @param cell dev-express cell object.
   */
  actionClicked(action: ActionTypes, cell: any): void {
    const d: SlipsheetTemplateModel = cell?.data
    const mode = action === ActionTypes.EDIT ? 'edit' : ''
    switch (action) {
      case ActionTypes.EDIT:
        this.store.dispatch(
          new Navigate([`/admin/system/slipsheet-template-settings/${mode}`], {
            slipsheetTemplateId: d.slipsheetTemplateId
          })
        )
        break
      case ActionTypes.DELETE: {
        const delRef = this.dialog.open(this.confirmDel, {
          closeOnNavigation: true,
          autoFocus: false,
          width: '380px'
        })
        const id = [+d?.slipsheetTemplateId]
        delRef
          .beforeClosed()
          .pipe(
            // mat-dialog action directive used. returns boolean value.
            filter((yes) => yes),
            //if clicked yes (truthy value)
            tap(() =>
              this.store.dispatch(new DeleteSlipsheetTemplateAction(id))
            ),
            //grab slice of deletion response
            switchMap(() =>
              this.store.select(
                SlipsheetTemplateSettingsStateSelector.SliceOf(
                  'removedResponse'
                )
              )
            ),
            //message must be defined
            filter((res) => !!res?.message),
            debounceTime(400),
            takeUntil(this.toDestroy$)
          )
          .subscribe({
            next: (res) => [
              this.cdr.markForCheck(),
              this.toast.success(res.message),
              (this.selectedKeys = []),
              this.dxDataGrid.instance.refresh()
            ]
          })
        break
      }

      case ActionTypes.DELETEAll: {
        const delRef = this.dialog.open(this.confirmDel, {
          closeOnNavigation: true,
          autoFocus: false,
          width: '380px'
        })

        delRef
          .beforeClosed()
          .pipe(
            // mat-dialog action directive used. returns boolean value.
            filter((yes) => yes),
            //if clicked yes (truthy value)
            tap(() =>
              this.store.dispatch(
                new DeleteSlipsheetTemplateAction(this.selectedKeys)
              )
            ),
            // grab slice of deletion response
            switchMap(() =>
              this.store.select(
                SlipsheetTemplateSettingsStateSelector.SliceOf(
                  'removedResponse'
                )
              )
            ),
            //message must be defined
            filter((res) => !!res?.message),
            debounceTime(400),
            takeUntil(this.toDestroy$)
          )
          .subscribe({
            next: (res) => {
              if (res?.status === 'Error') {
                this.toast.error(res.message)
              } else {
                this.cdr.markForCheck()
                this.toast.success(res.message)
                this.selectedKeys = []
                this.dxDataGrid.instance.refresh()
              }
            }
          })
        break
      }
    }
  }

  /**
   * Init startup data slice listener.
   */
  private initSlices(): void {
    // Select custom field data
    this.store
      .select(
        SlipsheetTemplateSettingsStateSelector.SliceOf(
          'ssTemplateSettingsDataset'
        )
      )
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((d) => {
        this.fieldData = d
      })
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  ngOnInit(): void {
    this.loadData()

    // Commentting this code as currently we are only enabling the delete option
    // if the license is without ECA. Commentting it until we confirm this from product team.
    // this.service
    //   .fetchLicenseStatus$('FEATURE', 'TOA')
    //   .pipe(takeUntil(this.toDestroy$))
    //   .subscribe((isEnabled: boolean) => {
    //     this.isECALicense = isEnabled
    //   })
  }

  loadData() {
    this.store.dispatch(new GetAllSlipSheetTemplateSettingsAction())
  }

  // Navigate to slipsheet creation view

  createSlipSheet() {
    this.store.dispatch(
      new Navigate(['admin/system/slipsheet-template-settings/create'])
    )
  }

  onSelectionChanged(event: any) {
    // Only allow selection of rows where editable is true
    const editableRows = event.selectedRowsData.filter(
      (row: any) => row.editable
    )
    const editableKeys = editableRows.map((row: any) => row.slipsheetTemplateId)

    // Keep only editable rows selected
    event.component.selectRows(editableKeys, false)
  }

  onRowPrepared(event: any) {
    if (event.rowType === 'data' && !event.data.editable) {
      event.rowElement.classList.add('dx-row-disabled')
    }
  }
}
