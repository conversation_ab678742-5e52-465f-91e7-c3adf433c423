import { I<PERSON><PERSON><PERSON><PERSON> } from '@admin-advance/models'
import { ClientModel } from '@admin-advance/models/client-management/client-management.model'
import {
  FetchSamlIdpServerSettingAction,
  FetchSamlIdpUsersAction,
  SamlIdpServerFacade,
  SamlIdpServerStateSelector
} from '@admin-advance/store'
import { ADUserDetails } from '@admin-advance/store/ad-login-settings/ad-login-settings-state.model'
import { FetchADUserAction } from '@admin-advance/store/ad-login-settings/ad-login-settings.action'
import { ADLoginSettingsSelectors } from '@admin-advance/store/ad-login-settings/ad-login-settings.selector'
import {
  ClientListAction,
  ClientMgmtStateSelector
} from '@admin-advance/store/client-management'
import { HttpErrorResponse } from '@angular/common/http'
import { Component, OnInit, ViewChild } from '@angular/core'
import { FormControl } from '@angular/forms'
import { MatDialogRef } from '@angular/material/dialog'
import { User } from '@auth/models/user.model'
import { getUserDetails } from '@auth/store/selectors/access.selectors'
import { ConfigService } from '@config/services/config.service'
import { select, Store as rxStore } from '@ngrx/store'
import { Store } from '@ngxs/store'
import { AllIDPGroups } from '@root/modules/launchpad/models/case-template-settings.model'
import { FetchAllIDPGroupsAction } from '@root/modules/launchpad/store/case-setting/case-setting.actions'
import { CaseSettingSelector } from '@root/modules/launchpad/store/case-setting/case-setting.selectors'
import { DxDataGridComponent } from 'devextreme-angular'
import { ToastrService } from 'ngx-toastr'
import { of, Subject } from 'rxjs'
import {
  catchError,
  debounceTime,
  filter,
  map,
  switchMap,
  take,
  takeUntil
} from 'rxjs/operators'
import { ResponseModel } from '../../../shared/models'

@Component({
  selector: 'app-import-ad',
  templateUrl: './import-ad.component.html',
  styleUrls: ['./import-ad.component.scss']
})
export class ImportAdComponent implements OnInit {
  /**
   * Formcontrol to select all document in the current page.
   */
  name: FormControl = new FormControl()

  source: ADUserDetails[]

  isSearching = false

  isImporting = false

  userExist = false

  adGroups: Array<AllIDPGroups>

  token: FormControl = new FormControl()

  groupType: FormControl = new FormControl()

  /**
   * Cleanup placeholder for the observers when the component get destroyed.
   */
  private readonly toDestroy$ = new Subject<void>()

  @ViewChild('userList')
  readonly userListGrid: DxDataGridComponent

  isADEnabled = false

  isIdpEnabled = false

  userDetails: User

  selectedProvider = IdPProvider.OKTA

  selectedClientId = 0

  clientData: ClientModel[] = []

  // Enum values mapped for dropdown display
  idPProviderEnum = IdPProvider

  providerKeys = Object.keys(IdPProvider) as Array<keyof typeof IdPProvider>

  tenantId: FormControl = new FormControl()

  applicationClientId: FormControl = new FormControl()

  applicationObjectId: FormControl = new FormControl()

  constructor(
    private store: Store,
    private rxStore: rxStore,
    private dialogRef: MatDialogRef<ImportAdComponent>,
    public toast: ToastrService,
    private config: ConfigService,
    private samlIdpServerFacade: SamlIdpServerFacade
  ) {
    this.isADEnabled = this.config.isADEnabled
    this.isIdpEnabled = this.config.isIdpEnabled
  }

  ngOnInit() {
    this.loadIdpGroupsInitiallyAfterSavedSettingsLoaded()

    this.rxStore.pipe(select(getUserDetails), take(1)).subscribe((uDetails) => {
      this.userDetails = uDetails
      if (
        !(
          this.userDetails.clientId == 1 && this.userDetails.globalRoleId == 1
        ) &&
        this.selectedProvider == IdPProvider.AZURE_AD
      ) {
        this.selectedClientId = this.userDetails.clientId
      }
    })

    this.loadEligibleClients()

    this.store
      .dispatch(new FetchSamlIdpServerSettingAction())
      .pipe(
        switchMap(() =>
          this.store.select(
            SamlIdpServerStateSelector.sliceOf('fetchSamlSettingResponse')
          )
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.token.patchValue(res?.data?.ssoToken)
      })

    this.store
      .dispatch(new FetchAllIDPGroupsAction())
      .pipe(
        switchMap(() =>
          this.store.select(CaseSettingSelector.sliceOf('allIDPGroups'))
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.adGroups = res
      })
  }

  private loadEligibleClients(): void {
    this.store
      .dispatch(new ClientListAction())
      .pipe(
        takeUntil(this.toDestroy$),
        switchMap(() =>
          this.store.select(ClientMgmtStateSelector.SliceOf('clientListModel'))
        ),
        switchMap((data) => {
          if (!data) {
            return of([]) // If no data, return an empty array
          }
          return this.store
            .select<ResponseModel>(
              SamlIdpServerStateSelector.sliceOf(
                'samlIdPGroupMappingSettingsStatusResponse'
              )
            )
            .pipe(
              map((res) => {
                if (res && res.data) {
                  // Extract provider names from res.data and filter providerKeys
                  const availableProviders = Array.from(
                    new Set(
                      res.data
                        .filter(
                          (item: any) => item.hasAnySettingDisabled === true
                        )
                        .map((item: any) => item.providerName)
                    )
                  )
                  this.providerKeys = Object.keys(IdPProvider).filter(
                    (key) => availableProviders.includes(key) // Compare enum key (string) to the availableProviders
                  ) as Array<keyof typeof IdPProvider>

                  // Set the first value of providerKeys to selectedProvider as an enum value
                  if (this.providerKeys.length > 0) {
                    // Map the string key to the actual enum value
                    this.selectedProvider =
                      IdPProvider[
                        this.providerKeys[0] as keyof typeof IdPProvider
                      ]
                  }

                  // Filter clientData based on res.data
                  return data.filter((client: any) =>
                    res.data.some(
                      (r: any) =>
                        r.clientId === client.clientId &&
                        r.hasAnySettingDisabled === true
                    )
                  )
                }
                return [] // Return empty array if no matching clients found
              }),
              catchError((ex: HttpErrorResponse) => {
                return of([]) // Return empty array on error
              })
            )
        })
      )
      .subscribe((filteredClientData) => {
        this.clientData = filteredClientData
        this.onProviderChange(null)
      })
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  searchClick() {
    this.isSearching = true
    if (this.isIdpEnabled) {
      const payload = {
        token: this.token.value,
        link: '',
        groupType: this.groupType.value,
        tenantId: this.tenantId.value,
        applicationClientId: this.applicationClientId.value,
        applicationObjectId: this.applicationObjectId.value,
        providerName: this.selectedProvider,
        clientId: this.selectedClientId,
        fetchGroupsFromDatabase: false
      }
      this.store
        .dispatch(new FetchSamlIdpUsersAction(payload))
        .pipe(
          switchMap(() =>
            this.store.select(
              SamlIdpServerStateSelector.sliceOf('samlIdpUsersResponse')
            )
          ),
          filter((res) => !!res),
          takeUntil(this.toDestroy$)
        )
        .subscribe({
          next: (res) => {
            this.source = res.data
            this.isSearching = false
          }
        })
    }
    if (this.isADEnabled) {
      this.store
        .dispatch(new FetchADUserAction(this.name.value))
        .pipe(
          switchMap(() =>
            this.store.select(ADLoginSettingsSelectors.SliceOf('adUser'))
          ),
          filter((res) => !!res),
          takeUntil(this.toDestroy$)
        )
        .subscribe({
          next: (res) => {
            this.source = res
            this.isSearching = false
          }
        })
    }
  }

  importAction() {
    const selectedUser = this.userListGrid.instance.getSelectedRowsData()
    const selectedUserCount =
      this.userListGrid.instance.getSelectedRowsData().length
    if (selectedUserCount == 0) {
      this.toast.error('User not selected for import.')
    } else {
      if (selectedUser[0].userAlreadyAdded) {
        this.toast.error('User has already been imported.')
      } else {
        const selectedClientId = this.selectedClientId
        this.dialogRef.close({ data: { selectedUser, selectedClientId } })
      }
    }
  }

  onCellPrepared(e) {
    if (e.rowType === 'data' && e.data.userAlreadyAdded) {
      e.cellElement.style.color = 'red'
      this.userExist = true
    }
  }

  public onClientChange(event: any): void {
    this.samlIdpServerFacade.fetchSamlIdpServerSettingData(
      this.selectedClientId
    )
  }

  public onProviderChange(event: any): void {
    if (this.selectedProvider === IdPProvider.OKTA) {
      this.selectedClientId = 0
    } else {
      if (this.userDetails.clientId == 1 && this.userDetails.globalRoleId == 1)
        this.selectedClientId = this.clientData[0].clientId
      else this.selectedClientId = this.userDetails.clientId
    }

    this.samlIdpServerFacade.fetchSamlIdpServerSettingData(
      this.selectedClientId
    )
  }

  private loadIdpGroupsInitiallyAfterSavedSettingsLoaded(): void {
    this.samlIdpServerFacade.selectSamlIdpServerFetchResponse$
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe((res) => {
        this.token.patchValue(res?.data.idPGroupRequest.token)
        this.tenantId.patchValue(res?.data.idPGroupRequest.tenantId)
        this.applicationClientId.patchValue(
          res?.data.idPGroupRequest.applicationClientId
        )
        this.applicationObjectId.patchValue(
          res?.data.idPGroupRequest.applicationObjectId
        )
      })
  }
}
