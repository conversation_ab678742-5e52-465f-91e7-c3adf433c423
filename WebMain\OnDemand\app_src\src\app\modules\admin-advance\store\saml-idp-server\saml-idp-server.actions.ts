import {
  GroupRequestPayload,
  IdPGroupInsertRequestModel,
  SamlGridUiDataType,
  SamlSettingModel
} from '@admin-advance/models/saml-idp-server/saml-idp-server.model'
import { SamlServerStateKeys } from './saml-idp-server-state.model'

export class SamlIdpServerSettingStateCleanupAction {
  static readonly type = '[SAML-Idp] Cleanups State'

  /**
   * @param stateKey list of state to be clear
   */
  public constructor(
    public stateKey: SamlServerStateKeys | SamlServerStateKeys[]
  ) {}
}

export class FetchSamlIdpServerSettingAction {
  static readonly type = '[SAML-Idp] Fetches Saml Idp Server Settings'

  constructor(public clientId?: number) {}
}

export class FetchSamlIdpGroupsAction {
  static readonly type = '[SAML-Idp] Fetches Saml Idp Groups'

  constructor(public payload: GroupRequestPayload) {}
}

export class InsertSamlIdpGroupsAction {
  static readonly type = '[SAML-Idp] Insert Saml Idp Groups'

  constructor(public payload: IdPGroupInsertRequestModel) {}
}

export class FetchSamlIdpServerSettingParseFromXmlMetafileAction {
  static readonly type =
    '[SAML-Idp] Fetches Parsed Saml Idp Server Settings From XML Metafile'

  constructor(public xmlMetafile: string) {}
}

export class ApplySamlIdpServerSettingAction {
  static readonly type = '[SAML-Idp] Apply Saml Idp Server Settings'

  constructor(public payload: SamlSettingModel) {}
}

export class StoreSamlIdpServerSettingAction {
  static readonly type = '[SAML-Idp] Store Saml Idp Server Settings'

  constructor(public payload: Partial<SamlGridUiDataType>) {}
}

export class FetchSamlIdpUsersAction {
  static readonly type = '[SAML-Idp] Fetches Saml Idp Users'

  constructor(public payload: GroupRequestPayload) {}
}

export class GetIdPGroupMappingSettingsStatusAction {
  static readonly type = '[SAML-Idp] Get IdP Group Mapping Settings Status'
}
