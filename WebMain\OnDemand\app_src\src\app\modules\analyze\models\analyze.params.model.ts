import { SearchDocumentCountModel } from '@shared/models'
import { JsonObject, JsonProperty } from 'json2typescript'
import {
  TagModel,
  TagSetting
} from '../../application-nav/models/application-nav-params'

@JsonObject('WidgetModelAnalyze')
export class WidgetModel {
  @JsonProperty('SearchTempTable', String, true) searchTempTable: string = null

  @JsonProperty('GroupId', Number, true) groupId = 0

  @JsonProperty('UserId', Number, true) userId: number = null

  @JsonProperty('ProjectId', Number, true) projectId: number = null

  @JsonProperty('TopLimit', String, true) topLimit: string = null

  @JsonProperty('DateRange', Object, true) dateRange: any = null

  @JsonProperty('WidgetName', String, true) widgetName: string = null

  @JsonProperty('WidgetType', String, true) widgetType = 'STATIC'
}

@JsonObject('SearchModelAnalyze')
export class SearchModel {
  @JsonProperty('SearchExpression', String, true)
  searchExpression: string = null

  @JsonProperty('LstMedia', [Number], true) lstMedia: number[] = null

  @JsonProperty('UserId', Number, true) userId: number = null

  @JsonProperty('ProjectId', Number, true) projectId: number = null

  @JsonProperty('UserType', String, true) userType: string = null

  @JsonProperty('IncludePC', Boolean, true) includePC: boolean = null

  @JsonProperty('SearchGuid', String, true) searchGuid: string = null

  @JsonProperty('SearcId', Number, true) searchId: number = null

  @JsonProperty('IsForwardFilter', Boolean, true)
  isForwardFilter: boolean = null

  @JsonProperty('DSID', String, true) DSID: string = null

  @JsonProperty('SessionId', String, true) sessionId: string = null

  @JsonProperty('BaseGUID', String, true) baseGUID: string = null
}

export class ChartTypeSelectionModel {
  widgetName: string

  dynamicWidget: any

  chartType: string

  size: number
}

@JsonObject('SaveFileTagsModelAnalyze')
export class SaveFileTagsModel {
  @JsonProperty('Comments', String, true) comments: string = null

  @JsonProperty('FileIds', [Number], true) fileIds: number[] = null

  @JsonProperty('isBatchSelected', Boolean, true)
  isBatchSelected: boolean = null

  @JsonProperty('IsNotReviewedOnly', Boolean, true)
  isNotReviewedOnly: boolean = null

  @JsonProperty('MarkAsReviewed', Boolean, true) markAsReviewed: boolean = null

  @JsonProperty('ModuleName', String, true) moduleName: string = null

  @JsonProperty('ProjectId', Number, true) projectId: number = null

  @JsonProperty('SearchTempTableName', String, true)
  searchTempTableName: string = null

  @JsonProperty('TagOperationType', Number, true)
  tagOperationType: number = null

  @JsonProperty('Tags', [TagModel], true) tags: TagModel[] = null

  @JsonProperty('TagSettings', TagSetting, true) tagSettings: TagSetting = null

  @JsonProperty('UnSelectedFileIds', [Number], true)
  unSelectedFileIds: number[] = null

  @JsonProperty('MediaList', [Number], true) medialist: number[] = null

  @JsonProperty('DocShareToken', String, true)
  docShareToken: string = null
}

@JsonObject('LayoutModelAnalyze')
export class LayoutModel {
  @JsonProperty('LayoutXml', String, true) layoutXml: string = null

  @JsonProperty('Module', String, true) module: string = null

  @JsonProperty('Name', String, true) name: string = null

  @JsonProperty('UserId', Number, true) userId: number = null
}

@JsonObject('SaveSearchModelAnalyze')
export class SaveSearchModel {
  @JsonProperty('SearchGuid', String, true) searchGuid: string = null

  @JsonProperty('SearchName', String, true) searchName: string = null

  @JsonProperty('UserType', String, true) userType: string = null
}

/**
 * API return type object (timeline data)
 */
export interface TimelineModel {
  Date: string | Date
  Emails: number
  Edocs: number
}

/**
 * API return type object
 */
export interface ProjectSummaryModel {
  Archives: number
  Custodians: number
  Documents: number
  Duplicates: number
  Medias: number
  Tagged: number
}

/**
 * Before pulling data for widgets, the search parameters are populated
 * which will then supplied with widget name with parameter to fetch data from `/results`
 */
export interface SearchParams {
  error: { errMessage: string; errStatus: boolean }
  tempTables: {
    computedSearchTempTable: string
    savedSearchTempTable: string
    searchResultTempTable: string
    userTempTable: string
    searchGuid: string
    searchId: number
    baseGUID: string
  }
  searchResultIntialParameters: {
    currentPage: number
    currentRecord: number
    fileId: number
    filteringResult: string
    globalTempTableName: string
    pageSize: number
    searchExpression: string
    searchHighlightList: any
    searchID: number
    totalHitCount: number
    invalidExpressions: any
    batchId: number
    searchScopeCount?: number
    searchedDocCount?: SearchDocumentCountModel
    filteredDocCount?: SearchDocumentCountModel
  }
}

export interface UnIndexMediaModel {
  sn: number
  mediaId: number
  mediaName: string
  fileName: string
  mediaStatus: string
  custodianName: string
}
