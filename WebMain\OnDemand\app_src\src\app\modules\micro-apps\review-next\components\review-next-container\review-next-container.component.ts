import { Location } from '@angular/common'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Injector,
  OnDestroy,
  SecurityContext,
  Type,
  ViewChild,
  ViewEncapsulation
} from '@angular/core'
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser'
import { ActivatedRoute, Router } from '@angular/router'
import { Store as RxStore } from '@ngrx/store'
import { Store } from '@ngxs/store'
import { GetSearchDuplicateOption } from '@root/modules/application-nav/store/actions'
import { FetchManualLinkAvailabilityAction } from '@root/modules/application-nav/stores/application-nav.actions'
import { IframeManagerService } from '@root/modules/micro-apps/config/iframe-manager.service'
import {
  IframeMessengerService,
  MessageType
} from '@root/modules/micro-apps/config/iframe-messenger.service'
import { AppIdentitiesTypes } from '@root/modules/micro-apps/models/app-identities-types'
import { SearchQueryModule } from '@shared/models'
import {
  FilterItem,
  FilterTypes
} from '@shared/search-builder/query-builder-static.model'
import { StoreQueryFilterItemAction } from '@shared/search-builder/sotre/query-builder.actions'
import { setSavedSearchId, unsetSearchQuery } from '@shared/store/actions'
import * as fromSharedDocumentActions from '@shared/store/actions/document.actions'
import { getSearchQuery } from '@shared/store/selectors/document.selectors'
import { Uuidv4Generator } from '@shared/utils'
import {
  FetchCustodianMedia,
  FetchMediaStatus,
  FetchUserRightsAction,
  InitUploadHistoryQueryAction,
  UpdateSearchRequestParam
} from '@stores/actions'
import {
  CaseSelectors,
  StartupStateSelector,
  UploadStateSelector
} from '@stores/selectors'
import { combineLatest, Subject } from 'rxjs'
import { debounceTime, filter, take, takeUntil, tap } from 'rxjs/operators'
import { environment } from '../../../../../../environments/environment'
import { SetServerSideSession } from '../../../../config/store/actions'
import { SearchQueryModel } from '../../../../shared/models'
@Component({
  selector: 'app-review-next-container',
  templateUrl: './review-next-container.component.html',
  styleUrls: ['./review-next-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class ReviewNextContainerComponent implements AfterViewInit, OnDestroy {
  readonly #toDestroy$ = new Subject<void>()

  isMicroAppLoading = true

  public readonly appIdentitiesTypes = AppIdentitiesTypes

  @ViewChild('reviewNextIframe', { static: true })
  private readonly reviewNextFrame: ElementRef<HTMLIFrameElement>

  #oldStyles: Partial<CSSStyleDeclaration> = {}

  get #iframe(): HTMLIFrameElement {
    return this.reviewNextFrame.nativeElement
  }

  /**
   * prepares micro app base url for both development & production
   * @see environment.ts
   * @see environment.prod.ts
   */
  private getUrl(params: {
    docShareToken?: string
    moduleData?: string
  }): string {
    /**
     * The micro app of review next port is 4300.
     * If you change port to the target micro app to different, please set that here.
     */
    const localDevelopmentPort = ':4300'
    /**
     * Deployment application base directory.
     * @note
     * 1. If you change the deployment directory, please set that here.
     * @see environment.microAppDeployUrl
     */
    const pathSegmentOrPort = !environment.production
      ? localDevelopmentPort
      : ''

    /**
     * On production, it becomes /VenioWeb/OnDemand/venio-next/documents/?media=1&projectId=1
     */
    let path = `${
      environment.microAppDeployUrl
    }${pathSegmentOrPort}/#/documents/?media=1&projectId=${
      this.#projectId
    }`.replace(/([^:]\/)\/+/g, '$1')

    // if docShareToken is available, append it to the path
    if (params.docShareToken) {
      path = `${path}&docShareToken=${encodeURIComponent(params.docShareToken)}`
    }

    // if moduleData is available, append it to the path
    if (params.moduleData) {
      path = `${path}&moduleData=${encodeURIComponent(params.moduleData)}`
    }
    if (this.#reviewSetId > 0) {
      path = `${path}&reviewSetId=${this.#reviewSetId}`
    }

    return path
  }

  venioNextUrl: SafeResourceUrl

  get #projectId(): number {
    return this.activatedRoute.snapshot.queryParams['projectId']
  }

  get #reviewSetId(): number {
    return this.activatedRoute.snapshot.queryParams['reviewSetId']
  }

  get #docShareToken(): string {
    return this.activatedRoute.snapshot.queryParams['docShareToken']
  }

  get #moduleData(): string {
    return this.activatedRoute.snapshot.queryParams['moduleData']
  }

  advancedSearchUiContainer: Promise<Type<unknown>>

  private selectedCommandEvent: unknown

  advancedSearchInjectorContext: Injector

  constructor(
    private sanitizer: DomSanitizer,
    private changeDetectorRef: ChangeDetectorRef,
    private iframeMessengerService: IframeMessengerService,
    private iframeManagerService: IframeManagerService,
    public router: Router,
    private location: Location,
    private activatedRoute: ActivatedRoute,
    private xsStore: Store,
    private rxStore: RxStore,
    private injector: Injector
  ) {
    this.#setIframeSrc()
  }

  ngAfterViewInit(): void {
    this.#notifyIframeToCloseAllPopoutWindows()
    this.iframeManagerService.findAndStoreIframeInstances()
    this.#requiredActionsForMakeItWorkWithExisting()
    this.#selectReviewNextDocumentReadyEvent()
    this.#fixParentContentDivStylesForThisAppOnly()
    this.#toggleLoadingFlagOnIframeLoad()
    this.#launchAdvancedSearch()
    this.#navigateToAnotherProject()
    this.#launchProduction()
    this.#handleIframeToUpdateNavigationUrl()
    this.#selectChildUiLayoutReady()
    this.#handleSendToAnalyzeRequest()
    this.#launchCaseLaunchPad()
  }

  ngOnDestroy(): void {
    this.#fixParentContentDivStylesForThisAppOnly(true)
    this.#toDestroy$.next()
    this.#toDestroy$.complete()
  }

  #setIframeSrc(): void {
    /**
     * Sanitize url properly & securely
     * @see https://stackoverflow.com/a/69211616/4444844
     */
    this.venioNextUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
      this.sanitizer.sanitize(
        SecurityContext.URL,
        this.getUrl({
          docShareToken: this.#docShareToken,
          moduleData: this.#moduleData
        })
      )
    )
  }

  #setLoadedStyles(element: HTMLElement): void {
    this.#oldStyles.background = element.style.background
    this.#oldStyles.paddingTop = element.style.paddingTop
    this.#oldStyles.paddingLeft = element.style.paddingLeft
    this.#oldStyles.paddingRight = element.style.paddingRight
    this.#oldStyles.paddingBottom = element.style.paddingBottom
    this.#oldStyles.margin = element.style.margin
  }

  /**
   * Because the left-side navigation is hidden during the review-next feature,
   * the container div continues to occupy space.
   * Therefore, we must address this in accordance with our conditions.
   * Once we reach this point, we adjust the space to occupy the full width.
   * When this component is subsequently destroyed, the class is removed to restore the previous state."
   * @see ReviewNextContainerComponent.ngOnDestroy
   */
  #fixParentContentDivStylesForThisAppOnly(isRemove = false): void {
    const contentDiv = document.querySelector('.page-content') as HTMLDivElement
    if (!isRemove && contentDiv) {
      this.#setLoadedStyles(contentDiv)
    }

    if (contentDiv) {
      contentDiv.style.background = isRemove
        ? this.#oldStyles.background
        : '#FFF'
      contentDiv.style.margin = isRemove ? this.#oldStyles.margin : '0'
      contentDiv.style.paddingTop = isRemove
        ? this.#oldStyles.paddingTop
        : '50px'
      contentDiv.style.paddingLeft = isRemove
        ? this.#oldStyles.paddingLeft
        : '0'
      contentDiv.style.paddingRight = isRemove
        ? this.#oldStyles.paddingRight
        : '0'
      contentDiv.style.paddingBottom = isRemove
        ? this.#oldStyles.paddingBottom
        : '0'
      contentDiv.classList.toggle('review-next-active')
    }
  }

  /**
   * This main app is handling the token refresh for auth so the same tokens should be shared across its children apps.
   * @see IframeMessengerService
   */
  #shareBaseRequirementForChildApps(): void {
    this.iframeMessengerService.sendMessage({
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: [
        {
          type: MessageType.TOKEN_UPDATE,
          content: {
            refreshToken: String(localStorage.getItem('refresh_token')),
            accessToken: String(localStorage.getItem('access_token'))
          }
        }
      ]
    })
  }

  #selectChildUiLayoutReady(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.LAYOUT_CHANGE &&
            e.payload['content']?.['layoutReady']
        ),

        tap(() => {
          // A workaround to hide the toolbar and left bar
          // Notifying multiple times to trigger state change in the child app.
          ;[0, 1, 2, 3, 4, 5].forEach((n) => {
            setTimeout(() => {
              this.iframeMessengerService.sendMessage({
                iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
                eventTriggeredFor: 'FRAME_WINDOW',
                type: 'MICRO_APP_DATA_CHANGE',
                payload: {
                  content: {
                    toolbar: 'hide',
                    leftBar: 'hide',
                    // Tell NGRX state to reflect the new changes.
                    timestamp: Date.now() + Math.random() * n
                  },
                  type: MessageType.UI_STATE_CHANGE
                }
              })
            }, n * 100)
          })
        }),
        debounceTime(150),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(() => {
        // Once the iframe is ready, and the information is shared, we can now show the iframe.
        this.#iframe.style.opacity = '1'
      })
  }

  /**
   * Once the #iframe is loaded, we need to toggle the loader
   */
  #toggleLoadingFlagOnIframeLoad(): void {
    // Make it invisible until the document is ready and the child app is ready to receive the data.
    this.#iframe.style.opacity = '0'

    // Allow iframe to perform post-message communication with the parent window.
    this.#iframe.onload = () => {
      this.changeDetectorRef.markForCheck()
      this.isMicroAppLoading = false
    }
  }

  /**
   * Once the child notifies that the document is ready state, we can then pass the additional info to
   * child apps that it can proceed with.
   * Such as configs, tokens, initial states are passed initially when app is loaded for the first time.
   */
  #selectReviewNextDocumentReadyEvent(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.DOCUMENT_READY
        ),
        tap(() => (this.#iframe.style.opacity = '0')),
        debounceTime(300),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(() => {
        this.#shareBaseRequirementForChildApps()
        this.#selectSearchFromOtherSource()
      })
  }

  /**
   * Subscribes to the message received from the iframe messenger service (from venio-next) and listens for a route change event.
   * When the event is received, the method extracts the new project ID from the event payload and updates the current route with the new project ID.
   */
  #navigateToAnotherProject(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e: any) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.ROUTE_CHANGE &&
            e.payload['content']['isProjectIdChanged'] &&
            e.payload['content']['projectId']
        ),
        takeUntil(this.#toDestroy$)
      )
      .subscribe((e: any) => {
        const projectId = e.payload['content']['projectId']
        const queryParams = { ...this.activatedRoute.snapshot.queryParams }

        // Update or add new query parameters
        queryParams['projectId'] = projectId

        // update ASP.NET session (Session['ProjectId']) with updated project id
        this.rxStore.dispatch(
          new SetServerSideSession(
            new Map<string, any>().set('projectid', projectId)
          )
        )

        // Navigate to the same route with updated query parameters
        this.router
          .navigate([], {
            relativeTo: this.activatedRoute,
            queryParams: queryParams,
            queryParamsHandling: 'merge' // merge with existing query params
          })
          .then(() => {
            // As soon as the project ID changes, we need to dispatch the required actions to make the component work with the new project.
            this.#requiredActionsForMakeItWorkWithExisting()
          })
      })
  }

  /**
   * Dispatches a series of actions required for integrating the component with existing systems.
   * This method is tailored to set up necessary data fetches based on the current project ID.
   * It is crucial for ensuring that the component has all the required data and permissions for its operation.
   *
   * The method performs the following actions:
   * - Dispatches multiple actions using `this.xsStore.dispatch()`.
   * - The dispatched actions are:
   *   - `FetchUserRightsAction(0)`: Fetches user rights. The parameter `0` may indicate a default or initial state.
   *   - `FetchCustodianMedia(this.#projectId)`: Fetches custodian media related to the current project.
   *   - `FetchMediaStatus(this.#projectId)`: Fetches the status of media for the current project.
   *   - `FetchManualLinkAvailabilityAction(this.#projectId)`: Checks the availability of manual links for the current project.
   *
   * These actions are typically used to fetch and synchronize state with a backend or a global store.
   * The `#projectId` is a private member variable of the class that holds the ID of the current project.
   */
  #requiredActionsForMakeItWorkWithExisting(): void {
    this.xsStore.dispatch([
      new FetchUserRightsAction(this.#projectId),
      new FetchCustodianMedia(this.#projectId),
      new FetchMediaStatus(this.#projectId),
      new FetchManualLinkAvailabilityAction(this.#projectId)
    ])

    this.rxStore.dispatch(new GetSearchDuplicateOption(this.#projectId))
  }

  #resetStateAfterUse(): void {
    // reset history instance state wherever it has been used by
    this.xsStore.dispatch(new InitUploadHistoryQueryAction(null))

    // reset another state that has been used for review
    this.rxStore.dispatch(unsetSearchQuery())
  }

  /**
   * Sends a search change message with specified parameters.
   * This method is responsible for communicating search-related data to another part of the application, typically via an iframe.
   *
   * @param {string} searchTerm - The search term to be sent. If this is empty, the method exits early.
   * @param {boolean} includeFamily - A boolean indicating whether the search should include family data.
   * @param {number} searchDuplicateOption - Search option for duplicate setting
   * @param {string} searchTriggerSource - The source of the search trigger. e.g. 'Analyze Search', 'Production Search', etc.
   *
   * The method performs the following actions:
   * - Checks if `searchTerm` is non-empty. If it's empty, the function returns early, performing no action.
   * - Sends a message using `iframeMessengerService.sendMessage()`.
   * - The message includes the `iframeIdentity` set to `AppIdentitiesTypes.VENIO_NEXT`.
   * - The payload of the message contains:
   *   - The type of the message (`MessageType.SEARCH_CHANGE`).
   *   - The content of the message, which includes `searchTerm`, `includeFamily`, and a flag `triggerSearch` that indicates whether a search should be triggered (based on whether `searchTerm` is non-empty and trimmed).
   *
   * Note: TODO If the number of parameters increases, the method should be refactored to use a model for easier parameter management.
   */
  #sendSearchChangeMessage(
    searchTerm: string,
    includeFamily: boolean,
    searchDuplicateOption: number,
    searchTriggerSource: string
  ): void {
    if (!searchTerm) return

    this.iframeMessengerService.sendMessage({
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.SEARCH_CHANGE,
        content: {
          searchTerm,
          includeFamily,
          searchDuplicateOption,
          // micro app will decide what to do with the term we pass
          triggerSearch: true,
          searchTriggerSource
        }
      }
    })
  }

  /**
   * Initiates a search based on either historical data or data from another source.
   * This method combines the latest values from two different selectors:
   * - `UploadStateSelector.SliceOf('initHistoryQuery')`
   * - `getSearchQuery`
   *
   * It then applies a series of RxJS operators to filter and transform the combined data:
   * - `debounceTime(500)`: Waits for 500ms of silence (no new events) before proceeding.
   * - `filter`: Filters out the combined array if both elements do not contain a valid query.
   * - `take(1)`: Takes only the first valid combination of data.
   * - `takeUntil(this.#toDestroy$)`: Ensures the subscription is canceled when the provided observable emits a value.
   *
   * Finally, the method subscribes to the processed observable, extracting the search term and whether to include family in the search.
   * It then triggers further actions based on the extracted values.
   */
  #selectSearchFromOtherSource(): void {
    combineLatest([
      this.xsStore.select(UploadStateSelector.SliceOf('initHistoryQuery')),
      this.rxStore.select(getSearchQuery)
    ])
      .pipe(
        debounceTime(500),
        filter(
          ([fromHistory, fromOtherSource]) =>
            Boolean(fromHistory?.query) || Boolean(fromOtherSource?.searchQuery)
        ),
        take(1),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(([fromHistory, fromOtherSource]) => {
        // Determines the search term from either history or other source.
        const searchTerm =
          (typeof fromHistory?.query === 'string' && fromHistory?.query) ||
          fromOtherSource?.searchQuery

        // We have to determine the source of the search trigger.
        // so the micro app can decide what to do with the search term we pass.
        // e.g. if the source is from analyze search, a breadcrumb will show it as titled 'Analyze Search'.
        // if the source is from production search, a breadcrumb will show it as titled 'Production Search' and so on.
        const sourceModule =
          fromOtherSource?.sourceModule || fromHistory?.sourceModule
        const source =
          sourceModule === SearchQueryModule.Analyze
            ? 'Analyze Search'
            : sourceModule === SearchQueryModule.UploadFileHistory
            ? 'File Upload History Search'
            : sourceModule === SearchQueryModule.Production
            ? 'Production Search'
            : sourceModule === SearchQueryModule.DocumentShare
            ? 'Document Share Search'
            : sourceModule === SearchQueryModule.Launchpad
            ? 'Launchpad Search'
            : sourceModule === SearchQueryModule.Admin
            ? 'Admin Search'
            : undefined

        // Determines if family search should be included.
        const includeFamily = Boolean(
          fromHistory?.includePc || fromOtherSource?.includeFamilySearch
        )

        const searchDuplicateOption = fromOtherSource?.searchDuplicateOption

        const medialist = this.xsStore.selectSnapshot(
          StartupStateSelector.SliceOf('selectedMediaScope')
        )
        const caseMediaStatus = this.xsStore.selectSnapshot(
          CaseSelectors.mediaStatus
        )
        let finalSearchExpression = searchTerm
        if (
          caseMediaStatus?.processedMediaIds.length !== medialist.length &&
          sourceModule === SearchQueryModule.Analyze
        ) {
          finalSearchExpression = `(${searchTerm}) AND MEDIA_ID IN (${medialist.join(
            ','
          )})`
        }
        // Sends a search change message with the determined search term and family inclusion flag.
        this.#sendSearchChangeMessage(
          finalSearchExpression,
          includeFamily,
          searchDuplicateOption,
          source
        )

        // Resets the state after this operation to prepare for future use.
        this.#resetStateAfterUse()
      })
  }

  /**
   * Initializes the advanced search functionality when specific conditions are met.
   * This method listens to messages received via `iframeMessengerService`, filtering for a specific event and message type.
   *
   * The method performs the following operations:
   * - Listens to `messageReceived` from `iframeMessengerService`.
   * - Filters the incoming messages based on two conditions:
   *   - The message is triggered for the 'PARENT_WINDOW'.
   *   - The message type is `MessageType.SEARCH_CHANGE`.
   * - Continues listening until a signal is received from `this.#toDestroy$`, which is used to clean up the subscription.
   *
   * Upon receiving the appropriate message, the method:
   * - Marks the component for a check with `changeDetectorRef.markForCheck()` to ensure that changes are detected.
   * - Dynamically imports the `AdvancedSearchContainerComponent` from its module.
   * - Assigns the imported component to `this.advancedSearchUiContainer` for further use.
   *
   * This setup allows for a dynamic, event-driven approach to initializing the advanced search feature.
   */
  #launchAdvancedSearch(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.SEARCH_CHANGE
        ),
        takeUntil(this.#toDestroy$)
      )
      .subscribe((message) => {
        // Extracts the selected command event from the message payload.
        this.selectedCommandEvent =
          message.payload?.['content']?.['selectedCommandEventPayload']

        const isCommandEvent = Boolean(this.selectedCommandEvent)

        const query = isCommandEvent
          ? (
              this.selectedCommandEvent?.['selectedFolder']?.[
                'searchSettings'
              ]?.['searchExpression']?.['expression'] || ''
            ).trim()
          : message?.payload['content']?.breadcrumbQuery

        const includePC = message?.payload['content']?.includePC
        const searchDuplicateOption =
          message?.payload['content']?.searchDuplicateOption

        this.xsStore.dispatch(
          new UpdateSearchRequestParam({
            ...message?.payload['content'].searchParams
          })
        )
        this.xsStore.dispatch(
          new StoreQueryFilterItemAction({
            query,
            type: FilterTypes.CUSTOM,
            itemOperator: 'AND',
            id: new Uuidv4Generator().uuid
          } as FilterItem)
        )
        // Marks the component for change detection.
        this.changeDetectorRef.markForCheck()

        this.advancedSearchInjectorContext = Injector.create({
          parent: this.injector,
          providers: [
            {
              provide: 'CHILD_DATA',
              useValue: {
                selectedCommandEvent: this.selectedCommandEvent,
                includePC,
                searchDuplicateOption
              }
            }
          ]
        })

        // Dynamically imports and assigns the AdvancedSearchContainerComponent.
        this.advancedSearchUiContainer = import(
          '../advanced-search-container/advanced-search-container.component'
        ).then((ac) => ac.AdvancedSearchContainerComponent)
      })
  }

  /**
   * Notifies the iframe to destroy all popout windows.
   * This method sends a message to the iframe to close all popout windows that will
   * be handled in the iframe window by listening to the message event.
   */
  #notifyIframeToCloseAllPopoutWindows(): void {
    window.addEventListener('beforeunload', () => {
      this.#iframe.contentWindow.postMessage(
        {
          type: 'MICRO_APP_DATA_CHANGE',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          payload: {
            type: MessageType.ROUTE_CHANGE,
            content: {
              closeAllPopoutWindows: true
            }
          }
        },
        environment.allowedOrigin
      )
    })
  }

  /**
   * Handles the iframe navigation URL update. The method listens for the message received from the iframe messenger service.
   */
  #handleIframeToUpdateNavigationUrl(): void {
    this.changeDetectorRef.markForCheck()
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e: any) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.ROUTE_CHANGE &&
            e.payload['content']['isUrlParameterChange'] &&
            e.payload['content']['removedParameters']
        ),
        debounceTime(500),
        takeUntil(this.#toDestroy$)
      )
      .subscribe((e: unknown) => {
        this.changeDetectorRef.markForCheck()
        const removedParameters = e['payload']['content']['removedParameters']
        const queryParams = { ...this.activatedRoute.snapshot.queryParams }

        // Remove the specified query parameters
        removedParameters.forEach((param: string) => {
          delete queryParams[param]
        })

        // Get the current URL tree
        const urlTree = this.router.createUrlTree([], {
          relativeTo: this.activatedRoute,
          queryParams
        })

        const url = urlTree.toString()

        // Update the browser URL without navigating
        this.location.replaceState(url)

        // we need to update the iframe source to reflect the changes
        this.venioNextUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
          this.sanitizer.sanitize(
            SecurityContext.URL,
            this.getUrl({
              docShareToken: this.#docShareToken
            })
          )
        )

        this.#requiredActionsForMakeItWorkWithExisting()
      })
  }

  /*
   * Listens for the production launch event from venio next and navigates to the production page after saving savedSearchId to old vod store.
   */
  #launchProduction(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e: any) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.PRODUCTION_LAUNCH &&
            e.payload['content']['savedSearchId'] &&
            e.payload['content']['projectId']
        ),
        takeUntil(this.#toDestroy$)
      )
      .subscribe((e) => {
        this.changeDetectorRef.markForCheck()

        // grab saved search id and project id from the payload sent from venio next
        const savedSearchId = +e.payload['content']['savedSearchId']
        const projectId = +e.payload['content']['projectId']

        // set saved search id to store taken from venio next
        this.#setSaveSearchIdInStore(savedSearchId)

        // Navigate to the production page
        this.#navigateToProduction(projectId)
      })
  }

  #launchCaseLaunchPad(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e: any) =>
            e.payload['type'] === MessageType.ROUTE_CHANGE &&
            e.payload['content']['isBatchReview']
        ),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(() => {
        this.router.navigate(['launchapad'])
      })
  }

  /*
   * Sets the saveSearchId to the old vod store.
   * The search is saved from venio next and savedSearchId is sent to the old vod which needs to be stored.
   * When launching production the savedSearchId is read and the source for production is automatically set to saved search.
   */
  #setSaveSearchIdInStore(savedSearchId: number): void {
    this.rxStore.dispatch(
      setSavedSearchId({
        payload: { savedSearchId }
      })
    )
  }

  /**
   * Navigates to the production page with the current project ID.
   */
  #navigateToProduction(projectId: number): void {
    this.router.navigate(['/production'], {
      queryParams: { projectId }
    })
  }

  // handle send to analyze page request from review next page
  #handleSendToAnalyzeRequest(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.ROUTE_CHANGE &&
            e.payload['content']?.['isSendToAnalyze'] &&
            e.payload['content']?.['searchQueryModel'] &&
            e.payload['content']['projectId']
        ),
        takeUntil(this.#toDestroy$)
      )
      .subscribe((e) => {
        const searchQueryModel = e.payload['content'][
          'searchQueryModel'
        ] as SearchQueryModel
        const projectId = e.payload['content']['projectId'] as number
        this.#sendToAnalyze(projectId, searchQueryModel)
      })
  }

  #sendToAnalyze(projectId: number, searchQueryModel: SearchQueryModel): void {
    this.rxStore.dispatch(
      fromSharedDocumentActions.setSearchQuery({
        payload: {
          searchQueryModel: searchQueryModel
        }
      })
    )
    this.router.navigate(['/analyze'], {
      queryParams: {
        projectId: projectId,
        docShareToken:
          this.activatedRoute.snapshot.queryParamMap.get('docShareToken')
      }
    })
  }
}
