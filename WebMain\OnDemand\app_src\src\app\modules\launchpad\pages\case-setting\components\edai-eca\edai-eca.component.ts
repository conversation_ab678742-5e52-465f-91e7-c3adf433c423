import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnDestroy,
  OnInit
} from '@angular/core'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators
} from '@angular/forms'
import { ControlNames } from '@root/modules/launchpad/pages/case-setting/components/edai-eca/edai-eca.model'
import { Subject } from 'rxjs'
import { takeUntil } from 'rxjs/operators'

/**
 * Component responsible for EDAI ECA functionality
 * Manages related form controls and their validation states
 */
@Component({
  selector: 'app-edai-eca',
  templateUrl: './edai-eca.component.html',
  styleUrls: ['./edai-eca.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class EdaiEcaComponent implements OnInit, OnDestroy {
  /** Parent form group to attach the EDAI ECA controls to */
  @Input() public parentForm: FormGroup

  /** Flag indicating if component is in edit mode */
  @Input() public isEditMode = false

  /** Subscription termination trigger */
  private toDestroy$ = new Subject<void>()

  public readonly controlNames = ControlNames

  public get isNonRelevantInvalid(): boolean {
    const ctrl = this.getNonRelevantDescControl()
    if (!ctrl) return false

    return this.getInvalidStatus(ctrl)
  }

  public get isRelevantInvalid(): boolean {
    const ctrl = this.getRelevantDescControl()
    if (!ctrl) return false

    return this.getInvalidStatus(ctrl)
  }

  public get isBackgroundInvalid(): boolean {
    const ctrl = this.getBackgroundControl()
    if (!ctrl) return false

    return this.getInvalidStatus(ctrl)
  }

  constructor(private formBuilder: FormBuilder) {}

  /**
   * Initialize component, setup form controls and listeners
   */
  public ngOnInit(): void {
    this.initializeFormControls()
    this.setupFormListeners()
    this.applyInitialValidationState()

    this.disableControlsInEditMode()
  }

  /**
   * Cleanup subscriptions on component destruction
   */
  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  private getInvalidStatus(control: AbstractControl): boolean {
    const value = (control.value || '').trim()

    return (
      (control.invalid && control.touched && control.dirty) ||
      (!value && control.touched && control.dirty)
    )
  }

  /**
   * Initialize form controls and add them to parent form
   */
  private initializeFormControls(): void {
    this.parentForm.addControl(
      this.controlNames.isEnabled,
      this.formBuilder.control(false)
    )
    this.parentForm.addControl(
      this.controlNames.background,
      this.formBuilder.control('')
    )
    this.parentForm.addControl(
      this.controlNames.relevantDesc,
      this.formBuilder.control('')
    )
    this.parentForm.addControl(
      this.controlNames.nonRelevantDesc,
      this.formBuilder.control('')
    )
  }

  /**
   * Setup listeners for form control value changes
   */
  private setupFormListeners(): void {
    this.getEnabledControl()
      .valueChanges.pipe(takeUntil(this.toDestroy$))
      .subscribe((isEnabled: boolean) => {
        this.updateValidationRules(isEnabled)
      })
  }

  /**
   * Apply initial validation state based on initial enabled value
   */
  private applyInitialValidationState(): void {
    const isEnabled = this.getEnabledControl().value
    this.updateValidationRules(isEnabled)
  }

  /**
   * Disable all controls when in edit mode
   */
  private disableControlsInEditMode(): void {
    if (!this.isEditMode) return
    this.getEnabledControl().disable()
    this.getBackgroundControl().disable()
    this.getRelevantDescControl().disable()
    this.getNonRelevantDescControl().disable()
  }

  /**
   * Update validation rules based on whether EDAI ECA is enabled
   * @param isEnabled Whether EDAI ECA is enabled
   */
  private updateValidationRules(isEnabled: boolean): void {
    if (isEnabled) {
      this.applyRequiredValidation()
    } else {
      this.clearValidationAndValues()
    }
  }

  /**
   * Apply required validation to all text area fields
   */
  private applyRequiredValidation(): void {
    this.getBackgroundControl().setValidators([Validators.required])
    this.getRelevantDescControl().setValidators([Validators.required])
    this.getNonRelevantDescControl().setValidators([Validators.required])

    this.updateAllControlsValidity()
  }

  /**
   * Clear validation rules and values for all text area fields
   */
  private clearValidationAndValues(): void {
    const controls = [
      this.getBackgroundControl(),
      this.getRelevantDescControl(),
      this.getNonRelevantDescControl()
    ]

    controls.forEach((control) => {
      control.clearValidators()
      control.setValue('')
      control.updateValueAndValidity()
    })
  }

  /**
   * Update validation status for all text area controls
   */
  private updateAllControlsValidity(): void {
    this.getBackgroundControl().updateValueAndValidity()
    this.getRelevantDescControl().updateValueAndValidity()
    this.getNonRelevantDescControl().updateValueAndValidity()
  }

  /**
   * Get the isEnabled form control
   * @returns AbstractControl for the isEnabled field
   */
  public getEnabledControl(): AbstractControl {
    return this.parentForm.get(this.controlNames.isEnabled)
  }

  /**
   * Get the background form control
   * @returns AbstractControl for the background field
   */
  private getBackgroundControl(): AbstractControl {
    return this.parentForm.get(this.controlNames.background)
  }

  /**
   * Get the relevant description form control
   * @returns AbstractControl for the relevant description field
   */
  private getRelevantDescControl(): AbstractControl {
    return this.parentForm.get(this.controlNames.relevantDesc)
  }

  /**
   * Get the non-relevant description form control
   * @returns AbstractControl for the non-relevant description field
   */
  private getNonRelevantDescControl(): AbstractControl {
    return this.parentForm.get(this.controlNames.nonRelevantDesc)
  }
}
