import {
  AdminLevelModel,
  GroupResponseModel,
  SamlGridUiDataType,
  SamlGridUiSettingData,
  SamlSettingModel
} from '@admin-advance/models/saml-idp-server/saml-idp-server.model'
import { SamlIdpServerFacade } from '@admin-advance/store/saml-idp-server/saml-idp-server.facade'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
  ViewChild
} from '@angular/core'
import { DxDataGridComponent } from 'devextreme-angular'
import { cloneDeep } from 'lodash'
import { Subject } from 'rxjs'
import { debounceTime, takeUntil } from 'rxjs/operators'

@Component({
  selector: 'app-saml-common-grid',
  templateUrl: './saml-common-grid.component.html',
  styleUrls: ['./saml-common-grid.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SamlCommonGridComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private readonly toDestroy$ = new Subject<void>()

  @Input()
  uiGridItem: SamlGridUiSettingData

  @Input()
  isParentEnable: boolean

  @Input()
  isDataLoading: boolean

  dataSource: AdminLevelModel[]

  @ViewChild(DxDataGridComponent)
  private readonly samlGridComponent: DxDataGridComponent

  isEnable: boolean

  private samlGroupData: Array<{ groupsid: string; name: string }>

  nextSamlGroupData: Array<{ groupsid: string; name: string }>

  constructor(
    private changeDetectorRef: ChangeDetectorRef,
    private samlIdpServerFacade: SamlIdpServerFacade
  ) {}

  ngOnInit(): void {
    this.setGroupLoadSubscription()
  }

  ngAfterViewInit(): void {
    this.setAdminGridChangeSubscription()
  }

  private setCheckboxValue(setting: SamlSettingModel): void {
    this.isEnable = setting?.[this.getPropertyByType()]
    this.adminLevelEnableChange(this.isEnable)
  }

  private readonly trimmer = (str) => (str || '').trim()

  private setNextIdpGroupAfterSelected(): void {
    this.changeDetectorRef.markForCheck()
    const masterCopy = cloneDeep(this.samlGroupData)
    this.nextSamlGroupData = masterCopy.filter(
      (g) => !this.dataSource.some((d) => d.groupsid === g.groupsid)
    )
  }

  private getPropertyByType(): string {
    return this.uiGridItem.gridUiType === 'venioApplicationAccess'
      ? 'checkEditApplicationLevel'
      : this.uiGridItem.gridUiType === 'venioAdminLevels'
      ? 'checkEditAdminLevel'
      : this.uiGridItem.gridUiType === 'venioUserGroups'
      ? 'checkEditProjectLevel'
      : this.uiGridItem.gridUiType === 'venioUsers'
      ? 'checkEditActiveUsers'
      : ''
  }

  private setAdminDataSource(setting: SamlSettingModel): void {
    if (!setting) return

    this.dataSource = setting[this.uiGridItem.gridUiType as string]?.map(
      (o) => ({
        ...o,
        groupsid: this.trimmer(o.groupsid)
      })
    )
    this.samlIdpServerFacade.storeSamlIdpServerSetting({
      type: this.uiGridItem.gridUiType,
      samlIdpSettings: cloneDeep(this.dataSource)
    })
  }

  private setGroupsDataSource(groups: GroupResponseModel[]): void {
    if (!Array.isArray(groups)) return

    this.samlGroupData = groups.map((g) => ({
      groupsid: this.trimmer(g.profile.name),
      name: this.trimmer(g.profile.name)
    }))

    const validGroupIds = new Set([
      ...this.samlGroupData.map((g) => g.groupsid),
      ''
    ])
    const updatedDataSource = this.dataSource.map((row) =>
      !validGroupIds.has(row.groupsid) ? { ...row, groupsid: null } : row
    )

    // Only update `this.dataSource` if there are invalid entries
    const hasChanges = this.dataSource.some(
      (row) => !validGroupIds.has(row.groupsid)
    )
    if (hasChanges) {
      this.dataSource = updatedDataSource
    }

    this.setNextIdpGroupAfterSelected()
  }

  private setGroupLoadSubscription = (): void => {
    // Handle settings response
    this.samlIdpServerFacade.selectSamlIdpServerFetchResponse$
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe((settingRes) => {
        this.changeDetectorRef.markForCheck()
        this.setCheckboxValue(settingRes?.data as SamlSettingModel)
        this.setAdminDataSource(settingRes?.data as SamlSettingModel)
      })

    // Handle groups data response
    this.samlIdpServerFacade.selectSamlIdpGroupsData$
      .pipe(debounceTime(400), takeUntil(this.toDestroy$))
      .subscribe((groupRes) => {
        this.changeDetectorRef.markForCheck()
        this.setGroupsDataSource(groupRes?.data as GroupResponseModel[])
      })
  }

  private setAdminGridChangeSubscription(): void {
    this.samlGridComponent.onRowUpdated
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.samlIdpServerFacade.storeSamlIdpServerSetting({
          type: this.uiGridItem.gridUiType,
          samlIdpSettings: cloneDeep(this.dataSource)
        })
        this.setNextIdpGroupAfterSelected()
      })
  }

  private getAdditionalFormData(checked: boolean): SamlGridUiDataType {
    return {
      type: 'formData',
      samlIdpSettings: {
        [this.getPropertyByType()]: checked
      } as Partial<SamlSettingModel>
    }
  }

  readonly adminLevelEnableChange = (checked: boolean): void => {
    this.samlIdpServerFacade.storeSamlIdpServerSetting(
      this.getAdditionalFormData(checked)
    )
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
