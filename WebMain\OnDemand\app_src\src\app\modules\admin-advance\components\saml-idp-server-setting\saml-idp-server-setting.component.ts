import { Id<PERSON>rovider } from '@admin-advance/models'
import { ClientModel } from '@admin-advance/models/client-management/client-management.model'
import {
  AdminLevelModel,
  SamlGridUiSettingData,
  SamlGridUiTypes,
  SamlSettingModel
} from '@admin-advance/models/saml-idp-server/saml-idp-server.model'
import { SamlIdpServerFacade } from '@admin-advance/store'
import { ClientMgmtStateSelector } from '@admin-advance/store/client-management'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewEncapsulation
} from '@angular/core'
import { Store } from '@ngxs/store'
import { animateHeight } from '@shared/animation/animations'
import { cloneDeep } from 'lodash'
import { ToastrService } from 'ngx-toastr'
import { Subject } from 'rxjs'
import { debounceTime, filter, takeUntil } from 'rxjs/operators'
import { SamlIdpServerComponentService } from './saml-idp-server-component.service'

@Component({
  selector: 'app-saml-idp-server-setting',
  templateUrl: './saml-idp-server-setting.component.html',
  styleUrls: ['./saml-idp-server-setting.component.scss'],
  animations: [animateHeight],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class SamlIdpServerSettingComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  isEnableAllUi: boolean

  isLoading: boolean

  isClientDataLoading: boolean

  isSamlApplying: boolean

  commonGridUiSettingData: SamlGridUiSettingData[]

  errorMessage: string

  settingPayload: SamlSettingModel

  isInvalidIdpSettings: boolean

  selectedProvider = IdPProvider.OKTA

  selectedClientId = 0

  clientData: ClientModel[] = []

  // Enum values mapped for dropdown display
  idPProviderEnum = IdPProvider

  providerKeys = Object.keys(IdPProvider) as Array<keyof typeof IdPProvider>

  hideClientName = true

  constructor(
    private samlIdpServerFacade: SamlIdpServerFacade,
    private samlIdpServerComponentService: SamlIdpServerComponentService,
    private changeDetectorRef: ChangeDetectorRef,
    private toastrService: ToastrService,
    private store: Store
  ) {}

  ngOnInit(): void {
    this.loadIdpGroupsInitiallyAfterSavedSettingsLoaded()
    this.samlIdpServerFacade.fetchSamlIdpServerSettingData(0)
    this.getClientList()
  }

  private getClientList(): void {
    this.store
      .select(ClientMgmtStateSelector.SliceOf('clientListModel'))
      .subscribe((data) => {
        if (data) {
          this.clientData = data
        }
      })
  }

  ngAfterViewInit(): void {
    this.setUiItems()
    this.setStoredSettingSubscription()
    this.setSamlSettingApplySubscription()
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.samlIdpServerFacade.resetSamlServerAllStates()
  }

  private setUiItems = (): void => {
    this.commonGridUiSettingData =
      this.samlIdpServerComponentService.samlGridUiSettingData.slice()
  }

  readonly enableAllUi = (checked: boolean): void => {
    this.changeDetectorRef.markForCheck()
    this.isEnableAllUi = checked
    this.samlIdpServerFacade.storeSamlIdpServerSetting({
      type: 'formData',
      samlIdpSettings: { enableIDP: checked }
    })
  }

  readonly onLoadingChanged = (isLoading: boolean): void => {
    this.isLoading = isLoading
  }

  private setPayloadValidationFlag(): void {
    this.changeDetectorRef.markForCheck()
    const {
      venioApplicationAccess,
      venioAdminLevels,
      venioUserGroups,
      venioUsers,
      checkEditActiveUsers,
      checkEditAdminLevel,
      checkEditApplicationLevel,
      checkEditProjectLevel
    } = this.settingPayload

    const hasNoGroupId = (data: AdminLevelModel[]) =>
      data.some((g) => !(g.groupsid || '').trim())

    this.isInvalidIdpSettings =
      (checkEditActiveUsers && hasNoGroupId(venioUsers)) ||
      (checkEditAdminLevel && hasNoGroupId(venioAdminLevels)) ||
      (checkEditApplicationLevel && hasNoGroupId(venioApplicationAccess)) ||
      (checkEditProjectLevel && hasNoGroupId(venioUserGroups))
  }

  private setPayload = (
    state: Partial<{ [key in SamlGridUiTypes]: unknown }>
  ): void => {
    const formValues = cloneDeep(state['formData']) as SamlSettingModel

    formValues.venioAdminLevels = Object.values(state['venioAdminLevels'] || {})
    formValues.venioApplicationAccess = Object.values(
      state['venioApplicationAccess'] || {}
    )
    formValues.venioUserGroups = Object.values(state['venioUserGroups'] || {})
    formValues.venioUsers = Object.values(state['venioUsers'] || {})
    formValues.enableIDP = this.isEnableAllUi
    formValues.apilink = formValues.groupAPIURL
    formValues.ssoToken = formValues.token
    formValues.ssO_URL = formValues.idpssoUrl
    formValues.providerName = this.selectedProvider
    formValues.clientId = this.selectedClientId
    formValues.idPGroupRequest = {
      tenantId: formValues.tenantId,
      applicationClientId: formValues.applicationClientId,
      applicationObjectId: formValues.applicationObjectId,
      token: formValues.token,
      clientId: formValues.clientId,
      link: formValues.groupAPIURL,
      providerName: this.selectedProvider,
      groupType:
        this.selectedProvider === IdPProvider.OKTA ? 'OKTA_GROUP' : null,
      fetchGroupsFromDatabase: false
    }
    this.settingPayload = formValues
    this.setPayloadValidationFlag()
  }

  private setStoredSettingSubscription(): void {
    this.samlIdpServerFacade.selectStoreSamlIdpServerData$
      .pipe(
        filter((state) => !!state?.['formData']),
        takeUntil(this.toDestroy$)
      )
      .subscribe(this.setPayload)
  }

  private loadIdpGroupsInitiallyAfterSavedSettingsLoaded(): void {
    this.samlIdpServerFacade.selectSamlIdpServerFetchResponse$
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe((res) => {
        this.isClientDataLoading = false
        this.changeDetectorRef.markForCheck()
        this.isEnableAllUi = res?.data?.['enableIDP']
      })
  }

  private setSamlSettingApplySubscription(): void {
    this.samlIdpServerFacade.selectSamlIdpServerApplyResponse$
      .pipe(
        debounceTime(100),
        filter((res) => !!res?.message),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.changeDetectorRef.markForCheck()
        this.isSamlApplying = false
        const isError = (res) => res && (res.status || '').match(/error/gi)
        isError(res)
          ? this.toastrService.error(res.message)
          : this.toastrService.success(res.message)
      })
  }

  readonly onApplySaml = (): void => {
    this.changeDetectorRef.markForCheck()
    this.isSamlApplying = true
    this.samlIdpServerFacade.applySamlIdpServerSettingData(this.settingPayload)
  }

  onClientChange(event: any): void {
    this.changeDetectorRef.markForCheck()
    this.samlIdpServerFacade.fetchSamlIdpServerSettingData(
      this.selectedClientId
    )
    this.isClientDataLoading = true
  }

  onProviderChange(event: any): void {
    this.changeDetectorRef.markForCheck()

    if (this.selectedProvider === IdPProvider.OKTA) {
      this.hideClientName = true
      this.selectedClientId = 0
    } else {
      this.hideClientName = false
      this.selectedClientId = this.clientData[0].clientId
    }

    this.samlIdpServerFacade.fetchSamlIdpServerSettingData(
      this.selectedClientId
    )
  }
}
