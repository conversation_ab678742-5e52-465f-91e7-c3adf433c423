export interface ImportConfig {
  loadFile: LoadFileConfig
  imageLoadFile: ImageLoadFileConfig
  native: NativeConfig
  image: ImageConfig
  fullText: FullTextConfig
  venioToLoadFileMapping: any
  custodian: CustodianConfig
  media: MediaConfig
  dataOverlay: DataOverlayConfig
  imageOverlay: ImageOverlayConfig
}

export interface FileConfig {
  filePath: string
}

export interface FolderConfig {
  folderPath: string
  fieldWithPath: string
}

export interface LoadFileConfig extends FileConfig {
  processLoadFile: boolean
  format: LoadFileFormatConfig
  dateFormat: string
  timeZone: string
}

export interface LoadFileFormatConfig {
  type: string
  fieldSeparator: number
  textQualifier: number
  lineBreak: number
}

export type ImageLoadFileConfig = FileConfig

export interface ResourcesConfig extends FolderConfig {
  isReplaceFullTextPathText: boolean
  replaceFullTextPathText: string
  replaceFullTextPathTextWith: string
}

export interface NativeConfig extends ResourcesConfig {
  processNative: boolean
  copyNative: boolean
  extractFulltextFromNative: boolean
  autoComputeFileSize: boolean
  autoComputeFileExtension: boolean
  autoComputeFileType: boolean
  validateFileExistance: boolean
}

export interface FullTextConfig extends ResourcesConfig {
  processFullText: boolean
  copyFullText: boolean
  hasExtractedTextInLoadFile: boolean
  validateHasOnlySpaceCharacter: boolean
  validateHasOnlySingleCharacter: boolean
  validateIsZeroByteFile: boolean
  extractedTextFields: string[]
  validateFileExistance: boolean
}

export interface ImageConfig extends ResourcesConfig {
  processImage: boolean
  copyImage: boolean
  imageMappingField: string
  validateFileExistance: boolean
}

export interface CustodianConfig {
  custodianFromLoadFileField: boolean
  custodianLoadFileField: string
  custodianName: string
}

export interface MediaConfig {
  mediaFromLoadFileField: boolean
  mediaLoadFileField: string
  mediaName: string
}

export interface ImportTemplateConfig {
  templateId?: number
  templateName: string
  templateDesc?: string
  importConfig?: ImportConfig
}
export interface OverlayModel {
  identifier: string
  delimiter: string
  fieldContent: string
}

export interface DataOverlayConfig {
  isOverlayLoadFileData: boolean
  isReplaceOverlay: boolean
  overlayFieldIdentifier: string
  overlayDelimiter: string
}

export interface ImageOverlayConfig {
  processImage: boolean
  overlayReferenceFieldName: Matcher
  customFieldName: string
  copyImageToProjectLocation: boolean
  autoTiffOcr: boolean
  pageLevelReplacement: boolean
}

export enum Matcher {
  ControlNumber = 0,
  FileID = 1,
  CustomField = 2,
  BatesNumber = 3,
  NewImport = 4
}
