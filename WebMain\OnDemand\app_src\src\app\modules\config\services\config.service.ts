import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import { EMPTY, Observable, of } from 'rxjs'
import { filter, map, switchMap, take, tap } from 'rxjs/operators'
import { ResponseModel } from '../../shared/models'
import { FetchBaseSettings, FetchControlSettings } from '../store/actions'
import { ConfigState } from '../store/reducers'
import {
  getBaseSettings,
  getControlSetting,
  getControlSettings
} from '../store/selectors'

const PREFIX = '--'

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  /** ------------------------------------ Static Properties Start ------------------------------------ **/

  /**
   * Client tag for theming.
   */
  static themeClient = 'venio'

  /**
   * User Id of logged in user.
   */
  static userId = -1

  /** ------------------------------------ Static Properties End ------------------------------------ **/

  private readonly webBaseUrl: string // eg. http://localhost/venioweb

  private readonly vodBaseUrl: string // eg. http://localhost/venioweb/ondemand

  private readonly webBasePath: string // eg. /venioweb

  // Properties set during the bootstrapping of the config module
  public isVodEnabled: boolean // If VOD is enabled or not. If false, this refers to RVOD.

  public themeFilePath // Theme variables file path

  public apiUrl = '' // API URL

  public squareLogoPath = 'OnDemand/Images/venio-logo-fav.png' // Short logo path

  public logoPath = 'OnDemand/Images/venio-logo-full.png' // Default logo path

  public loginImageBackgroundPath = '' // Default login background path

  /**
   * Default favorite icon path
   */
  public favIconPath = 'OnDemand/Images/favicon-venio.ico'

  public isTwoFactorAuthenticationEnabled = false // Check whether two factor authentication is enabled

  public isIdpEnabled = false // Check whether IDP is enabled

  public companyName: string // Company Name (eg. Venio OnDemand, Ricoh, etc.)

  public venioVersion: string

  public isADEnabled = false // Check whether AD is enabled

  constructor(
    private httpClient: HttpClient,
    private store: Store<ConfigState>
  ) {
    const currentUrl = window.location.href
    this.vodBaseUrl = currentUrl.substring(
      0,
      currentUrl.toLowerCase().lastIndexOf('/appplus')
    )
    this.webBaseUrl = this.vodBaseUrl.substring(
      0,
      currentUrl.toLowerCase().lastIndexOf('/ondemand')
    )

    const baseUrl = window.location.origin
    this.webBasePath = this.webBaseUrl
      .toLowerCase()
      .replace(baseUrl.toLowerCase(), '')
  }

  /**
   * Used up as a factory method in APP_INITIALIZER token to fetch application's base settings.
   */
  resolveBaseSettings(): Observable<boolean> {
    this.store.dispatch(new FetchBaseSettings())
    return this.store.pipe(
      select(getBaseSettings),
      filter((settings) => !!settings['apiUrl']),
      tap(() => this.store.dispatch(new FetchControlSettings())),
      switchMap(() =>
        this.store.pipe(
          select(getControlSettings),
          filter((value) => typeof value !== 'undefined' || value !== null)
        )
      ),
      map(() => true),
      take(1)
    )
  }

  /**
   * Returns resolved web base url.
   */
  getWebBaseUrl(): string {
    return this.webBaseUrl
  }

  /**
   * Returns resolved web base path.
   */
  getWebBasePath(): string {
    return this.webBasePath
  }

  /**
   * Returns resolved vod base URL.
   */
  getVodBaseUrl(): string {
    return this.vodBaseUrl
  }

  /**
   * Returns OnDemand API URL.
   */
  getApiUrl(): string {
    return this.apiUrl
  }

  /**
   * Client-side service endpoint that fetches base settings from the service by called to BootstrapServive.asmx page.
   */
  fetchBaseSettings$() {
    const csrfToken = 'kdfdk-eirouye-dkjei'
    const headers = new HttpHeaders({
      __RequestVerificationToken: csrfToken
    })
    return this.httpClient.get(
      this.vodBaseUrl + '/BootstrapService.asmx/GetBaseSettings',
      {
        headers: headers
      }
    )
  }

  /**
   * Service endpoint for fetching control settings.
   */
  fetchControlSettings$() {
    return this.httpClient.get(this.apiUrl + '/config/control_settings')
  }

  /**
   * Fetch user guide help links from database
   */
  fetchHelpLinks$() {
    return this.httpClient.get(this.apiUrl + '/config/help_links')
  }

  /**
   * Service endpoint for fetching theme variables.
   * @param path Theme file path
   */
  fetchTheme$(path: string) {
    return this.httpClient.get(path)
  }

  /**
   * Service endpoint for fetch project info.
   * @param projectId Project Id
   */
  fetchProjectInfo$(projectId) {
    return this.httpClient.get(
      this.apiUrl + `/cases/project/${projectId}/case-info`
    )
  }

  /**
   * Service endpoint for setting server side sessions.
   * It does so by calling MaintainSession.aspx page.
   * @param session Map of key and values to be set in the Server-side Sessions
   */
  setServerSession$(session: Map<string, any>): Observable<any> {
    const sessionObj = {}
    session.forEach((value, key, map) => {
      sessionObj[key] = value
    })
    return this.httpClient.post(
      this.getWebBaseUrl() + '/OnDemand/MaintainSession.aspx',
      sessionObj,
      { responseType: 'text' }
    )
  }

  /**
   * Service endpoint for fetching user rights.
   * @param userId User Id
   * @param projectId Project Id
   */
  fetchUserRights$(projectId, docToken) {
    return this.httpClient.get(this.apiUrl + '/right/projectwiseuserrights', {
      params: new HttpParams()
        .set('projectId', projectId)
        .set('docToken', docToken ?? '')
    })
  }

  /**
   * Get the value of a computed style custom property.
   * @param name Custom CSS property name
   */
  readCustomProperty(name: string): string {
    const bodyStyles = window.getComputedStyle(document.body)
    return bodyStyles.getPropertyValue(PREFIX + name)
  }

  /**
   * Get the value of a control setting ENABLE_LIMITED_ONDEMAND_ACCESS.
   * This method is to provide a quick access. You can also access this property by subscribing to a store property selector.
   */
  getLimitedOnDemandAccess(): Observable<boolean> {
    return this.store.pipe(
      select(getControlSetting('ENABLE_LIMITED_ONDEMAND_ACCESS'))
    )
  }

  getVodURL(): Observable<string> {
    return this.store.pipe(select(getControlSetting('WEB_BASE_URL')))
  }

  getOAuthClient(): Observable<string> {
    return this.store.pipe(select(getControlSetting('OATH_CLIENT_ID')))
  }

  /**
   * Service endpoint for fetch user local storage info.
   * @param userId user Id
   * @param projectId project Id
   */
  fetchUserLocalstorageInfo$(projectId) {
    if (!projectId) return of(EMPTY)
    return this.httpClient.get(this.apiUrl + '/user/userlocalstorageinfo', {
      params: new HttpParams().set('projectId', projectId ?? 0)
    })
  }

  setEulaAcceptance$() {
    return this.httpClient.get(`${this.apiUrl}/user/eulaacceptance`)
  }

  /**
   * Service endpoint for fetching license status.
   * @param component component
   * @param subComponent sub component
   */
  fetchLicenseStatus$(component: string, subComponent: string) {
    return this.httpClient.get<boolean>(this.apiUrl + '/licenses/status', {
      params: new HttpParams()
        .set('component', component)
        .set('subComponent', subComponent)
    })
  }

  /**
   * Service endpoint for fetching license status.
   * @param component component
   * @param subComponent sub component
   */
  fetchLicenseValidity$(): Observable<ResponseModel> {
    return this.httpClient.get<ResponseModel>(
      this.apiUrl + '/licenses/validity'
    )
  }

  clearSession() {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json; charset=utf-8',
      DataType: 'json'
    })
    const options = { headers: headers }
    return this.httpClient.post(
      this.getVodBaseUrl() + '/VodWebService.asmx/ClearSession',
      null,
      options
    )
  }

  isSessionExpired() {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json; charset=utf-8',
      DataType: 'json'
    })
    const options = { headers: headers }
    return this.httpClient.post(
      this.getVodBaseUrl() + '/VodWebService.asmx/IsSessionExpired',
      null,
      options
    )
  }
}
