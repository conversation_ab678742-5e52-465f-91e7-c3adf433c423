import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@admin-advance/models'
import { CustomFieldsModel } from '@admin-advance/models/custom-field/custom-field.model'
import {
  CustomFieldStateSelector,
  FetchCustomFieldsWithPredefinedFieldsAction,
  GetCustodians
} from '@admin-advance/store'
import { CustodianSelector } from '@admin-advance/store/custodian/custodian.selectors'
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  QueryList,
  ViewChildren
} from '@angular/core'
import {
  FormBuilder,
  FormControlName,
  FormGroup,
  Validators
} from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ConfigState } from '@config/store/reducers'
import { getThemeClient } from '@config/store/selectors'
import { RouterReducerState } from '@ngrx/router-store'
import { select, Store } from '@ngrx/store'
import { Select, Store as XsStore } from '@ngxs/store'
import { UserRights } from '@root/helpers/user-rights'
import { ConfirmationModalComponent } from '@shared/components/confirmation-modal/confirmation-modal.component'
import {
  GenericValidator,
  MessageModel,
  validateBeforeSubmit
} from '@shared/validators'
import { StartupStateSelector } from '@stores/selectors'
import { cloneDeep } from 'lodash'
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal'
import { ToastrService } from 'ngx-toastr'
import { combineLatest, Observable, Subject } from 'rxjs'
import {
  debounceTime,
  filter,
  map,
  startWith,
  switchMap,
  takeUntil,
  withLatestFrom
} from 'rxjs/operators'
import { ChannelService } from '../../../../services/channel.service'
import { GlobalErrorAction } from '../../../../store/actions'
import {
  getRouterState,
  RouterStateUrl,
  State
} from '../../../../store/reducers'
import { CreateCustomFieldsComponent } from '../../components/create-custom-fields/create-custom-fields.component'
import { ImportProgressComponent } from '../../components/import-progress/import-progress.component'
import { ImportTemplateComponent } from '../../components/import-template/import-template.component'
import { ImportConfig, Matcher, OverlayModel } from '../../models/import-config'
import { ImportStatus } from '../../models/import-status'
import { SharedService } from '../../services/shared-service.service'
import {
  AddLoadFileToVenioFieldMapping,
  AddVenioToLoadFileFieldMapping,
  FetchCustomDelimiterAction,
  FetchImportStatus,
  RemoveLoadFileToVenioFieldMapping,
  RemoveVenioToLoadFileFieldMapping,
  ResetImportProgress,
  SetDataCustodianMediaConfigAction,
  SetDataOverlayConfigAction,
  SetImageOverlayConfigAction,
  SetValidateFileExistence,
  ShowImportSpinner,
  ShowValidateSpinner,
  StartImport,
  ValidateFieldMapping
} from '../../store/actions'
import {
  getConfigFullTextFilePathField,
  getConfigFullTextProcessOption,
  getConfigHasFullTextInLoadFile,
  getConfigImageLoadFilePath,
  getConfigImageProcessOption,
  getConfigLoadFileProcessOption,
  getConfigLoadFileToVenioMapping,
  getConfigNativeFilePathField,
  getConfigNativeProcessOption,
  getConfigUnmappedMandatoryVenioFields,
  getConfigUnmappedVenioFields,
  getConfigValidateFileExistence,
  getConfigVenioToLoadFileMapping,
  getDelimiters,
  getImportConfig,
  getImportStatus,
  isImportSpinnerShowing,
  isValidateSpinnerShowing,
  loadFileSelectionComplete,
  mapFilePathComplete
} from '../../store/selectors/import-config.selectors'
import {
  getParamsMandatoryVenioFields,
  getParamsVenioFields
} from '../../store/selectors/import-params.selectors'
import {
  getConfigUnmappedLoadFileFields,
  getSummaryLoadFileFields,
  getSummaryLoadFileFieldValues
} from '../../store/selectors/import-summary.selectors'

@Component({
  selector: 'app-fields-mapping-page',
  templateUrl: './fields-mapping-page.component.html',
  styleUrls: ['./fields-mapping-page.component.scss']
})
export class FieldsMappingPageComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  /**
   * Theme client name
   */
  client: string

  /**
   * Filter options for mapped/unmapped fields
   * TODO We can use enum for this purpose
   */
  readonly mappedFilters: string[] = ['All', 'Mapped Fields', 'Unmapped Fields']

  /**
   * Active mapped/unmapped filter
   */
  mappedFilter = 'All'

  /**
   * Active search term filter
   */
  searchTerm = ''

  /**
   * Load file field values. This will be used for samples.
   */
  summaryLoadFileFieldValues: any

  /**
   * Load file fields.
   */
  summaryLoadFileFields: string[]

  /**
   * List of venio fields.
   */
  paramsVenioFields: string[]

  /**
   * List of mandatory venio fields.
   */
  paramsMandatoryVenioFields: string[]

  /**
   * Venio to load file field mapping.
   */
  configVenioToLoadFileMapping: string[]

  /**
   * Load file to venio field mapping.
   */
  configLoadFileToVenioMapping: string[]

  /**
   * List of unmapped venio fields.
   */
  configUnmappedVenioFields: string[]

  /**
   * List of unmapped mandatory venio fields.
   */
  configUnmappedMandatoryVenioFields: string[]

  /**
   * List of unmapped load file fields.
   */
  configUnmappedLoadFileFields: string[]

  /**
   * Field in the load file that has native file paths.
   */
  configNativeFilePathField: string

  /**
   * Field in the load file that has full text file paths.
   */
  configFullTextFilePathField: string

  /**
   * Maximum number of samples to show for each load file field.
   */
  numberOfSamples = 1

  /**
   * Flag that controls the view - Load File Fields <-> Venio Fields
   */
  mapVenioFields = false

  /**
   * Map for load file field and values to show as sample.
   */
  samples: Map<string, string[]> = new Map<string, string[]>()

  /**
   * Project id for the import
   */
  projectId: number

  /**
   * Import Id for the import
   */
  importId: number

  /**
   * Reference to custom fields creation modal.
   */
  customFieldsModalRef: BsModalRef

  /**
   * Reference to import template modal
   */
  private importTemplateModalRef: BsModalRef

  /**
   * Flag that controls validation progress spinner.
   */
  validateSpinner$: Observable<boolean>

  /**
   * Flag that controls import progress spinner.
   */
  importSpinner$: Observable<boolean>

  /**
   * Import Status
   */
  importStatus: ImportStatus

  /**
   * Flag that controls load file selection status. If not, user will be prompted to go back to the step.
   */
  isLoadFileSelectionComplete = false

  /**
   * Flag that controls file path mapping status. If not, user will be prompted to go back to the step.
   */
  mapFilePathsComplete = false

  private unsubscribe$ = new Subject<void>()

  /**
   * Check if is OVerlay
   */
  isOverlay: boolean

  /**
   * Reactive form group to handle user input, validations.
   */
  overlayForm: FormGroup

  imageOverlayConfigForm: FormGroup

  custodianMediaForm: FormGroup

  /**
   * List of identifier fields.
   */
  identifierFields: string[]

  /**
   * List of identifier fields for overlay only.
   */
  extraFields: string[] = ['Control Number', 'Bates Number']

  /**
   * Selects a slice of delimiter data from the store.
   */
  delimiters: string[]

  /**
   * Stating Overall errors in a single line of message.
   */
  formErrorMessage: string

  /**
   * A generic form validation class
   */
  private genericValidator: GenericValidator

  /**
   * Directives to monitor changes so we can perform validation and update message rules accordingly.
   */
  @ViewChildren(FormControlName, { read: ElementRef })
  private readonly formInputElements: QueryList<ElementRef>

  /**
   * Object containing validation fail message defined on generic class.
   */
  displayMessage: MessageModel

  processLoadFile: boolean

  processImage: boolean

  overlayReferenceFieldNameType = Matcher

  customFields: CustomFieldsModel[] = []

  custodianList: ICustodian[] = []

  custodianNameOptions: Observable<any[]>

  custodianMediaFormSubmitted = false

  public disableValidatingFileExistence = true

  public validateFileExistence = false

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_CREATE_UPDATE_IMPORT_TEMPLATE
    )
  )
  allowToCreateEditTemplate$: Observable<boolean>

  private importProgressModalRef: BsModalRef

  constructor(
    private configStore: Store<ConfigState>,
    private store: Store<State>,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    public sharedService: SharedService,
    private channelService: ChannelService,
    private modalService: BsModalService,
    private fb: FormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private xsStore: XsStore
  ) {}

  // filter custodian name
  private _filter(name: string): any[] {
    const filterValue = name.toLowerCase()

    return this.custodianList.filter((option) =>
      option.custodianName.toLowerCase().includes(filterValue)
    )
  }

  ngOnInit() {
    // Subscribe to the theme client name.
    // TODO Change this to ConfigService.themeClient
    this.configStore
      .pipe(select(getThemeClient), takeUntil(this.unsubscribe$))
      .subscribe((client: string) => {
        this.client = client
      })

    // Set the import wizard step index
    this.sharedService.sharedProperties.importStep = 3

    // Get the project id and import id by subscribing to the router state.
    this.store
      .pipe(
        select(getRouterState),
        takeUntil(this.unsubscribe$),
        map(
          (routerState: RouterReducerState<RouterStateUrl>) => routerState.state
        )
      )
      .subscribe((routerState: RouterStateUrl) => {
        this.projectId = routerState.queryParams.projectId
        this.importId = routerState.queryParams.importId
        this.isOverlay = routerState.queryParams.isOverlay == '1' ? true : false
        if (routerState.queryParams.extUserId) {
          this.isOverlay = false
        }
      })

    // Subscribe to load file selection step completion.
    this.store
      .pipe(
        select(loadFileSelectionComplete),
        takeUntil(this.unsubscribe$),
        map((isComplete: boolean) => isComplete)
      )
      .subscribe((isComplete: boolean) => {
        this.isLoadFileSelectionComplete = isComplete
      })

    // Subscribe to file path selection step completion.
    this.store
      .pipe(
        select(mapFilePathComplete),
        takeUntil(this.unsubscribe$),
        map((isComplete: boolean) => isComplete)
      )
      .subscribe((isComplete: boolean) => {
        this.mapFilePathsComplete = isComplete
      })

    // Subscribe to the available load file fields from the import summary.
    this.store
      .pipe(select(getSummaryLoadFileFields), takeUntil(this.unsubscribe$))
      .subscribe((fields: string[]) => {
        this.summaryLoadFileFields = fields
      })

    // Subscribe to the available load file field values from the import summary.
    // These are for displaying samples.
    this.store
      .pipe(select(getSummaryLoadFileFieldValues), takeUntil(this.unsubscribe$))
      .subscribe((values) => {
        this.summaryLoadFileFieldValues = values
      })

    // Subscribe to the list of available venio fields.
    this.store
      .pipe(select(getParamsVenioFields), takeUntil(this.unsubscribe$))
      .subscribe((fields: string[]) => {
        this.paramsVenioFields = fields
      })

    // Subscribe to the list of mandatory venio fields.
    this.store
      .pipe(select(getParamsMandatoryVenioFields), takeUntil(this.unsubscribe$))
      .subscribe((fields: string[]) => {
        this.paramsMandatoryVenioFields = fields
      })

    // Subscribe to the list of unmapped venio fields.
    this.store
      .pipe(select(getConfigUnmappedVenioFields), takeUntil(this.unsubscribe$))
      .subscribe((fields: string[]) => {
        if (this.isOverlay) {
          this.configUnmappedVenioFields = fields
            .concat(this.extraFields.filter((item) => fields.indexOf(item) < 0))
            .sort(function (a, b) {
              return a.localeCompare(b) //using String.prototype.localCompare()
            })
        } else {
          this.configUnmappedVenioFields = fields
        }
      })

    // Subscribe to the list of unmapped mandatory venio fields.
    this.store
      .pipe(
        select(getConfigUnmappedMandatoryVenioFields),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((fields: string[]) => {
        this.configUnmappedMandatoryVenioFields = fields
      })

    // Subscribe to the list of unmapped load file fields.
    this.store
      .pipe(
        select(getConfigUnmappedLoadFileFields),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((fields: string[]) => {
        this.configUnmappedLoadFileFields = fields
      })

    // Subscribe to the native file path field.
    // If set, this will be automatically populated in the mapping table.
    combineLatest([
      this.store.select(getConfigNativeProcessOption),
      this.store.select(getConfigNativeFilePathField)
    ])
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(([importNative, nativeMappedField]) => {
        if (importNative) {
          this.configNativeFilePathField = nativeMappedField
          this.onVenioFieldAdd(nativeMappedField, null)
        } else {
          this.configNativeFilePathField = null
          this.onVenioFieldRemove(nativeMappedField, null)
        }
      })

    // Subscribe to the full text file path field.
    // If set, this will be automatically populated in the mapping table.
    combineLatest([
      this.store.select(getConfigFullTextProcessOption),
      this.store.select(getConfigFullTextFilePathField)
    ])
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(([importFulltext, fulltextMappedField]) => {
        if (importFulltext) {
          this.configFullTextFilePathField = fulltextMappedField
          this.onVenioFieldAdd(fulltextMappedField, null)
        } else {
          this.configFullTextFilePathField = null
          this.onVenioFieldRemove(fulltextMappedField, null)
        }
      })

    // Subscribe to venio fields to load file fields mapping.
    this.store
      .pipe(
        select(getConfigVenioToLoadFileMapping),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((mapping: string[]) => {
        this.configVenioToLoadFileMapping = mapping
        this.identifierFields = Object.keys(
          this.configVenioToLoadFileMapping
        ).filter(function (el) {
          return el != 'null'
        })
        this.refreshSamples()
      })

    // Subscribe to load file fields to venio fields mapping.
    this.store
      .pipe(
        select(getConfigLoadFileToVenioMapping),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((mapping: string[]) => {
        this.configLoadFileToVenioMapping = mapping
      })

    // Subscribe to validation task progress spinner.
    this.validateSpinner$ = this.store.pipe(
      select(isValidateSpinnerShowing),
      takeUntil(this.unsubscribe$)
    )

    // Subscribe to import task progress spinner.
    this.importSpinner$ = this.store.pipe(
      select(isImportSpinnerShowing),
      takeUntil(this.unsubscribe$)
    )

    // Subscribe to import status.
    this.store
      .pipe(select(getImportStatus), takeUntil(this.unsubscribe$))
      .subscribe((importStatus: ImportStatus) => {
        this.importStatus = importStatus
      })

    //Initialize form if is overlay.
    if (this.isOverlay) {
      this.initFormForOverlay()
      this.updateFormOverlayValidation()
    }
    this.getConfigOptions()
    this.initFormForCustodianMedia()
    this.getCustodianList()
    this.custodianNameValueChanges()

    // this.xsStore
    // .select(CustodianSelector.SliceOf('custodianList'))
    // .pipe(debounceTime(500), takeUntil(this.unsubscribe$))
    // .subscribe((res: ICustodian[]) => {
    //   this.custodianList = res
    // })

    this.loadValidateFileExistence()
  }

  public loadValidateFileExistence(): void {
    this.store
      .pipe(
        select(getConfigValidateFileExistence),
        withLatestFrom(
          this.store.select(getConfigFullTextProcessOption),
          this.store.select(getConfigNativeProcessOption),
          this.store.select(getConfigImageProcessOption),
          this.store.select(getConfigHasFullTextInLoadFile)
        ),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(
        ([
          validateFileExistence,
          fullTextEnabled,
          nativeEnabled,
          imageEnabled,
          hasFullTextInLoadFile
        ]) => {
          // if native, image or full text is enabled, then enable validating file existence checkbox
          // if only full text is enabled but has full text in load file, then disable validating file existence checkbox
          if (
            nativeEnabled ||
            imageEnabled ||
            (fullTextEnabled && !hasFullTextInLoadFile)
          ) {
            this.disableValidatingFileExistence = false
            this.validateFileExistence = validateFileExistence
          } else {
            this.disableValidatingFileExistence = true
            this.validateFileExistence = false
          }
        }
      )
  }

  // filter value according
  custodianNameValueChanges() {
    this.custodianNameOptions = this.custodianMediaForm
      .get('custodian.custodianName')
      .valueChanges.pipe(
        startWith(''),
        map((value) => {
          const name = typeof value === 'string' ? value : value?.custodianName
          return name
            ? this._filter(name as string)
            : this.custodianList.slice()
        })
      )
  }

  ngAfterViewInit() {
    //Initialize form if is overlay.
    if (this.isOverlay) {
      this.store.dispatch(new FetchCustomDelimiterAction())

      // Subscribe to import status.
      this.store
        .pipe(select(getDelimiters), takeUntil(this.unsubscribe$))
        .subscribe((delimiters: string[]) => {
          this.delimiters = delimiters
        })

      this.enableDisableForm()

      this.initValidationRules()

      this.validationWatcher()

      this.getCustomFields()
    }
  }

  getCustomFields(): void {
    this.xsStore
      .dispatch(new FetchCustomFieldsWithPredefinedFieldsAction(this.projectId))
      .pipe(
        switchMap(() =>
          this.xsStore.select(
            CustomFieldStateSelector.SliceOf('customFieldsWithPredefinedField')
          )
        ),
        filter((c) => !!c),
        takeUntil(this.unsubscribe$)
      )
      .subscribe({
        next: (d: CustomFieldsModel[]) => [
          (this.customFields = d.map((c) => c.displayName))
        ]
      })
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()

    if (this.customFieldsModalRef) {
      this.customFieldsModalRef.hide()
    }

    if (this.importTemplateModalRef) {
      this.importTemplateModalRef.hide()
    }
  }

  getConfigOptions(): void {
    this.store
      .pipe(
        select(getConfigLoadFileProcessOption),
        takeUntil(this.unsubscribe$),
        map((loadFileProcessOption: boolean) => loadFileProcessOption)
      )
      .subscribe((loadFileProcessOption: boolean) => {
        this.processLoadFile = loadFileProcessOption
        if (loadFileProcessOption && this.isOverlay) {
          const overlayFieldIdentifierForm = this.overlayForm.get(
            'overlayFieldIdentifier'
          )
          overlayFieldIdentifierForm.reset()
          overlayFieldIdentifierForm.setValidators(Validators.required)
          overlayFieldIdentifierForm.updateValueAndValidity()
        }
        this.overlayForm?.patchValue({
          isOverlayLoadFileData: this.processLoadFile ?? false
        })
      })

    combineLatest([
      this.store.pipe(select(getConfigImageProcessOption)),
      this.store.pipe(select(getConfigImageLoadFilePath))
    ])
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(([imageProcessOption, imageFilePath]: [boolean, string]) => {
        this.processImage =
          imageProcessOption && imageFilePath?.trim().length > 0
        this.imageOverlayConfigForm?.patchValue({
          processImage: imageProcessOption
        })
      })
  }

  /**
   * Toggle the view between 'Load File Field <-> Venio Field' mapping.
   */
  toggleFieldMapper() {
    if (this.mapVenioFields === true) {
      this.setMapVenioFieldsFlag(false)
    } else {
      this.setMapVenioFieldsFlag(true)
    }
  }

  /**
   * Sets the flag that controls whether we are mapping Venio Fields to Load File Fields or,
   * Load File Fields to Venio Fields.
   * @param mapMandatoryFields
   */
  setMapVenioFieldsFlag(mapMandatoryFields: boolean): void {
    this.mapVenioFields = mapMandatoryFields
  }

  /**
   * Get the list of samples for the provided load file field.
   * @param loadFileFieldName Load File Field Name
   */
  fieldSamples(loadFileFieldName: string): string[] {
    const values: string[] = []
    if (!this.summaryLoadFileFieldValues) {
      return values
    }
    // Iterate through each row and add the sample field value until we reach the max number of samples.
    for (const row of this.summaryLoadFileFieldValues) {
      const value = row[loadFileFieldName]
      if (value && value.length > 0) {
        values.push(value)
        if (values.length === this.numberOfSamples) {
          break
        }
      }
    }
    return values
  }

  /**
   * Handles when a venio field is selected in the multiselect box while mapping load file to venio field(s).
   * @param loadFileField Load File Field
   * @param venioField Venio Field
   */
  onVenioFieldAdd = (loadFileField: string, venioField: string): void => {
    this.store.dispatch(
      new AddLoadFileToVenioFieldMapping(loadFileField, venioField)
    )
  }

  /**
   * Handles on venio field removed.
   * One load file can be mapped to multiple venio fields. This is the case where one of the venio fields is cleared
   * from the multiselect box.
   * @param loadFileField Load File Field
   * @param event Event
   */
  onVenioFieldRemove = (loadFileField: string, event: any) => {
    this.store.dispatch(
      new RemoveLoadFileToVenioFieldMapping(loadFileField, event?.value)
    )
  }

  /**
   * Handles on venio field cleared.
   * One load file can be mapped to multiple venio fields. This is the case where venio fields are all cleared
   * from the multiselect box.
   * @param loadFileField Load File Field
   */
  onVenioFieldClear = (loadFileField: string) => {
    this.store.dispatch(
      new RemoveLoadFileToVenioFieldMapping(loadFileField, null)
    )
  }

  /**
   * Handles on load file field change event.
   * @param venioField Venio Field
   * @param loadFileField Load File Field
   */
  onLoadFileFieldChange = (venioField: string, loadFileField: string) => {
    this.loadSamples(venioField, loadFileField)

    if (!loadFileField) {
      this.store.dispatch(new RemoveVenioToLoadFileFieldMapping(venioField))
    } else {
      this.store.dispatch(new RemoveVenioToLoadFileFieldMapping(venioField))
      this.store.dispatch(
        new AddVenioToLoadFileFieldMapping(venioField, loadFileField)
      )
    }
  }

  /**
   * Load samples for the field mapping table.
   * @param venioField Venio Field
   * @param loadFileField Load File Field
   */
  private loadSamples = (venioField, loadFileField) => {
    // See if the venio field is mapped to load file field,
    // if not, remove the samples from view.
    if (!loadFileField) {
      if (this.samples.has(venioField)) {
        this.samples.delete(venioField)
      }
    } else {
      // If it is mapped, then set the map of samples.
      const samples = this.fieldSamples(loadFileField)
      this.samples.set(venioField, samples)
    }
  }

  /**
   * Updates the samples in the field mapping page.
   */
  private refreshSamples = (): void => {
    if (!this.configVenioToLoadFileMapping) {
      return
    }

    this.samples.clear()

    Object.keys(this.configVenioToLoadFileMapping).forEach((key) => {
      this.loadSamples(key, this.configVenioToLoadFileMapping[key])
    })
  }

  /**
   * Handles search term change.
   * @param event
   */
  onSearchChanged(event): void {
    // Set the search term so that it can be used by the pipe for filtering the fields.
    this.searchTerm = event.target.value
  }

  /**
   * Navigates to resource folder selection page.
   */
  onPreviousClick(): void {
    if (this.isOverlay && !this.processLoadFile && this.processImage)
      this.router.navigate(['../load_file'], {
        relativeTo: this.route,
        queryParamsHandling: 'preserve'
      })
    else
      this.router.navigate(['../resource_folders'], {
        relativeTo: this.route,
        queryParamsHandling: 'preserve'
      })
  }

  /**
   * Handles on validation button click.
   */
  onValidateClick(): void {
    this.formErrorMessage = null
    let body: OverlayModel = null
    /**
     * Ensures the form is valid and Marks all controls as dirty to propagate validation
     * states so the end user can see the validation rules appearing.
     */
    if (this.isOverlay) {
      const errorMessage = validateBeforeSubmit(this.overlayForm)
      if (errorMessage) {
        this.formErrorMessage = errorMessage
        return
      }
      body = this.overlayForm.value
    }

    // If the load file selection is not complete, prompt user to go back and complete the step.
    // This probably happens if user directly tries to access this page without completing the previous step.
    if (!this.isLoadFileSelectionComplete && this.processLoadFile) {
      this.sharedService.openLoadFileSelectionIncompleteModal()
      return
    }
    // If the file path selection is not complete, prompt user to go back and complete the step.
    if (!this.mapFilePathsComplete && this.processLoadFile) {
      this.sharedService.openMapFilePathIncompleteModal()
      return
    }

    // User must map the mandatory fields.
    if (!this.areMandatoryFieldsMapped()) {
      return
    }
    this.setOverlayConfigs()
    this.store.dispatch(new ResetImportProgress()) // User should not see junk import progress from previous operation
    this.store.dispatch(new ShowValidateSpinner())
    this.sharedService.sharedProperties.validateClick = true
    const subscription = this.store
      .pipe(select(getImportConfig))
      .subscribe((importConfig: ImportConfig) => {
        const _importConfig = cloneDeep(importConfig)
        if (
          (_importConfig?.imageLoadFile === null ||
            _importConfig?.imageLoadFile?.filePath === null ||
            _importConfig?.imageLoadFile?.filePath === '') &&
          _importConfig.image !== null
        ) {
          _importConfig.image.processImage = false
        }
        if (
          _importConfig.fullText.processFullText &&
          !_importConfig.fullText.hasExtractedTextInLoadFile
        ) {
          _importConfig.fullText.validateFileExistance =
            this.validateFileExistence
        }
        if (importConfig.native.processNative) {
          _importConfig.native.validateFileExistance =
            this.validateFileExistence
        }
        if (_importConfig.image.processImage) {
          _importConfig.image.validateFileExistance = this.validateFileExistence
        }
        this.store.dispatch(
          new ValidateFieldMapping({
            projectId: this.projectId,
            importId: this.importId,
            importConfig: _importConfig,
            isOverlay: this.isOverlay,
            overlay: body
          })
        )
      })

    subscription.unsubscribe()
    this.openImportProgressModal()
  }

  setOverlayConfigs(): void {
    this.store.dispatch(new SetDataOverlayConfigAction(this.overlayForm?.value))
    this.store.dispatch(
      new SetImageOverlayConfigAction(this.imageOverlayConfigForm?.value)
    )
    this.store.dispatch(
      new SetDataCustodianMediaConfigAction(
        this.custodianMediaForm?.value.custodian,
        this.custodianMediaForm?.value.media
      )
    )
  }

  /**
   * Handles import button click.
   */
  onImportClick(): void {
    this.store.dispatch(new FetchImportStatus(this.projectId, this.importId))
    // check custodianMediaForm Validation
    if (!this.isOverlay) {
      const errorMessage = validateBeforeSubmit(this.custodianMediaForm)
      if (errorMessage) {
        this.custodianMediaFormSubmitted = true
        this.formErrorMessage = errorMessage
        window.scroll(0, 0)
        return
      } else {
        this.custodianMediaFormSubmitted = false
      }
    }

    // If the load file selection is not complete, prompt user to go back and complete the step.
    // This probably happens if user directly tries to access this page without completing the previous step.
    if (!this.isLoadFileSelectionComplete && this.processLoadFile) {
      this.sharedService.openLoadFileSelectionIncompleteModal()
      return
    }
    // If the file path selection is not complete, prompt user to go back and complete the step.
    if (!this.mapFilePathsComplete && this.processLoadFile) {
      this.sharedService.openMapFilePathIncompleteModal()
      return
    }

    // User must map the mandatory fields.
    if (!this.areMandatoryFieldsMapped()) {
      return
    }

    // If the phase is not 4, this means that it hasn't completed validation phase.
    // Prompt user with the warning.
    if (this.importStatus && this.importStatus.phase < 4) {
      const modal = this.modalService.show(
        ConfirmationModalComponent,
        Object.assign({}, { ignoreBackdropClick: true })
      )
      if (this.isOverlay) {
        ;(<ConfirmationModalComponent>modal.content).showConfirmationModal(
          'Overlay Confirmation',
          'Looks like no validation was done. Do you still want to overlay?',
          'Overlay',
          'Cancel'
        )
      } else {
        ;(<ConfirmationModalComponent>modal.content).showConfirmationModal(
          'Import Confirmation',
          'Looks like no validation was done. Do you still want to import?',
          'Import',
          'Cancel'
        )
      }
      ;(<ConfirmationModalComponent>modal.content).onClose.subscribe(
        (result) => {
          if (result === true) {
            this.doImport()
          } else if (result === false) {
            return
          }
        }
      )
    } else {
      this.doImport()
    }
  }

  /**
   * Starts the import.
   * @private
   */
  private doImport(): void {
    this.formErrorMessage = null
    let body: OverlayModel = null
    /**
     * Ensures the form is valid and Marks all controls as dirty to propagate validation
     * states so the end user can see the validation rules appearing.
     */
    if (this.isOverlay) {
      const errorMessage = validateBeforeSubmit(this.overlayForm)
      if (errorMessage) {
        this.formErrorMessage = errorMessage
        return
      }
      body = this.overlayForm.value
    }
    this.setOverlayConfigs()
    this.store.dispatch(new ResetImportProgress()) // User should not see junk import progress from previous operation
    this.store.dispatch(new ShowImportSpinner()) // Set the store prop to show the spinner.

    const subscription = this.store
      .pipe(select(getImportConfig))
      .subscribe((importConfig: ImportConfig) => {
        const _importConfig = cloneDeep(importConfig)
        if (
          (_importConfig?.imageLoadFile === null ||
            _importConfig?.imageLoadFile?.filePath === null ||
            _importConfig?.imageLoadFile?.filePath === '') &&
          _importConfig.image !== null
        ) {
          _importConfig.image.processImage = false
        }

        // If fulltext is extracted from the load file, then we don't need to set the folder path and field path, and copyfulltext needs to be set as true.
        if (_importConfig?.fullText.hasExtractedTextInLoadFile) {
          _importConfig.fullText.fieldWithPath = null
          _importConfig.fullText.folderPath = null
          _importConfig.fullText.copyFullText = true
        }

        this.store.dispatch(
          new StartImport({
            projectId: this.projectId,
            importId: this.importId,
            importConfig: _importConfig,
            overlay: body,
            isOverlay: this.isOverlay
          })
        )
      })

    subscription.unsubscribe()
    this.openImportProgressModal()
  }

  /**
   * Opens the modal to create custom fields.
   */
  onCreateCustomFieldsClicked(): void {
    this.customFieldsModalRef = this.modalService.show(
      CreateCustomFieldsComponent,
      Object.assign({}, { class: 'modal-lg', ignoreBackdropClick: true })
    )
    this.customFieldsModalRef.content.modalRef = this.customFieldsModalRef
  }

  onSaveTemplateClick(): void {
    if (!this.isLoadFileSelectionComplete) {
      this.sharedService.openLoadFileSelectionIncompleteModal()
      return
    }

    if (!this.mapFilePathsComplete) {
      this.sharedService.openMapFilePathIncompleteModal()
      return
    }

    if (!this.areMandatoryFieldsMapped()) {
      return
    }

    this.importTemplateModalRef = this.modalService.show(
      ImportTemplateComponent,
      Object.assign({}, { class: 'modal-md', ignoreBackdropClick: true })
    )
  }

  public onValidateFileExistenceChange(value: boolean): void {
    this.store.dispatch(new SetValidateFileExistence(value))
  }

  /**
   * Checks whether the mandatory fields are mapped.
   */
  private areMandatoryFieldsMapped(): boolean {
    if (!this.isOverlay) {
      if (
        this.configUnmappedMandatoryVenioFields &&
        this.configUnmappedMandatoryVenioFields.length > 0
      ) {
        this.store.dispatch(
          new GlobalErrorAction(
            new Error('Not all mandatory fields were mapped'),
            true,
            false
          )
        )
        return false
      }
    }

    return true
  }

  /**
   * Init form
   */
  private initFormForOverlay() {
    this.overlayForm = this.fb.group({
      overlayFieldIdentifier: [''],
      isReplaceOverlay: [true],
      isOverlayLoadFileData: [true],
      overlayDelimiter: [{ value: '', disabled: true }]
    })
    this.imageOverlayConfigForm = this.fb.group({
      processImage: [false],
      overlayReferenceFieldName: [this.overlayReferenceFieldNameType.FileID],
      customFieldName: [''],
      copyImageToProjectLocation: [true],
      autoTiffOcr: [false],
      pageLevelReplacement: [false]
    })
  }

  initFormForCustodianMedia() {
    this.custodianMediaForm = this.fb.group({
      custodian: this.fb.group({
        custodianFromLoadFileField: [true],
        custodianLoadFileField: ['', Validators.required],
        custodianName: ['', Validators.required]
      }),
      media: this.fb.group({
        mediaFromLoadFileField: [true],
        mediaLoadFileField: ['', Validators.required],
        mediaName: ['', Validators.required]
      })
    })
  }

  private updateFormOverlayValidation(): void {
    this.imageOverlayConfigForm
      .get('overlayReferenceFieldName')
      .valueChanges.pipe(takeUntil(this.unsubscribe$))
      .subscribe((res: Matcher) => {
        if (res === this.overlayReferenceFieldNameType.CustomField) {
          const selectedCustomFieldForm =
            this.imageOverlayConfigForm.get('customFieldName')
          selectedCustomFieldForm.reset()
          selectedCustomFieldForm.setValidators(Validators.required)
          selectedCustomFieldForm.updateValueAndValidity()
        }
      })
  }

  /**
   * Enable/Disable form control
   */
  enableDisableForm() {
    this.overlayForm
      .get('isReplaceOverlay')
      .valueChanges.pipe(takeUntil(this.unsubscribe$))
      .subscribe((formEnable: boolean) => {
        if (!formEnable) this.overlayForm.get('overlayDelimiter').enable()
        else this.overlayForm.get('overlayDelimiter').disable()
      })
  }

  /**
   * This validation watcher goes to `ngAfterViewInit` life cycle hook because of we need
   * to track the inputs, elements and render the validation rule messages accordingly.
   *
   * Compares the rule with user input based on validation sets of form group control and populates
   * messages which is defined in `initValidationRules` fn.
   */
  private validationWatcher(): void {
    this.genericValidator
      .initValidationProcess(this.overlayForm, this.formInputElements)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: (m) => [this.cdr.markForCheck(), (this.displayMessage = m)]
      })
  }

  /**
   * A generic validation rules, properties with message.
   * Must be called before the form being initialized.
   */
  private initValidationRules(): void {
    this.genericValidator = new GenericValidator({
      // the parent properties are same as the form group property and
      // the child properties are the either of angular validator or custom property
      overlayFieldIdentifier: {
        required: 'Overlay Field Identifier is a required field.'
      }
    })
  }

  private getCustodianList() {
    this.xsStore
      .dispatch(new GetCustodians(this.projectId))
      .pipe(
        switchMap(() =>
          this.xsStore.select(CustodianSelector.SliceOf('custodianList'))
        ),
        debounceTime(800),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((res) => {
        this.custodianList = res
      })
  }

  openImportProgressModal() {
    this.importProgressModalRef = this.modalService.show(
      ImportProgressComponent,
      Object.assign({}, { class: 'modal-md', ignoreBackdropClick: true })
    )
    this.importProgressModalRef.content.modalRef = this.importProgressModalRef
  }
}
