import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { MediaProcessingStatusDialogComponent } from './media-processing-status-dialog.component'

describe('MediaProcessingStatusDialogComponent', () => {
  let component: MediaProcessingStatusDialogComponent
  let fixture: ComponentFixture<MediaProcessingStatusDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [MediaProcessingStatusDialogComponent],
      providers: [provideMockStore({})]
    }).compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(MediaProcessingStatusDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
