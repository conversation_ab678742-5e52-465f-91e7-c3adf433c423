import { ClientModel } from '@admin-advance/models/client-management/client-management.model'
import { GetTranscript, TranscriptStateSelector } from '@admin-advance/store'
import { FetchIfRepositoryConfiguredAction } from '@admin-advance/store/case/case.actions'
import { CaseStateSelector } from '@admin-advance/store/case/case.selectors'
import { ClientListAction } from '@admin-advance/store/client-management/client-management.actions'
import { ClientMgmtStateSelector } from '@admin-advance/store/client-management/client-management.selectors'
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling'
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild
} from '@angular/core'
import { FormControl } from '@angular/forms'
import { MatCheckboxChange } from '@angular/material/checkbox'
import { MatDialog, MatDialogRef } from '@angular/material/dialog'
import { Title } from '@angular/platform-browser'
import { ActivatedRoute, Router } from '@angular/router'
import { UserLocalStorageModel } from '@config/models'
import { FetchProjectInfo, ResetActiveProjectInfo } from '@config/store/actions'
import { ConfigState } from '@config/store/reducers'
import { select, Store } from '@ngrx/store'
import { Navigate } from '@ngxs/router-plugin'
import { Select, Store as XSStore } from '@ngxs/store'
import { ReviewDataSourceType } from '@review/models/review.model'
import {
  ResetReviewState,
  ResetSelectedReviewLayout,
  ReviewSetStateSelector,
  SaveSelectedTagsFilter,
  SetSelectedReviewSetId
} from '@review/xs-store'
import { LaunchpadService } from '@root/modules/launchpad/services/launchpad.service'
import {
  IframeMessengerService,
  MessageType
} from '@root/modules/micro-apps/config/iframe-messenger.service'
import { AppIdentitiesTypes } from '@root/modules/micro-apps/models/app-identities-types'
import {
  ActionType,
  LaunchpadNextEvent
} from '@root/modules/micro-apps/models/launchpad-next.model'
import { ReportsContainerComponent } from '@root/modules/micro-apps/review-next/components/reports-container/reports-container.component'
import { Production2FormService } from '@root/modules/production2/services/production-form.service'
import { ServiceUploadContainerComponent } from '@root/modules/service-upload/service-upload-container.component'
import {
  CardHeaderModel,
  CardHeaderToolbarModel,
  NestedObjectKeys,
  ProjectReviewSetInfo
} from '@shared/models'
import { NavigateToAction } from '@shared/store/actions'
import { clearSearchResponse } from '@shared/store/actions/search.actions'
import {
  DebounceTimer,
  ReviewNavigation,
  useNavigationState
} from '@shared/utils'
import { InsertProjectLoginDetails } from '@shared/xsStore'
import {
  FetchProjectList,
  FetchUserLocalStorageInfo,
  FetchUserRightsAction,
  SetSelectedFolderScope,
  SetSelectedMediaScope
} from '@stores/actions'
import {
  CaseSelectors,
  ReviewSelectors,
  StartupStateSelector
} from '@stores/selectors'
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal'
import { ToastrService } from 'ngx-toastr'
import { combineLatest, fromEvent, Observable, of, Subject } from 'rxjs'
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs/operators'
import { UserRights } from 'src/app/helpers/user-rights'
import {
  FetchUser,
  SetServerSideSession
} from 'src/app/modules/auth/store/actions'
import { ConfigService } from 'src/app/modules/config/services/config.service'
import { InitUploadHistoryQueryAction } from '../../../../stores/actions'
import { SetVODRSettings } from '../../../../stores/actions/production.actions'
import * as ReviewActions from '../../../../stores/actions/review.actions'
import * as UActions from '../../../../stores/actions/upload.actions'
import {
  ReviewSetFilterType,
  SharedDocDetails,
  SharedDocRequestType
} from '../../../../stores/models'
import { UploadStateSelector } from '../../../../stores/selectors'
import { Client, User } from '../../../auth/models/user.model'
import {
  getClientInfo,
  getUserDetails
} from '../../../auth/store/selectors/access.selectors'
import {
  getBaseSetting,
  getControlSettings,
  getProjectInfo,
  getThemeClient
} from '../../../config/store/selectors'
import { ProductionReproduceService } from '../../../production2/services/production-reproduce.service'
import { animateHeight, fadeInUpDown, fadeInX } from '../../../shared/animation'
import { ConfirmationDialogComponent } from '../../components/confirmation-dialog/confirmation-dialog.component'
import { InviteToUploadComponent } from '../../components/invite-to-upload/invite-to-upload.component'
import { MessagePopUpComponent } from '../../components/message-pop-up/message-pop-up.component'
import { CaseModel } from '../../models/case.model'
import { LegalHoldModel, LegalHoldSummary } from '../../models/legal-hold.model'
import { StateErrorStateSelector } from '../../store'
import {
  ClearCaseList,
  ClearStoreProperty,
  FetchCases,
  GetExtUsersListToInvite,
  GetProjectMediaStatus,
  GetProjectRightList,
  GetUsersListToInvite
} from '../../store/actions'
import {
  DeleteLegalHold,
  FetchLegalHolds,
  FetchLegalHoldSummary
} from '../../store/legal-hold/legal-hold.actions'
import { LegalHoldStateSelector } from '../../store/legal-hold/legal-hold.selectors'
import { CaseState } from '../../store/reducers/case.reducers'
import {
  caseRightListInfo,
  getCases,
  projectMediaStatus
} from '../../store/selectors/case.selectors'
import { CheckStateErrorExistAction } from '../../store/state-error/state-error.actions'
import { SearchQueryModule } from './../../../shared/models/documents.model'
import { CreateHoldComponent } from './modal/create-hold/create-hold.component'
import { StateErrorDetailComponent } from './modal/state-error-detail/state-error-detail.component'

enum PanelType {
  Case,
  SharedDoc,
  ReviewSet
}

enum CardHeaderItem {
  Reprocessing = 'REPROCESSING',
  Upload = 'UPLOAD',
  UploadInvite = 'UPLOAD_INVITE',
  Analyze = 'ANALYZE',
  Review = 'REVIEW',
  Production = 'PRODUCTION',
  Status = 'STATUS',
  Reproduce = 'REPRODUCE'
}

enum CaseSortType {
  Id = 'projectId',
  Name = 'projectName'
}

/**
 * Enum for Reports Types.
 * There could be more types in the future that we extend here.
 */
export enum ReportTypes {
  LOG_IN_OUT_REPORTS = 'Login & Logout Report',
  LOCKED_USERS_REPORTS = 'Locked Users Report',
  UNLOCKED_USERS_REPORTS = 'Unlocked Users Report',
  CREATION_AND_DEACTIVATION_REPORTS = 'Creation & Deactivation Report',
  DATA_EXPORT_AND_DOWNLOAD_REPORTS = 'Data Export & Download Report',
  ROLE_CHANGE_REPORTS = 'Role Change Report',
  DELETED_EXPORTS = 'Deleted Exports',
  PROJECT_ACCESS_REPORT = 'Project Access Report',
  ACTIVITY_REPORT = 'Activity Report',
  ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT = 'Activated & Deactivated Custodian Report',
  MATTER_DETAIL_REPORT = 'Matter Detail Report'
  // Add more types here
}

@Component({
  selector: 'app-case-launchpad',
  templateUrl: './case-launchpad.component.html',
  styleUrls: ['./case-launchpad.component.scss'],
  animations: [animateHeight, fadeInUpDown, fadeInX]
})
export class CaseLaunchpadComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild('caseListVirtualScrollViewPort', { static: false })
  virtualScroll: CdkVirtualScrollViewport

  /**
   * Confirmation wrapper to ensure deletion.
   */
  @ViewChild('removeConfirm')
  private readonly confirmDel: TemplateRef<any>

  PANEL_TYPE = PanelType

  CASE_SORT_TYPE = CaseSortType

  @ViewChild('searchInput', { static: false }) searchInputRef: ElementRef

  @ViewChild('clientInput', { static: false }) clientInputRef: ElementRef

  dialogRef: MatDialogRef<CreateHoldComponent>

  // List of legal holds`

  holds: LegalHoldModel[]

  summary: LegalHoldSummary

  source: 'draft' | 'issued' | 'lifted'

  /**
   * Tag name of the client for theming.
   */
  client: string

  companyName: string

  bsModalRef: BsModalRef

  /**
   * User Details
   */
  userDetails: User

  mediaStatus: any

  case: any

  module: string

  message: string

  navUrl: string

  resultType: string

  rightListInfo: any

  get isVenioAdmin(): boolean {
    return (
      this.userDetails?.globalRoleName?.trim()?.toLowerCase() === 'venio admin'
    )
  }

  isInviteUserJustClosed: boolean

  linkButtonClicked = false

  isInAppRouting = false

  enableLimitedOnDemandAccess = true

  showProjectSizeOnDemand = false

  filteredMenuItems$: Observable<any[]>

  // Define an array of menu items with their titles and icons
  menuItems = [
    {
      title: 'Login / Logout Report',
      icon: 'fa fa-sign-in-alt',
      type: ReportTypes.LOG_IN_OUT_REPORTS,
      requiredRight: UserRights.ALLOW_TO_VIEW_SYSTEM_LOGIN_LOGOUT_REPORT
    },
    {
      title: 'Locked Users Report',
      icon: 'fas fa-lock',
      type: ReportTypes.LOCKED_USERS_REPORTS,
      requiredRight: UserRights.ALLOW_TO_VIEW_LOCKED_USERS_REPORT
    },
    // TODO: future implementation
    // {
    //   title: 'Unlocked Users Report',
    //   icon: 'fas fa-unlock-alt',
    //   type: ReportTypes.UNLOCKED_USERS_REPORTS
    // },
    {
      title: 'Creation & Deactivation Report',
      icon: 'fas fa-user-cog',
      type: ReportTypes.CREATION_AND_DEACTIVATION_REPORTS,
      requiredRight:
        UserRights.ALLOW_TO_VIEW_USER_CREATION_AND_DEACTIVATION_REPORTS
    },
    {
      title: 'Data Export & Download Report',
      icon: 'fas fa-file-export',
      type: ReportTypes.DATA_EXPORT_AND_DOWNLOAD_REPORTS,
      requiredRight: UserRights.ALLOW_TO_VIEW_EXPORT_AND_DOWNLOAD_REPORT
    },
    {
      title: 'Role Change Report',
      icon: 'fas fa-exchange-alt',
      type: ReportTypes.ROLE_CHANGE_REPORTS,
      requiredRight: UserRights.ALLOW_TO_VIEW_ROLE_CHANGE_REPORT
    },
    {
      title: 'Deleted Export Report',
      icon: 'fas fa-trash',
      type: ReportTypes.DELETED_EXPORTS,
      requiredRight: UserRights.ALLOW_TO_GENERATE_PRODUCTION_DELETE_REPORT
    },
    {
      title: 'Project Access Report',
      icon: 'fas fa-key',
      type: ReportTypes.PROJECT_ACCESS_REPORT,
      requiredRight: UserRights.ALLOW_TO_VIEW_PROJECT_ACCESS_REPORT
    },
    {
      title: 'Activity Report',
      icon: 'fas fa-clipboard-list',
      type: ReportTypes.ACTIVITY_REPORT,
      requiredRight: UserRights.ALLOW_TO_VIEW_USER_ACTIVITY_REPORT
    },
    {
      title: 'Active & Terminated Custodian Report',
      icon: 'fa fa-sign-in-alt',
      type: ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT,
      requiredRight:
        UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_ACTIVE_TERMINATED_CUSTODIAN_DETAIL_REPORT
    },
    {
      title: 'Matter Detail Report',
      icon: 'fa fa-sign-in-alt',
      type: ReportTypes.MATTER_DETAIL_REPORT,
      requiredRight: UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_CUSTODIAN_DETAIL_REPORT
    }
  ]

  legalholdReportMenuItems = [
    {
      title: 'Active & Terminated Custodian Report',
      icon: 'fa fa-sign-in-alt',
      type: ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT,
      requiredRight:
        UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_ACTIVE_TERMINATED_CUSTODIAN_DETAIL_REPORT
    },
    {
      title: 'Matter Detail Report',
      icon: 'fa fa-sign-in-alt',
      type: ReportTypes.MATTER_DETAIL_REPORT,
      requiredRight: UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_CUSTODIAN_DETAIL_REPORT
    }
  ]

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_SYSTEM_LOGIN_LOGOUT_REPORT
    )
  )
  allowLogInOutReports$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_LOCKED_USERS_REPORT
    )
  )
  allowLockedUsersReports$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_USER_CREATION_AND_DEACTIVATION_REPORTS
    )
  )
  allowCreationAndDeactivationReports$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_EXPORT_AND_DOWNLOAD_REPORT
    )
  )
  allowDataExportReports$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_ROLE_CHANGE_REPORT
    )
  )
  allowRoleChangeReports$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_GENERATE_PRODUCTION_DELETE_REPORT
    )
  )
  allowDeletedExports$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_PROJECT_ACCESS_REPORT
    )
  )
  allowProjectAccessReports$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_USER_ACTIVITY_REPORT
    )
  )
  allowActivityReports$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_CUSTODIAN_DETAIL_REPORT
    )
  )
  allLegalHoldDetailReport$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_ACTIVE_TERMINATED_CUSTODIAN_DETAIL_REPORT
    )
  )
  allowActiveDeactiveDetailReport$: Observable<boolean>

  // TODO Remove the usage of localstorage. This is probably being set via injected javascript. BAD PRACTICE
  venioApplicationVersion = localStorage.getItem('VenioApplicationVersion')

  get hasAccessToken(): boolean {
    return localStorage.getItem('access_token') !== null
  }

  /**
   * Gets an observable of application version which was fetched on app startup.
   * Ref. in the view to compare with project updated version.
   * Some UI controls are enable/disable based on version.
   */
  readonly vodVersion = this.store.select<string>(
    getBaseSetting('venioVersion')
  )

  /**
   * Flag to tell if this is VOD or RVOD.
   */
  isVodrEnabled = false

  /**
   * Zooms in to individual panel if set. If null all available panels will be shown.
   */
  focusedPanel: PanelType = null

  /** -------------------------------------- Case Panel Variables Start -------------------------------------- */

  /**
   * List of cases.
   */
  caseList: CaseModel[] = []

  /**
   * Search input control for case list.
   */
  searchInputCtrl = new FormControl()

  clientInputCtrl = new FormControl()

  /**
   * Sort by type {@link CaseSortType}
   */
  sortType: CaseSortType = CaseSortType.Id

  /**
   * Sort in descending order?
   */
  sortDesc = true

  /**
   * Start index for the list of cases.
   */
  pageIndex = 1

  /**
   * Size of the batch of cases to be fetched.
   */
  batchSize = 5

  /**
   * Toggle UI indicator whether the search is happening for the case list.
   */
  isLoading = true

  /**
   * Toggle loading indicator when user wants to load more cases by clicking to the `load more` button
   */
  isLoadingMore: boolean

  /**
   * Whether the all cases are loaded and there is no more case to load.
   */
  isAllCaseLoaded: boolean

  /**
   * When an user clicks to the load more button, we record the requested page size to use later.
   */
  loadMorePageSize: number

  /**
   * Whether the result was populated by search term. user should be able to reset the search.
   */
  isSearchResults: boolean

  /** -------------------------------------- Case Panel Variables End -------------------------------------- */

  /** -------------------------------------- Shared Docs Variables Start -------------------------------------- */

  /**
   * Shared document models for the user.
   */
  sharedDocs: SharedDocDetails[] = []

  /**
   * Search input control for shared document list.
   */
  searchInputCtrlDocs = new FormControl()

  /**
   * Search filters for shared documents.
   */
  sharedDocsFilters: { [key: string]: any[] }

  /**
   * Shared docs sort type.
   */
  sharedDocsSortType: keyof SharedDocDetails = 'documentShareId'

  /**
   * Sort shared docs by desc order?
   */
  sharedDocsSortOrder: 'asc' | 'desc' = 'desc'

  /**
   * Toggle UI indicator whether the search is happening for the shared docs.
   */
  isLoadingDocs = true

  /**
   * Card header for the shared documents.
   */
  cardHeadersDocs: Map<number, CardHeaderModel> = new Map()

  /**
   * Whether the shared docs result was populated by search term. user should be able to reset the search.
   */
  isSearchResultsForDocs: boolean

  /** -------------------------------------- Shared Docs Variables End -------------------------------------- */

  /** -------------------------------------- Review Set Variables Start -------------------------------------- */

  projectReviewSets: ProjectReviewSetInfo[] = []

  cardHeadersReviewSets: Map<string, CardHeaderModel> = new Map()

  /**
   * Whether the shared docs result was populated by search term. user should be able to reset the search.
   */
  isSearchResultsForReviewSet: boolean

  /**
   * Search filters for review sets.
   */
  reviewSetFilters: { [key: string]: any[] }

  /**
   * Review Set sort type.
   */
  reviewSetSortType: NestedObjectKeys<ProjectReviewSetInfo> =
    'reviewSetInfo.createdOn'

  /**
   * Sort Review Set by desc order?
   */
  reviewSetSortOrder: 'asc' | 'desc' = 'desc'

  /**
   * Toggle UI indicator whether the search is happening for the review sets.
   */
  isLoadingReviewSets = true

  /** -------------------------------------- Review Set Variables End -------------------------------------- */

  /**
   * Token used to destroy subscriptions.
   */
  private toDestroy$ = new Subject<void>()

  /**
   * User right to create new project
   */
  @Select(StartupStateSelector.hasGlobalRight(UserRights.CREATE_NEW_PROJECT))
  allowToCreateProject$: Observable<boolean>

  /**
   * User right to open project
   */
  @Select(StartupStateSelector.hasGlobalRight(UserRights.OPEN_PROJECTS))
  allowToOpenProject$: Observable<boolean>

  allowToOpenProject = false

  /**
   * User right to view the shared document sets
   */
  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_VIEW_SHARED_DOCUMENT
    )
  )
  allowToViewSharedDocumentSets$: Observable<boolean>

  allowToViewSharedDocumentSets = false

  /** login detail id of the user */
  detailId: number

  /** check whether there are transcript files */
  transcriptStatus: boolean

  /**
   * case list for project total count
   */
  totalCaseCount: number

  /** true when the control setting to enable second review set view */
  enableReview2 = false

  /**
   * whether the partial feature of review is enabled
   */
  isLinearReviewEnabled = false

  /**
   * User right to manage legal hold
   */
  @Select(
    StartupStateSelector.hasGlobalRight(UserRights.ALLOW_TO_MANAGE_LEGAL_HOLD)
  )
  allowToManageLegalHold$: Observable<boolean>

  allowToManageLegalHold = false

  legalHoldLicense = false

  isCheckingStateError = true

  stateErrorButtonName = 'Checking State'

  isButtonDisabled = true

  isRepositoryConfigured = false

  readonly caseItemHeightSize = 200

  // set selected case
  selectedClientInfo: ClientModel[] = []

  previousSelectedClientInfo: ClientModel[] = []

  selectedClientSearcedCnt = 0

  internalClient: Client

  clientData: ClientModel[] = []

  allClientData: ClientModel[] = []

  clientIdString = ''

  filtered = false

  isCheckingLicense = true

  /**
   * For a certain period, we will redirect between the old UI and new UI.
   * We store the base path for the new UI to redirect when the new UI feature is enabled.
   * Both apps are hosted on the same origin in production,
   * which means that if one app obtains a JWT token and stores it in storage, the other app can easily access it.
   * However, the key used must be the same for both apps.
   * @private
   */
  private newUiBaseurl = ''

  reportDialogRef: MatDialogRef<ReportsContainerComponent, any>

  serviceUploadDialogRef: MatDialogRef<ServiceUploadContainerComponent, any>

  isControlSettingLoading = true

  VOD_VERSION = 0

  isFullFeatureLicense = false

  constructor(
    private configStore: Store<ConfigState>,
    private store: Store<CaseState>,
    private xsStore: XSStore,
    private router: Router,
    private modalService: BsModalService,
    public configService: ConfigService,
    private title: Title,
    public dialog: MatDialog,
    public toastr: ToastrService,
    private production2FormService: Production2FormService,
    private changeDetectorRef: ChangeDetectorRef,
    private launchpadService: LaunchpadService,
    private productionReproduceService: ProductionReproduceService,
    public iframeMessengerService: IframeMessengerService,
    private activatedRoute: ActivatedRoute
  ) {
    this.isVodrEnabled = !this.configService.isVodEnabled

    // Set the title of current HTML document
    if (this.isVodrEnabled) this.title.setTitle('Ricoh OnDemand - Case')
    else this.title.setTitle('VenioOne OnDemand - Case')
  }

  // check or uncheck all
  toggle(item, event: MatCheckboxChange) {
    this.filtered = false
    if (event.checked) {
      this.selectedClientInfo.push(item)
    } else {
      const index = this.selectedClientInfo.indexOf(item)
      if (index >= 0) {
        this.selectedClientInfo.splice(index, 1)
      }
    }
  }

  exists(item) {
    return this.selectedClientInfo.indexOf(item) > -1
  }

  isIndeterminate() {
    this.filtered = false
    return this.selectedClientInfo.length > 0 && !this.isChecked()
  }

  isChecked() {
    this.filtered = false
    return this.selectedClientInfo.length === this.clientData.length
  }

  toggleAll(event: MatCheckboxChange) {
    this.filtered = false
    this.selectedClientInfo = []
    if (event.checked) {
      this.clientData.forEach((row) => {
        this.selectedClientInfo.push(row)
      })
    } else {
      this.selectedClientInfo.length = 0
    }
  }

  openCreateHold() {
    const dialogRef = this.dialog.open(CreateHoldComponent, {
      width: '600px',
      data: { modalTitle: 'Create' }
    })
  }

  openStateLevelError() {
    const dialogRef = this.dialog.open(StateErrorDetailComponent, {
      closeOnNavigation: true,
      autoFocus: false,
      minHeight: '60vh',
      //width: 'calc(100% / 1.5)',
      width: '90%',
      disableClose: true
    })
  }

  /**
   * TODO Get rid of this method once old vod support is removed.
   */
  private static setToLocalStorage(c: CaseModel) {
    localStorage.setItem('SelectedMediaScope', '-1')
    localStorage.setItem('DocShareEnabled', 'false')
    localStorage.setItem('ProjectId', c.ProjectId.toString())
    localStorage.setItem('ProjectName', c.ProjectName)
    localStorage.setItem(
      'IsFilteringService',
      c.IsFilteringServiceCase.toString()
    )
    localStorage.setItem(
      'IsEmailAnalysisAllowed',
      c.AllowEmailAnalysis.toString()
    )
  }

  /**
   * Initialize on component mount or view mount
   */
  private async init() {
    this.store.dispatch(new ClearCaseList())
    await this.getRights()
    this.caseBySearchTerm()
    this.clientSearchEvent()
    this.xsStore.dispatch(new ClientListAction())
    // Clear out the values for the review navigation
    useNavigationState().clearNavigatedCache()

    /**
     * Clears out the query parameters (projectId) from the url when we arrive to the listing page.
     * NOTE: if we need to add other additional parameters in this component, this line of code needs to be tweak
     * or implement it differently.
     */
    this.router.navigate([], { replaceUrl: true })

    /**
     * Clears out previous state of project info which might cause
     * confusion by displaying previous data on the UI until the next request get success to display its data.
     */
    this.store.dispatch(new ResetActiveProjectInfo())

    if (this.VOD_VERSION !== 3) {
      this.xsStore.dispatch([new FetchProjectList()])
    }
    this.configService
      .fetchLicenseStatus$('FEATURE', 'LEGAL HOLD')
      .subscribe((isEnabled: boolean) => {
        this.legalHoldLicense = Boolean(isEnabled)
      })

    this.xsStore.dispatch([
      new SetSelectedMediaScope([]),
      new SetSelectedFolderScope([])
    ])
  }

  ngOnInit() {
    this.store
      .pipe(select(getControlSettings), takeUntil(this.toDestroy$))
      .subscribe({
        next: (settings) => {
          // Avoid runtime error if settings are null or undefined
          settings = settings || {}

          this.enableLimitedOnDemandAccess =
            settings.ENABLE_LIMITED_ONDEMAND_ACCESS as boolean
          this.enableReview2 = settings.ENABLE_REVIEW_2 as boolean
          this.VOD_VERSION = +settings.VOD_VERSION
          this.isLinearReviewEnabled = settings.ENABLE_LINEAR_REVIEW as boolean
          this.showProjectSizeOnDemand =
            settings.SHOW_PROJECT_SIZE_ONDEMAND as boolean

          /**
           * This method of redirecting users from the old UI to the new one is only temporary.
           * The redirect will be removed once the updated UI is fully functional.
           */
          const isNewUiEnabled = this.VOD_VERSION === 3

          if (isNewUiEnabled) {
            this.newUiBaseurl = `/review-next`
          } else {
            this.newUiBaseurl = ''
          }

          this.isControlSettingLoading = false
        },
        error: () => {
          this.isControlSettingLoading = false
        }
      })

    this.store.dispatch(clearSearchResponse())

    this.configStore
      .pipe(select(getThemeClient), takeUntil(this.toDestroy$))
      .subscribe((client) => (this.client = client))

    this.xsStore
      .select(StartupStateSelector.SliceOf('selectedProjectId'))
      .pipe(
        tap((id) => this.xsStore.dispatch(new FetchUserLocalStorageInfo(id))),
        switchMap(() =>
          this.xsStore.select(
            StartupStateSelector.SliceOf('userLocalStorageInfo')
          )
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res: UserLocalStorageModel) => {
        this.detailId = res?.DetailID
      })

    this.store
      .pipe(
        select(caseRightListInfo),
        filter((right) => !!right),
        takeUntil(this.toDestroy$)
      )
      .subscribe((right) => {
        this.rightListInfo = right
      })

    this.store
      .pipe(
        select(getUserDetails),
        filter((user) => user?.userId > 0),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((userDetails: User) => {
        this.userDetails = userDetails
        this.xsStore.dispatch(new FetchUserRightsAction(0))

        // Do not load cases if the VOD version is 3 as we switch to new UI
        if (this.VOD_VERSION === 3) {
          return
        }

        // When page startup, page index and page size is fixed.
        let param
        if (this.isVodrEnabled) {
          param = this.prepareParam(this.pageIndex, this.batchSize)
        } else {
          param = this.prepareParam(this.pageIndex, 100)
        }

        // Start to load case lists
        this.isLoading = true
        this.store.dispatch(new FetchCases(param))
        this.xsStore.dispatch(
          new ReviewActions.FetchReviewSets(ReviewSetFilterType.ALL)
        )
      })

    // this.store
    //   .pipe(select(transcriptStatus), takeUntil(this.toDestroy$))
    //   .subscribe((res: boolean) => (this.transcriptStatus = res))

    this.companyName = this.configService.companyName

    this.initLegalHold()

    this.store
      .pipe(select(getClientInfo), takeUntil(this.toDestroy$))
      .subscribe((res: any) => (this.internalClient = res))

    this.store.dispatch(new FetchUser())

    this.initReportMenu()
    this.fetchLicenseInfo()
    if (this.VOD_VERSION !== 3) {
      this.#handleUploadInvitation()
    }
    this.#selectReviewNextNavigateEvent()
  }

  private fetchLicenseInfo(): void {
    this.configService
      .fetchLicenseStatus$('FEATURE', 'FULL FEATURE')
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((isEnabled: boolean) => {
        this.isFullFeatureLicense = isEnabled
      })
  }

  private initReportMenu(): void {
    // Create a mapping of rights observables to menu items
    const rightsToMenuItemsMap: { [key: string]: Observable<boolean> } = {
      [UserRights.ALLOW_TO_VIEW_SYSTEM_LOGIN_LOGOUT_REPORT]:
        this.allowLogInOutReports$,
      [UserRights.ALLOW_TO_VIEW_LOCKED_USERS_REPORT]:
        this.allowLockedUsersReports$,
      [UserRights.ALLOW_TO_VIEW_USER_CREATION_AND_DEACTIVATION_REPORTS]:
        this.allowCreationAndDeactivationReports$,
      [UserRights.ALLOW_TO_VIEW_EXPORT_AND_DOWNLOAD_REPORT]:
        this.allowDataExportReports$,
      [UserRights.ALLOW_TO_VIEW_ROLE_CHANGE_REPORT]:
        this.allowRoleChangeReports$,
      [UserRights.ALLOW_TO_GENERATE_PRODUCTION_DELETE_REPORT]:
        this.allowDeletedExports$,
      [UserRights.ALLOW_TO_VIEW_PROJECT_ACCESS_REPORT]:
        this.allowProjectAccessReports$,
      [UserRights.ALLOW_TO_VIEW_USER_ACTIVITY_REPORT]:
        this.allowActivityReports$,
      [UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_ACTIVE_TERMINATED_CUSTODIAN_DETAIL_REPORT]:
        this.allowActiveDeactiveDetailReport$,
      [UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_CUSTODIAN_DETAIL_REPORT]:
        this.allLegalHoldDetailReport$
    }

    // Combine all observables for rights
    const rights$ = combineLatest(
      Object.entries(rightsToMenuItemsMap).map(([key, observable]) =>
        observable.pipe(map((hasRight) => ({ key, hasRight })))
      )
    )

    // Populate the menu items based on the rights
    this.filteredMenuItems$ = rights$.pipe(
      debounceTime(300),
      map((rightsArray) => {
        const allowedRights = new Set(
          rightsArray
            .filter((right) => right.hasRight)
            .map((right) => right.key)
        )
        return this.menuItems.filter((item) => {
          // Disable Login/Logout Report for FBI
          if (
            this.enableReview2 &&
            item.type === ReportTypes.LOG_IN_OUT_REPORTS
          ) {
            return false
          }
          return allowedRights.has(item.requiredRight)
        })
      })
    )
  }

  private initLegalHold = (): void => {
    this.xsStore
      .dispatch(new FetchLegalHoldSummary())
      .pipe(
        switchMap(() =>
          this.xsStore.select(LegalHoldStateSelector.sliceOf('summary'))
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((s) => (this.summary = s))

    this.xsStore
      .dispatch(new FetchLegalHolds())
      .pipe(
        switchMap(() =>
          this.xsStore.select(LegalHoldStateSelector.sliceOf('legalHolds'))
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((h) => {
        const holdData = h
        this.holds = holdData.map((item) => ({
          ...item,
          customLegalHoldIssued: item.holdIssued ? 'Yes' : 'No',
          customTransferedToNetworkShare:
            item?.transferedToNetworkShare === 'yes' ? 'Yes' : 'No',
          customLegaholdStatus:
            item?.legalHoldStatus === 'open' ? 'Open' : 'Closed',
          customLegalHoldNoticeDate: item.legalHoldNoticeDate
            ? new Date(item.legalHoldNoticeDate)
            : null,
          customFollowUpDate: item.followUpDate
            ? new Date(item.followUpDate)
            : null,
          customdocumentMemoRetentionDate: item.documentMemoRetentionDate
            ? new Date(item.documentMemoRetentionDate)
            : null
        }))
      })
  }

  private NavigateToUserRoute() {
    let isTranscriptChecked = false
    if (this.module === 'add') {
      if (this.isVodrEnabled) {
        this.navUrl =
          '/launchpad/service_request/add-data?projectId=' +
          this.case.ProjectId +
          '&existingCase=' +
          true
        this.resultType = 'redirect'
        this.isInAppRouting = true
      } else {
        this.navUrl = '/upload?projectId=' + this.case.ProjectId
        this.resultType = 'redirect'
        this.isInAppRouting = true
      }
    } else {
      if (this.module === 'production' && this.isVodrEnabled) {
        this.navUrl =
          '/production/production_status?projectId=' + this.case.ProjectId
        this.resultType = 'redirect'
        this.isInAppRouting = true
      } else if (!this.mediaStatus.MediaList) {
        if (this.module === 'review') {
          isTranscriptChecked = true
          this.checkForTranscript()
        } else {
          this.resultType = 'error'
          this.message =
            'No media has been added to the case. Please use the upload page to add media in the case.'
        }
      } else if (!this.mediaStatus.ProcessedMediaList) {
        this.resultType = 'error'
        if (this.isVodrEnabled) {
          this.message = 'The media in the case are being processed.'
        } else {
          this.message =
            'The media in the case are being processed. Please use the upload page to view the processing status.'
        }
      } else {
        if (this.module === 'analyze') {
          this.isInAppRouting = true
          this.navUrl = '/analyze?media=1&projectId=' + this.case.ProjectId
          this.resultType = 'redirect'
        } else if (this.module === 'review') {
          this.isInAppRouting = true
          this.navUrl =
            `${
              this.enableReview2
                ? '/review2'
                : this.newUiBaseurl
                ? '/review-next'
                : '/review'
            }` +
            '?media=1&projectId=' +
            this.case.ProjectId
          this.resultType = 'redirect'
        } else if (this.module === 'production') {
          this.isInAppRouting = true
          if (
            !this.rightListInfo.ALLOW_EXPORT &&
            (this.rightListInfo.ALLOW_TO_VIEW_EXPORT_STATUS ||
              this.rightListInfo.ALLOW_TO_DOWNLOAD_EXPORT_ARCHIVES)
          ) {
            const isStatusTab = Boolean(this.case['isStatusTab'])
            this.navUrl =
              '/production/production_status?projectId=' +
              this.case.ProjectId +
              `${isStatusTab ? '&status=true' : ''}`
          } else {
            const isStatusTab = Boolean(this.case['isStatusTab'])
            this.navUrl =
              '/production?projectId=' +
              this.case.ProjectId +
              `${isStatusTab ? '&status=true' : ''}`
          }
          this.resultType = 'redirect'
        } else if (this.module === 'reprocessing') {
          this.isInAppRouting = true
          this.navUrl = '/reprocessing?media=1&projectId=' + this.case.ProjectId
          this.resultType = 'redirect'
        }
        if (!this.mediaStatus.IsAllMediaProcessed) {
          this.resultType = 'confirm'
          this.message =
            'There are data that are currently being processed.' +
            'Would you like to ' +
            this.module +
            ' all the data that has been completed?'
        }
      }
    }

    if (!isTranscriptChecked) {
      this.handleResultType()
    }
  }

  handleResultType(): void {
    if (!this.isInviteUserJustClosed && this.linkButtonClicked) {
      if (this.resultType === 'redirect') {
        this.resultType = null
        if (this.VOD_VERSION !== 3) {
          this.xsStore.dispatch(
            new InsertProjectLoginDetails(this.case.ProjectId, this.detailId)
          )
        }
        this.store.dispatch(
          new SetServerSideSession(
            this.case,
            false,
            this.navUrl,
            this.isInAppRouting
          )
        )
      } else if (this.resultType === 'confirm') {
        this.resultType = null
        if (
          this.VOD_VERSION === 3 &&
          (this.module === 'review' || this.module === 'analyze')
        ) {
          // If the application is ReviewNext, do not display the confirmation notification.
          this.#handleConfirmationResponse(true, this.module)
          this.#notifyMicroAppToShowMessage(this.resultType)
          return
        }
        this.showConfirmationModal()
      } else if (this.resultType === 'error') {
        if (this.VOD_VERSION === 3) {
          this.#notifyMicroAppToShowMessage(this.resultType)
          this.resultType = null
          this.case = null
          return
        }
        this.resultType = null
        this.case = null
        const initialState = {
          list: [this.message],
          title: this.companyName,
          closeBtnName: 'Close'
        }

        this.bsModalRef = this.modalService.show(
          MessagePopUpComponent,
          Object.assign(
            { initialState },
            { class: 'modal-dialog-centered', ignoreBackdropClick: true }
          )
        )
      }
    }

    /* NOTE: we are hitting multiple subscription for media status and showing dialogs even when
       we are not doing other stuffs than clicking on the buttons. So we check this prop
       is null before  next stream and then set to null when done. We need to implement this in a different way */
    this.module = null
  }

  #notifyMicroAppToShowMessage(resultType: string): void {
    this.iframeMessengerService.sendMessage({
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'FRAME_WINDOW',
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VOD,
      payload: {
        type: MessageType.NOTIFY_CHANGE,
        content: {
          caseNotificationMessage: true,
          message: this.message,
          projectId: this.case.ProjectId,
          resultType: resultType
        }
      }
    })
  }

  #selectReviewNextNavigateEvent(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.ROUTE_CHANGE &&
            e.payload['content']?.['shouldRedirect']
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.store.dispatch(
          new SetServerSideSession(
            this.case,
            false,
            this.navUrl,
            this.isInAppRouting,
            true
          )
        )
      })
  }

  checkForTranscript(): void {
    // Dispatch the FetchProjectInfo action
    this.store.dispatch(new FetchProjectInfo(this.case.ProjectId))

    this.store
      .select(getProjectInfo)
      .pipe(
        debounceTime(500),
        take(1), // Take only the latest value
        takeUntil(this.toDestroy$),
        switchMap((projectInfo) => {
          if (projectInfo.enableTranscriptViewer) {
            // Dispatch the GetTranscript action
            return this.xsStore
              .dispatch(new GetTranscript(this.case.ProjectId))
              .pipe(
                switchMap(() =>
                  this.xsStore
                    .select(TranscriptStateSelector.SliceOf('transcriptList'))
                    .pipe(
                      filter(
                        (transcripts) =>
                          transcripts !== undefined && transcripts !== null
                      ),
                      take(1), // Take only the latest value
                      catchError((err) => {
                        return of([])
                      })
                    )
                )
              )
          } else {
            return of([])
          }
        })
      )
      .subscribe((transcripts) => {
        if (transcripts.length > 0) {
          // Redirect to review page if transcripts are available
          this.isInAppRouting = true
          this.navUrl =
            `${
              this.enableReview2
                ? '/review2'
                : this.newUiBaseurl
                ? '/review-next'
                : '/review'
            }` +
            '?media=1&projectId=' +
            this.case.ProjectId
          this.resultType = 'redirect'
        } else {
          // No media and no transcripts
          this.resultType = 'error'
          this.message =
            'No media has been added to the case. Please use the upload page to add media in the case.'
        }
        this.handleResultType()
      })
  }

  ngAfterViewInit(): void {
    this.init()

    this.store
      .pipe(
        select(getCases),
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((cases) => {
        this.populateCaseList(cases ?? [])
      })

    this.xsStore
      .select(ClientMgmtStateSelector.SliceOf('clientListModel'))
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe((data) => {
        if (data) {
          this.clientData = data
          this.allClientData = data
        }
      })

    this.xsStore
      .select(ReviewSelectors.sliceOf('sharedDocuments'))
      .pipe(
        filter((res) => !!res),
        distinctUntilChanged(),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe({ next: this.populateSharedDocumentsList })

    this.xsStore
      .select(ReviewSelectors.sliceOf('reviewSets'))
      .pipe(
        filter((res) => !!res),
        distinctUntilChanged(),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe({ next: this.populateReviewSetList })

    fromEvent(this.searchInputRef?.nativeElement, 'input')
      .pipe(map((event: Event) => (event.target as HTMLInputElement).value))
      .pipe(debounceTime(800))
      .pipe(distinctUntilChanged())
      .subscribe((data) => {
        if (data.length) {
          this.search(this.searchInputRef?.nativeElement)
        } else {
          this.resetSearch(this.searchInputRef?.nativeElement)
        }
      })

    this.getTotalCaseCount()

    this.xsStore
      .dispatch(new CheckStateErrorExistAction())
      .pipe(
        switchMap(() =>
          this.xsStore.select(
            StateErrorStateSelector.SliceOf('getStateErrorExistResponse')
          )
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((s) => {
        this.isCheckingStateError = false
        if (s) {
          this.stateErrorButtonName = 'Warning: State Error Found'
          this.isButtonDisabled = false
        } else {
          this.stateErrorButtonName = ''
        }
      })
  }

  getRights(): Promise<void> {
    return new Promise((resolve, reject) => {
      combineLatest([
        this.allowToOpenProject$,
        this.allowToViewSharedDocumentSets$,
        this.allowToManageLegalHold$
      ])
        .pipe(takeUntil(this.toDestroy$))
        .subscribe(
          ([allowOpenProject, allowViewSharedDocs, allowLegalHold]) => {
            this.allowToOpenProject = allowOpenProject
            this.allowToViewSharedDocumentSets = allowViewSharedDocs
            this.allowToManageLegalHold = Boolean(allowLegalHold)
            this.checkLegalHoldLicense()
            if (allowViewSharedDocs) {
              this.xsStore.dispatch(
                new ReviewActions.FetchSharedDocuments(
                  -1,
                  SharedDocRequestType.SharedToMe
                )
              )
            }
            resolve()
          }
        )
    })
  }

  /** get total case count */
  private getTotalCaseCount() {
    this.xsStore
      .select(CaseSelectors.projects)
      .pipe(
        filter((p) => p.length > 0),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (projects) => {
          this.totalCaseCount = projects?.length
        }
      })
  }

  ngOnDestroy() {
    this.toDestroy$.next()
    this.toDestroy$.complete()

    // Clear any store properties that we need to clear
    this.store.dispatch(new ClearStoreProperty('caseRightListInfo'))

    if (this.reportDialogRef) {
      this.reportDialogRef.close()
    }
  }

  /**
   * Whether or not to show shared panel. Don't include focused panel condition here.
   * The conditions to check can be whether or not there are any shared documents for the user, or user rights to view shared docs, etc.
   */
  get showSharedDocsPanel() {
    return this.sharedDocs?.length > 0 && this.allowToViewSharedDocumentSets
  }

  /** ----------------------------------------- Case Panel Methods Start ----------------------------------------- **/
  trackByFn = (index: number, item: CaseModel) =>
    `${item.ProjectId}_${item.ProjectName}_${index}`

  /**
   * Whether the application and project versions are equal.
   */
  public isCompatibleVersion = (vodVersion: string, projectVersion: string) => {
    const hasValues = vodVersion.trim() && projectVersion.trim()
    return hasValues && vodVersion === projectVersion
  }

  /**
   * Prepares query param for the case list to get from API
   * @param start Page index begin
   * @param end Page index end
   * @param searchTerm Search term
   */
  private prepareParam(start: number, end: number, searchTerm?: string) {
    // We don't have to worry about checking search term value here cause we've validated in service class.
    return {
      start: start,
      end: end,
      sortBy: this.sortType,
      isDesc: this.sortDesc,
      searchTerm: searchTerm,
      clientIdString: this.clientIdString
    }
  }

  sortProjects(sortType: CaseSortType, sortDesc: boolean): void {
    this.caseList = []

    this.sortType = sortType
    this.sortDesc = sortDesc
    this.isAllCaseLoaded = undefined
    this.loadMorePageSize = this.batchSize
    this.isLoadingMore = undefined
    const searchTerm = (this.searchInputCtrl.value || '').trim()
    // When sort order is applied, the initial page index and page size is fixed.

    let param
    if (this.enableReview2) {
      param = this.prepareParam(
        this.pageIndex,
        100,
        searchTerm ? searchTerm : null
      )
    } else {
      param = this.prepareParam(
        this.pageIndex,
        this.batchSize,
        searchTerm ? searchTerm : null
      )
    }
    this.isLoading = true
    this.store.dispatch(new FetchCases(param))
  }

  private clientSearchEvent() {
    this.clientInputCtrl.valueChanges
      .pipe(takeUntil(this.toDestroy$), debounceTime(600))
      .subscribe({
        next: (term) => {
          this.clientData = this.allClientData

          const clientList = this.clientData.filter(
            (x) =>
              x.clientName
                .toLocaleLowerCase()
                .indexOf(term.toLocaleLowerCase()) > -1
          )
          this.clientData = clientList
        }
      })
  }

  private caseBySearchTerm() {
    this.searchInputCtrl.valueChanges
      .pipe(
        filter(() => this.userDetails.userId > 0),
        tap(() => [(this.isLoading = true), (this.caseList = [])]),
        debounceTime(600)
      )
      .subscribe({
        next: (term) => {
          // Apply default order when no term
          if (this.enableReview2) {
            this.store.dispatch(
              new FetchCases({
                ...this.prepareParam(this.pageIndex, 100),
                searchTerm: term,
                clientIdString: this.clientIdString
              })
            )
          } else {
            this.store.dispatch(
              new FetchCases({
                ...this.prepareParam(this.pageIndex, this.batchSize),
                searchTerm: term,
                clientIdString: this.clientIdString
              })
            )
          }
        }
      })
  }

  /**
   * Search for cases based on the search term.
   * @param input HTML input element for search
   */
  search(input: HTMLInputElement) {
    const value = (input.value || '').trim()

    if (value.length <= 0) {
      return false
    }
    this.caseList = []
    this.isSearchResults = true
    this.isAllCaseLoaded = undefined
    this.loadMorePageSize = this.batchSize
    this.isLoadingMore = undefined
    // Changes to this control is listened to for fetching new cases.
    this.searchInputCtrl.reset(value)
  }

  searchClient(input: string) {
    if (input.length <= 0) {
      this.clientData = this.allClientData
      return false
    }
    this.clientInputCtrl.reset(input)
  }

  onMenuClosed() {
    this.selectedClientInfo = []
    setTimeout(() => {
      if (!this.filtered) {
        if (this.previousSelectedClientInfo.length > 0) {
          this.selectedClientInfo = []
          this.previousSelectedClientInfo.forEach((row) => {
            this.selectedClientInfo.push(row)
          })
        }
      }
    }, 500)
  }

  clientCaseSearch() {
    this.filtered = true
    if (this.searchInputRef?.nativeElement.value.length > 0)
      this.resetSearch(this.searchInputRef?.nativeElement)

    this.clientIdString = ''

    this.selectedClientSearcedCnt = this.selectedClientInfo.length
    this.previousSelectedClientInfo = this.selectedClientInfo
    if (this.allClientData.length !== this.selectedClientInfo.length)
      this.clientIdString = this.selectedClientInfo
        .map((x) => x.clientId)
        .join(',')
    this.caseList = []

    this.store.dispatch(
      new FetchCases({
        ...this.prepareParam(this.pageIndex, this.batchSize),
        searchTerm: '',
        clientIdString: this.clientIdString
      })
    )
  }

  /**
   * Resets the search and fetch default project list.
   * @param input HTML input element for search
   */
  resetSearch(input: HTMLInputElement) {
    input.value = null

    // Resetting this control is listened to inorder to fetch new cases without any search term filter.
    this.searchInputCtrl.reset()
    this.isSearchResults = undefined
    this.isAllCaseLoaded = undefined
    this.loadMorePageSize = this.batchSize
    this.isLoadingMore = undefined
  }

  resetClientSearch(input: HTMLInputElement) {
    input.value = null
    this.clientInputCtrl.reset()
  }

  /**
   * Checks whether there are more cases left to be loaded.
   */
  get hasMoreData(): boolean {
    return (
      !this.isLoading &&
      this.caseList.length > 0 &&
      this.caseList.length >= this.batchSize
    )
  }

  /**
   * Load more cases.
   */
  loadMore(): void {
    this.isLoadingMore = true
    this.loadMorePageSize = this.caseList.length + this.batchSize
    const searchTerm = (this.searchInputCtrl.value || '').trim()
    const param = this.prepareParam(
      this.caseList.length + 1,
      this.loadMorePageSize,
      searchTerm
    )
    this.store.dispatch(new FetchCases(param))
  }

  @DebounceTimer(200)
  onCaseScrollIndexChanged(): void {
    if (
      !this.virtualScroll ||
      this.isAllCaseLoaded ||
      this.isLoadingMore ||
      this.isLoading
    ) {
      return
    }

    const end = this.virtualScroll.getRenderedRange().end
    const total = this.virtualScroll.getDataLength()

    if (end === total) {
      this.loadMore()
    }
  }

  /**
   * Populate the case list.
   */
  @DebounceTimer(200)
  private populateCaseList = (caseList: CaseModel[]) => {
    this.isLoading = false
    this.isLoadingMore = undefined

    this.caseList = [...this.caseList, ...caseList].reduce((cases, item) => {
      const index = cases.findIndex((c) => c.ProjectId === item.ProjectId)
      const headerMapped = {
        ...item,
        cardHeader: this.prepareCardHeader(item)
      }
      if (index > -1) {
        cases[index] = headerMapped
      } else {
        cases.push(headerMapped)
      }
      return cases
    }, [])

    this.isAllCaseLoaded = this.totalCaseCount === this.caseList?.length
  }

  /**
   * Prepares card header for the case card model.
   * @param c Case Model {@link CaseModel}
   */
  prepareCardHeader(c: CaseModel): CardHeaderModel {
    const actionLinks: CardHeaderModel = {
      title:
        this.isVodrEnabled && c.CaseName && !c.BackupStatus
          ? `${c.CaseName}(${c.ProjectName})`
          : c.ProjectName,
      config: {
        iconClass: 'fas fa-briefcase'
      },
      toolbar: [],
      extra: c.ProjectId
    }

    if (this.allowToOpenProject) {
      // Add `Reprocessing` button in the toolbar.
      if (
        c.ProjectVersion === this.venioApplicationVersion &&
        c.DisplayReprocessingLink &&
        !c.BackupStatus
      ) {
        actionLinks.toolbar.push({
          uuid: CardHeaderItem.Reprocessing,
          text: 'Reprocessing',
          iconClass: 'fas fa-undo',
          extra: c.ProjectId
        })
      }

      // Add `Upload` | `Add Data` button in the toolbar.
      if (
        c.ProjectVersion === this.venioApplicationVersion &&
        c.DisplayUploadLink &&
        !c.BackupStatus
      ) {
        const uploadButton: CardHeaderToolbarModel = {
          uuid: CardHeaderItem.Upload,
          text: this.isVodrEnabled
            ? 'Add Data'
            : c.IsServiceTypeCase
            ? 'New Upload'
            : 'Upload',
          iconClass: 'fas fa-upload',
          children: [],
          extra: c.ProjectId
        }
        if (c.DisplayUploadInviteLink) {
          uploadButton.children.push({
            uuid: CardHeaderItem.UploadInvite,
            text: 'Invite to ' + (this.isVodrEnabled ? 'Add Data' : 'Upload'),
            iconClass: 'fas fa-envelope',
            extra: c.ProjectId
          })
        }
        actionLinks.toolbar.push(uploadButton)
      }

      // Add `Analyze` button in the toolbar.
      if (
        c.ProjectVersion === this.venioApplicationVersion &&
        c.DisplayAnalyzeLink &&
        !c.BackupStatus
      ) {
        actionLinks.toolbar.push({
          uuid: CardHeaderItem.Analyze,
          text: 'Analyze',
          iconClass: 'fas fa-chart-line',
          extra: c.ProjectId
        })
      }

      // Add `Review` button in the toolbar.
      if (
        c.ProjectVersion === this.venioApplicationVersion &&
        c.DisplayReviewLink &&
        !c.BackupStatus
      ) {
        actionLinks.toolbar.push({
          uuid: CardHeaderItem.Review,
          text: 'Review',
          iconClass: 'fas fa-search',
          extra: c.ProjectId
        })
      }

      // Add `Production` button in the toolbar.
      if (
        c.ProjectVersion === this.venioApplicationVersion &&
        c.DisplayProductionLink &&
        !c.BackupStatus
      ) {
        actionLinks.toolbar.push({
          uuid: CardHeaderItem.Production,
          text: this.isVodrEnabled
            ? 'Download'
            : c.IsServiceTypeCase
            ? 'Production Status'
            : 'Produce',
          iconClass: 'fas fa-download',
          customClass: c.IsServiceTypeCase ? 'custom-tooltip-position' : null,
          extra: c.ProjectId
        })
      }
    }

    return actionLinks
  }

  async launchpadNextActionClick(item: LaunchpadNextEvent): Promise<void> {
    const { actionType, content } = item
    // The `ActionType` enums must match with the micro-app action types.
    if (actionType === ActionType.CREATE_CASE) {
      this.onCreateCase()
      return
    }

    // Takes to the admin > case > review set > create page.
    // Once new UI for review set is ready, we will remove this or becomes unused.
    if (actionType === ActionType.REVIEW_SET_CREATE) {
      this.store.dispatch(
        new NavigateToAction('/admin/case/review-set/create', true)
      )
      return
    }

    // Takes to the admin > case > review set > edit or clone page.
    // Once new UI for review set is ready, we will remove this or becomes unused.
    if (
      actionType === ActionType.REVIEW_SET_EDIT ||
      actionType === ActionType.REVIEW_SET_CLONE
    ) {
      const urlPath =
        actionType === ActionType.REVIEW_SET_EDIT ? 'edit' : 'clone'
      const url = `/admin/case/review-set/${urlPath}?projectId=${content?.projectId}&reviewSetId=${content?.reviewSetId}`
      this.store.dispatch(new NavigateToAction(url, true))
      return
    }

    // So far, user is asking to enter in review mode
    if (actionType === ActionType.ENTER_REVIEW_SET_REVIEW) {
      await this.onNavigateReview({
        projectId: content?.projectId,
        reviewSetId: content?.reviewSetId
      } as ProjectReviewSetInfo)
      return
    }

    if (actionType === ActionType.DIRECT_EXPORT) {
      this.xsStore
        .dispatch(new SetVODRSettings(content?.settingsInfo))
        .pipe(take(1))
        .subscribe(() => {
          this.navigateTo(
            content?.projectId,
            content?.isExistingCase,
            content?.overrideSettings,
            content?.settingId,
            content?.afterConflict
          )
        })
    }
    /**
     * Content is optional, but for some operations it's required.
     * @remarks
     *  We can't use an object sent from a child here as it has its own
     *  structure and logic is implemented as per the data structure returned.
     *  Here, we use projectId to fetch case info from old endpoint and just passing the object.
     */
    let caseModel: CaseModel
    const projectId = content?.projectId || content?.ProjectId

    try {
      // Project ID is required to fetch case info.
      if (!projectId) {
        return
      }

      const caseInfo = await this.launchpadService
        .fetchCaseInfoById$(projectId)
        .toPromise()
      caseModel = caseInfo['data'] as CaseModel
    } catch {
      // If the case is not found, throw an error to ensure the reason is clear.
      // This is a developer error, not a user error for better debugging.
      throw new Error(`Case not found for the project id: ${projectId}`)
    }

    if (!caseModel) {
      return
    }

    const type =
      actionType === ActionType.ANALYZE
        ? CardHeaderItem.Analyze
        : actionType === ActionType.REVIEW
        ? CardHeaderItem.Review
        : actionType === ActionType.PRODUCE_NEW
        ? CardHeaderItem.Production
        : actionType === ActionType.UPLOAD_REPROCESS
        ? CardHeaderItem.Reprocessing
        : actionType === ActionType.UPLOAD_INVITE
        ? CardHeaderItem.UploadInvite
        : actionType === ActionType.UPLOAD_NEW
        ? CardHeaderItem.Upload
        : actionType === ActionType.PRODUCE_STATUS
        ? CardHeaderItem.Status
        : actionType === ActionType.REPRODUCE
        ? CardHeaderItem.Reproduce
        : null

    switch (type) {
      case CardHeaderItem.Reprocessing:
        this.navigateToNextPage('reprocessing', caseModel)
        break
      case CardHeaderItem.Upload: {
        this.navigateToNextPage('add', caseModel, true)
        break
      }
      case CardHeaderItem.UploadInvite:
        this.openInviteToUpload(caseModel)
        break
      case CardHeaderItem.Analyze:
        this.navigateToNextPage('analyze', caseModel)
        break
      case CardHeaderItem.Review:
        if (content?.sharedFolderLineage !== undefined) {
          this.xsStore.dispatch(
            new InitUploadHistoryQueryAction({
              query: `FOLDERS("${content?.sharedFolderLineage}")`,
              includePc: false
            })
          )
        }
        if (content?.exportName !== undefined) {
          this.xsStore.dispatch(
            new InitUploadHistoryQueryAction({
              query: 'EXPORT_NAME="' + content?.exportName + '"',
              includePc: false,
              sourceModule: SearchQueryModule.Production
            })
          )
        }
        await this.setReviewSetId(-1)
        this.navigateToNextPage('review', caseModel)
        break
      case CardHeaderItem.Production:
      case CardHeaderItem.Status:
        caseModel['isStatusTab'] = type === CardHeaderItem.Status
        this.navigateToNextPage('production', caseModel)
        break
      case CardHeaderItem.Reproduce:
        {
          let connector = 'localrepository'
          if (content?.isRelativityImportEnabled) {
            connector = 'relativity'
          }
          this.productionReproduceService.setReproduceState(
            connector,
            content?.exportId
          )
          this.navigateToNextPage('production', caseModel)
        }
        break
    }
  }

  /**
   * Handle click event on card header and toolbar items.
   * @param item
   */
  async cardItemClicked(item: any) {
    const caseModel = this.caseList.find((c) => c.ProjectId === item.extra)
    switch (item?.uuid) {
      case CardHeaderItem.Reprocessing:
        this.navigateToNextPage('reprocessing', caseModel)
        break
      case CardHeaderItem.Upload:
        this.navigateToNextPage('add', caseModel, false)
        break
      case CardHeaderItem.UploadInvite:
        this.openInviteToUpload(caseModel)
        break
      case CardHeaderItem.Analyze:
        this.navigateToNextPage('analyze', caseModel)
        break
      case CardHeaderItem.Review:
        await this.setReviewSetId(-1)
        this.navigateToNextPage('review', caseModel)
        break
      case CardHeaderItem.Production:
        this.navigateToNextPage('production', caseModel)
        break
      default:
        // fix can navigate to review without right
        if (caseModel?.DisplayReviewLink && this.allowToOpenProject) {
          this.navigateToNextPage('review', caseModel)
        }
        break
    }
  }

  navigateToNextPage(
    moduleName: string,
    selectedProject: CaseModel,
    navigateToNextPage = true
  ): void {
    if (
      !selectedProject?.IsServiceTypeCase ||
      (selectedProject?.IsServiceTypeCase && navigateToNextPage)
    ) {
      this.store.dispatch(new GetProjectRightList(selectedProject.ProjectId))
      if (
        !selectedProject.BackupStatus &&
        selectedProject.ProjectVersion === this.venioApplicationVersion
      ) {
        this.xsStore.dispatch([
          new FetchUserLocalStorageInfo(selectedProject.ProjectId)
          //new FetchUserRightsAction(selectedProject.ProjectId)
        ])
        this.xsStore
          .select(StartupStateSelector.SliceOf('selectedProjectId'))
          .pipe(
            filter((id) => id > 0),
            debounceTime(200),
            takeUntil(this.toDestroy$)
          )
          .subscribe({
            next: (id) => {
              if (id !== selectedProject.ProjectId) {
                this.xsStore.dispatch(new SaveSelectedTagsFilter([]))
              }
            }
          })

        // Set some keys to localstorage, we'll remove this later on.
        CaseLaunchpadComponent.setToLocalStorage(selectedProject)

        this.isInviteUserJustClosed = false
        this.linkButtonClicked = true
        this.case = selectedProject
        this.module = moduleName
        this.store.dispatch(new ClearStoreProperty('projectMediaStatus'))
        this.store.dispatch(new GetProjectMediaStatus(this.case.ProjectId))

        //When the action button is clicked, permissions are loaded asynchronously,
        // which means the store may not be filled with the permissions associated with the clicked project yet.
        // As a result, we must ensure that further events are triggered only after the state is updated.
        // To avoid mixing up asynchronous and synchronous actions,
        // we should derive an event triggered specifically when a user clicks an action button.
        // This approach ensures that the permission data is correctly utilized once it becomes available.
        combineLatest([
          this.store.pipe(select(projectMediaStatus)),
          this.store.pipe(select(caseRightListInfo))
        ])
          .pipe(
            filter(() => !!this.module),
            filter(([media, right]) => media && right),
            take(1)
          )
          .subscribe(([caseMediaStatus, rightListInfo]) => {
            this.rightListInfo = rightListInfo
            this.mediaStatus = caseMediaStatus
            this.NavigateToUserRoute()
          })
      }
    } else {
      this.openUploadDialog(false, selectedProject?.ProjectId)
    }
  }

  /**
   * Opens the dialog to invite user for uploading data.
   * @param selectedCase
   */
  private openInviteToUpload(selectedCase: CaseModel) {
    this.store.dispatch(new GetUsersListToInvite(selectedCase.ProjectId))
    this.store.dispatch(new GetExtUsersListToInvite())
    this.resultType = null
    this.message = null
    const initialState = {
      projectId: selectedCase.ProjectId,
      isServiceTypeCase: selectedCase?.IsServiceTypeCase
    }
    const bsModalRef1 = this.modalService.show(
      InviteToUploadComponent,
      Object.assign(
        { initialState },
        {
          class: 'modal-invite-to-upload',
          ignoreBackdropClick: true,
          keyboard: false
        }
      )
    )
    bsModalRef1.content.onClose.subscribe((value) => {
      this.isInviteUserJustClosed = value
    })
  }

  /**
   * Navigate to VOD Case Creation Page.
   */
  onCreateCase() {
    this.xsStore
      .dispatch(new FetchIfRepositoryConfiguredAction())
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$),
        switchMap(() =>
          this.xsStore.select(
            CaseStateSelector.SliceOf('isRepositoryConfigured')
          )
        )
      )
      .subscribe((res: any) => {
        this.isRepositoryConfigured = res.data
        if (this.isRepositoryConfigured == true) {
          this.router.navigate(['launchpad/casesetting'])
        } else if (this.isRepositoryConfigured == false) {
          const error = {
            name: 'Invalid Operation',
            message:
              'Repository has not been configured. Please configure Client Management.'
          }
          this.toastr.error(error.message)
        }
      })
  }

  /**
   * Function to identify whether to show the case list or not
   */
  get showCasePanel(): boolean {
    return this.userDetails?.globalRoleName !== 'Reviewer'
  }

  private navigateTo(
    projectId: number,
    isExistingCase: boolean,
    overrideSettings: boolean,
    settingId: number,
    afterConflict: boolean
  ): void {
    this.router.navigate(['/upload'], {
      queryParams: {
        projectId: projectId,
        existingCase: isExistingCase,
        overrideSettings: overrideSettings,
        settingId: settingId,
        afterConflict: afterConflict,
        structuredData: false // Automatically selects structured data type.
      }
    })
  }

  /** ----------------------------------------- Case Panel Methods End ----------------------------------------- **/

  /** ----------------------------------------- Shared Documents Panel Methods Start ----------------------------------------- **/

  trackSharedDocByFn = (index: number, item: SharedDocDetails) =>
    item.documentShareId

  /**
   * Searches for the shared documents.
   * @param searchText
   */
  searchSharedDocs(searchText: string) {
    if (searchText.trim().length === 0) this.isSearchResultsForDocs = false
    else this.isSearchResultsForDocs = true

    // Set the filters
    this.sharedDocsFilters = {
      shareName: [searchText]
    }
  }

  /**
   * Clears out all the search filters for shared documents.
   * @param inputElement
   */
  resetSharedDocsSearch(inputElement: HTMLInputElement) {
    inputElement.value = null
    this.sharedDocsFilters = null
    this.isSearchResultsForDocs = false
  }

  /**
   * Sorts the list of shared documents based on the sort type and order provided.
   * @param sortBy {@type keyof SharedDocDetails}
   * @param sortOrder 'asc' for ascending and 'desc' for descending
   */
  sortSharedDocs(sortBy: keyof SharedDocDetails, sortOrder: 'asc' | 'desc') {
    this.sharedDocsSortType = sortBy
    this.sharedDocsSortOrder = sortOrder
  }

  /**
   * Populates shared documents list.
   * @param sharedDocList List of {@link SharedDocDetails}
   */
  private populateSharedDocumentsList = (sharedDocList: SharedDocDetails[]) => {
    this.sharedDocs = sharedDocList
    this.cardHeadersDocs.clear()

    this.sharedDocs.forEach((s) =>
      this.cardHeadersDocs.set(
        s.documentShareId,
        this.prepareCardHeaderForSharedDocs(s)
      )
    )
    this.isLoadingDocs = false
  }

  /**
   * Prepares card headers for the shared document model.
   * @param sharedDoc Shared Document Details {@link SharedDocDetails}
   */
  private prepareCardHeaderForSharedDocs(
    sharedDoc: SharedDocDetails
  ): CardHeaderModel {
    return {
      title: sharedDoc.shareName,
      extra: sharedDoc,
      config: {
        iconClass: 'fas fa-share-alt'
      },
      toolbar: [
        {
          uuid: CardHeaderItem.Review,
          text: 'Review',
          iconClass: 'fas fa-search',
          extra: sharedDoc
        }
      ]
    }
  }

  /**
   * Click event handler for shared documents card.
   * @param item {@link CardHeaderModel} or {@link CardHeaderToolbarModel}
   */
  sharedDocumentsCardItemClicked(item: any) {
    // set value for review navigation from shared doc list
    useNavigationState().setNavigatedValue(ReviewNavigation.NAVIGATE_REVIEW)

    switch (item.uuid) {
      case CardHeaderItem.Review:
        this.navigateToReviewForSharedDocs(item.extra as SharedDocDetails)
        break
      default:
        this.navigateToReviewForSharedDocs(item.extra as SharedDocDetails)
        break
    }
  }

  private navigateToReviewForSharedDocs(sharedDoc: SharedDocDetails) {
    this.router.navigate([`${this.enableReview2 ? '/review2' : '/review'}`], {
      queryParams: {
        projectId: sharedDoc?.projectId,
        docShareToken: sharedDoc?.token
      }
    })
  }

  /** ----------------------------------------- Shared Documents Panel Methods End ----------------------------------------- **/

  /**------------------------------------------ Review Set Panel Methods Start-----------------------------------------------**/

  get showReviewSetPanel() {
    return this.projectReviewSets?.length > 0
  }

  private populateReviewSetList = (reviewSets: ProjectReviewSetInfo[]) => {
    this.projectReviewSets = reviewSets
    this.cardHeadersReviewSets.clear()

    this.projectReviewSets.forEach((r) =>
      this.cardHeadersReviewSets.set(
        r.projectId + '_' + r.reviewSetInfo.reviewSetId,
        this.prepareCardHeaderForReviewSets(r)
      )
    )
    this.isLoadingReviewSets = false
  }

  private prepareCardHeaderForReviewSets(
    projectReviewSet: ProjectReviewSetInfo
  ): CardHeaderModel {
    return {
      title: projectReviewSet?.reviewSetInfo.name,
      extra: projectReviewSet?.reviewSetInfo.reviewSetId,
      config: {
        iconClass: 'fas fa-suitcase'
      },
      tagLine: projectReviewSet?.reviewSetInfo.isCalReviewSet ? 'CAL' : '',
      toolbar: [
        {
          uuid: CardHeaderItem.Review,
          text: 'Review',
          iconClass: 'fas fa-search',
          extra: projectReviewSet
        }
      ]
    }
  }

  trackReviewSetByFn = (index: number, item: ProjectReviewSetInfo) =>
    item.reviewSetInfo.reviewSetId

  /**
   * Searches for the shared documents.
   * @param searchText
   */
  searchReviewSets(searchText: string) {
    if (searchText.trim().length === 0) this.isSearchResultsForReviewSet = false
    else this.isSearchResultsForReviewSet = true

    // Set the filters
    this.reviewSetFilters = {
      reviewSetName: [searchText]
    }
  }

  /**
   * Clears out all the search filters for review sets.
   * @param inputElement
   */
  resetReviewSetSearch(inputElement: HTMLInputElement) {
    inputElement.value = null
    this.reviewSetFilters = null
    this.isSearchResultsForReviewSet = false
  }

  /**
   * Sorts the list of review sets based on the sort type and order provided.
   * @param sortBy {@type keyof ProjectReviewSetInfo}
   * @param sortOrder 'asc' for ascending and 'desc' for descending
   */
  sortReviewSets(
    sortBy: NestedObjectKeys<ProjectReviewSetInfo>,
    sortOrder: 'asc' | 'desc'
  ) {
    this.reviewSetSortType = sortBy
    this.reviewSetSortOrder = sortOrder
  }

  /**
   * Review set card header clicked
   * @param item {@link CardHeaderModel} or {@link CardHeaderToolbarModel}
   */
  async reviewSetCardItemClicked(item: any) {
    let projectReviewInfo: ProjectReviewSetInfo = null

    // set value for review navigation from shared doc list
    useNavigationState().setNavigatedValue(ReviewNavigation.NAVIGATE_REVIEW)

    //this is when review link is clicked from 'Review Set Name'. In this case, item's type is of CardReviewSetModel
    //So, get the projectReviewSetInfo from the item object.

    if (item.toolbar) {
      projectReviewInfo = item.toolbar[0].extra as ProjectReviewSetInfo
    } else projectReviewInfo = item.extra as ProjectReviewSetInfo

    this.onNavigateReview(projectReviewInfo)
  }

  async onNavigateReview(projectReviewInfo: ProjectReviewSetInfo) {
    await this.setReviewSetId(projectReviewInfo?.reviewSetId)
    this.xsStore.dispatch([
      new ResetSelectedReviewLayout(),
      new ResetReviewState()
    ])
    this.xsStore.dispatch(
      new Navigate(
        [
          `${
            this.enableReview2
              ? '/review2'
              : this.isLinearReviewEnabled
              ? '/linear-review'
              : '/review'
          }`
        ],
        {
          projectId: projectReviewInfo.projectId,
          reviewSetId: projectReviewInfo.reviewSetId
        }
      )
    )
    if (this.VOD_VERSION !== 3)
      this.xsStore.dispatch(
        new InsertProjectLoginDetails(
          projectReviewInfo.projectId,
          this.detailId
        )
      )
  }

  setReviewSetId(reviewSetId: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const actions: any = [new SetSelectedReviewSetId(reviewSetId)]
      const selectedSourceType = this.xsStore.selectSnapshot(
        ReviewSetStateSelector.SliceOf('reviewSourceType')
      )
      if (selectedSourceType == ReviewDataSourceType.ReviewSet)
        actions.push(new ResetSelectedReviewLayout())
      actions.push(new SaveSelectedTagsFilter([]))

      this.xsStore
        .dispatch(actions)
        .pipe(
          switchMap(() =>
            this.xsStore.selectOnce(
              ReviewSetStateSelector.SliceOf('selectedReviewSetId')
            )
          ),
          takeUntil(this.toDestroy$)
        )
        .subscribe((reviewSetId: number) => {
          resolve()
        })
    })
  }

  navigateCaseAction(e) {
    this.navigateToNextPage(e.module, e.project)
  }

  handlePagination(e) {
    if (e.page === 'next') {
      this.loadMore()
    } else if (e.page === 'prev') {
      this.isLoadingMore = true
      this.loadMorePageSize = this.caseList.length + this.batchSize
      const searchTerm = (this.searchInputCtrl.value || '').trim()
      const param = this.prepareParam(
        this.caseList.length,
        this.caseList.length,
        searchTerm
      )
      this.store.dispatch(new FetchCases(param))
    }
  }

  /**------------------------------------------ Review Set Panel Methods Start-----------------------------------------------**/

  showConfirmationModal(): void {
    const modal = this.modalService.show(ConfirmationDialogComponent, {
      class: 'modal-dialog-centered'
    })
    ;(<ConfirmationDialogComponent>modal.content).showConfirmationModal(
      this.companyName,
      this.message
    )
    ;(<ConfirmationDialogComponent>modal.content).onClose.subscribe(
      (result) => {
        this.#handleConfirmationResponse(result)
      }
    )
  }

  #handleConfirmationResponse(result: boolean | null, module?: string): void {
    if (result === true) {
      const shouldRedirect = !(
        this.VOD_VERSION === 3 &&
        (module === 'review' || module === 'analyze')
      )
      // When pressed Yes
      if (this.VOD_VERSION !== 3)
        this.xsStore.dispatch(
          new InsertProjectLoginDetails(this.case.ProjectId, this.detailId)
        )
      this.store.dispatch(
        new SetServerSideSession(
          this.case,
          false,
          this.navUrl,
          this.isInAppRouting,
          shouldRedirect
        )
      )
    } else if (result === false) {
      // When pressed No
      this.case = null
    } else {
      // When closing the modal without no or yes
      this.case = null
    }
  }

  reload() {
    this.xsStore.dispatch(new FetchLegalHolds())
  }

  // readonly reload = (): void => {this.store
  //       .dispatch<any>(new FetchLegalHolds())}

  readonly actionClicked = (a: 'EDIT' | 'DELETE', holdDetails): void => {
    const holdId = holdDetails.holdId
    switch (a) {
      case 'EDIT':
        this.dialog.open(CreateHoldComponent, {
          width: '600px',
          data: { holdId: holdId, modalTitle: 'Update' }
        })
        break
      case 'DELETE':
        {
          const delRef = this.dialog.open(this.confirmDel, {
            closeOnNavigation: true,
            autoFocus: false,
            width: '380px'
          })
          delRef
            .beforeClosed()
            .pipe(
              // mat-dialog action directive used. returns boolean value.
              filter((yes) => yes),
              // if clicked yes (truthy value)
              tap(() => this.xsStore.dispatch(new DeleteLegalHold(holdId))),
              // grab slice of deletion response
              switchMap(() =>
                //this.xsStore.select(RepositoryStateSelector.SliceOf('deleteResponse'))
                this.xsStore.select(
                  LegalHoldStateSelector.sliceOf('deleteHold')
                )
              ),
              // message must be defined
              filter((res) => !!res?.message),
              debounceTime(400),
              takeUntil(this.toDestroy$)
            )
            .subscribe({
              next: (res) => {
                if (res.status.toLowerCase() == 'success') {
                  this.toastr.success('Successfully deleted legal hold.')
                  this.xsStore.dispatch(new FetchLegalHolds())
                  this.xsStore.dispatch(new FetchLegalHoldSummary())
                } else if (res.status == undefined) {
                  this.toastr.error(
                    'An error occurred while deleting legal hold.'
                  )
                } else {
                  this.toastr.error(res.message)
                }
              }
            })
        }
        break
    }
  }

  checkLegalHoldLicense() {
    const legalHoldLicenseSub = combineLatest([
      this.configService.fetchLicenseStatus$('FEATURE', 'LEGAL HOLD')
    ]).subscribe(([legalHoldlicense]) => {
      this.legalHoldLicense = legalHoldlicense
      this.isCheckingLicense =
        this.allowToManageLegalHold === undefined ? true : false
      const invalidLicense = this.allowToManageLegalHold && !legalHoldlicense
      if (this.allowToManageLegalHold && invalidLicense) {
        this.toastr.error('Legal Hold license not applied.')
        legalHoldLicenseSub.unsubscribe()
      }
    })
  }

  // Open the reports container dialog
  openReportsDialog(data: unknown) {
    this.reportDialogRef = this.dialog.open(ReportsContainerComponent, {
      data,
      minHeight: '60vh',
      height: '98vh',
      width: '98vw',
      maxWidth: '98vw'
    })
  }

  openLegalHoldReportsDialog(data: unknown) {
    this.reportDialogRef = this.dialog.open(ReportsContainerComponent, {
      data,
      minHeight: '60vh',
      height: '98vh',
      width: '98vw',
      maxWidth: '98vw'
    })
  }

  // Open the Service Upload container dialog
  openUploadDialog(isCaseCreationFlow: boolean, projectId?: number) {
    this.serviceUploadDialogRef = this.dialog.open(
      ServiceUploadContainerComponent,
      {
        data: {
          projectId: projectId,
          isCaseCreationFlow: isCaseCreationFlow
        },
        minHeight: '70vh',
        height: '70vh',
        width: '70vw',
        maxWidth: '70vw',
        disableClose: true
      }
    )
  }

  #handleUploadInvitation(): void {
    this.activatedRoute.queryParams
      .pipe(
        map((params) =>
          //lower case of all parameter keys
          Object.keys(params).reduce((acc, key) => {
            acc[key.toLowerCase()] = params[key]
            return acc
          }, {} as Record<string, any>)
        ),
        filter((params) => {
          const projectId = Number(params['projectid'])
          return (
            params['action']?.toLowerCase() === 'directexport' &&
            !isNaN(projectId) &&
            projectId > 0
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((params) => {
        this.#checkUploadInvitationAccess(
          Number(params['projectid']),
          params['userwisetoken']
        )
      })
  }

  // upload invitation token and handle process
  #checkUploadInvitationAccess(projectId: number, userWiseToken: string): void {
    this.xsStore
      .dispatch(new UActions.FetchInvitationHandle(projectId, userWiseToken))
      .pipe(
        switchMap(() =>
          this.xsStore.selectOnce(UploadStateSelector.invitationHandleResponse)
        ),
        filter((response) => !!response),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: string) => {
        if (this.invitationHandle(response)) {
          this.openUploadDialog(false, projectId)
        }
      })
  }

  // upload invitation handle response and show message
  private invitationHandle(response: string): boolean {
    if (!response) return false

    response = response.toUpperCase()
    let invitationHandleMessage = ''

    if (response === 'PROJECT_DOES_NOT_EXIST' || response === 'EXPIRED') {
      invitationHandleMessage =
        response === 'PROJECT_DOES_NOT_EXIST'
          ? 'The Project does not exists.'
          : 'Upload link has expired. This link cannot be further used to upload data.'
    } else if (response === 'UNAUTHORIZED_ACCESS') {
      invitationHandleMessage = `You don't have the right to access this page.`
      this.toastr.error(invitationHandleMessage)
    } else if (response !== 'SUCCESS') {
      invitationHandleMessage = 'The upload invitation link is not valid.'
    }

    if (invitationHandleMessage) {
      this.toastr.error(invitationHandleMessage)
      return false
    } else {
      return true
    }
  }
}
