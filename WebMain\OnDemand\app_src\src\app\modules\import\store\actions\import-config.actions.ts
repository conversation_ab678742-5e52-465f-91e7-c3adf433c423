import { Action } from '@ngrx/store'
import { CustomField } from '../../models/custom-field'
import {
  CustodianConfig,
  DataOverlayConfig,
  ImageOverlayConfig,
  ImportConfig,
  ImportTemplateConfig,
  LoadFileFormatConfig,
  MediaConfig
} from '../../models/import-config'
import { ImportProgress } from '../../models/import-progress'
import { ImportStatus } from '../../models/import-status'
import { ValidateFilePathResult } from '../../models/validate-file-path-result'

export enum ImportConfigActionTypes {
  ConfigureLoadFilePath = '[Import Config] Configure Load File Path',
  ConfigureLoadFileFormat = '[Import Config] Configure Load File Format',
  ConfigureLoadFileProcessOption = '[Import Config] Configure Load File Process Option',

  ConfigureImageLoadFilePath = '[Import Config] Configure Image Load File Path',

  ConfigureNativeProcessOption = '[Import Config] Configure Native Process Option',
  ConfigureNativeFilePathField = '[Import Config] Configure Native File Path Field',
  ConfigureNativeFolderPath = '[Import Config] Configure Natives',
  ConfigureNativeExtractFulltext = '[Import Config] Configure Extract Fulltext From Native',
  ConfigureAutoComputeFileSize = '[Import Config] Configure Auto Compute File Size',
  ConfigureAutoComputeFileExtension = '[Import Config] Configure Auto Compute File Extension',
  ConfigureAutoComputeFileType = '[Import Config] Configure Auto Compute File Type',

  ConfigureImageProcessOption = '[Import Config] Configure Image Process Option',
  ConfigureImageMappingField = '[Import Config] Configure Image Mapping Field',
  ConfigureImageFolderPath = '[Import Config] Configure Images',

  ConfigureFullTextProcessOption = '[Import Config] Configure Full Text Process Option',
  ConfigureFullTextFilePathField = '[Import Config] Configure Full Text File Path Field',
  ConfigureFullTextFolderPath = '[Import Config] Configure Texts',
  ConfigureFulltextFromLoadFile = '[Import Config] Configure Fulltext From Load File',
  ConfigureExtractedTextFields = '[Import Config] Configure Extracted Text Fields',

  ConfigureDateFormat = '[Import Config] Configure Date Format',
  ConfigureTimeZone = '[Import Config] Configure Time Zone',

  AddLoadFileToVenioMapping = '[Import Config] Add Load File To Venio Field Mapping',
  RemoveLoadFileToVenioMapping = '[Import Config] Remove Load File To Venio Field Mapping',
  AddVenioToLoadFileMapping = '[Import Config] Add Venio To Load File Field Mapping',
  RemoveVenioToLoadFileMapping = '[Import Config] Remove Venio To Load File Field Mapping',
  MapCustodian = '[Import Config] Map Custodian',

  ConfigureMapping = '[Import Config] Configure Mapping',
  ClearMapping = '[Import Config] Clear Mapping',

  ConfigureCustodian = '[Import Config] Configure Custodian',

  FetchImportConfig = '[Import Config] Fetch Import Config',
  FetchImportConfigSuccess = '[Import Config] Fetch Import Config Success',

  ValidateLoadFilesAndResources = '[Import Config] Validate Load Files And Resources',
  ValidateFilePaths = '[ImportConfig] Validate File Paths',
  ValidateFilePathsSuccess = '[ImportConfig] Validate File Paths Success',
  ValidateFilePathsFailure = '[ImportConfig] Validate File Paths Failure',
  ValidateFieldMapping = '[Import Config] Validate Fields Mapping',
  StartImport = '[Import Config] Start Import',
  ShowValidateSpinner = '[Import Config] Show Validate Spinner',
  HideValidateSpinner = '[Import Config] Hide Validate Spinner',
  ShowValidateFilePathsSpinner = '[Import Config] Show Validate File Paths Spinners',
  HideValidateFilePathsSpinner = '[Import Config] Hide Validate File Paths Spinners',
  ShowAnalyzeSpinner = '[Import Config] Show Analyze Spinner',
  HideAnalyzeSpinner = '[Import Config] Hide Analyze Spinner',
  ShowImportSpinner = '[Import Config] Show Import Spinner',
  HideImportSpinner = '[Import Config] Hide Import Spinner',

  AddCustomFields = '[Import Config] Add Custom Fields',
  AddCustomFieldsSuccess = '[Import Config] Add Custom Fields Success',
  AddCustomFieldsError = '[Import Config] Add Custom Fields Error',
  ShowCustomFieldsSpinner = '[Import Config] Show Custom Fields Spinner',
  HideCustomFieldsSpinner = '[Import Config] Hide Custom Fields Spinner',
  ClearCustomFieldsState = '[Import Config] Clear Custom Fields State',

  FetchImportStatus = '[Import Config] Fetch Import Status',
  FetchImportStatusSuccess = '[ImportConfig] Fetch Import Status Success',
  FetchImportStatusFailure = '[Import Config] Fetch Import Status Failure',
  FetchImportProgress = '[Import Config] Fetch Import Progress',
  FetchImportProgressSuccess = '[ImportConfig] Fetch Import Progress Success',
  FetchImportProgressFailure = '[Import Config] Fetch Import Progress Failure',
  RefreshImportTicker = '[Import Config] Reset Import Ticker',
  StartPollingForProgress = '[Import Config] Start Polling For Progress',
  StopPollingForProgress = '[Import Config] Stop Polling For Progress',
  StartSchedulerForImportProgress = '[Import Config] Start Scheduler For Import Push Updates',

  ResetImportProgress = '[Import Config] Reset Import Progress',
  ClearUI = '[Import Config] Clear UI',
  CreateImportTemplate = '[Import Config] Create Import Template',
  UpdateImportTemplate = '[Import Config] Update Import Template',
  FetchImportTemplateConfig = '[Import Config] Fetch Import Config From Template',
  FetchImportTemplates = '[Import Config] Fetch Import Templates',
  FetchImportTemplatesSuccess = '[Import Config] Fetch Import Templates Success',
  SetImportTemplate = '[Import Config] Set Import Template',
  CheckImportTemplateExistance = '[Import Config] Check If Template Name Already Exists',
  SetImportTemplateExistance = '[Import Config] Set if Import Template Already Exist Or Not',
  FetchCustomDelimiterAction = '[Import Config] Fetch custom delimiters',
  SetCustomDelimiterAction = '[Import Config] Set custom delimiters',
  FetchDataOverlayConfigAction = '[Import Config] Fetch Data Overlay Config',
  SetDataOverlayConfigAction = '[Import Config] Set Data Overlay Config',
  SetImageOverlayConfigAction = '[Import Config] Set Image Overlay Config',
  SetImageOverlayFlagAction = '[Import Config] Set Image Overlay Flag',
  SetDataOverlayFlagAction = '[Import Config] Set Data Overlay Flag',
  SetDataCustodianMediaConfigAction = '[Import Config] Set Data Custodian Media Config',
  SetProcessCheckboxValueChange = '[Import Config] Set Checkbox Value Change',
  SetValidateFileExistence = '[Import Config] Set Validate File Existence'
}

/* Actions related to configuring load file path and format */

export class ConfigureLoadFilePath implements Action {
  readonly type = ImportConfigActionTypes.ConfigureLoadFilePath

  constructor(readonly payload: string) {}
}

export class ConfigureLoadFileFormat implements Action {
  readonly type = ImportConfigActionTypes.ConfigureLoadFileFormat

  constructor(readonly payload: LoadFileFormatConfig) {}
}

/**
 * Action to set the flag whether to import the fields from load file or not
 */
export class ConfigureLoadFileProcessOption implements Action {
  readonly type = ImportConfigActionTypes.ConfigureLoadFileProcessOption

  constructor(readonly processLoadFile: boolean) {}
}

/* Actions related to configuring image load file path */

export class ConfigureImageLoadFilePath implements Action {
  readonly type = ImportConfigActionTypes.ConfigureImageLoadFilePath

  constructor(readonly payload: string) {}
}

/* Actions related to configuring native files import */

export class ConfigureNativeProcessOption implements Action {
  readonly type = ImportConfigActionTypes.ConfigureNativeProcessOption

  constructor(readonly payload: boolean) {}
}

export class ConfigureNativeExtractFulltext implements Action {
  readonly type = ImportConfigActionTypes.ConfigureNativeExtractFulltext

  constructor(readonly payload: boolean) {}
}

export class ConfigureAutoComputeFileSize implements Action {
  readonly type = ImportConfigActionTypes.ConfigureAutoComputeFileSize

  constructor(readonly payload: boolean) {}
}

export class ConfigureAutoComputeFileExtension implements Action {
  readonly type = ImportConfigActionTypes.ConfigureAutoComputeFileExtension

  constructor(readonly payload: boolean) {}
}

export class ConfigureAutoComputeFileType implements Action {
  readonly type = ImportConfigActionTypes.ConfigureAutoComputeFileType

  constructor(readonly payload: boolean) {}
}

export class ConfigureNativeFilePathField implements Action {
  readonly type = ImportConfigActionTypes.ConfigureNativeFilePathField

  constructor(readonly payload: string) {}
}

export class ConfigureNativeFolderPath implements Action {
  readonly type = ImportConfigActionTypes.ConfigureNativeFolderPath

  constructor(readonly payload: string) {}
}

/* Actions related to configuring fulltext files import */

export class ConfigureFullTextProcessOption implements Action {
  readonly type = ImportConfigActionTypes.ConfigureFullTextProcessOption

  constructor(readonly payload: boolean) {}
}

export class ConfigureFullTextFilePathField implements Action {
  readonly type = ImportConfigActionTypes.ConfigureFullTextFilePathField

  constructor(readonly payload: string) {}
}

export class ConfigureFullTextFolderPath implements Action {
  readonly type = ImportConfigActionTypes.ConfigureFullTextFolderPath

  constructor(readonly payload: string) {}
}

export class ConfigureFulltextFromLoadFile implements Action {
  readonly type = ImportConfigActionTypes.ConfigureFulltextFromLoadFile

  constructor(readonly payload: boolean) {}
}

export class ConfigureExtractedTextFields implements Action {
  readonly type = ImportConfigActionTypes.ConfigureExtractedTextFields

  constructor(readonly payload: string[]) {}
}

/* Actions related to configuring image files import */

export class ConfigureImageProcessOption implements Action {
  readonly type = ImportConfigActionTypes.ConfigureImageProcessOption

  constructor(readonly payload: boolean) {}
}

export class ConfigureImageMappingField implements Action {
  readonly type = ImportConfigActionTypes.ConfigureImageMappingField

  constructor(readonly payload: string) {}
}

export class ConfigureImageFolderPath implements Action {
  readonly type = ImportConfigActionTypes.ConfigureImageFolderPath

  constructor(readonly payload: string) {}
}

/* Actions related to configuring date and timezone */

export class ConfigureDateFormat implements Action {
  readonly type = ImportConfigActionTypes.ConfigureDateFormat

  constructor(readonly payload: string) {}
}

export class ConfigureTimeZone implements Action {
  readonly type = ImportConfigActionTypes.ConfigureTimeZone

  constructor(readonly payload: string) {}
}

/** Actions related to configuring custodian **/

export class ConfigureCustodian implements Action {
  readonly type = ImportConfigActionTypes.ConfigureCustodian

  constructor(readonly custodian: CustodianConfig) {}
}

/** Actions related to configuring field mapping **/

export class AddLoadFileToVenioFieldMapping implements Action {
  readonly type = ImportConfigActionTypes.AddLoadFileToVenioMapping

  constructor(readonly loadFileField: string, readonly venioField: string) {}
}

export class RemoveLoadFileToVenioFieldMapping implements Action {
  readonly type = ImportConfigActionTypes.RemoveLoadFileToVenioMapping

  constructor(readonly loadFileField: string, readonly venioField: string) {}
}

export class AddVenioToLoadFileFieldMapping implements Action {
  readonly type = ImportConfigActionTypes.AddVenioToLoadFileMapping

  constructor(readonly venioField: string, readonly loadFileField: string) {}
}

export class RemoveVenioToLoadFileFieldMapping implements Action {
  readonly type = ImportConfigActionTypes.RemoveVenioToLoadFileMapping

  constructor(readonly venioField: string) {}
}

export class MapCustodian implements Action {
  readonly type = ImportConfigActionTypes.MapCustodian
}

export class ConfigureMapping implements Action {
  readonly type = ImportConfigActionTypes.ConfigureMapping

  constructor(readonly mapping: any) {}
}

export class ClearMapping implements Action {
  readonly type = ImportConfigActionTypes.ClearMapping
}

/* Actions related to fetching import config */

export class FetchImportConfig implements Action {
  readonly type = ImportConfigActionTypes.FetchImportConfig

  constructor(readonly payload: any) {}
}

export class FetchImportConfigSuccess implements Action {
  readonly type = ImportConfigActionTypes.FetchImportConfigSuccess

  constructor(readonly payload: any) {}
}

/* Actions related to running validation and importing */

export class ValidateLoadFilesAndResources implements Action {
  readonly type = ImportConfigActionTypes.ValidateLoadFilesAndResources

  constructor(readonly payload: any) {}
}

export class ValidateFilePaths implements Action {
  readonly type = ImportConfigActionTypes.ValidateFilePaths

  constructor(
    readonly importConfig: ImportConfig,
    readonly projectId: number,
    readonly importId: number,
    readonly isOverlay: boolean
  ) {}
}

export class ValidateFilePathsSuccess implements Action {
  readonly type = ImportConfigActionTypes.ValidateFilePathsSuccess

  constructor(
    readonly message: string,
    readonly validateFilePathResult: ValidateFilePathResult
  ) {}
}

export class ValidateFilePathsFailure implements Action {
  readonly type = ImportConfigActionTypes.ValidateFilePathsFailure

  constructor(readonly errorMessage: string, readonly errorData: any) {}
}

export class ValidateFieldMapping implements Action {
  readonly type = ImportConfigActionTypes.ValidateFieldMapping

  constructor(readonly payload: any) {}
}

export class StartImport implements Action {
  readonly type = ImportConfigActionTypes.StartImport

  constructor(readonly payload: any) {}
}

export class ShowValidateSpinner implements Action {
  readonly type = ImportConfigActionTypes.ShowValidateSpinner
}

export class HideValidationSpinner implements Action {
  readonly type = ImportConfigActionTypes.HideValidateSpinner
}

export class ShowValidateFilePathsSpinner implements Action {
  readonly type = ImportConfigActionTypes.ShowValidateFilePathsSpinner
}

export class HideValidateFilePathsSpinner implements Action {
  readonly type = ImportConfigActionTypes.HideValidateFilePathsSpinner
}

export class ShowImportSpinner implements Action {
  readonly type = ImportConfigActionTypes.ShowImportSpinner
}

export class HideImportSpinner implements Action {
  readonly type = ImportConfigActionTypes.HideImportSpinner
}

export class ShowAnalyzeSpinner implements Action {
  readonly type = ImportConfigActionTypes.ShowAnalyzeSpinner
}

export class HideAnalyzeSpinner implements Action {
  readonly type = ImportConfigActionTypes.HideAnalyzeSpinner
}

/* Actions related to custom fields */

export class AddCustomFields implements Action {
  readonly type = ImportConfigActionTypes.AddCustomFields

  constructor(
    readonly projectId: number,
    readonly customFields: CustomField[]
  ) {}
}

export class AddCustomFieldsSuccess implements Action {
  readonly type = ImportConfigActionTypes.AddCustomFieldsSuccess

  constructor(readonly customFields: any) {}
}

export class AddCustomFieldError implements Action {
  readonly type = ImportConfigActionTypes.AddCustomFieldsError

  constructor(readonly errorMessage: string, readonly errorData: any) {}
}

export class ShowCustomFieldsSpinner implements Action {
  readonly type = ImportConfigActionTypes.ShowCustomFieldsSpinner
}

export class HideCustomFieldsSpinner implements Action {
  readonly type = ImportConfigActionTypes.HideCustomFieldsSpinner
}

export class ClearCustomFieldsState implements Action {
  readonly type = ImportConfigActionTypes.ClearCustomFieldsState
}

export class FetchImportStatus implements Action {
  readonly type = ImportConfigActionTypes.FetchImportStatus

  constructor(readonly projectId: number, readonly importId: number) {}
}

export class FetchImportStatusSuccess implements Action {
  readonly type = ImportConfigActionTypes.FetchImportStatusSuccess

  constructor(readonly importStatus: ImportStatus) {}
}

export class FetchImportStatusFailure implements Action {
  readonly type = ImportConfigActionTypes.FetchImportStatusFailure
}

export class FetchImportProgress implements Action {
  readonly type = ImportConfigActionTypes.FetchImportProgress

  constructor(readonly projectId: number, readonly importId: number) {}
}

export class FetchImportProgressSuccess implements Action {
  readonly type = ImportConfigActionTypes.FetchImportProgressSuccess

  constructor(readonly importProgress: ImportProgress) {}
}

export class FetchImportProgressFailure implements Action {
  readonly type = ImportConfigActionTypes.FetchImportProgressFailure
}

export class RefreshImportTicker implements Action {
  readonly type = ImportConfigActionTypes.RefreshImportTicker
}

export class StartPollingForProgress implements Action {
  readonly type = ImportConfigActionTypes.StartPollingForProgress

  constructor(readonly projectId: number, readonly importId: number) {}
}

export class StopPollingForProgress implements Action {
  readonly type = ImportConfigActionTypes.StopPollingForProgress
}

export class StartSchedulerForImportPushUpdates implements Action {
  readonly type = ImportConfigActionTypes.StartSchedulerForImportProgress
}

export class ResetImportProgress implements Action {
  readonly type = ImportConfigActionTypes.ResetImportProgress
}

export class ClearUI implements Action {
  readonly type = ImportConfigActionTypes.ClearUI
}

/**
 * Action to create the import template
 */
export class CreateImportTemplate implements Action {
  readonly type = ImportConfigActionTypes.CreateImportTemplate

  constructor(readonly payload: any) {}
}

/**
 * Action to update import template
 */
export class UpdateImportTemplate implements Action {
  readonly type = ImportConfigActionTypes.UpdateImportTemplate

  constructor(readonly payload: any) {}
}

/**
 * Action to fetch the import config from template
 */
export class FetchImportTemplateConfig implements Action {
  readonly type = ImportConfigActionTypes.FetchImportTemplateConfig

  constructor(readonly payload: any) {}
}

export class FetchImportTemplates implements Action {
  readonly type = ImportConfigActionTypes.FetchImportTemplates

  constructor(readonly projectId: number) {}
}

export class FetchImportTemplateSuccess implements Action {
  readonly type = ImportConfigActionTypes.FetchImportTemplatesSuccess

  constructor(readonly payload: any) {}
}

export class SetImportTemplate implements Action {
  readonly type = ImportConfigActionTypes.SetImportTemplate

  constructor(readonly importTemplate: ImportTemplateConfig) {}
}

export class CheckImportTemplateExistance implements Action {
  readonly type = ImportConfigActionTypes.CheckImportTemplateExistance

  constructor(readonly importTemplateName: string) {}
}

export class SetImportTemplateExistance implements Action {
  readonly type = ImportConfigActionTypes.SetImportTemplateExistance

  constructor(readonly importTemplateAlreadyExist: boolean) {}
}

export class FetchCustomDelimiterAction implements Action {
  readonly type = ImportConfigActionTypes.FetchCustomDelimiterAction
}

export class SetCustomDelimiterAction implements Action {
  readonly type = ImportConfigActionTypes.SetCustomDelimiterAction

  constructor(readonly delimiters: string[]) {}
}

export class SetDataOverlayConfigAction implements Action {
  readonly type = ImportConfigActionTypes.SetDataOverlayConfigAction

  constructor(readonly dataOverlayConfigs: DataOverlayConfig) {}
}

export class SetImageOverlayConfigAction implements Action {
  readonly type = ImportConfigActionTypes.SetImageOverlayConfigAction

  constructor(readonly imageOverlayConfig: ImageOverlayConfig) {}
}
export class SetDataOverlayFlagAction implements Action {
  readonly type = ImportConfigActionTypes.SetDataOverlayFlagAction

  constructor(readonly flag: boolean) {}
}

export class SetImageOverlayFlagAction implements Action {
  readonly type = ImportConfigActionTypes.SetImageOverlayFlagAction

  constructor(readonly flag: boolean) {}
}

export class SetDataCustodianMediaConfigAction implements Action {
  readonly type = ImportConfigActionTypes.SetDataCustodianMediaConfigAction

  constructor(
    readonly custodian: CustodianConfig,
    readonly media: MediaConfig
  ) {}
}

export class SetValidateFileExistence implements Action {
  readonly type = ImportConfigActionTypes.SetValidateFileExistence;
  constructor(public payload: boolean) {}
}

export type ImportConfigActions =
  | ConfigureLoadFilePath
  | ConfigureLoadFileFormat
  | ConfigureLoadFileProcessOption
  | ConfigureImageLoadFilePath
  | ConfigureNativeProcessOption
  | ConfigureNativeFilePathField
  | ConfigureNativeFolderPath
  | ConfigureNativeExtractFulltext
  | ConfigureFullTextProcessOption
  | ConfigureFullTextFilePathField
  | ConfigureFullTextFolderPath
  | ConfigureFulltextFromLoadFile
  | ConfigureExtractedTextFields
  | ConfigureImageProcessOption
  | ConfigureImageMappingField
  | ConfigureImageFolderPath
  | ConfigureDateFormat
  | ConfigureTimeZone
  | ConfigureCustodian
  | AddVenioToLoadFileFieldMapping
  | RemoveVenioToLoadFileFieldMapping
  | AddLoadFileToVenioFieldMapping
  | RemoveLoadFileToVenioFieldMapping
  | MapCustodian
  | ConfigureMapping
  | ClearMapping
  | FetchImportConfig
  | FetchImportConfigSuccess
  | ValidateLoadFilesAndResources
  | ValidateFilePaths
  | ValidateFieldMapping
  | StartImport
  | AddCustomFields
  | AddCustomFieldsSuccess
  | AddCustomFieldError
  | ShowCustomFieldsSpinner
  | HideCustomFieldsSpinner
  | ClearCustomFieldsState
  | ShowAnalyzeSpinner
  | HideAnalyzeSpinner
  | ShowValidateSpinner
  | HideValidationSpinner
  | ShowImportSpinner
  | HideImportSpinner
  | ShowValidateFilePathsSpinner
  | HideValidateFilePathsSpinner
  | FetchImportStatus
  | FetchImportStatusSuccess
  | FetchImportStatusFailure
  | FetchImportProgress
  | FetchImportProgressSuccess
  | FetchImportProgressFailure
  | StartPollingForProgress
  | StopPollingForProgress
  | RefreshImportTicker
  | StartSchedulerForImportPushUpdates
  | ResetImportProgress
  | ClearUI
  | CreateImportTemplate
  | UpdateImportTemplate
  | FetchImportTemplateConfig
  | FetchImportTemplates
  | FetchImportTemplateSuccess
  | SetImportTemplate
  | CheckImportTemplateExistance
  | SetImportTemplateExistance
  | FetchCustomDelimiterAction
  | SetCustomDelimiterAction
  | SetDataOverlayConfigAction
  | SetImageOverlayConfigAction
  | SetDataOverlayFlagAction
  | SetImageOverlayFlagAction
  | SetDataCustodianMediaConfigAction
  | ConfigureAutoComputeFileSize
  | ConfigureAutoComputeFileExtension
  | ConfigureAutoComputeFileType
  | SetValidateFileExistence
