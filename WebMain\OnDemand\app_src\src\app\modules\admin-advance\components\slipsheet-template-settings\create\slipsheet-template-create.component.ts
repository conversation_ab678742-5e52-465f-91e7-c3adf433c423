import {
  SlipSheetQueryParams,
  SlipSheetTemplateField,
  SlipsheetTemplateModel,
  SlipSheetType
} from '@admin-advance/models/slipsheet-template-settings/slipsheet-template-settings.model'
import { SlipSheetTemplateSettingsService } from '@admin-advance/services'
import {
  AddOrUpdateSlipSheetTemplateAction,
  GetFontsAction,
  GetSlipSheetfieldAction,
  GetSlipSheetfieldByIdAction,
  SlipsheetTemplateSettingsStateSelector
} from '@admin-advance/store'
import { Overlay } from '@angular/cdk/overlay'
import { TemplatePortal } from '@angular/cdk/portal'
import { HttpErrorResponse } from '@angular/common/http'
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  QueryList,
  TemplateRef,
  ViewChild,
  ViewChildren,
  ViewContainerRef
} from '@angular/core'
import {
  FormBuilder,
  FormControlName,
  FormGroup,
  Validators
} from '@angular/forms'
import { MatDialog } from '@angular/material/dialog'
import { MatRadioChange } from '@angular/material/radio'
import { DomSanitizer } from '@angular/platform-browser'
import { ActivatedRoute } from '@angular/router'
import { ConfigService } from '@config/services/config.service'
import { Navigate } from '@ngxs/router-plugin'
import { Store } from '@ngxs/store'
import { animateHeight, fadeInX } from '@shared/animation'
import {
  GenericValidator,
  MessageModel,
  validateBeforeSubmit
} from '@shared/validators/generic-validators'
import { Guid } from 'guid-typescript'
import { ToastrService } from 'ngx-toastr'
import { fromEvent, Observable, Subject, timer } from 'rxjs'
import {
  debounceTime,
  filter,
  map,
  switchMap,
  takeUntil,
  tap
} from 'rxjs/operators'

@Component({
  selector: 'app-slipsheet-template-create',
  templateUrl: './slipsheet-template-create.component.html',
  styleUrls: ['./slipsheet-template-create.component.scss'],
  animations: [fadeInX, animateHeight]
})
export class SlipSheetTemplateCreateComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  isWorking = true

  fontStyleList = [
    { value: 'normal', name: 'Regular' },
    { value: 'Bold', name: 'Bold' },
    { value: 'Italic', name: 'Oblique' },
    { value: 'Bold,Italic', name: 'Bold Oblique' }
  ]

  /**
   * Whether the form is submitting.
   */
  isSubmitting: boolean

  /**
   * Reactive form group to handle user input, validations.
   */
  slipsheetTemplateForm: FormGroup

  fontForm: FormGroup

  fieldForm: FormGroup

  isTemplateNameExists = false

  /**
   * A generic form validation class
   */
  private genericValidator: GenericValidator

  /**
   * Directives to monitor changes so we can perform validation and update message rules accordingly.
   */
  @ViewChildren(FormControlName, { read: ElementRef })
  private readonly formInputElements: QueryList<ElementRef>

  /**
   * Object containing validation fail message defined on generic class.
   */
  displayMessage: MessageModel

  /**
   * Stating Overall errors in a single line of message.
   */
  formErrorMessage: string

  /**
   * Error message container element.
   */
  @ViewChild('messageWrap')
  private readonly errMsgWrap: ElementRef<HTMLDivElement>

  /**
   * Static service of app config.
   */
  config = ConfigService

  /**
   * Holds default name (patch) value of editing mode when it was first pulled from the server.
   * This will get compared when an user tries to change field name to new if it wasn't for create so
   * we'll send API request to validate duplication.
   */
  editingNameValue: string

  /**
   * Confirmation wrapper to ensure deletion.
   */
  @ViewChild('slipsheetField')
  private readonly openTemplateField: TemplateRef<any>

  @ViewChild('font')
  private readonly openFont: TemplateRef<any>

  @ViewChild('imagePreview')
  private readonly imagePreview: TemplateRef<any>

  fieldTypesData: SlipSheetTemplateField[]

  selectedFieldType = ''

  selectedFileType: string

  fontSizeList = [
    { value: '8pt', name: 8 },
    { value: '9pt', name: 9 },
    { value: '10pt', name: 10 },
    { value: '11pt', name: 11 },
    { value: '12pt', name: 12 },
    { value: '14pt', name: 14 },
    { value: '16pt', name: 16 },
    { value: '18pt', name: 18 },
    { value: '20pt', name: 20 },
    { value: '22pt', name: 22 },
    { value: '24pt', name: 24 },
    { value: '26pt', name: 26 },
    { value: '28pt', name: 28 },
    { value: '36pt', name: 36 },
    { value: '48pt', name: 48 },
    { value: '72pt', name: 72 }
  ]

  fontNameList = []

  selectedFont = 'Arial'

  selectedFontWeight = 'normal'

  selectedFontStyle = 'normal'

  selectedFontSize = '8pt'

  pHTextFont = this.selectedFont + ',' + this.selectedFontSize

  slipSheetType = SlipSheetType.usePlaceHolderText // slipsheet selected type

  isImageLoading: boolean

  //base64 string for imf source
  imageToShow: any

  qParams: SlipSheetQueryParams

  @ViewChild('fileInput', { static: false })
  InputVar: ElementRef

  //determine slipsheet type is text or file
  isTextType: boolean

  isCreateMode = true

  showSpinner$: Observable<boolean>

  locationList = [
    'Top Left',
    'Top Center',
    'Top Right',
    'Bottom Left',
    'Bottom Center',
    'Bottom Right',
    'Center'
  ]

  selectedFile: string

  tiffUrl = 'about:blank'

  @ViewChild('tiffViewer') tiffViewer: ElementRef

  /**
   * Token to unsubscribe on component destruction.
   */
  unsubscribed$ = new Subject<void>()

  //default image size on load
  imgWidth = '100%'

  imgHeight = '65vh'

  //actual image size
  actualWidth: any

  actualHeight: any

  isEditRestricted = false

  constructor(
    private dialog: MatDialog,
    private store: Store,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private route: ActivatedRoute,
    private toast: ToastrService,
    private overlay: Overlay,
    private vcr: ViewContainerRef,
    private ssService: SlipSheetTemplateSettingsService,
    private domSanitizer: DomSanitizer
  ) {
    this.qParams = route.snapshot.queryParams as SlipSheetQueryParams
    this.initSlices()
  }

  ngOnInit(): void {
    this.initForm()
    this.LoadData()
  }

  LoadData() {
    this.store.dispatch(new GetSlipSheetfieldAction())
    this.store.dispatch(new GetFontsAction())
  }

  defaultPlaceHolderPosition: any

  LoadDefaultData() {
    this.defaultPlaceHolderPosition = this.locationList[6] //StampLocation.Center
    this.selectedFontStyle = this.fontStyleList[0].value
  }

  ngAfterViewInit(): void {
    timer(700)
      .pipe(
        tap(() => [(this.isWorking = false)]),
        debounceTime(500),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: () => [this.cdr.detectChanges()]
      })

    this.initValidationRules()

    this.validationWatcher()

    this.LoadDefaultData()
    this.loadByQueryParam()
  }

  /**
   * Captures the query param change event, loads data based on those IDs
   */
  private loadByQueryParam(): void {
    this.route.queryParamMap
      .pipe(
        debounceTime(500),
        // capture param value as our type
        map(() => this.route.snapshot.queryParams as SlipSheetQueryParams),
        // see if we are getting correct values
        filter((q) => q?.slipsheetTemplateId > 0),
        tap((q) =>
          // load data object of that ID
          this.store.dispatch(
            new GetSlipSheetfieldByIdAction(q.slipsheetTemplateId)
          )
        ),
        // once it got loaded, select a slice and patch form with values.
        switchMap(() =>
          this.store.select(
            SlipsheetTemplateSettingsStateSelector.SliceOf('editableSlipsheet')
          )
        ),
        debounceTime(200)
      )
      .subscribe((res) => {
        this.patchForm(res)
      })
  }

  /**
   * Initialize a new instance of form group with controls & sync/async validation rules
   */
  private initForm(): void {
    this.isTextType = true
    this.cdr.markForCheck()
    this.slipsheetTemplateForm = this.fb.group({
      slipsheetTemplateId: 0,
      slipsheetTemplateName: [null, Validators.compose([Validators.required])],
      rdPlaceholderText: this.isTextType,
      placeHolderPosition: this.locationList[6],
      placeHolderText: '',
      rdPlaceholderTiffFile: null,
      placeHolderFile: null,
      placeHolderTextFont: this.pHTextFont
    })

    this.fontForm = this.fb.group({
      fontFamily: this.fontNameList[0],
      fontWeight: this.fontStyleList[0].value,
      fontSize: this.fontSizeList[0].value
    })

    this.fieldForm = this.fb.group({
      fieldType: ''
    })

    if (this.isTextType) {
      const ctrlText = this.slipsheetTemplateForm.get('placeHolderText')
      const ctrlFile = this.slipsheetTemplateForm.get('placeHolderFile')
      ctrlFile.setErrors(null)
      ctrlText.setValidators(Validators.required)
      ctrlText.updateValueAndValidity()
      // clear validation of removed elements
      ctrlFile.clearValidators()
      this.slipsheetTemplateForm.controls['placeHolderText'].enable()
    }

    this.imgWidth = '800px'
    this.imgHeight = '900px'
  }

  private initSlices(): void {
    this.store
      .select(SlipsheetTemplateSettingsStateSelector.SliceOf('fontName'))
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe((d) => {
        if (d?.length > 0) {
          this.fontNameList = d
          const index = this.fontNameList.findIndex(
            (a) => a === 'Microsoft Sans Serif'
          )
          this.fontForm.get('fontFamily').setValue(this.fontNameList[index])
          this.selectedFont = this.fontNameList[index]
        }
      })

    this.store
      .select(SlipsheetTemplateSettingsStateSelector.SliceOf('fieldList'))
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe((d) => {
        if (d?.length > 0) {
          this.fieldTypesData = d
          this.fieldForm.get('fieldType').setValue(this.fieldTypesData[0])
        }
      })
  }

  saveChanges() {
    this.cdr.markForCheck()
    this.formErrorMessage = null
    const errorMessage = validateBeforeSubmit(this.slipsheetTemplateForm)
    if (errorMessage) {
      this.errMsgWrap.nativeElement.scrollIntoView()
      this.formErrorMessage = errorMessage
      return
    }
    this.pHTextFont =
      this.fontForm.get('fontFamily').value +
      ',' +
      this.fontForm.get('fontSize').value +
      (this.fontForm.get('fontWeight').value === 'normal'
        ? ''
        : ',style=' + this.fontForm.get('fontWeight').value)
    this.slipsheetTemplateForm
      .get('placeHolderTextFont')
      .setValue(this.pHTextFont)
    const body: SlipsheetTemplateModel = this.slipsheetTemplateForm.value
    if (!this.isTextType) {
      body.slipSheetType = this.slipSheetType
    }

    this.store
      .dispatch(new AddOrUpdateSlipSheetTemplateAction(body))
      .pipe(
        switchMap(() =>
          this.store.select(
            SlipsheetTemplateSettingsStateSelector.SliceOf('addUpdateResponse')
          )
        ),
        filter((res) => !!res),
        debounceTime(800),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (res) => {
          this.cdr.markForCheck()
          this.isSubmitting = false
          // a long listing instruction error message
          if (res instanceof HttpErrorResponse) {
            this.errMsgWrap.nativeElement.scrollIntoView()
            this.formErrorMessage = res?.error.message
          } else {
            this.toast.success(res.message)
            // redirect to the manage section
            this.store.dispatch(
              new Navigate(['/admin/system/slipsheet-template-settings/manage'])
            )
          }
        }
      })
  }

  /**
   * Patch an existing for with values.
   * @param d Supply the data t patch values to modify or set as default.
   */
  private patchForm(d: SlipsheetTemplateModel): void {
    if (d !== undefined) {
      this.cdr.markForCheck()

      // disable editing if user go the tempate edit page from URL
      this.isEditRestricted = !d.editable

      this.editingNameValue = d.slipsheetTemplateName

      this.slipsheetTemplateForm.patchValue({
        slipsheetTemplateId: d.slipsheetTemplateId,
        slipsheetTemplateName: d.slipsheetTemplateName,
        placeHolderPosition: d.placeHolderPosition,
        placeHolderText: d.placeHolderText,
        placeHolderFile: d.placeHolderFile,
        placeHolderTextFont: d.placeHolderTextFont,
        slipSheetType: d.slipSheetType
      })
      this.getFontProperties(d.placeHolderTextFont)
      this.isTextType = true
      this.slipsheetTemplateForm.controls['placeHolderText'].enable()
      this.isCreateMode = false
      this.toggleValidation('')
      this.selectedFile = ''
      if (d.slipSheetType === SlipSheetType.usePlaceHolderFile) {
        this.defaultPlaceHolderPosition = this.locationList[6]
        this.slipsheetTemplateForm
          .get('placeHolderPosition')
          .setValue(this.locationList[6])
        this.isTextType = false
        this.slipsheetTemplateForm.controls['placeHolderText'].disable()
        this.toggleValidation('rdPlaceholderTiffFile')
        this.selectedFile = Guid.create().toString() + '.tiff'
        this.imageToShow = 'data:image/png;base64,' + d.placeHolderFile
      }
    }
  }

  getFontProperties(st: string) {
    if (st !== '' && st !== undefined && st !== null) {
      const t = st.split(',')
      let familyIndex = 0
      let styleIndex = 0
      let sizeIndex = 0
      if (t.length >= 2) {
        const family = t[0]?.trim()
        familyIndex = this.fontNameList?.findIndex((a) => a === family)
        const size = t[1]?.trim()
        sizeIndex = this.fontSizeList?.findIndex((a) => a.value === size)
        if (t.length === 4) {
          const style = t[2]?.trim().replace('style=', '') + ',' + t[3].trim()
          styleIndex = this.fontStyleList.findIndex((a) => a.value === style)
        } else if (t.length > 2) {
          const style = t[2]?.trim().replace('style=', '')
          styleIndex = this.fontStyleList.findIndex((a) => a.value === style)
        }
      }
      this.fontForm.patchValue({
        fontFamily:
          familyIndex !== -1
            ? this.fontNameList[familyIndex]
            : this.fontNameList[0],
        fontWeight:
          styleIndex !== -1
            ? this.fontStyleList[styleIndex]?.value
            : this.fontStyleList[0]?.value,
        fontSize:
          sizeIndex !== -1
            ? this.fontSizeList[sizeIndex]?.value
            : this.fontSizeList[0]?.value
      })
    }
    return ''
  }

  selectFile() {
    const element: HTMLElement = document.querySelector(
      'input[type="file"]'
    ) as HTMLElement
    element.click()
  }

  handleFileInput(e) {
    this.imageToShow = ''
    const filename = document.getElementById('placeHolderFile').nodeValue
    document.getElementById('placeHolderFile').nodeValue = filename
    const file = (e.target as HTMLInputElement).files[0]
    this.selectedFileType = (e.target as HTMLInputElement).files[0]?.type
    this.selectedFile = file.name
    const reader = new FileReader()
    reader.onload = () => {
      this.imageToShow = reader.result as string
    }
    reader.onloadend = (e) => {
      this.slipsheetTemplateForm
        .get('placeHolderFile')
        .setValue(this.imageToShow.split(',')[1])
    }
    reader.readAsDataURL(file)
  }

  /**
   * A generic validation rules, properties with message.
   * Must be called before the form being initialized.
   */
  private initValidationRules(): void {
    this.genericValidator = new GenericValidator({
      slipsheetTemplateName: {
        required: 'Template Name is a required field.',
        exist: 'Template with same name already exists. Please try another.'
      },
      placeHolderText: {
        required: 'Slipsheet text is required.'
      },
      placeHolderPosition: {
        required: 'Slipsheet text location is required.'
      },
      placeHolderFile: {
        required: 'Slipsheet file is required.'
      }
    })
  }

  /**
   * This validation watcher goes to `ngAfterViewInit` life cycle hook because of we need
   * to track the inputs, elements and render the validation rule messages accordingly.
   *
   * Compares the rule with user input based on validation sets of form group control and populates
   * messages which is defined in `initValidationRules` fn.
   */
  private validationWatcher(): void {
    this.genericValidator
      .initValidationProcess(this.slipsheetTemplateForm, this.formInputElements)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({
        next: (m) => [this.cdr.markForCheck(), (this.displayMessage = m)]
      })
  }

  resetForm(tpl: TemplateRef<Element>): void {
    if (
      this.slipsheetTemplateForm.get('slipsheetTemplateName').value === '' ||
      this.slipsheetTemplateForm.get('slipsheetTemplateName').value ===
        undefined ||
      this.slipsheetTemplateForm.get('slipsheetTemplateName').value === null
    )
      return

    // we use cdk overlay module by angular to prompt reminder alert
    const portal = new TemplatePortal(tpl, this.vcr)

    // check if we have updated values on the directive.
    this.cdr.detectChanges()

    const overlayRef = this.overlay.create({
      disposeOnNavigation: true,
      positionStrategy: this.overlay
        .position()
        .global()
        .centerHorizontally()
        .centerVertically(),
      hasBackdrop: true,
      maxWidth: '300px',
      scrollStrategy: this.overlay.scrollStrategies.block()
    })
    overlayRef.attach(portal)

    overlayRef
      .backdropClick()
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({ next: () => [overlayRef.detach()] })

    // we're searching elements within this overlay context and binding the events as our need.
    const actions = overlayRef.overlayElement.querySelectorAll('.close-confirm')

    fromEvent(actions, 'click')
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((el) => {
        const button = el.target as HTMLButtonElement
        const isReset = button.getAttribute('data-reset') === 'true'
        if (isReset) {
          this.cdr.markForCheck()

          // cleanup
          this.formErrorMessage = null
          //this.toDefault()
          const initialValues = {
            slipsheetTemplateId: 0,
            placeHolderText: '',
            placeHolderPosition: 6
          }
          this.isCreateMode = true
          this.InputVar.nativeElement.value = ''
          this.slipsheetTemplateForm.reset(initialValues)
        }
        overlayRef.detach()
      })
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  openField() {
    const delRef = this.dialog.open(this.openTemplateField, {
      closeOnNavigation: true,
      autoFocus: false,
      width: '350px'
    })
    delRef.afterOpened().subscribe((res) => {
      //this.fieldForm.get('fieldType').setValue(this.fieldTypesData[0])
    })
  }

  AddFieldType() {
    const initialData = this.slipsheetTemplateForm.get('placeHolderText').value
    this.slipsheetTemplateForm
      .get('placeHolderText')
      .setValue(initialData + '%' + this.fieldForm.get('fieldType').value + '%')
  }

  onrdPlaceholderTextClick($event: MatRadioChange) {
    const name = $event.source.id
    this.toggleValidation(name)
  }

  //change the image dimension as per selection
  radioChange($event: MatRadioChange) {
    const name = $event.source.value
    if (name === 'isActualSize') {
      this.imgWidth = this.actualWidth + 'px'
      this.imgHeight = this.actualHeight + 'px'
    } else {
      this.imgWidth = '100%'
      this.imgHeight = '65vh'
    }
  }

  //capture the actual size of the image on load
  loadImage(e) {
    if (e && e.target) {
      this.actualWidth = e.target.naturalWidth
      this.actualHeight = e.target.naturalHeight
    }
  }

  toggleValidation(name) {
    const ctrlText = this.slipsheetTemplateForm.get('placeHolderText')
    const ctrlFile = this.slipsheetTemplateForm.get('placeHolderFile')
    if (name === 'rdPlaceholderTiffFile') {
      this.slipSheetType = SlipSheetType.usePlaceHolderFile

      ctrlText.setErrors(null)
      ctrlFile.setValidators(Validators.required)
      ctrlFile.updateValueAndValidity()
      // clear validation of removed elements
      ctrlText.clearValidators()
      this.isTextType = false
      this.slipsheetTemplateForm.controls['placeHolderText'].disable()
    } else {
      this.slipSheetType = SlipSheetType.usePlaceHolderText
      ctrlFile.setErrors(null)
      ctrlText.setValidators(Validators.required)
      ctrlText.updateValueAndValidity()
      this.slipsheetTemplateForm.controls['placeHolderText'].enable()
      // clear validation of removed elements
      ctrlFile.clearValidators()
      this.isTextType = true
    }
  }

  openFontStyle() {
    const delRef = this.dialog.open(this.openFont, {
      closeOnNavigation: true,
      autoFocus: false,
      width: '600px'
    })
    delRef.afterOpened().subscribe((res) => {
      this.selectedFont = this.fontForm.get('fontFamily').value
      this.selectedFontSize = this.fontForm.get('fontSize').value
      this.selectedFontWeight = this.fontForm.get('fontWeight').value
      this.selectedFontStyle = this.fontForm.get('fontWeight').value
    })
  }

  fontFamilyChanged(e): void {
    this.selectedFont = e.value
  }

  fontWeightChanged(e): void {
    this.selectedFontStyle = ''
    if (e.value === 'Italic') {
      this.selectedFontStyle = e.value
      return
    } else if (e.value === 'Bold,Italic') {
      this.selectedFontStyle = 'Italic'
      this.selectedFontWeight = 'Bold'
      return
    }
    this.selectedFontWeight = e.value
  }

  fontSizeChanged(e): void {
    this.selectedFontSize = e.value
  }

  customizeText(cellInfo) {
    if (cellInfo.value === '' || cellInfo.value === null) {
      return cellInfo.value + ' NA'
    }
    return cellInfo.value
  }

  showPreview(event) {
    const file = (event.target as HTMLInputElement).files[0]
    const reader = new FileReader()
    reader.onload = () => {
      this.imageToShow = reader.result as string
    }
    reader.readAsDataURL(file)
  }

  previewImage() {
    this.isImageLoading = true

    // const placeHolderPosition = StampLocation[body.placeHolderPosition]

    const selectedStyle =
      this.fontForm.get('fontWeight').value === 'normal'
        ? ''
        : ',' + this.fontForm.get('fontWeight').value

    this.selectedFont = this.fontForm.get('fontFamily').value
    this.selectedFontSize = this.fontForm.get('fontSize').value
    this.pHTextFont =
      this.selectedFont + ',' + this.selectedFontSize + selectedStyle
    this.slipsheetTemplateForm
      .get('placeHolderTextFont')
      .setValue(this.pHTextFont)
    const body: SlipsheetTemplateModel = this.slipsheetTemplateForm.value
    if (this.isTextType) {
      if (body.placeHolderText === '') {
        this.toast.error('Please provide slipsheet text for previewing')
        return
      }
    } else {
      if (this.imageToShow === null || this.imageToShow === undefined) {
        this.toast.error('Please provide slipsheet tiff file for previewing')
        return
      }
    }
    const payload = body
    const imageRef = this.dialog.open(this.imagePreview, {
      closeOnNavigation: false,
      autoFocus: false,
      width: '80%',
      maxWidth: '700px',
      height: '90vh',
      maxHeight: '800px',
      disableClose: true
    })
    if (this.isTextType) {
      this.imageToShow = ''
      imageRef
        .afterOpened()
        .pipe(
          switchMap(() => this.ssService.GetTiffPreview(payload)),
          debounceTime(100),
          takeUntil(this.toDestroy$)
        )
        .subscribe(
          (data) => {
            this.imageToShow = this.domSanitizer.bypassSecurityTrustResourceUrl(
              'data:image/png;base64,' + data?.data
            )
            this.isImageLoading = false
            this.imgWidth = '100%'
            this.imgHeight = '65vh'
          },
          (error) => {
            this.isImageLoading = false
            console.log(error)
          }
        )
    } else {
      if (
        this.selectedFileType === 'image/tiff' ||
        this.selectedFileType === undefined
      ) {
        imageRef
          .afterOpened()
          .pipe(
            switchMap(() => this.ssService.GetTiffPreviewFromImage(payload)),
            debounceTime(100),
            takeUntil(this.toDestroy$)
          )
          .subscribe(
            (data) => {
              this.imageToShow =
                this.domSanitizer.bypassSecurityTrustResourceUrl(
                  'data:image/jpg;base64,' + data?.data
                )
              this.isImageLoading = false
              this.imgWidth = '100%'
              this.imgHeight = '65vh'
            },
            (error) => {
              this.isImageLoading = false
              console.log(error)
            }
          )
      }
      // else {
      //   this.dialog.open(this.imagePreview, {
      //     closeOnNavigation: false,
      //     autoFocus: false,
      //     width: '600px',
      //     disableClose: true
      //   })
      // }
      this.isImageLoading = false
    }
  }

  createImageFromBlob(image: Blob) {
    const reader = new FileReader()
    reader.addEventListener(
      'load',
      () => {
        this.imageToShow = reader.result

        this.isImageLoading = false
      },
      false
    )

    if (image) {
      reader.readAsDataURL(image)
    }
  }
}
