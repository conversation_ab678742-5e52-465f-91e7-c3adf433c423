<form class="case-service-container" [formGroup]="settingsForm">
  <div class="col-md-8 d-flex flex-row p-0">
    <div class="form-group col-4 p-0 mr-3">
      <ng-select
        id="service-type"
        class="form-control"
        formControlName="ServiceRequestType"
        [items]="serviceTypeList"
        bindLabel="serviceTypeDisplayName"
        bindValue="serviceTypeId"
        [clearable]="false"
        [searchable]="false"
        placeholder="Select Service Type"
        (change)="onServiceRequestSelection($event)"
        [class.is-invalid]="
          settingsForm.get('ServiceRequestType').invalid &&
          settingsForm.get('ServiceRequestType').touched
        "
      >
        <div
          *ngIf="
            settingsForm.get('ServiceRequestType').invalid &&
            settingsForm.get('ServiceRequestType').touched
          "
          class="isa_error"
        >
          Service Type is required.
        </div>
      </ng-select>
    </div>

    <div
      class="form-group col-4 p-0 mr-3"
      *ngIf="(existingCase && showCaseNameInput) || !existingCase"
    >
      <input
        type="text"
        id="case-name"
        class="form-control"
        [class.is-invalid]="
          settingsForm.get('caseName').invalid &&
          settingsForm.get('caseName').touched
        "
        placeholder="Type Case Name"
        formControlName="caseName"
        (blur)="settingsForm.get('caseName').updateValueAndValidity()"
      />
      <div
        *ngIf="
          settingsForm.get('caseName').touched &&
          settingsForm.get('caseName').hasError('required')
        "
        class="isa_error"
      >
        Case Name is required.
      </div>
      <div
        *ngIf="
          settingsForm.get('caseName').errors?.notUniqueName &&
          settingsForm.get('caseName').touched
        "
        class="isa_error"
      >
        Case Name must be unique.
      </div>
    </div>

    <div
      class="form-group col-4 p-0"
      *ngIf="existingCase && !showCaseNameInput"
    >
      <ng-select
        id="service-type"
        class="form-control"
        formControlName="selectedCase"
        [items]="caseList"
        bindLabel="CaseName"
        bindValue="ProjectId"
        [clearable]="false"
        [searchable]="false"
        placeholder="Select Your Case"
        (change)="selectedCaseChanged($event?.ProjectId)"
      >
      </ng-select>
    </div>
  </div>

  <div
    class="form-group col-12 p-0"
    *ngIf="existingCase && this.selectedCaseId > 0"
  >
    <mat-checkbox
      [ngModel]="overrideSettings"
      [ngModelOptions]="{ standalone: true }"
      (change)="updateOverrideSettingsOption($event.checked)"
      id="override-settings"
    >
      Override Service Request Settings
    </mat-checkbox>
  </div>
  <accordion
    class="message"
    [isAnimated]="true"
    [closeOthers]="!openAllAccordions"
    *ngIf="isValidFormFilled()"
  >
    <!-- General Settings: Start -->
    <accordion-group heading="General Settings" [isOpen]="generalSettingsOpen">
      <form formGroupName="generalSettings">
        <div class="form-row m-0">
          <div class="col-3">
            <div class="form-group">
              <label for="deduplication-option" class="font-weight-bold"
                >Deduplication Option</label
              >
              <ng-select
                id="deduplication-option"
                class="form-control"
                formControlName="deduplicationOption"
                [items]="deduplicationOptions"
                bindLabel="displayName"
                bindValue="value"
                [clearable]="false"
                [searchable]="false"
              ></ng-select>
            </div>
          </div>
          <div class="col-3">
            <div class="form-group">
              <label for="timezone-select" class="font-weight-bold"
                >Timezone</label
              >
              <ng-select
                id="timezone-select"
                class="form-control"
                formControlName="timeZone"
                [bindLabel]="'displayName'"
                [bindValue]="'tzTimeZone'"
                [items]="timeZones$ | async"
                [clearable]="false"
                [virtualScroll]="true"
                [searchable]="false"
              >
              </ng-select>
            </div>
          </div>
          <div class="col-3">
            <div class="form-group">
              <label for="csv-excel-handling" class="font-weight-bold"
                >CSV/Excel Handling</label
              >
              <ng-select
                id="csv-excel-handling"
                class="form-control"
                formControlName="csvExcelHandling"
                [items]="csvExcelOptions"
                bindLabel="displayName"
                bindValue="value"
                [clearable]="false"
                [searchable]="false"
              ></ng-select>
            </div>
          </div>
          <div class="col-3">
            <div class="form-group">
              <label for="discovery-exception-handling" class="font-weight-bold"
                >Discovery Exception Handling</label
              >
              <ng-select
                id="discovery-exception-handling"
                class="form-control"
                formControlName="discoveryExceptionHandling"
                [items]="exceptionHandlingOptions"
                bindLabel="displayName"
                bindValue="value"
                [clearable]="false"
                [searchable]="false"
              >
                <ng-template
                  ng-option-tmp
                  let-item="item"
                  let-index="index"
                  let-search="searchTerm"
                >
                  <span
                    [matTooltip]="item.displayName"
                    matTooltipPosition="above"
                    >{{ item.displayName }}</span
                  >
                </ng-template></ng-select
              >
            </div>
          </div>
        </div>
      </form>
      <form formGroupName="imageConversionSettings">
        <div class="form-row m-0">
          <div class="col">
            <div class="form-group">
              <label class="font-weight-bold" for="passwords">Passwords</label
              ><br />
              <label class="font-weight-lighter" for="passwords"
                >Please list passwords below, list each password on a separate
                line:</label
              >
              <textarea
                class="form-control"
                id="passwords"
                rows="3"
                formControlName="passwordList"
              ></textarea>
            </div>
          </div>
        </div>
      </form>
    </accordion-group>
    <!-- General Settings: End -->

    <!-- Image Conversion Options: Start -->
    <accordion-group heading="Image Conversion Options">
      <form formGroupName="imageConversionSettings">
        <div class="form-row m-0">
          <div class="col">
            <label class="font-weight-bold mb-4">Color Conversion</label>

            <div
              class="d-flex flex-column gap-1"
              formGroupName="imageColorConversion"
            >
              <div class="form-group d-flex flex-row m-0">
                <label class="col-sm-4 col-form-label p-0"
                  >Image File Type (png, tif, jpg, gif)</label
                >
                <div class="col-sm-8">
                  <div
                    class="custom-control custom-radio custom-control-inline"
                  >
                    <input
                      class="custom-control-input"
                      type="radio"
                      name="imageFileType"
                      id="image-file-type-bw"
                      [value]="1"
                      formControlName="imageFileType"
                    />
                    <label class="custom-control-label" for="image-file-type-bw"
                      >Black and White</label
                    >
                  </div>
                  <div
                    class="custom-control custom-radio custom-control-inline"
                  >
                    <input
                      class="custom-control-input"
                      type="radio"
                      name="imageFileType"
                      id="image-file-type-color"
                      [value]="2"
                      formControlName="imageFileType"
                    />
                    <label
                      class="custom-control-label"
                      for="image-file-type-color"
                      >Color</label
                    >
                  </div>
                </div>
              </div>
              <div class="form-group d-flex flex-row m-0">
                <label class="col-sm-4 col-form-label p-0">PDF Files</label>
                <div class="col-sm-8">
                  <div
                    class="custom-control custom-radio custom-control-inline"
                  >
                    <input
                      class="custom-control-input"
                      type="radio"
                      name="pdfFiles"
                      id="pdf-files-bw"
                      [value]="1"
                      formControlName="pdfFiles"
                    />
                    <label class="custom-control-label" for="pdf-files-bw"
                      >Black and White</label
                    >
                  </div>
                  <div
                    class="custom-control custom-radio custom-control-inline"
                  >
                    <input
                      class="custom-control-input"
                      type="radio"
                      name="pdfFiles"
                      id="pdf-files-color"
                      [value]="2"
                      formControlName="pdfFiles"
                    />
                    <label class="custom-control-label" for="pdf-files-color"
                      >Color</label
                    >
                  </div>
                </div>
              </div>
              <div class="form-group d-flex flex-row m-0">
                <label class="col-sm-4 col-form-label p-0">Powerpoint</label>
                <div class="col-sm-8">
                  <div
                    class="custom-control custom-radio custom-control-inline"
                  >
                    <input
                      class="custom-control-input"
                      type="radio"
                      name="powerpoint"
                      id="power-point-bw"
                      [value]="1"
                      formControlName="powerpoint"
                    />
                    <label class="custom-control-label" for="power-point-bw"
                      >Black and White</label
                    >
                  </div>
                  <div
                    class="custom-control custom-radio custom-control-inline"
                  >
                    <input
                      class="custom-control-input"
                      type="radio"
                      name="powerpoint"
                      id="power-point-color"
                      [value]="2"
                      formControlName="powerpoint"
                    />
                    <label class="custom-control-label" for="power-point-color"
                      >Color</label
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </accordion-group>
    <!-- Image Conversion Options: End -->

    <!-- Control Numbering and Endorsement Settings: Start -->
    <accordion-group
      heading="Control Numbering and Endorsement"
      [isOpen]="openAllAccordions"
    >
      <form formGroupName="controlNumberAndEndorsementSettings">
        <div class="form-row m-0 col-3 p-0">
          <div class="col pl-0">
            <div class="form-group mb-0">
              <ng-select
                id="sort-order-select"
                class="form-control"
                formControlName="sortOrder"
                [items]="sortOrderOptions"
                bindLabel="displayName"
                bindValue="value"
                [clearable]="false"
                [searchable]="false"
              ></ng-select>
            </div>
          </div>
        </div>

        <div class="dashed-divider"></div>

        <div class="row" formGroupName="ControlNumberSetting">
          <div class="col">
            <div class="form-row m-0">
              <div class="form-group col-3 mb-0 pl-0">
                <input
                  type="text"
                  class="form-control"
                  id="prefix"
                  [class.is-invalid]="
                    settingsForm.get(
                      'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberPrefix'
                    )?.invalid
                  "
                  placeholder="Prefix"
                  formControlName="controlNumberPrefix"
                />
                <div
                  *ngIf="
                    settingsForm.get(
                      'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberPrefix'
                    ).invalid
                  "
                  class="invalid-feedback"
                >
                  Prefix is required.
                </div>
              </div>
              <div class="form-group col-3 mb-0">
                <ng-select
                  id="prefix-delimiter"
                  class="form-control"
                  formControlName="controlNumberDelimiter"
                  [items]="prefixDelimiterOptions"
                  bindLabel="displayName"
                  bindValue="value"
                  [clearable]="false"
                  [searchable]="false"
                ></ng-select>
              </div>
              <div class="form-group col-3 mb-0">
                <input
                  type="text"
                  class="form-control"
                  [class.is-invalid]="
                    (this.settingsForm
                      .get(
                        'controlNumberAndEndorsementSettings.ControlNumberSetting'
                      )
                      .hasError('requiredStartingNumber') ||
                      this.settingsForm
                        .get(
                          'controlNumberAndEndorsementSettings.ControlNumberSetting'
                        )
                        .hasError('nonNumericStartingNumber')) &&
                    settingsForm.get(
                      'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberStartingNumber'
                    ).touched
                  "
                  id="starting-number"
                  maxlength="18"
                  placeholder="Starting Number"
                  formControlName="controlNumberStartingNumber"
                />
                <div
                  class="isa_error"
                  *ngIf="
                    settingsForm
                      .get(
                        'controlNumberAndEndorsementSettings.ControlNumberSetting'
                      )
                      .hasError('requiredStartingNumber') &&
                    settingsForm.get(
                      'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberStartingNumber'
                    ).touched
                  "
                >
                  Starting number is required.
                </div>
                <div
                  class="isa_error"
                  *ngIf="
                    settingsForm
                      .get(
                        'controlNumberAndEndorsementSettings.ControlNumberSetting'
                      )
                      .hasError('nonNumericStartingNumber') &&
                    settingsForm.get(
                      'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberStartingNumber'
                    ).touched
                  "
                >
                  Please enter numeric value.
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="dashed-divider"></div>

        <div
          class="d-flex flex-row col-8 p-0"
          formGroupName="ControlNumberSetting"
        >
          <div class="col-5 p-0 mr-4">
            <div class="form-group mb-0">
              <div class="custom-control custom-checkbox">
                <input
                  type="checkbox"
                  class="custom-control-input"
                  id="endorse-control-number-checkbox"
                  formControlName="endorseControlNumber"
                  #endorseControlNumber
                />
                <label
                  class="custom-control-label"
                  for="endorse-control-number-checkbox"
                  >Endorse Control Number</label
                >
              </div>
            </div>
            <div class="form-group mb-0" *ngIf="endorseControlNumber.checked">
              <ng-select
                id="controlNumberLocation"
                class="form-control"
                formControlName="controlNumberLocation"
                [items]="controlNumberLocationOptions"
                bindLabel="displayName"
                bindValue="value"
                [clearable]="false"
                [searchable]="false"
              ></ng-select>
            </div>
          </div>
          <div class="col-6 p-0">
            <div class="form-group mb-0">
              <div class="custom-control custom-checkbox">
                <input
                  type="checkbox"
                  class="custom-control-input"
                  id="endorse-optional-message"
                  formControlName="endorseOptionalMessage"
                  #endorseOptionalMessage
                />
                <label
                  class="custom-control-label"
                  for="endorse-optional-message"
                  >Endorse Optional Message</label
                >
              </div>
            </div>
            <div class="form-group" *ngIf="endorseOptionalMessage.checked">
              <textarea
                class="form-control"
                id="endorse-message-text"
                rows="3"
                placeholder="Message"
                formControlName="messageText"
              ></textarea>
            </div>
            <div class="form-group" *ngIf="endorseOptionalMessage.checked">
              <ng-select
                id="messageTextLocation"
                class="form-control"
                formControlName="messageTextLocation"
                [items]="controlNumberLocationOptions"
                bindLabel="displayName"
                bindValue="value"
                [clearable]="false"
                [searchable]="false"
              ></ng-select>
            </div>
          </div>
        </div>

        <div class="dashed-divider"></div>

        <div class="form-group row" formGroupName="ControlNumberSetting">
          <div class="col-5">
            <input
              type="text"
              class="form-control"
              id="volume-id"
              placeholder="Volume ID *"
              formControlName="volumeId"
            />
            <div
              class="isa_error"
              *ngIf="
                settingsForm
                  .get(
                    'controlNumberAndEndorsementSettings.ControlNumberSetting'
                  )
                  .hasError('invalidVolumeId') &&
                settingsForm.get(
                  'controlNumberAndEndorsementSettings.ControlNumberSetting.volumeId'
                ).touched
              "
            >
              Volume id contains invalid characters.
            </div>
            <div
              class="isa_error"
              *ngIf="
                settingsForm
                  .get(
                    'controlNumberAndEndorsementSettings.ControlNumberSetting'
                  )
                  .hasError('requiredVolumeId') &&
                settingsForm.get(
                  'controlNumberAndEndorsementSettings.ControlNumberSetting.volumeId'
                ).touched
              "
            >
              Volume id is required.
            </div>
          </div>
        </div>
      </form>
    </accordion-group>
    <!-- Control Numbering and Endorsement Settings: End -->

    <!-- Production Settings: Start -->
    <accordion-group heading="Production Options">
      <form formGroupName="productionSettings">
        <div class="form-group row mb-0">
          <div class="col-sm-7">
            <div class="input-group mb-3">
              <ng-select
                id="field-template"
                #fieldTemplate
                formControlName="fieldTemplateId"
                [bindLabel]="'name'"
                [bindValue]="'id'"
                [items]="exportTemplates | async"
                [clearable]="false"
                [virtualScroll]="true"
              ></ng-select>
            </div>
            <!-- <div
              class="isa_error"
              *ngIf="
                settingsForm.controls['ProductionOptions'].hasError(
                  'requiredFieldTemplate'
                ) && formSubmitted
              "
            >
              Please select a field template.
            </div> -->
          </div>
        </div>
      </form>
    </accordion-group>
    <!-- Production Settings: End -->

    <!-- PDF Service Settings: Start -->
    <accordion-group heading="PDF Services">
      <form formGroupName="pdfServiceSettings">
        <div class="form-row m-0">
          <div class="form-group col-5 p-0 d-flex flex-row">
            <label class="font-weight-bold mr-4">PDF Type</label>
            <div class="custom-control custom-radio mr-4 mb-0">
              <input
                type="radio"
                name="pdfType"
                id="pdf-type-searchable"
                class="custom-control-input"
                [value]="0"
                formControlName="pdfType"
              />
              <label class="custom-control-label" for="pdf-type-searchable"
                >Searchable</label
              >
            </div>
            <div class="custom-control custom-radio">
              <input
                type="radio"
                name="pdfType"
                id="pdf-type-image"
                class="custom-control-input"
                [value]="1"
                formControlName="pdfType"
              />
              <label class="custom-control-label" for="pdf-type-image"
                >Image Only</label
              >
            </div>
          </div>
          <div class="form-group col-7 p-0 d-flex flex-row">
            <label class="font-weight-bold mr-4">Family File Handling</label>
            <div class="custom-control custom-radio mr-4 mb-0">
              <input
                type="radio"
                name="pdfFamilyFileHandling"
                id="family-file-handling-together"
                class="custom-control-input"
                [value]="0"
                formControlName="pdfFamilyFileHandling"
              />
              <label
                class="custom-control-label"
                for="family-file-handling-together"
                >Parent/Child as 1 Doc</label
              >
            </div>
            <div class="custom-control custom-radio">
              <input
                type="radio"
                name="pdfFamilyFileHandling"
                id="family-file-handling-separate"
                class="custom-control-input"
                [value]="1"
                formControlName="pdfFamilyFileHandling"
              />
              <label
                class="custom-control-label"
                for="family-file-handling-separate"
                >Parent and Children Separate</label
              >
            </div>
          </div>
        </div>
        <div class="dashed-divider"></div>

        <div class="form-row m-0">
          <div class="form-group col-12 p-0 d-flex flex-row">
            <label class="font-weight-bold mr-4">PDF File Naming</label>
            <div class="custom-control custom-radio mr-4 mb-0">
              <input
                type="radio"
                name="pdfFileNamingConvention"
                id="pdf-naming-after-cn"
                class="custom-control-input"
                [value]="0"
                formControlName="pdfFileNamingConvention"
              />
              <label class="custom-control-label" for="pdf-naming-after-cn"
                >Name After Control Number</label
              >
            </div>
            <div class="custom-control custom-radio">
              <input
                type="radio"
                name="pdfFileNamingConvention"
                id="pdf-naming-maintain"
                class="custom-control-input"
                [value]="1"
                formControlName="pdfFileNamingConvention"
              />
              <label class="custom-control-label" for="pdf-naming-maintain"
                >Maintain Original File Name and Folder Structure</label
              >
            </div>
          </div>
        </div>
      </form>
    </accordion-group>
    <!-- PDF Service Settings: End -->
  </accordion>
</form>
