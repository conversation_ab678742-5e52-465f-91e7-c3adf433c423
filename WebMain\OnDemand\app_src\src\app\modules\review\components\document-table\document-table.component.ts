import { RestrictionMode } from '@admin-advance/models'
import { ReviewPanelType } from '@admin-advance/models/layout/layout.const'
import { DatePipe } from '@angular/common'
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Inject,
  Injector,
  NgModuleFactory,
  OnDestroy,
  OnInit,
  TemplateRef,
  Type,
  ViewChild
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { RightModel } from '@config/models/index'
import { ProjectInfo } from '@config/models/project.model'
import { ConfigService } from '@config/services/config.service'
import { select, Store } from '@ngrx/store'
import { Select, Store as XsStore } from '@ngxs/store'
import { DisplayFieldOrderModel } from '@review/models/layout.model'
import { DocumentsService } from '@review/services/documents.service'
import { MultiWindowSimilarService } from '@review/services/multi-window-similar.service'
import { ReviewService } from '@review/services/review.service'
import { DocumentTagsComponent } from '@review2/components/document-tags/document-tags.component'
import {
  DocumentTagInjectorModel,
  TagActionType
} from '@review2/models/tag.model'
import { GlobalDataService } from '@root/services/global-data.service'
import { inOut } from '@shared/animation'
import { CustodianMediaComponent } from '@shared/components/custodian-media/custodian-media.component'
import { CustodianMediaModule } from '@shared/components/custodian-media/custodian-media.module'
import { FolderSelectorComponent } from '@shared/components/folder/folder-selector/folder-selector.component'
import { FolderSelectorModule } from '@shared/components/folder/folder-selector/folder-selector.module'
import { BatchModel, ReviewSetInfo, SortingFieldModel } from '@shared/models'
import { BreadCrumb } from '@shared/models/breadcrumb.model'
import {
  InitialSearchResultParameter,
  SearchDupOption,
  SearchResponseModel,
  TempTableResponseModel
} from '@shared/models/search.model'
import { SetSearchResultDupOption } from '@shared/search-builder/sotre/query-builder.actions'
import { QueryBuilderSelector } from '@shared/search-builder/sotre/query-builder.selector'
import { SharedService } from '@shared/services/shared.service'
import { search } from '@shared/store/actions/search.actions'
import { FilterState } from '@shared/store/reducers/filter.reducer'
import { SearchState as SharedSearchState } from '@shared/store/reducers/search.reducer'
import { getSavedSearchId } from '@shared/store/selectors/document.selectors'
import { getBreadcrumbs } from '@shared/store/selectors/filter.selector'
import { getSearchResponse } from '@shared/store/selectors/search.selector'
import { TagModel } from '@stores/models'
import { StartupStateSelector } from '@stores/selectors'
import { AgGridAngular } from 'ag-grid-angular'
import { GridOptions } from 'ag-grid-community'
import { DxTreeListComponent } from 'devextreme-angular'
import CheckBox from 'devextreme/ui/check_box'
import * as GoldenLayout from 'golden-layout'
import { difference, intersection, isEqual, uniq } from 'lodash'
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal'
import { PageChangedEvent } from 'ngx-bootstrap/pagination'
import { GoldenLayoutContainer } from 'ngx-golden-layout'
import { combineLatest, EMPTY, Observable, Subject } from 'rxjs'
import {
  debounceTime,
  filter,
  switchMap,
  take,
  takeUntil,
  withLatestFrom
} from 'rxjs/operators'
import { NotificationService } from 'src/app/services/notification.service'
import { UserRights } from '../../../../helpers/user-rights'
import { GlobalErrorAction } from '../../../../store/actions'
import { User } from '../../../auth/models/user.model'
import { getUserDetails } from '../../../auth/store/selectors/access.selectors'
import {
  getControlSetting,
  getProjectInfo,
  getThemeClient
} from '../../../config/store/selectors'
import { OverlayCustomFieldsFacade } from '../../../shared/xsStore/overlay-custom-fields/overlay-custom-fields.facade'
import { CodingSummary } from '../../models/document.model'
import {
  DocumentTableUpdateModel,
  ReviewDataSourceType
} from '../../models/review.model'
import { ReviewViewType } from '../../models/search.model'
import { MultiWindowSelectionService } from '../../services/multi-window-selection.service'
import { MultiWindowTranscriptService } from '../../services/multi-window-transcript.service'
import * as fromDocumentsActions from '../../store/actions/documents.actions'
import * as fromLayoutActions from '../../store/actions/layout.actions'
import { ReviewSpinnerType } from '../../store/reducers/documents.reducer'
import { SearchState } from '../../store/reducers/search.reducer'
import {
  getCurrentDocument,
  getCurrentDocumentTablePage,
  getIsBatchSelection,
  getSelectedDocuments,
  getSelectedDocumentsCount,
  getSpinnerState,
  getUnselectedDocuments,
  getUpDatedCodingFields
} from '../../store/selectors/documents.selectors'
import {
  getAllSearchResultFileIds,
  getSearchResultFieldValues
} from '../../store/selectors/search.selectors'
import {
  PrepareInclusiveEmailData,
  ReviewSetStateSelector,
  SetDocumentTableVisibleFieldsOrder,
  SetSearchResultIncludeFamilySearch,
  UpdateDocumentTableAction
} from '../../xs-store'
import { DocumentSortingComponent } from '../document-sorting/document-sorting.component'
import { DocumentSortingModule } from '../document-sorting/document-sorting.module'
import { DocumentTableFilterComponent } from '../document-table-filter/document-table-filter.component'
import { TagRuleConflictViewerComponent } from '../tag-rule-conflict-viewer/tag-rule-conflict-viewer.component'
import { TallyComponent } from '../tally/tally.component'

@Component({
  selector: 'app-document-table',
  templateUrl: './document-table.component.html',
  styleUrls: ['./document-table.component.scss'],
  animations: [inOut]
})
export class DocumentTableComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  public DEFAULT_SEARCH_EXPRESSION = 'FileId>0'

  public client: string

  @ViewChild('agGrid') agGrid: AgGridAngular

  gridOptions: GridOptions

  projectInfo: ProjectInfo

  private userDetails: User

  /**
   * Include family hits `false` by default
   */
  includeFamilySearch = false

  /**
   * search Duplicate Option, default = -1 (not set), 1: Show all hits in the selected scope (No DeDupe)
   */
  searchDuplicateOption = SearchDupOption.DEFAULT

  showSpinner$: Observable<boolean>

  /**
   * List of all fields that user have permissions to view in search grid
   */
  allFields: string[] = []

  /**
   * Fields to show in grid by default when loading search result
   */
  visibleFields: string[] = []

  /**
   * Search grid column informations
   */
  columnsData: any = []

  /**
   * Array of field values to show in search result grid
   */
  tableData: any = []

  /**
   * Array of selected document file ids
   */
  selectedDocuments: number[]

  /**
   * No. of document selected from search result
   */
  selectedDocumentsCount: number

  //THIS FIELD DOESN'T SEEMS TO BE NEEDED.
  searchDataLoaded: boolean

  /**
   * Flag to indicate if it is ready to search
   */
  searchQueryLoaded: boolean

  /**
   * Query that will be searched. This property is set:
   * 1. When user types the query from search textbox
   * 2. When documents are filtered
   * 3. When query sent from analyse to review.
   */
  searchQuery: string

  /**
   * Information that is returned by search
   */
  searchResultParameter: InitialSearchResultParameter

  /**
   * Holds the name of different temp table and search related informations used during search
   */
  tempTables: TempTableResponseModel

  /**
   * Total number of documents searched
   */
  totalDocuments = 0

  totalThreadCount = 0

  /**
   * Size of search result grid per page.
   */
  pageSize = 0

  /**
   * Selected page of search result grid.
   */
  currentPage = 1

  /**
   * max no of pages
   */
  maxPageSize = 1

  /**
   * Selected file id
   */
  currentDocumentId: number

  /**
   * Boolean flag that indicates the send to production action is triggered.
   */
  navigateToProduction: boolean

  showSearchForm: boolean

  /**
   * Summary of the coding fields that are applied to the document
   */
  updatedCodingFields: CodingSummary[]

  /**
   * Holds the breadcrumb of filters applied.
   */
  queryStack: BreadCrumb[] = []

  private searchHelpModalRef: BsModalRef

  private showHideColumnsRef: BsModalRef

  private printDocumentModalRef: BsModalRef

  /**
   * Subject to complete the subscriptions
   */
  private unsubscribed$ = new Subject<void>()

  /**
   * Text to display in filter breadcrumb
   */
  filterTxt: string

  /**
   * Flag to indicate whether the query is sent from analyze module using 'Send to analyze'.
   */
  isSearchQueryEmpty = true

  /**
   * Lazy component'module to load on project scope (custodian/media)
   */
  custodianMediaModule: NgModuleFactory<CustodianMediaModule>

  /**
   * Lazy component to load on project scope (custodian/media)
   */
  custodianMediaComp: Promise<Type<CustodianMediaComponent>>

  /**
   *  Injector provider for lazy component
   */
  custodianMediaInjector: Injector

  /**
   * template ref of remove view all fields by template Id
   */
  @ViewChild('custodianMedia')
  private readonly tplCustodianMedia: TemplateRef<unknown>

  // selected media list
  mediaList: number[] = []

  /**
   * Folder list for search scope for the user having document restricted by folder
   */
  folderList: number[] = []

  // check condition to display project scope (custodian/media) when the page is loading
  isPageLoadShowProjectScope: boolean

  projectUserGroupRightList: RightModel

  /**
   * Review set id to be loaded. This value is available, when review set is selected from launchpad.
   */
  reviewSetId: number

  /**
   * Value can be either 'search' or 'reviewSet' based on the data to be loaded via search or review set.
   */
  reviewSourceType: string

  /**
   * Store tag icon color details
   */
  tagIconDetails = {}

  /**
   * Loads the folder tree for folder scope selection
   */
  folderSelectorComponent: Promise<Type<FolderSelectorComponent>>

  /**
   * Lazy component module to load for scope selection
   */
  folderSelectorModule: NgModuleFactory<FolderSelectorModule>

  /**
   * Value injector in lazy loaded component
   */
  folderInjector: Injector

  /**
   * stores the currently sorting field information.
   */
  currentSortFieldModel: SortingFieldModel

  /**
   * module of lazy loaded DocumentSortingComponent
   */
  documentSortingModule: NgModuleFactory<DocumentSortingModule>

  /**
   * Lazy component to load on user demand for DocumentSortingComponent
   */
  documentSortingComponent: Promise<Type<DocumentSortingComponent>>

  /**
   *  Injector provider for lazy component
   */
  documentSortingInjector: Injector

  /**
   * template ref of remove view all fields by template Id
   */
  @ViewChild('tplDocumentSorting')
  private readonly documentSortingTemplateRef: TemplateRef<any>

  /**
   * If the review datasource is review set, get review set options of selected review set id.
   */
  selectedReviewSetInfo: ReviewSetInfo

  /**
   * tag info of the selected review tag. This is required to create a query for filtering all/none reviewed documents
   */
  reviewTagInfo: TagModel

  /**
   * Dynamic Folder ID
   */
  dynamicFolderId: number

  /**
   * Dynamic Folder Type (Global or Local)
   */
  isDynamicFolderGlobal: boolean

  /**
   * Value to hold the Select all from all pages option and send to image
   */
  documentTableIsBatchSelected: boolean

  /**
   * Value to hold unselected documents during the 'Slect all from all pages' case
   */
  documentTableUnSelectedDocs: number[]

  /**
   * User right to print
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_PRINT))
  allowToPrint$: Observable<boolean>

  /**
   * User right to send the documents to analyze
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_VIEW_DASHBOARD)
  )
  allowAnalyze$: Observable<boolean>

  /**
   * Whether show/hide advance query builder ui button
   */
  showAdvanceFilter: boolean

  /**
   * handles text input event to perform UI toggling task or  element manipulation.
   */
  readonly forQueryBuilder = new EventEmitter()

  searchQueryFromOtherSource: boolean

  /**
   * review set reviewed documents detail
   */
  selectedReviewSetBatchInfo: BatchModel

  /**
   * Key id used to represent tree structure in document table treelist
   */
  keyId: string

  /**
   * Parent id used to represent tree structure in document table treelist.
   * When its value is set null, treelist behaves as grid with no hierarchy
   */
  parentId: string

  private tallyModalRef: BsModalRef

  /**
   * Selected view type
   */
  selectedViewType: ReviewViewType

  ReviewViewType = ReviewViewType

  /**
   * Option to expand all nodes in tree list. This is applicable when email thread view is selected.
   */
  autoExpandAllTreeNodes: boolean

  // reference for dx-tree-list for programmatical grid functions
  @ViewChild('dxGrid') dataGridRef: DxTreeListComponent

  /**
   * isSavedSearch is true when search is done with multiple search term
   */
  isSavedSearch: boolean

  /**
   * isSearchFromHistory is true when search is done from saved search tab or search history tab
   */
  isSearchFromHistory: boolean

  /**
   * to check if search history search loadFile
   */
  isLoadFile: boolean

  /**
   * search history id
   */
  searchHistoryId: number

  /**
   * Document restriction mode refference
   */
  restrictionMode = RestrictionMode

  /**
   * Document restriction mode assigned to the user group
   */
  userDocRestrictonMode = RestrictionMode.None

  /**
   * Restricted folder ids to user group
   */
  restrictedFolderIds: number[] = []

  allFileIds: number[]

  showHelp = false

  /**
   * Template reference for loading the  tag tree component.
   */
  @ViewChild('documentTagTemplate')
  private documentTagTemplate: TemplateRef<any>

  /**
   * Loads the folder tree component lazily
   */
  documentTagComponent: Promise<Type<DocumentTagsComponent>>

  /**
   * Value injector in lazy loaded component
   */
  tagInjector: Injector

  enumTagActionType = TagActionType

  restrictSelectionChangeEvent = false

  firstExpandedNode = [20]

  isrowExpandCollapse: boolean

  fetchErroredDocuments: number[] = []

  fetchNearNativeDocCount = 5

  errMessage: string

  /**
   * mat dailog reference for CAL batch confirmation
   */
  calBatchRef

  @ViewChild('calBatchWarningMessage')
  private readonly confirmCALBatch: TemplateRef<any>

  isReviewForTagRuleConflicted = false

  previousLayoutId = 0

  constructor(
    @Inject(GoldenLayoutContainer) private container: GoldenLayout.Container,
    private multiWindowSelectionService: MultiWindowSelectionService,
    private store: Store<SearchState>,
    private configService: ConfigService,
    private modalService: BsModalService,
    private router: Router,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private xsStore: XsStore,
    private filterStore: Store<FilterState>,
    private sharedSearchstore: Store<SharedSearchState>,
    private injector: Injector,
    private notification: NotificationService,
    private transcriptService: MultiWindowTranscriptService,
    private datePipe: DatePipe,
    private reviewService: ReviewService,
    private multiWindowSimilarService: MultiWindowSimilarService,
    private docService: DocumentsService,
    private overlayCustomFieldsFacade: OverlayCustomFieldsFacade,
    private sharedService: SharedService,
    private globalService: GlobalDataService
  ) {
    this.route.queryParamMap
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((params) => {
        this.reviewSetId = +params.get('reviewSetId')
      })
  }

  ngOnInit() {
    this.reviewService.setDocumentTableFieldSortingInStore$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((callback) => {
        const layoutIdInStore = this.xsStore.selectSnapshot(
          ReviewSetStateSelector.SliceOf('selectedReviewLayoutId')
        )
        //Update the DocumentTableVisibleFieldsOrder only when previous layoutid and layout stored in store is same
        //This is becuase when another layout is loaded, it should not preserve the current state.
        // Instead, it should load the fields that are available in new layout.
        if (+sessionStorage.layoutId ?? 0 === layoutIdInStore) {
          this.SetDocumentTableVisibleFieldsOrder()
        } else sessionStorage.layoutId = layoutIdInStore

        callback()
      })
    this.isReviewForTagRuleConflicted =
      this.sharedService.isReviewForTagRuleConflicted()
    this.observeResize()
    this.showSearchForm = false
    this.gridOptions = {
      defaultColDef: {
        resizable: true,
        filter: true,
        filterParams: {
          clearButton: true,
          applyButton: true,
          debounceMs: 200
        }
      }
    }
    this.multiWindowSelectionService.currentDocumentId
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((currentDocumentId: number) => {
        this.currentDocumentId = currentDocumentId
        window.dispatchEvent(new Event('resize'))
      })
    this.store
      .pipe(select(getThemeClient), takeUntil(this.unsubscribed$))
      .subscribe((client: string) => {
        this.client = client
      })

    this.store
      .pipe(
        select(getProjectInfo),
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((projectInfo: ProjectInfo) => {
        this.projectInfo = projectInfo

        this.fetchReviewSetInfo()
      })
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('reviewViewType'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((viewType) => {
        this.selectedViewType = viewType
        if (this.selectedViewType === ReviewViewType.EmailThread) {
          this.keyId = '__Id'
          this.parentId = '__ParentId'
        } else {
          this.keyId = '__FileID'
          this.parentId = '__ParentId'
        }
      })

    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('reviewTag'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((tagInfo: TagModel) => {
        this.reviewTagInfo = tagInfo
      })

    this.store
      .pipe(select(getUserDetails), takeUntil(this.unsubscribed$))
      .subscribe((userDetails: User) => {
        this.userDetails = userDetails
      })

    this.sharedSearchstore
      .pipe(
        select(getSearchResponse),
        debounceTime(300),
        // filter((res) => !!res),
        withLatestFrom(this.store.pipe(select(getCurrentDocumentTablePage))),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(
        ([searchresponse, currentPage]: [SearchResponseModel, number]) => {
          if (!searchresponse?.searchResultIntialParameters) {
            this.searchResultParameter = null
            this.totalDocuments = 0
            this.pageSize = 0
            this.currentPage = 1
            this.transcriptService.onDocumentPage = {
              pageSize: this.pageSize,
              currentPage: this.currentPage
            }
            return
          }
          /**
           * dispatch action to clear the batch selection everytime new search response is received
           */
          this.store.dispatch(
            fromDocumentsActions.setIsBatchSelection({
              payload: { isBatchSelection: false }
            })
          )
          this.tempTables = searchresponse.tempTables
          this.searchResultParameter =
            searchresponse.searchResultIntialParameters
          this.totalDocuments = this.searchResultParameter.totalHitCount
          this.totalThreadCount =
            this.selectedViewType == ReviewViewType.EmailThread
              ? this.searchResultParameter.totalThreadCount
              : this.searchResultParameter.totalHitCount
          this.pageSize = this.searchResultParameter.pageSize
          this.currentPage = currentPage
            ? currentPage
            : this.searchResultParameter.currentPage
          this.maxPageSize =
            Math.ceil(this.totalThreadCount / this.pageSize) > 0
              ? Math.ceil(this.totalThreadCount / this.pageSize)
              : 1
          this.transcriptService.onDocumentPage = {
            pageSize: this.pageSize,
            currentPage: this.currentPage
          }
        }
      )

    combineLatest([
      this.store.pipe(
        select(getSearchResultFieldValues),
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      ),
      this.xsStore.select(
        ReviewSetStateSelector.SliceOf('tableDataSortedVisibleFields')
      )
    ])
      .pipe(
        filter(([data, fields]) => !!data),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(
        ([fieldValues, displayFields]: [any, DisplayFieldOrderModel[]]) => {
          const allCustomFields = this.xsStore.selectSnapshot(
            ReviewSetStateSelector.SliceOf('allCustomFields')
          )

          const allVenioFields = this.xsStore.selectSnapshot(
            ReviewSetStateSelector.SliceOf('allVenioFields')
          )
          //Convert the date string to local date string
          fieldValues.forEach((t) => {
            const fieldKeys = Object.keys(t)

            const venioDateType = allVenioFields.filter(
              (x) => x?.fieldDataType?.toLowerCase() === 'date'
            )

            const venioDateTimeType = allVenioFields.filter(
              (x) => x?.fieldDataType?.toLowerCase() === 'datetime'
            )

            const dateType = allCustomFields.filter(
              (c) =>
                c.fieldDataType.toLowerCase() === 'date' ||
                c.fieldDataType.toLowerCase() === 'datetime'
            )

            dateType.forEach((c) => {
              if (
                fieldKeys.find((d) => d === c.internalFieldName) &&
                t[c.internalFieldName]
              ) {
                if (
                  c.fieldDataType.toLowerCase() === 'date' &&
                  t[c.internalFieldName]
                ) {
                  const dateConverted = new Date(
                    t[c.internalFieldName]
                  ).toLocaleDateString()
                  if (dateConverted && dateConverted !== 'Invalid Date') {
                    t[c.internalFieldName] = dateConverted
                  }
                }
                if (
                  c.fieldDataType.toLowerCase() === 'datetime' &&
                  t[c.internalFieldName]
                ) {
                  const datetimeConverted = new Date(
                    t[c.internalFieldName]
                  ).toLocaleString()
                  if (
                    datetimeConverted &&
                    datetimeConverted !== 'Invalid Date'
                  ) {
                    t[c.internalFieldName] = datetimeConverted
                  }
                }
              }
            })
            venioDateType.forEach((x) => {
              if (
                fieldKeys.find((d) => d === x.displayFieldName) &&
                t[x.displayFieldName]
              ) {
                t[x.displayFieldName] = this.datePipe.transform(
                  new Date(t[x.displayFieldName]),
                  'MM/dd/YYYY'
                )
              }
            })

            venioDateTimeType.forEach((x) => {
              if (
                fieldKeys.find((d) => d === x.displayFieldName) &&
                t[x.displayFieldName]
              ) {
                t[x.displayFieldName] = new Date(
                  t[x.displayFieldName]
                ).toLocaleString()
              }
            })
          })

          this.updateDocumentTable(fieldValues)
        }
      )

    this.store
      .pipe(select(getSelectedDocumentsCount), takeUntil(this.unsubscribed$))
      .subscribe((count: number) => {
        this.selectedDocumentsCount = count
      })

    this.store
      .pipe(select(getSelectedDocuments), takeUntil(this.unsubscribed$))
      .subscribe((selectedDocuments: number[]) => {
        this.selectedDocuments = selectedDocuments
        //this.populateDocumentSelection()
      })

    this.store
      .pipe(select(getCurrentDocumentTablePage), takeUntil(this.unsubscribed$))
      .subscribe((currentPage: number) => {
        this.currentPage = currentPage == null ? 1 : currentPage
      })

    this.store
      .pipe(
        select(getCurrentDocument),
        filter((res) => !!res && res > 0),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(async (currentDoc: number) => {
        const isNearNativeActive: boolean = this.xsStore.selectSnapshot(
          ReviewSetStateSelector.isReviewPanelActive(
            ReviewPanelType.NearNativeViewer
          )
        )
        if (!isNearNativeActive) return
        let currentDocIndex = this.tableData
          .map((item) => item.__FileID)
          .indexOf(currentDoc)
        const thresholdSize =
          await this.globalService.getHtmlConversionThresholdSize()
        const invalidExtensions =
          await this.globalService.getHtmlConversionInvalidExtensions()

        const eligibleFilesForNearNative = this.tableData.filter(
          (row) =>
            !invalidExtensions.includes(row.__extension) &&
            !this.fetchErroredDocuments.includes(row.__FileID) &&
            row.__FileID > 0 &&
            row.__filesize < thresholdSize &&
            this.tableData.indexOf(row) > currentDocIndex - 1
        )

        currentDocIndex = eligibleFilesForNearNative
          .map((item) => item.__FileID)
          .indexOf(currentDoc)
        const nextFileIds: number[] = eligibleFilesForNearNative
          .slice(
            currentDocIndex,
            currentDocIndex + this.fetchNearNativeDocCount
          )
          .map((item) => item?.__FileID)
        if (nextFileIds.length > 0) this.initNearNativeWorker(nextFileIds)
      })

    this.showSpinner$ = this.store.pipe(
      select(getSpinnerState(ReviewSpinnerType.Table)),
      takeUntil(this.unsubscribed$)
    )

    this.filterStore
      .pipe(select(getBreadcrumbs), takeUntil(this.unsubscribed$))
      .subscribe((breadcrumbs) => {
        this.queryStack = breadcrumbs
      })

    this.store
      .pipe(select(getSavedSearchId), takeUntil(this.unsubscribed$))
      .subscribe((savedSearchId: number) => {
        if (savedSearchId && this.navigateToProduction) {
          this.router.navigate(['/production'], {
            queryParams: { projectId: this.projectInfo.projectId }
          })
        }
      })

    this.store
      .pipe(select(getUpDatedCodingFields), takeUntil(this.unsubscribed$))
      .subscribe((codingFields: CodingSummary[]) => {
        this.updatedCodingFields = codingFields

        const codingFieldAndValues: DocumentTableUpdateModel[] =
          codingFields.map((field) => {
            return {
              fileId: this.currentDocumentId,
              displayFieldName: field.fieldDisplayName,
              fieldValue: field.newValue
            }
          })
        this.xsStore.dispatch(
          new UpdateDocumentTableAction(codingFieldAndValues)
        )
      })

    this.getTiffLimitSetting()

    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('reviewSourceType'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((sourceType: ReviewDataSourceType) => {
        this.reviewSourceType = sourceType.toString()
      })

    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('sortingFieldModel'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((sortFieldModel: SortingFieldModel) => {
        this.currentSortFieldModel = sortFieldModel
      })
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('documentTableUpdatingData'))
      .pipe(
        filter((res) => res?.length > 0),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((data) => {
        this.updateDocumentTableUI(data)
        this.xsStore.dispatch(new UpdateDocumentTableAction([]))
      })

    this.store
      .pipe(
        select(getControlSetting('EXPAND_MESSAGE_THREAD_VIEW')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((isEnabled: boolean) => {
        this.autoExpandAllTreeNodes = isEnabled
      })

    //for bulk imaging parameter
    this.store
      .pipe(select(getIsBatchSelection), takeUntil(this.unsubscribed$))
      .subscribe((documentTableIsBatchSelected: boolean) => {
        this.documentTableIsBatchSelected = documentTableIsBatchSelected
      })

    //for bulk imaging parameter
    this.store
      .pipe(select(getUnselectedDocuments), takeUntil(this.unsubscribed$))
      .subscribe((documentTableUnSelectedDocs: number[]) => {
        this.documentTableUnSelectedDocs = documentTableUnSelectedDocs
      })

    this.store
      .pipe(select(getAllSearchResultFileIds), takeUntil(this.unsubscribed$))
      .subscribe((fileIds: number[]) => {
        this.allFileIds = fileIds
      })

    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('currentDocPageNumber'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((pageNumber) => {
        this.currentPage = pageNumber || this.currentPage
      })

    // this.reviewService.updateVisibleFieldsSubject
    //   .pipe(takeUntil(this.unsubscribed$))
    //   .subscribe(() => {
    //     const columns: Column[] = this.dataGridRef.instance
    //       .getVisibleColumns()
    //       .sort((a, b) => a.visibleIndex - b.visibleIndex)
    //     this.xsStore.dispatch(
    //       new SetDocumentTableVisibleFieldsOrder(columns.map((col) => ({ fieldName: col.name, visibleFieldIndex: col.visibleIndex })))
    //     )
    //   })

    this.multiWindowSelectionService.setCurrentTablePage$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((request) => {
        this.store.dispatch(
          fromDocumentsActions.setCurrentDocumentTablePage(request)
        )
      })

    this.docService.updateDocumentSelection$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe(() => {
        const layoutIdInStore = this.xsStore.selectSnapshot(
          ReviewSetStateSelector.SliceOf('selectedReviewLayoutId')
        )
        if (this.previousLayoutId != layoutIdInStore) {
          this.previousLayoutId = layoutIdInStore
          return
        }
        this.updateGridSelection()
      })

    this.store
      .pipe(
        select(getCurrentDocument),
        filter((res) => !!res && res > 0),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(async (currentDoc: number) => {
        if (this.selectedViewType == ReviewViewType.EmailThread)
          this.ExpandFirstEmailThread(currentDoc)
      })

    this.store
      .pipe(
        select(getControlSetting('NEAR_NATIVE_FETCH_COUNT')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((docCount: number) => {
        this.fetchNearNativeDocCount = docCount
      })
  }

  private ExpandFirstEmailThread(currentDoc: number) {
    const currentDocConversationId: string = this.tableData.find(
      (item) => item.__FileID == currentDoc
    )?.__conversationId
    if (currentDocConversationId?.length > 0) {
      const baseconversationId = currentDocConversationId.substring(0, 44)
      const key = this.tableData.find(
        (item) => item.__conversationId == baseconversationId
      )?.__Id
      setTimeout(() => {
        this.dataGridRef?.instance?.expandRow(key)
      }, 200)
    }
  }

  ngAfterViewInit(): void {
    try {
      this.container.parent.parent['header'].controlsContainer
        .find('.lm_popout')
        .hide()
    } catch (e) {
      this.store.dispatch(
        new GlobalErrorAction(
          new Error('Unable to hide popout icon on document table panel'),
          false,
          true
        )
      )
    }
    // returns documents left to be reviewed and total document counts
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('selectedReviewSetBatchInfo'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((batchmodel) => {
        if (batchmodel) {
          this.selectedReviewSetBatchInfo = batchmodel
          this.sharedService.setBatchOptions(batchmodel)
        }
      })

    this.transcriptService.documentLoad
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((data) => {
        if (data) {
          this.onRowClick(null, parseInt(data.documentId), data.documentName)
        }
      })

    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('batchCheckOutResponse'))
      .pipe(
        filter((res) => !!res),
        switchMap((res) => {
          if (
            this.reviewSetId > 0 &&
            res.error?.errStatus &&
            res.error?.errMessage?.length > 0
          ) {
            this.errMessage = res.error.errMessage
            this.calBatchRef = this.dialog.open(this.confirmCALBatch, {
              closeOnNavigation: true,
              autoFocus: false,
              width: '380px'
            })
            return this.calBatchRef.afterClosed().pipe(filter((yes) => !!yes))
          }
          return EMPTY
        }),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({
        next: (res) => {
          this.store.dispatch(
            search({
              payload: {
                searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
                isResetBaseGuid: true,
                reviewSetId: this.reviewSetId,
                isInitialSearch: true,
                reviewBatchAfterCALThreshold: true
              }
            })
          )
        }
      })

    this.selectOverlayCustomFieldChanged()
  }

  onHeaderClick(info) {
    const allVenioFields = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('allVenioFields')
    )
    const allCustomFields = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('allCustomFields')
    )
    const allFields = [...allVenioFields, ...allCustomFields]
    const internalFieldName = allFields.filter(
      (field) => field.displayFieldName === info.column.name
    )[0]?.internalFieldName
    this.tallyModalRef = this.modalService.show(
      TallyComponent,
      Object.assign(
        {},
        {
          initialState: {
            fieldName: internalFieldName,
            isSearchableField: this.isSearchableField(info.column.name),
            isNullSearchableField: this.isNullSearchableField(info.column.name)
          },
          ignoreBackdropClick: true,
          class: 'modal-lg'
        }
      )
    )
  }

  isSearchableField(fieldName: string): boolean {
    const allCustomFields = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('allCustomFields')
    )
    if (allCustomFields.some((field) => field.displayFieldName === fieldName)) {
      return allCustomFields?.find(
        (field) => field?.displayFieldName === fieldName
      )?.isSearchableField
    }
    const allVenioFields = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('allVenioFields')
    )
    if (allVenioFields.some((field) => field.displayFieldName === fieldName)) {
      return allVenioFields?.find(
        (field) => field?.displayFieldName === fieldName
      )?.isSearchableField
    }
    return false
  }

  isNullSearchableField(fieldName: string): boolean {
    const allVenioFields = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('allVenioFields')
    )
    if (allVenioFields.some((field) => field.displayFieldName === fieldName)) {
      return allVenioFields?.find(
        (field) => field?.displayFieldName === fieldName
      )?.allowNullSearch
    }
    return false
  }

  onRowPrepared(e) {
    // if (e.rowType === 'data' && e.data?.__FileID < 0) {
    //   e.rowElement.style.backgroundColor = '#ebebeb'
    // }
  }

  setCssClasses(cell) {
    return {
      'text-success':
        cell.text.toLowerCase().startsWith('re:') &&
        cell?.row?.data?.__FileID > 0,
      'color-blue':
        cell.text.toLowerCase().startsWith('fw:') &&
        cell?.row?.data?.__FileID > 0,
      'text-muted': cell?.row?.data?.__FileID < 0
    }
  }

  toggleHelp() {
    this.showHelp = !this.showHelp
  }

  fetchReviewSetInfo() {
    if (this.reviewSetId > 0)
      // this.xsStore
      //   .dispatch(
      //     new FetchSelectedReviewSetInfo(
      //       this.projectInfo.projectId,
      //       this.reviewSetId
      //     )
      //   )
      //   .pipe(
      //     switchMap(() => {
      //       return this.xsStore
      //         .select(ReviewSetStateSelector.SliceOf('selectedReviewSetInfo'))
      //         .pipe(takeUntil(this.unsubscribed$))
      //     })
      //   )
      //   .subscribe(
      //     (reviewSetInfo) => (this.selectedReviewSetInfo = reviewSetInfo)
      //   )

      this.xsStore
        .select(ReviewSetStateSelector.SliceOf('selectedReviewSetInfo'))
        .pipe(takeUntil(this.unsubscribed$))
        .subscribe(
          (reviewSetInfo) => (this.selectedReviewSetInfo = reviewSetInfo)
        )
  }

  ngOnDestroy(): void {
    this.reviewService.setDocumentTableFieldSortingInStore$.next(() => {
      if (this.searchHelpModalRef) {
        this.searchHelpModalRef.hide()
      }
      if (this.showHideColumnsRef) {
        this.showHideColumnsRef.hide()
      }
      if (this.printDocumentModalRef) {
        this.printDocumentModalRef.hide()
      }
      this.unsubscribed$.next()
      this.unsubscribed$.complete()
    })
  }

  // Auto size columns when first data is rendered (NOT BEING USED, KEEP FOR FUTURE)
  onFirstDataRendered(params) {
    const colIds = params.columnApi
      .getAllDisplayedColumns()
      .map((col) => col.getColId())

    params.columnApi.autoSizeColumns(colIds)
  }

  updateDocumentTable(fieldValues: any) {
    if (fieldValues?.length === 0) {
      //When no documents are selected, set the document as -1 and it will clear all the review panels.
      this.store.dispatch(
        fromDocumentsActions.setCurrentDocument({
          payload: {
            documentId: -1,
            resetSelection: false,
            conversationId: null
          }
        })
      )
      this.tableData = []
      return
    }

    let tableDataSortedVisibleFields =
      this.xsStore.selectSnapshot(
        ReviewSetStateSelector.SliceOf('tableDataSortedVisibleFields')
      ) ?? []
    // const layoutVenioFields: number[] = this.xsStore.selectSnapshot(ReviewSetStateSelector.selectedVenioFields(ReviewPanelType.Table, true))
    // const layoutCustomFields = this.xsStore.selectSnapshot(ReviewSetStateSelector.selectedCustomFields(ReviewPanelType.Table, true))

    const fieldNames = Object.keys(fieldValues[0])

    tableDataSortedVisibleFields = tableDataSortedVisibleFields.filter(
      (f) => fieldNames.includes(f.fieldName) || f.fieldName == ''
    )
    // if (!tableDataSortedVisibleFields.some(f => f.fieldName == ''))
    //   tableDataSortedVisibleFields = [{ fieldId: -5, fieldName: '', visibleFieldIndex: -5, isCustomField: false }, ...tableDataSortedVisibleFields]
    if (
      this.selectedViewType == ReviewViewType.EmailThread &&
      !tableDataSortedVisibleFields.some((f) => f.fieldName == '__emailSubject')
    )
      tableDataSortedVisibleFields = [
        {
          fieldId: -4,
          fieldName: '__emailSubject',
          visibleFieldIndex: -4,
          isCustomField: false
        },
        ...tableDataSortedVisibleFields
      ]
    else if (
      this.selectedViewType == ReviewViewType.Search &&
      tableDataSortedVisibleFields.some((f) => f.fieldName == '__emailSubject')
    )
      tableDataSortedVisibleFields = tableDataSortedVisibleFields.filter(
        (f) => f.fieldName !== '__emailSubject'
      )
    if (this.reviewSetId <= 0)
      tableDataSortedVisibleFields = tableDataSortedVisibleFields.filter(
        (f) => f.fieldName !== '__isReviewed'
      )
    else {
      if (
        !tableDataSortedVisibleFields.some((f) => f.fieldName == '__isReviewed')
      ) {
        const tagIconFieldIndex = tableDataSortedVisibleFields.findIndex(
          (f) => f.fieldName == '__tagicon'
        )
        tableDataSortedVisibleFields.splice(tagIconFieldIndex + 1, 0, {
          fieldId: -1,
          fieldName: '__isReviewed',
          visibleFieldIndex: -1,
          isCustomField: false
        })
      }
    }

    if (this.isReviewForTagRuleConflicted) {
      tableDataSortedVisibleFields = [
        {
          fieldId: -5,
          fieldName: '__viewConflictedTagRules',
          visibleFieldIndex: -5,
          isCustomField: false
        },
        ...tableDataSortedVisibleFields
      ]
    }

    this.columnsData = tableDataSortedVisibleFields.map((f) => ({
      headerName: f.fieldName,
      field: f.fieldName,
      resizable: true
    }))
    this.visibleFields = tableDataSortedVisibleFields.map((f) => f.fieldName)

    this.tableData = fieldValues
    this.transcriptService.setTableData = fieldValues

    this.tagIconDetails = fieldValues.map((e) => {
      return { [e.__FileID]: this.getTagIconColorDetails(e.__tagicon) }
    })

    const searchResultIncludeFamilySearch = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('includeFamilySearch')
    )
    this.xsStore.dispatch(
      new SetSearchResultIncludeFamilySearch(searchResultIncludeFamilySearch)
    )

    const searchResultDuplicateOption = this.xsStore.selectSnapshot(
      QueryBuilderSelector.sliceOf('searchDuplicateOption')
    )
    this.xsStore.dispatch(
      new SetSearchResultDupOption(searchResultDuplicateOption)
    )
  }

  /**
   * Update the document table for selected documents.
   * @param fileIds list of file ids of whose metadata need to be updated
   */
  updateDocumentTableUI(data: DocumentTableUpdateModel[]) {
    const input = {
      tableData: this.tableData,
      fieldValues: data
    }
    if (typeof Worker !== 'undefined') {
      const worker = new Worker(
        new URL('../../services/document-table-update.worker', import.meta.url),
        { type: 'module' }
      )
      worker.onmessage = ({ data }) => {
        setTimeout(() => {
          this.tableData = data.tableData
          if (data.tagIconFileIds.length > 0) {
            this.tagIconDetails = this.tableData
              .filter((row) => data.tagIconFileIds.includes(row.__FileID))
              .map((e) => {
                return {
                  [e.__FileID]: this.getTagIconColorDetails(e.__tagicon)
                }
              })
          }

          this.dataGridRef?.instance?.repaintRows(data.rowIndices)
        }, 100)

        worker.terminate()
      }
      worker.postMessage(input)
    } else {
      console.log('Web worker is not supported')
    }
  }

  updateGridSelection() {
    if (this.selectedViewType == ReviewViewType.EmailThread)
      this.updateGridSelectionEmailThreadView()
    else this.updateGridSelectionNormalView()
  }

  updateGridSelectionNormalView() {
    const isBatchSelection = this.documentTableIsBatchSelected
    if (!isBatchSelection) {
      const selectedFileIds = this.selectedDocuments
      const currentPageSelectedDocs = intersection(
        this.allFileIds,
        selectedFileIds
      )
      //if (currentPageSelectedDocs?.length == 0)
      this.restrictSelectionChangeEvent = true
      this.dataGridRef?.instance.deselectAll().then(() => {
        this.dataGridRef?.instance
          .selectRows(currentPageSelectedDocs, true)
          .then(() => {
            this.restrictSelectionChangeEvent = false
          })
      })
    } else {
      const unselectedFileIds = this.documentTableUnSelectedDocs

      this.restrictSelectionChangeEvent = true

      this.dataGridRef?.instance.deselectAll().then(() => {
        const currentPageSelectedDocs = difference(
          this.allFileIds,
          unselectedFileIds
        )
        this.dataGridRef?.instance
          .selectRows(currentPageSelectedDocs, true)
          .then(() => {
            this.restrictSelectionChangeEvent = false
          })
      })
    }
  }

  updateGridSelectionEmailThreadView() {
    const isBatchSelection = this.documentTableIsBatchSelected
    if (!isBatchSelection) {
      const selectedFileIds = this.selectedDocuments
      const currentPageSelectedDocs = intersection(
        this.allFileIds,
        selectedFileIds
      )
      const keys = this.tableData
        .filter((d) => currentPageSelectedDocs.some((fid) => fid == d.__FileID))
        ?.map((d) => d.__Id)

      this.restrictSelectionChangeEvent = true
      this.dataGridRef?.instance.deselectAll().then(() => {
        this.dataGridRef?.instance.selectRows(keys, true).then(() => {
          this.restrictSelectionChangeEvent = false
        })
      })
    } else {
      const unselectedFileIds = this.documentTableUnSelectedDocs

      this.restrictSelectionChangeEvent = true
      this.dataGridRef?.instance.deselectAll().then(() => {
        const currentPageSelectedDocs = difference(
          this.allFileIds,
          unselectedFileIds
        )
        const keys = this.tableData
          .filter((d) =>
            currentPageSelectedDocs.some((fid) => fid == d.__FileID)
          )
          ?.map((d) => d.__Id)
        this.dataGridRef?.instance.selectRows(keys, true).then(() => {
          this.restrictSelectionChangeEvent = false
        })
      })
    }
  }

  onSelectionChanged(e) {
    if (this.restrictSelectionChangeEvent) return
    //Documents are unselected.
    let documentIds: number[] = []
    if (e?.currentDeselectedRowKeys?.length) {
      if (this.selectedViewType === ReviewViewType.EmailThread)
        documentIds = this.tableData
          .filter((d) => e.currentDeselectedRowKeys.some((id) => id == d.__Id))
          .map((d) => +d.__FileID)
      else documentIds = e?.currentDeselectedRowKeys

      this.store.dispatch(
        fromDocumentsActions.removeFromSelectedDocuments({
          payload: { documentIds: documentIds }
        })
      )
    } else if (e?.currentSelectedRowKeys?.length) {
      let fileId = -1
      let conversationId = null
      if (this.selectedViewType === ReviewViewType.EmailThread) {
        documentIds = this.tableData
          .filter((d) => e.currentSelectedRowKeys.some((id) => id == d.__Id))
          .map((d) => +d.__FileID)
        //fileId = documentIds[0]
        fileId = documentIds[documentIds.length - 1]
        if (fileId < 0)
          conversationId = this.tableData
            .find((d) => d.__FileID == documentIds[documentIds.length - 1])
            ?.map((d) => d?.__conversationId)
      } else {
        documentIds = e?.currentSelectedRowKeys
        //fileId = documentIds[0]
        fileId = documentIds[documentIds.length - 1]
      }
      this.store.dispatch(
        fromDocumentsActions.addToSelectedDocuments({
          payload: { documentIds: documentIds }
        })
      )
    }
  }

  onRowClick(e, fileId?: number, fileName?: string) {
    if (this.isrowExpandCollapse) return

    let rowData
    let documentId
    if (e) {
      rowData = e.data
      documentId = +rowData['__FileID']
    }
    if (this.currentDocumentId == documentId) return
    this.store.dispatch(
      fromDocumentsActions.setIsBatchSelection({
        payload: { isBatchSelection: false }
      })
    )
    this.store.dispatch(
      fromDocumentsActions.setSelectedDocuments({
        payload: { selectedDocuments: [fileId ? fileId : documentId] }
      })
    )
    this.store.dispatch(
      fromDocumentsActions.setUnSelectedDocuments({
        payload: { unselectedDocuments: [] }
      })
    )

    if (documentId < -1) {
      this.store.dispatch(
        fromDocumentsActions.setCurrentDocument({
          payload: {
            documentId: documentId,
            resetSelection: true,
            conversationId: rowData['__conversationId']
          }
        })
      )
      return
    } else
      this.store.dispatch(
        fromDocumentsActions.setCurrentDocument({
          payload: {
            documentId: documentId,
            resetSelection: true,
            conversationId: ''
          }
        })
      )
    this.multiWindowSimilarService.setAddRemoveSimilarViewLock$.next(false)
  }

  onRowExpanded(e) {
    this.isrowExpandCollapse = false
  }

  onRowCollapsing(e) {
    this.isrowExpandCollapse = true
  }

  onRowCollapsed(e) {
    this.isrowExpandCollapse = false
  }

  initNearNativeWorker(fileIds: number[]) {
    const accessToken = localStorage.access_token
    const input = {
      fileIds: fileIds,
      projectId: this.projectInfo.projectId,
      accessToken: accessToken,
      serviceUrl: this.configService.getApiUrl()
    }
    if (typeof Worker !== 'undefined') {
      // Create a new
      const worker = new Worker(
        new URL('../../services/document-fetch.worker', import.meta.url),
        { type: 'module' }
      )
      worker.onmessage = ({ data }) => {
        if (data?.erroredFiles?.length) {
          this.fetchErroredDocuments = uniq([
            ...this.fetchErroredDocuments,
            ...data?.erroredFiles
          ])
        }
        worker.terminate()
      }
      worker.postMessage(input)
    } else {
      console.log('Web worker is not supported')
    }
  }

  onCellPrepared(e) {
    //Customize the select column when source is documents because it will loads the documents with pagination.
    let firstFieldHeaderTitle = ''
    if (this.selectedViewType == ReviewViewType.Search)
      firstFieldHeaderTitle = this.isReviewForTagRuleConflicted
        ? 'Tag Rule Conflict'
        : 'Tag Color'
    else
      firstFieldHeaderTitle = this.isReviewForTagRuleConflicted
        ? 'Tag Rule Conflict'
        : 'Subject'
    if (e.rowType === 'header' && e.column.index == 0) {
      const commandCell = e.cellElement
      commandCell.innerHTML = `<div class="d-flex"><app-doc-select-header></app-doc-select-header> <span class="pl-1">${firstFieldHeaderTitle}</span></div>`
    } else if (
      e.rowType == 'data' &&
      e.column.index == 0 &&
      e.data.__FileID < 0
    ) {
      const instance = CheckBox.getInstance(
        e.cellElement.querySelector('.dx-select-checkbox')
      )
      instance.option('disabled', true)
    }
  }

  onRowExpanding(e) {
    this.isrowExpandCollapse = true
    const childNodes = this.tableData.filter((r) => r.__ParentId == e.key)
    if (childNodes.length) {
      childNodes.forEach((n) => {
        this.dataGridRef.instance.expandRow(n.__Id)
      })
    }
  }

  ExpandCollapse(isExpandAll) {
    const parentNodes = this.dataGridRef.instance
      .getDataSource()
      ?.items()
      ?.filter((n) => n.level == 0)
    if (isExpandAll) {
      parentNodes.forEach((node) => {
        this.dataGridRef.instance.expandRow(node?.key)
      })
      this.autoExpandAllTreeNodes = true
    } else {
      parentNodes.forEach((node) => {
        this.dataGridRef.instance.collapseRow(node?.key)
      })
      this.autoExpandAllTreeNodes = false
    }
  }

  pageChanged(event: PageChangedEvent): void {
    if (event.page === this.currentPage) {
      return
    }

    this.reviewService.setDocumentTableFieldSortingInStore$.next(() => {
      //this.xsStore.dispatch(new AddRemoveSimilarViewLock(false))
      this.multiWindowSimilarService.setAddRemoveSimilarViewLock$.next(false)

      this.store.dispatch(
        fromDocumentsActions.setCurrentDocumentTablePage({
          payload: {
            pageNumber: event.page,
            resetSelectionItem: this.selectedDocumentsCount > 1 ? '' : 'first'
          }
        })
      )
    })
  }

  onColumnMoved(params) {
    let columns = params.columnApi
      .getAllGridColumns()
      .map((c) => c.colDef.headerName)
    columns = columns.filter(
      (c) => c !== '' && c !== '__doctype' && c !== '__isReviewed'
    )
    // We want to save visible columns config only if the columns are reordered or they are shown/hidden
    // We would want to avoid unnecessary dispatching of action if they are merely resized
    if (!isEqual(columns, this.visibleFields)) {
      this.store.dispatch(
        fromLayoutActions.setVisibleFields({
          payload: { visibleFields: columns }
        })
      )
    }
  }

  onShowHideColumnsClicked = () => {
    this.reviewService.setDocumentTableFieldSortingInStore$.next(() => {})
    this.showHideColumnsRef = this.modalService.show(
      DocumentTableFilterComponent,
      Object.assign(
        {},
        {
          initialState: {
            reviewPanel: ReviewPanelType.Table,
            client: this.client,
            fields: this.allFields,
            selectedFields: [...this.visibleFields]
          },
          ignoreBackdropClick: true,
          class: 'modal-md'
        }
      )
    )
  }

  callDownloadCSV() {
    this.notification.showSuccess('Downloading CSV file.', true)

    //get the visible column names from the grid
    const gridColumns: any = this.dataGridRef?.instance
      ?.getVisibleColumns()
      .map((col) => col.name)

    this.store.dispatch(
      fromDocumentsActions.downloadCSVReviewDocument({
        payload: {
          pageSize: 1000,
          pageNumber: 1, //Set pageNumber as 1, further values to be set in API
          totalDocuments: this.totalDocuments, //Total Documents displayed in UI
          columnData: gridColumns // Columns displayed in Document-Table
        }
      })
    )
  }

  onDownloadCsvClicked = () => {
    const totalDocs =
      this.selectedViewType == ReviewViewType.Search
        ? this.selectedDocumentsCount
        : this.totalDocuments

    if (totalDocs > 1000) {
      if (
        confirm(
          'Exporting large number of records may take some time for the application to prepare the csv file. \n Do you still want to continue with current selection?'
        )
      ) {
        this.callDownloadCSV()
      }
    } else if (totalDocs == 0) {
      alert('Please select at least one document to export.')
    } else {
      this.callDownloadCSV()
    }
  }

  showHideSearchForm() {
    this.showSearchForm = !this.showSearchForm
  }

  getTiffLimitSetting() {
    this.multiWindowSelectionService.checkTiffLimit = {
      payload: {
        projectId: this.projectInfo.projectId,
        userId: this.userDetails.userId
      }
    }
  }

  async openSortingDialog() {
    const sortingDialogRef = this.dialog.open(this.documentSortingTemplateRef, {
      autoFocus: false,
      closeOnNavigation: true,
      width: '60vh'
    })

    this.documentSortingInjector = Injector.create({
      providers: [
        {
          provide: 'data',
          useValue: {
            projectId: this.projectInfo.projectId,
            DSID: this.projectInfo.dsid,
            tempTables: this.tempTables,
            isExternalUser:
              this.userDetails.userRole.toLowerCase() === 'external',
            reviewSourceType: this.reviewSourceType,
            reviewSetId: this.reviewSetId,
            dialogRef: sortingDialogRef
          }
        }
      ],
      parent: this.injector
    })

    await import('../document-sorting/document-sorting.module')

    this.documentSortingComponent = import(
      '../document-sorting/document-sorting.component'
    ).then(({ DocumentSortingComponent }) => DocumentSortingComponent)
  }

  // triggers after grid content is ready
  onContentReady(e) {
    // populate first document selection while initializing grid
    //this.populateDocumentSelection()
    this.updateGridSelection()
  }

  private SetDocumentTableVisibleFieldsOrder() {
    const datagridColumns: DisplayFieldOrderModel[] = this.dataGridRef?.instance
      ?.getVisibleColumns()
      .map((col) => {
        const fieldDetails = this.xsStore.selectSnapshot(
          ReviewSetStateSelector.getFieldIdByDisplayFieldName(col.name)
        )

        return {
          fieldId: fieldDetails?.fieldId,
          fieldName: col.name,
          visibleFieldIndex: col.visibleIndex,
          isCustomField: fieldDetails?.isCustomField
        }
      })
    const prevDatagridColumns = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('tableDataSortedVisibleFields')
    )
    const fieldsWithoutMandatoryFields = datagridColumns?.filter(
      (f) =>
        f?.fieldName?.toLowerCase() != '__tagicon' &&
        f?.fieldName?.toLowerCase() != '__isreviewed' &&
        f?.fieldName?.toLowerCase() != 'viewConflictedTagRules'
    )
    if (
      datagridColumns?.length > 0 &&
      fieldsWithoutMandatoryFields?.length > 0 &&
      !isEqual(datagridColumns, prevDatagridColumns)
    ) {
      this.xsStore.dispatch(
        new SetDocumentTableVisibleFieldsOrder(datagridColumns)
      )
    }
  }

  /***
   * adds focused class to selected(or checked) rows which highlight or make blue background to the grid row
   * @param {array} selectedRows has array of selected/checked rows elements
   * *** DxGrid Selection could have highlight the selected rows background but we have used custom checkbox selection
   * therefor custom focusgrid is used inorder to skip from any issue that may occur in future regarding selection
   */
  focusGridRows(selectedRows: Element[]) {
    if (selectedRows?.length) {
      selectedRows.forEach((element: Element) => {
        const arr = element[0]?.className?.split(' ')
        if (arr.indexOf('dx-row-focused') === -1) {
          element[0].className += ' dx-row-focused dx-cell-focus-disabled'
          try {
            element[0].scrollIntoViewIfNeeded(true, {
              behavior: 'smooth',
              block: 'end',
              inline: 'nearest'
            })
          } catch (err) {
            element[0].scrollIntoView(false, {
              behavior: 'smooth',
              block: 'nearest',
              inline: 'nearest'
            })
          }
        }
      })
    }
  }

  /**
   * removes focused class from selected rows
   * @param {array} focusedRows has array of focused rows elements
   */

  removeGridRowsFocus(focusedRows: HTMLElement[]) {
    if (focusedRows?.length) {
      focusedRows.forEach((ele: HTMLElement) => {
        ele.classList.remove('dx-row-focused')
        ele.classList.remove('dx-cell-focus-disabled')
      })
    }
  }

  keyEnter(e): void {
    this.reviewService.setDocumentTableFieldSortingInStore$.next(() => {
      const element: HTMLInputElement = document.querySelector(
        'input.dx-texteditor-input'
      ) as HTMLInputElement
      const num = +element?.value
      if (num) {
        this.store.dispatch(
          fromDocumentsActions.setCurrentDocumentTablePage({
            payload: {
              pageNumber: num,
              resetSelectionItem: 'first'
            }
          })
        )
        this.currentPage = num
      }
    })
  }

  /* Get tag icon color details from specified tagIconInfo
   * @param tagIconInfo
   * @returns
   */
  getTagIconColorDetails(tagIconInfo: string) {
    const tagIconColorDetails: TagModel[] = []
    if (tagIconInfo == undefined || tagIconInfo.length == 0)
      return tagIconColorDetails
    const colorDetails = tagIconInfo.split(',')
    for (let i = 0; i < colorDetails.length; i++) {
      // use # to separate tagName and color value as color value begins with #
      const index = colorDetails[i].lastIndexOf('#')
      const tagDetails: TagModel = new TagModel()
      // set tagName for tooltip
      tagDetails.tagName = colorDetails[i].substring(0, index)
      // set color value for icon color
      tagDetails.color = colorDetails[i].substring(index)
      tagIconColorDetails.push(tagDetails)
    }
    return tagIconColorDetails
  }

  /***
   * observes document-table-container and emits when container gets resized
   * grid is refreshed to solve ui issue that occurs inside grid due to container resized
   *  */
  observeResize() {
    this.container.on('resize', () => {
      this.dataGridRef?.instance?.refresh()
    })
  }

  getInternalFieldName(displayFieldName: string): string {
    const allVenioFields = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('allVenioFields')
    )
    const allCustomFields = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('allCustomFields')
    )
    const allFields = [...allVenioFields, ...allCustomFields]
    const internalFieldName =
      allFields.filter(
        (field) => field.displayFieldName === displayFieldName
      )[0]?.internalFieldName || displayFieldName
    return internalFieldName
  }

  enableColumnReordering(colName: string) {
    const disableOrderingFields: string[] = [
      '',
      '__doctype',
      '__tagicon',
      '__isReviewed'
    ]
    if (disableOrderingFields.includes(colName)) return false
    else return true
  }

  async showTags(tagActionType: TagActionType) {
    // if (this.store.selectSnapshot(ReviewStateSelector.selectedDocumentCount) === 0) {
    //   this.toast.warning('No documents are selected.')
    //   return
    // }
    if (tagActionType == TagActionType.TagAllInclusiveEmails) {
      this.xsStore
        .dispatch(
          new PrepareInclusiveEmailData(this.projectInfo?.projectId, {
            searchTempTable: this.tempTables?.searchResultTempTable
          })
        )
        .pipe(
          switchMap(() =>
            this.xsStore.select(
              ReviewSetStateSelector.SliceOf('inclusiveEmailData')
            )
          ),
          take(1)
        )
        .subscribe(async (response) => {
          const injectorData = {
            projectId: this.projectInfo?.projectId,
            isBulkTag: true,
            mainTempTable: response.inclusiveEmailTempTable,
            selectedDocuments: [],
            unselectedDocuments: [],
            isBatchSelection: true,
            tagActionType: tagActionType
          } as DocumentTagInjectorModel
          await this.openTagDialog(injectorData)
        })
    } else if (
      tagActionType == TagActionType.TagWholeThreadOfSelectedDocuments
    ) {
      const injectorData = {
        projectId: this.projectInfo?.projectId,
        isBulkTag: true,
        mainTempTable: this.tempTables?.searchResultTempTable,
        selectedDocuments: this.selectedDocuments,
        unselectedDocuments: this.documentTableUnSelectedDocs,
        isBatchSelection: this.documentTableIsBatchSelected,
        tagActionType: tagActionType
      } as DocumentTagInjectorModel
      this.openTagDialog(injectorData)
    }
  }

  private async openTagDialog(injectorData: DocumentTagInjectorModel) {
    const tagRef = this.dialog.open(this.documentTagTemplate, {
      closeOnNavigation: true,
      autoFocus: false,
      width: '600px'
    })
    this.tagInjector = Injector.create({
      providers: [
        {
          provide: 'TagData',
          useValue: { ...injectorData, dialogRef: tagRef }
        }
      ],
      parent: this.injector
    })
    await import('@review2/components/document-tags/document-tags.module')
    this.documentTagComponent = import(
      '@review2/components/document-tags/document-tags.component'
    ).then(({ DocumentTagsComponent }) => DocumentTagsComponent)
  }

  /**
   * When Overlay custom fields changes, we need to load
   * changed content in the document table to reflect changes.
   */
  private selectOverlayCustomFieldChanged(): void {
    this.overlayCustomFieldsFacade.selectIsOverlayCustomFieldsChanged$
      .pipe(
        filter((isChanged) => isChanged),
        debounceTime(200),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.currentPage = 0
        this.selectedDocuments = []
        this.pageChanged({ page: 1, itemsPerPage: 50 })
      })
  }

  getEmailIcon(category, childCount) {
    if (category === 'INTERNAL' || category === 'EXTERNAL') {
      return childCount === 0 ? 'svg-icon-email' : 'svg-icon-email-attach'
    } else if (category === 'INBOUND') {
      return childCount === 0
        ? 'svg-icon-inbound-email'
        : 'svg-icon-inbound-email-attach'
    } else if (category === 'OUTBOUND') {
      return childCount === 0
        ? 'svg-icon-outbound-email'
        : 'svg-icon-outbound-email-attach'
    }
  }

  getDocTypeTooltip(category, childCount) {
    if (category === 'INTERNAL' || category === 'EXTERNAL') {
      return childCount === 0 ? 'Email' : 'Email With Attachment'
    } else if (category === 'INBOUND') {
      return childCount === 0
        ? 'Inbound Email'
        : 'Inbound Email With Attachment'
    } else if (category === 'OUTBOUND') {
      return childCount === 0
        ? 'Outbound Email'
        : 'Outbound Email With Attachment'
    }
  }

  async viewConflictedTagRules(fileId) {
    await import('../tag-rule-conflict-viewer/tag-rule-conflict-viewer.module')

    const dialogRef = this.dialog.open(TagRuleConflictViewerComponent, {
      width: '400px',
      height: '300px',
      disableClose: true,
      data: {
        projectId: this.projectInfo.projectId,
        fileId: fileId
      }
    })
  }
}
