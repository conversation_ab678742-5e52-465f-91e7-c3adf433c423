import { ActionReducerMap, createFeatureSelector } from '@ngrx/store'
import { LicenseValidityModel, ProjectInfo } from '../../models/index'
import { ConfigActions, ConfigActionTypes } from '../actions'

export interface ConfigState {
  baseSettings: any
  controlSettings: any
  cssThemeVariables: any
  projectInfo: ProjectInfo
  helpLinks: any
  licenceValidity: LicenseValidityModel | null
}

export const initialState: ConfigState = {
  baseSettings: {},
  controlSettings: {},
  cssThemeVariables: {},
  projectInfo: {} as ProjectInfo,
  helpLinks: {},
  licenceValidity: null
}

export function reducer(
  state = initialState,
  action: ConfigActions
): ConfigState {
  switch (action.type) {
    case ConfigActionTypes.SetBaseSettings:
      return {
        ...state,
        baseSettings: action.baseSettings
      }

    case ConfigActionTypes.SetControlSettings:
      return {
        ...state,
        controlSettings: action.controlSettings
      }

    case ConfigActionTypes.SetHelpLinks:
      return {
        ...state,
        helpLinks: action.helpLinks
      }

    case ConfigActionTypes.SetCssThemeVariables:
      return {
        ...state,
        cssThemeVariables: action.cssThemeVariables
      }

    case ConfigActionTypes.SetProjectInfo:
      return {
        ...state,
        projectInfo: action.projectInfo
      }

    /**
     * when we go back to the project list, we need to clear the previous project state from the history
     * so user won't see previous data being displayed which might get confusion when HTTP request fails.
     */
    case ConfigActionTypes.ResetProjectInfo:
      return {
        ...state,
        projectInfo: null
      }

    case ConfigActionTypes.SetLicenseValidity:
      return {
        ...state,
        licenceValidity: action.licenseValidity
      }

    default:
      return state
  }
}

export interface State {
  configState: ConfigState
}

export const reducers: ActionReducerMap<State> = {
  configState: reducer
}

export const getConfigState = createFeatureSelector<State>('config')
