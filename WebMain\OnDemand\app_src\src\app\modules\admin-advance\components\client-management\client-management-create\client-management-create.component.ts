import {
  FileServerInfo,
  TemplateInfo
} from '@admin-advance/models/client-management/client-management.model'
import {
  ClearClientStateResponse,
  ClientMgmtStateSelector,
  CreateClientAction,
  EditClientAction,
  FetchClientDetailAction,
  FetchClientServiceListAction,
  FetchPreRequisiteAction
} from '@admin-advance/store/client-management'
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnInit,
  QueryList,
  TemplateRef,
  ViewChild,
  ViewChildren
} from '@angular/core'
import {
  FormBuilder,
  FormControlName,
  FormGroup,
  Validators
} from '@angular/forms'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute } from '@angular/router'
import { ConfigService } from '@config/services/config.service'
import { getControlSetting } from '@config/store/selectors'
import { Store as StoreX } from '@ngrx/store'
import { Navigate } from '@ngxs/router-plugin'
import { Store } from '@ngxs/store'
import {
  <PERSON>ricValidator,
  MessageModel,
  validateBeforeSubmit
} from '@shared/validators'
import { DxDataGridComponent } from 'devextreme-angular'
import { ToastrService } from 'ngx-toastr'
import { EMPTY, Subject } from 'rxjs'
import {
  catchError,
  debounceTime,
  filter,
  switchMap,
  takeUntil,
  tap
} from 'rxjs/operators'

interface GridMappingConfig {
  serviceType: string
  projectTemplateGrid: string
  exportTemplateGrid: string
  selectedProjectTemplateId: string
  selectedExportTemplateId: string
  serviceProjectTemplateInfo: string
  serviceExportTemplateInfo: string
}
@Component({
  selector: 'app-client-management-create',
  templateUrl: './client-management-create.component.html',
  styleUrls: ['./client-management-create.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ClientManagementCreateComponent implements OnInit {
  @ViewChild('mainWrapper', { static: true }) mainWrapperRef: TemplateRef<any>

  private readonly toDestroy$ = new Subject<void>()

  config = ConfigService

  displayTemplate: TemplateRef<any>

  public clientForm: FormGroup

  public confirmationForm: FormGroup

  fileServerList: FileServerInfo[]

  projectTemplate: TemplateInfo[]

  exportTemplate: TemplateInfo[]

  exportFieldTemplate: TemplateInfo[]

  formErrorMessage: string

  @ViewChild('projectTemplateGrid', { static: false })
  projectTemplateGrid: DxDataGridComponent

  @ViewChild('exportTemplateGrid', { static: false })
  exportTemplateGrid: DxDataGridComponent

  @ViewChild('exportFieldTemplateGrid', { static: false })
  exportFieldTemplateGrid: DxDataGridComponent

  @ViewChild('projectTemplateGrid1', { static: false })
  projectTemplateGrid1: DxDataGridComponent

  @ViewChild('exportTemplateGrid1', { static: false })
  exportTemplateGrid1: DxDataGridComponent

  @ViewChild('projectTemplateGrid2', { static: false })
  projectTemplateGrid2: DxDataGridComponent

  @ViewChild('exportTemplateGrid2', { static: false })
  exportTemplateGrid2: DxDataGridComponent

  selectedProjectTemplateData: TemplateInfo[] = []

  selectedExportTemplateData: TemplateInfo[] = []

  selectedExportFieldTemplateData: TemplateInfo[] = []

  uploadSharedLocationInfo: string

  projectSharedLocationInfo: string

  exportSharedLocationInfo: string

  private gridMapping: { [key: string]: GridMappingConfig } = {}

  public gridMappingArray = []

  /**
   * A generic form validation class
   */
  private genericValidator: GenericValidator

  /**
   * Object containing validation fail message defined on generic class.
   */
  displayMessage: MessageModel

  queryStringclientId = 0

  currentMode: 'Create' | 'Edit'

  selectedProjectTemplateRows: number[] = []

  selectedExportTemplateRows: number[] = []

  selectedExportFieldTemplateRows: number[] = []

  beforeUpdateProjectLocationFSID: number

  MaxIndexLocationLength = 150

  selectedProjectTemplateId: number | null = null

  selectedExportTemplateId: number | null = null

  selectedExportFieldTemplateId: number | null = null

  selectionType = '-1'

  isSingleSelection = false

  serviceTypeList: any

  serviceProjectTemplateInfo: TemplateInfo[]

  serviceExportTemplateInfo: TemplateInfo[]

  // Method to handle dropdown changes
  onSelectionTypeChange(selectionType: string) {
    this.selectionType = selectionType
    this.filterDataBasedOnSelection(this.selectionType)
  }

  /**
   * Template reference to show delete client
   */
  @ViewChild('editConfirm')
  private readonly confirmEditProjectLocation: TemplateRef<any>

  isSubmitting: boolean

  @ViewChildren(FormControlName, { read: ElementRef })
  private readonly formInputElements: QueryList<ElementRef>

  constructor(
    private xsstore: Store,
    private storeX: StoreX,
    private toast: ToastrService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private configService: ConfigService,
    private cdr: ChangeDetectorRef
  ) {
    this.queryStringclientId = route.snapshot.queryParams['clientId']
    this.currentMode = this.queryStringclientId > 0 ? 'Edit' : 'Create'
  }

  ngOnInit() {
    this.displayTemplate = this.mainWrapperRef
    this.xsstore.dispatch(new ClearClientStateResponse())

    this.initAction()

    this.reflectLocationDetails()

    this.generateGridMapping()
  }

  private generateGridMapping() {
    this.serviceTypeList?.serviceTypes?.forEach((service, index) => {
      const serviceType = service?.serviceTypeName

      this.gridMapping[serviceType] = {
        serviceType: serviceType,
        projectTemplateGrid: `projectTemplateGrid_${index}`,
        exportTemplateGrid: `exportTemplateGrid_${index}`,
        selectedProjectTemplateId: `selectedProjectTemplateId_${index}`,
        selectedExportTemplateId: `selectedExportTemplateId_${index}`,
        serviceProjectTemplateInfo: `serviceProjectTemplateInfo_${index}`,
        serviceExportTemplateInfo: `serviceExportTemplateInfo_${index}`
      }
    })

    this.gridMappingArray = Object.values(this.gridMapping)
  }

  ngAfterViewInit() {
    this.initValidationRules()
    this.validationWatcher()
    this.projectTemplateGrid.instance.deselectAll()
    this.exportTemplateGrid.instance.deselectAll()
    this.exportFieldTemplateGrid.instance.deselectAll()
  }

  initAction() {
    this.confirmationForm = this.fb.group({
      checkEditUpdateExistingProjectLocation: [false]
    })

    this.clientForm = this.fb.group({
      name: ['', Validators.required],
      address: [''],
      personName: [''],
      mobileNumber: ['', Validators.compose([Validators.pattern(/^\d+$/)])],
      phoneNumber: ['', Validators.compose([Validators.pattern(/^\d+$/)])],
      email: [
        '',
        Validators.compose([
          Validators.required,
          Validators.pattern(
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          )
        ])
      ],
      fax: [''],
      accountNumber: [''],
      memo: [''],
      uploadLocation: [{ value: '' }],
      projectLocation: [{ value: '' }],
      exportLocation: [{ value: '' }]
    })

    this.uploadSharedLocationInfo = ''

    this.projectSharedLocationInfo = ''

    this.exportSharedLocationInfo = ''

    this.xsstore
      .dispatch(new FetchPreRequisiteAction())
      .pipe(
        takeUntil(this.toDestroy$),
        switchMap(() =>
          this.xsstore.select(
            ClientMgmtStateSelector.SliceOf('clientMgmtPreRequisiteModel')
          )
        ),
        // catch error here
        catchError((res) => {
          this.toast.error(res?.error.message)
          // this.isLoading = false

          return EMPTY
        })
      )
      .subscribe((data) => {
        if (data) {
          this.fileServerList = data.FileServerInfoList
          this.projectTemplate = data.ProjectTemplateInfo
          this.exportTemplate = data.ExportTemplateInfo
          this.exportFieldTemplate = data.ExportFieldTemplateInfo
          this.filterDataBasedOnSelection(this.selectionType)
          if (this.currentMode === 'Edit') {
            this.setClientFormValue(this.queryStringclientId)
          }
        }
      })
    this.storeX

      .select(getControlSetting('MAX_INDEX_LOCATION_LENGTH'))
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((maxIndexLocationLength: number) => {
        console.log('maxIndexLocationLength', maxIndexLocationLength)
        if (maxIndexLocationLength > 0)
          this.MaxIndexLocationLength = maxIndexLocationLength
      })
    this.xsstore
      .select(ClientMgmtStateSelector.SliceOf('responseModel'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: any) => {
        this.cdr.markForCheck()
        this.isSubmitting = false
        if (response != null && response.data && response.status !== 'Error') {
          this.toast.success(response.message)
          this.xsstore.dispatch(
            new Navigate(['/admin/system/client-management/manage'])
          )
        } else {
          this.toast.error(response.message)
        }
      })

    this.xsstore
      .select(ClientMgmtStateSelector.SliceOf('editResponseModel'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: any) => {
        this.cdr.markForCheck()
        this.isSubmitting = false
        if (response != null && response.data && response.status !== 'Error') {
          this.toast.success(response.message)
          this.xsstore.dispatch(
            new Navigate(['/admin/system/client-management/manage'])
          )
        } else {
          this.toast.error(response.message)
        }
      })

    this.fetchServiceType(this.queryStringclientId)
  }

  saveChanges() {
    const errorMessage = validateBeforeSubmit(this.clientForm)
    if (errorMessage) {
      this.formErrorMessage = errorMessage
      return
    }
    this.formErrorMessage = null

    const repoLocation = {
      UploadLocationFSID: this.clientForm.value.uploadLocation,
      ProjectLocationFSID: this.clientForm.value.projectLocation,
      ExportDownloadLocationFSID: this.clientForm.value.exportLocation
    }

    // Dynamically construct service template info based on gridMapping
    const serviceExportTemplateInfo = []
    const serviceProjectTemplateInfo = []

    Object.values(this.gridMapping as unknown as GridMappingConfig[]).forEach(
      (config) => {
        const exportInfo = this[config.serviceExportTemplateInfo] || []
        const projectInfo = this[config.serviceProjectTemplateInfo] || []

        if (exportInfo.length) {
          serviceExportTemplateInfo.push(...exportInfo)
        }
        if (projectInfo.length) {
          serviceProjectTemplateInfo.push(...projectInfo)
        }
      }
    )

    const createClientModel = {
      ClientId: this.queryStringclientId,
      ClientName: this.clientForm.value.name,
      Address: this.clientForm.value.address,
      PhoneNumber: this.clientForm.value.phoneNumber,
      MobileNumber: this.clientForm.value.mobileNumber,
      Email: this.clientForm.value.email,
      AccountNumber: this.clientForm.value.accountNumber,
      Fax: this.clientForm.value.fax,
      Memo: this.clientForm.value.memo,
      IsVodClient: true,
      RepositoryLocation: repoLocation,
      AutoSendCaseCompletedNotification: false,
      IsThirdPartyBillingEnabled: false,
      UserId: ConfigService.userId,
      ProjectTemplateInfo: this.selectedProjectTemplateData,
      ExportTemplateInfo: this.selectedExportTemplateData,
      ExportFieldTemplateInfo: this.selectedExportFieldTemplateData,
      serviceExportTemplateInfo: serviceExportTemplateInfo,
      serviceProjectTemplateInfo: serviceProjectTemplateInfo,
      ContactPersonName: this.clientForm.value.personName
    }

    const applicableRemoteSharedPathLength =
      this.MaxIndexLocationLength - createClientModel.ClientName.length - 23 // where 23 is derived from '\\Case\CN\VenioIndexLoc'. Here CN is case name which is min 2 char.

    if (
      this.projectSharedLocationInfo.length > applicableRemoteSharedPathLength
    ) {
      this.toast.error(
        `Case location length should not be greater than ${applicableRemoteSharedPathLength} characters.`
      )
      return
    }

    if (this.currentMode === 'Create') {
      this.cdr.markForCheck()
      this.isSubmitting = true
      /**Create start */
      this.xsstore.dispatch(new CreateClientAction(createClientModel))
      /**Create end */
    } else {
      /**Edit start */
      if (
        this.beforeUpdateProjectLocationFSID !==
        createClientModel.RepositoryLocation.ProjectLocationFSID
      ) {
        // Show confirmation
        const delRef = this.dialog.open(this.confirmEditProjectLocation, {
          closeOnNavigation: true,
          autoFocus: false,
          width: '380px'
        })

        delRef
          .beforeClosed()
          .pipe(
            filter((yes) => yes), // If clicked yes
            tap(() => {
              this.cdr.markForCheck()
              this.isSubmitting = true
              this.xsstore.dispatch(
                new EditClientAction(
                  createClientModel,
                  this.confirmationForm.value.checkEditUpdateExistingProjectLocation
                )
              )
            }),
            switchMap(() =>
              this.xsstore.select(
                ClientMgmtStateSelector.SliceOf('editResponseModel')
              )
            ),
            debounceTime(400),
            takeUntil(this.toDestroy$)
          )
          .subscribe((response: any) => {
            if (
              response != null &&
              response.data &&
              response.status !== 'Error'
            ) {
              this.toast.success(response.message)
              this.xsstore.dispatch(
                new Navigate(['/admin/system/client-management/manage'])
              )
            } else if (response != null && response.status === 'Error') {
              this.toast.error(response?.message)
            }
          })
      } else {
        this.cdr.markForCheck()
        this.isSubmitting = true
        this.xsstore.dispatch(new EditClientAction(createClientModel, false))
      }
      /**Edit end */
    }
  }

  /**
   * A generic validation rules, properties with message.
   * Must be called before the form being initialized.
   */
  private initValidationRules(): void {
    this.genericValidator = new GenericValidator({
      // the parent properties are same as the form group property and
      // the child properties are the either of angular validator or custom property
      name: {
        required: 'Client name is a required field.'
      },
      mobileNumber: {
        pattern: 'Invalid mobile number.'
      },
      phoneNumber: {
        pattern: 'Invalid phone number.'
      },
      email: {
        required: 'Email address is a required field.',
        pattern: 'Invalid email address.'
      }
    })
  }

  private validationWatcher() {
    this.genericValidator
      .initValidationProcess(this.clientForm, this.formInputElements)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({
        next: (m) => {
          ;[(this.displayMessage = m)]
        }
      })
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  // gets repository info
  setClientFormValue(clientId: number) {
    this.xsstore
      .dispatch(new FetchClientDetailAction(clientId))
      .pipe(
        switchMap(() =>
          this.xsstore.select(
            ClientMgmtStateSelector.SliceOf('clientManagementModel')
          )
        ),
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (response: any) => {
          if (response) {
            const clientMgmtModel = response

            if (clientMgmtModel) {
              this.clientForm.patchValue({
                name: clientMgmtModel.clientName,
                address: clientMgmtModel.address,
                personName: clientMgmtModel.contactPersonName,
                mobileNumber: clientMgmtModel.mobileNumber,
                phoneNumber: clientMgmtModel.phoneNumber,
                email: clientMgmtModel.email,
                fax: clientMgmtModel.fax,
                accountNumber: clientMgmtModel.accountNumber,
                memo: clientMgmtModel.memo,
                uploadLocation:
                  clientMgmtModel.repositoryLocation.uploadLocationFSID,
                projectLocation:
                  clientMgmtModel.repositoryLocation.projectLocationFSID,
                exportLocation:
                  clientMgmtModel.repositoryLocation.exportDownloadLocationFSID
              })

              // Update location details
              this.reflectLocationDetails()

              // Ensure change detection is triggered
              this.cdr.markForCheck()
              this.initValidationRules()
              this.validationWatcher()

              // Initialize grid selections
              this.initializeGridSelections(clientMgmtModel)
            } else {
              this.toast.error(response.message)
            }
          }
        }
      })
  }

  // Initialize grid selections to prefill after data is fetched
  initializeGridSelections(clientMgmtModel?: any) {
    if (!clientMgmtModel) return

    // Initialize default selections when selectionType === -1
    this.initializeDefaultSelections(clientMgmtModel)

    // Initialize service-based grid selections dynamically
    this.initializeServiceSelections(clientMgmtModel)
  }

  // Function to handle default selection when selectionType === -1
  private initializeDefaultSelections(clientMgmtModel: any) {
    this.selectedProjectTemplateRows = this.getSelectedRows(
      this.selectedProjectTemplateData,
      clientMgmtModel?.projectTemplateInfo
    )

    this.selectedExportTemplateRows = this.getSelectedRows(
      this.selectedExportTemplateData,
      clientMgmtModel?.exportTemplateInfo
    )

    this.selectedExportFieldTemplateRows = this.getSelectedRows(
      this.selectedExportFieldTemplateData,
      clientMgmtModel?.exportFieldTemplateInfo
    )
  }

  // Function to handle service-based grid selections dynamically
  private initializeServiceSelections(clientMgmtModel: any) {
    Object.entries(this.gridMapping).forEach(
      ([serviceKey, config]: [string, any]) => {
        const serviceProjectTemplates = this.filterByServiceType(
          clientMgmtModel?.serviceProjectTemplateInfo,
          config.serviceType
        )

        const serviceExportTemplates = this.filterByServiceType(
          clientMgmtModel?.serviceExportTemplateInfo,
          config.serviceType
        )

        // Assign values dynamically
        this[config.serviceProjectTemplateInfo] = serviceProjectTemplates
        this[config.serviceExportTemplateInfo] = serviceExportTemplates

        // Set selected IDs dynamically
        this[config.selectedProjectTemplateId] = this.getFirstTemplateId(
          serviceProjectTemplates
        )
        this[config.selectedExportTemplateId] = this.getFirstTemplateId(
          serviceExportTemplates
        )
      }
    )

    this.cdr.detectChanges()
  }

  // Utility function to select rows or fallback to model data
  private getSelectedRows(selectedData: any[], modelData: any[]): any[] {
    return selectedData?.length
      ? selectedData
      : modelData?.map((element) => element.templateId) || []
  }

  // Utility function to filter data by service type
  private filterByServiceType(dataArray: any[], serviceType: string): any[] {
    if (!dataArray || !this.serviceTypeList?.templateTypes) {
      return []
    }

    // Find all matching serviceTypeIds for the given serviceTypeName
    const matchingServiceTypeIds = this.serviceTypeList.templateTypes
      .filter((template) => template.serviceTypeName === serviceType)
      .map((template) => template.serviceTypeId)

    // Filter dataArray by checking if the item.serviceTypeId exists in matchingServiceTypeIds
    return dataArray.filter((item) =>
      matchingServiceTypeIds.includes(item.serviceTypeId)
    )
  }

  // Utility function to get the first template ID or null
  private getFirstTemplateId(dataArray: any[]): number | null {
    return dataArray?.length ? dataArray[0].templateId : null
  }

  private reflectLocationDetails() {
    this.clientForm.get('uploadLocation').valueChanges.subscribe((data) => {
      if (data > 0) {
        const uploadShared = this.fileServerList.filter(
          (el) => el.FSID === data
        )
        this.uploadSharedLocationInfo = uploadShared[0].SharedFolder
      }
    })

    this.clientForm.get('projectLocation').valueChanges.subscribe((data) => {
      if (data > 0) {
        const projectShared = this.fileServerList.filter(
          (el) => el.FSID === data
        )
        this.projectSharedLocationInfo = projectShared[0].SharedFolder
      }
    })

    this.clientForm.get('exportLocation').valueChanges.subscribe((data) => {
      if (data > 0) {
        const exportShared = this.fileServerList.filter(
          (el) => el.FSID === data
        )
        this.exportSharedLocationInfo = exportShared[0].SharedFolder
      }
    })
  }

  disabledAplhabetKeyPress(event: any) {
    const charCode = event.which ? event.which : event.keyCode
    return !(charCode > 31 && (charCode < 48 || charCode > 57))
  }

  private filterDataBasedOnSelection(selectedDisplayName: string) {
    if (selectedDisplayName === '-1') {
      if (this.serviceTypeList?.templateTypes?.length > 0) {
        const projectTemplate = this.serviceTypeList.templateTypes.find(
          (template) => template.serviceTypeName === 'VOD_SERVICE'
        )
        const exportTemplate = this.serviceTypeList.templateTypes.find(
          (template) => template.serviceTypeName === 'VOD_SERVICE_EXPORT'
        )

        const projectTemplateId = projectTemplate?.serviceTypeId
        const exportTemplateId = exportTemplate?.serviceTypeId

        if (projectTemplateId && exportTemplateId) {
          this.projectTemplate = this.projectTemplate.map((template) => ({
            ...template,
            ServiceTypeId: projectTemplateId
          }))

          this.exportTemplate = this.exportTemplate.map((template) => ({
            ...template,
            ServiceTypeId: exportTemplateId
          }))
        } else {
          console.warn('Service Type IDs could not be determined.')
        }
      } else {
        console.warn('Template types are not available or empty.')
      }
    } else {
      const filteredTemplateTypes = this.serviceTypeList?.templateTypes.filter(
        (template) => template.serviceTypeName === selectedDisplayName
      )

      const templateTypeMap = new Map<string, number>()
      filteredTemplateTypes.forEach((template) => {
        templateTypeMap.set(template.templateType, template.serviceTypeId)
      })

      this.projectTemplate = this.projectTemplate.map((template) => {
        if (templateTypeMap.has(template.TemplateType)) {
          return {
            ...template,
            ServiceTypeId: templateTypeMap.get(template.TemplateType)
          }
        }
        return template
      })

      this.exportTemplate = this.exportTemplate.map((template) => {
        if (templateTypeMap.has(template.TemplateType)) {
          return {
            ...template,
            ServiceTypeId: templateTypeMap.get(template.TemplateType)
          }
        }
        return template
      })
    }

    this.cdr.markForCheck()
  }

  private fetchServiceType(clientId) {
    this.xsstore
      .dispatch(new FetchClientServiceListAction(clientId))
      .pipe(
        takeUntil(this.toDestroy$),
        switchMap(() =>
          this.xsstore.select(
            ClientMgmtStateSelector.SliceOf('serviceTypeListModel')
          )
        )
      )
      .subscribe({
        next: (response: any) => {
          if (response) {
            if (response) {
              this.serviceTypeList = response
              this.generateGridMapping()
            }
          }
        }
      })
  }

  onRadioButtonSelect(
    templateId: number,
    gridType: string,
    serviceType: string
  ) {
    const gridKeys = this.gridMapping[serviceType]

    if (gridKeys) {
      if (gridType === gridKeys.projectTemplateGrid) {
        this[gridKeys.selectedProjectTemplateId] = templateId
      } else if (gridType === gridKeys.exportTemplateGrid) {
        this[gridKeys.selectedExportTemplateId] = templateId
      }
      this.updateSelectedData(templateId, gridType, serviceType)
    }
  }

  private updateSelectedData(
    templateId: number,
    gridType: string,
    serviceType: string
  ) {
    const gridKeys = this.gridMapping[serviceType]

    if (gridKeys) {
      const dataSource =
        gridType === gridKeys.projectTemplateGrid
          ? this.projectTemplate
          : this.exportTemplate
      const selectedInfoKey =
        gridType === gridKeys.projectTemplateGrid
          ? gridKeys.serviceProjectTemplateInfo
          : gridKeys.serviceExportTemplateInfo

      this[selectedInfoKey] = dataSource?.find(
        (template) => template.TemplateId === templateId
      )
        ? [dataSource.find((template) => template.TemplateId === templateId)]
        : []
    }
  }

  onGridSelectionChanged(
    gridType:
      | 'projectTemplateGrid'
      | 'exportTemplateGrid'
      | 'exportFieldTemplateGrid',
    event: any
  ) {
    if (event) {
      switch (gridType) {
        case 'projectTemplateGrid':
          this.selectedProjectTemplateData = event?.selectedRowsData?.length
            ? [...event?.selectedRowsData]
            : []
          // Ensure currentDeselectedRowKeys are processed correctly
          if (event?.currentDeselectedRowKeys?.length) {
            this.selectedProjectTemplateData =
              this.selectedProjectTemplateData.filter(
                (data) =>
                  !event.currentDeselectedRowKeys.some(
                    (deselectedKey) => deselectedKey === data.TemplateId
                  )
              )
          }
          this.selectedProjectTemplateRows =
            this.selectedProjectTemplateData.map(
              (element) => element.TemplateId
            )
          break
        case 'exportTemplateGrid':
          this.selectedExportTemplateData = event?.selectedRowsData?.length
            ? [...event?.selectedRowsData]
            : []
          if (event?.currentDeselectedRowKeys?.length) {
            this.selectedExportTemplateData =
              this.selectedExportTemplateData.filter(
                (data) =>
                  !event.currentDeselectedRowKeys.some(
                    (deselectedKey) => deselectedKey === data.TemplateId
                  )
              )
          }
          this.selectedExportTemplateRows = this.selectedExportTemplateData.map(
            (element) => element.TemplateId
          )
          break
        case 'exportFieldTemplateGrid':
          this.selectedExportFieldTemplateData = event?.selectedRowsData?.length
            ? [...event?.selectedRowsData]
            : []
          if (event?.currentDeselectedRowKeys?.length) {
            this.selectedExportFieldTemplateData =
              this.selectedExportFieldTemplateData.filter(
                (data) =>
                  !event.currentDeselectedRowKeys.some(
                    (deselectedKey) => deselectedKey === data.TemplateId
                  )
              )
          }
          this.selectedExportFieldTemplateRows =
            this.selectedExportFieldTemplateData.map(
              (element) => element.TemplateId
            )
          break
      }
    }
  }
}
