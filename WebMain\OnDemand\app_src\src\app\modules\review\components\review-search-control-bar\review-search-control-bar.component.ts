import {
  DocumentRestrictionModel,
  RestrictionMode
} from '@admin-advance/models'
import { ViewTagRuleConflictModel } from '@admin-advance/models/tag-rule/tag-rule.model'
import {
  DocumentRestrictionStateSelector,
  FetchUserDocumentRestrictionAction
} from '@admin-advance/store'
import {
  AfterViewInit,
  Compiler,
  Component,
  ElementRef,
  EventEmitter,
  Injector,
  NgModuleFactory,
  OnDestroy,
  OnInit,
  TemplateRef,
  Type,
  ViewChild
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { User } from '@auth/models/user.model'
import { getUserDetails } from '@auth/store/selectors/access.selectors'
import { ProjectInfo } from '@config/models'
import { ConfigService } from '@config/services/config.service'
import {
  getControlSetting,
  getProjectInfo,
  getThemeClient,
  isImageTypePdf
} from '@config/store/selectors'
import { Action, select, Store } from '@ngrx/store'
import { Navigate } from '@ngxs/router-plugin'
import { Select, Store as XsStore } from '@ngxs/store'
import { DocNavigation } from '@review/models/document.model'
import { DocumentsService } from '@review/services/documents.service'
import { HistoryDocumentCommunicatorService } from '@review/services/history-document-communicator.service'
import { ReviewService } from '@review/services/review.service'
import { UserSettingModel } from '@root/modules/application-nav/models/application-nav-params'
import { GetSearchDuplicateOption } from '@root/modules/application-nav/store/actions'
import {
  hideSingleTagConfirmation,
  searchDuplicateOption
} from '@root/modules/application-nav/store/selectors/application-nav.selectors'
import { fadeInOut, inOut } from '@shared/animation'
import { CustodianMediaComponent } from '@shared/components/custodian-media/custodian-media.component'
import { DynamicFolderCreateComponent } from '@shared/components/dynamic-folder/dynamic-folder-create/dynamic-folder-create.component'
import { AutoFolderingComponent } from '@shared/components/folder/auto-foldering/auto-foldering.component'
import { FolderManagerComponent } from '@shared/components/folder/folder-manager/folder-manager.component'
//import { FolderManagerModule } from '@shared/components/folder/folder-manager/folder-manager.module'
import { FolderSelectorComponent } from '@shared/components/folder/folder-selector/folder-selector.component'
import { FolderSelectorModule } from '@shared/components/folder/folder-selector/folder-selector.module'
import { VenMatDialogUtilityService } from '@shared/components/ven-mat-confirm-dialog/ven-mat-dialog-utility.service'
import { ContainerComponent, OptionTypes } from '@shared/doc-commands'
import {
  CacheStorageKeysModel,
  CommandTypes,
  IVenMatConfirmDialog,
  PageSyncModel,
  ResponseModel,
  ReviewSetInfo,
  ReviewSetSummary,
  SaveTagForProductionRequestModel,
  SearchExpressionCount,
  SearchQueryModel,
  SearchQueryModule,
  SortingFieldModel
} from '@shared/models'
import { BreadCrumb, FilterParams } from '@shared/models/breadcrumb.model'
import { FolderActionType, Module } from '@shared/models/folder.model'
import {
  DyanamicFolderCreateModel,
  FolderInjectorModel,
  FolderSourcePanel,
  InitialSearchResultParameter,
  SearchDupOption,
  SearchInputParams,
  SearchResponseModel,
  TempTableResponseModel
} from '@shared/models/search.model'
import * as CONSTANT from '@shared/search-builder/child/search-duplicate-option/search-duplicate-option.const'
import {
  SetSearchDupOption,
  TriggerResetAdvancedSearchUiAction
} from '@shared/search-builder/sotre/query-builder.actions'
import { QueryBuilderSelector } from '@shared/search-builder/sotre/query-builder.selector'
import { SharedService } from '@shared/services/shared.service'
import { setFilterParameters, updateBreadCrumbs } from '@shared/store/actions'
import * as fromSharedDocumentActions from '@shared/store/actions/document.actions'
import * as fromSharedSearchActions from '@shared/store/actions/search.actions'
import { FilterState } from '@shared/store/reducers/filter.reducer'
import {
  SearchState,
  SearchState as SharedSearchState
} from '@shared/store/reducers/search.reducer'
import {
  getSavedSearchId,
  getSearchQuery
} from '@shared/store/selectors/document.selectors'
import {
  getBreadcrumbs,
  getCurrentFilterParameters
} from '@shared/store/selectors/filter.selector'
import {
  getSearchResponse,
  getSearchTempTableName
} from '@shared/store/selectors/search.selector'
import { DebounceTimer } from '@shared/utils'
import {
  ReviewNavigation,
  useNavigationState
} from '@shared/utils/ReviewNavigate'
import { StringUtils } from '@shared/utils/string-utils'
import { ClearIndexedDb } from '@shared/xsStore'
import {
  SetSelectedFolderScope,
  SetSelectedMediaScope,
  UploadStateResetAction
} from '@stores/actions'
import { TagModel } from '@stores/models'
import {
  CaseSelectors,
  StartupStateSelector,
  UploadStateSelector
} from '@stores/selectors'
import { cloneDeep, difference } from 'lodash'
import * as moment from 'moment'
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal'
import { ToastrService } from 'ngx-toastr'
import { combineLatest, EMPTY, fromEvent, Observable, of, Subject } from 'rxjs'
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs/operators'
import { UserRights } from 'src/app/helpers/user-rights'
import { SearchInputs } from 'src/app/modules/analyze/models'
import { GlobalErrorAction } from 'src/app/store/actions'
import * as fromCaseStateActions from '../../../../stores/actions/case.actions'
import { OverlayCustomFieldsFacade } from '../../../shared/xsStore/overlay-custom-fields/overlay-custom-fields.facade'
import { Limit } from '../../models/print.model'
import {
  ReviewDataSourceType,
  ReviewSearchActions
} from '../../models/review.model'
import {
  ReviewViewType,
  SaveSearchRequestModel,
  SearchHistory,
  SearchQueryRequest
} from '../../models/search.model'
import { MultiWindowSelectionService } from '../../services/multi-window-selection.service'
import { MultiWindowTranscriptService } from '../../services/multi-window-transcript.service'
import { QueryTextCommunicatorService } from '../../services/query-text-communicator.service'
import * as fromDocumentActions from '../../store/actions/documents.actions'
import * as fromSearchActions from '../../store/actions/search.actions'
import {
  getIsBatchSelection,
  getSelectedDocuments,
  getSelectedDocumentsCount,
  getTiffLimitSetting,
  getUnselectedDocuments
} from '../../store/selectors/documents.selectors'
import {
  CheckInReviewBatchAction,
  GetEmailThreadViewStatus,
  MarkAsReviewedBulkAction,
  QueueEntityForExtraction,
  SetcurrentDocNumber,
  SetReviewViewType
} from '../../xs-store'
import { ReviewSetStateSelector } from '../../xs-store/review.selectors'
import { AudioTranscribeComponent } from '../audio-transcribe/audio-transcribe.component'
import { DeleteDocumentComponent } from '../delete-document/delete-document.component'
import { DocumentBulkImagingComponent } from '../document-bulk-imaging/document-bulk-imaging.component'
import { DocumentDownloadComponent } from '../document-download/document-download.component'
import { DocumentsPrintComponent } from '../documents-print/documents-print.component'
import { DocumentsShareComponent } from '../documents-share/documents-share.component'
import { DocumentsSlipsheetComponent } from '../documents-slipsheet/documents-slipsheet.component'
import { DocumentsSlipsheetModule } from '../documents-slipsheet/documents-slipsheet.module'
import {
  LayoutMode,
  LoadSaveLayoutComponent
} from '../load-save-layout/load-save-layout.component'
import { ResponsivePstCreatorComponent } from '../responsive-pst-creator/responsive-pst-creator.component'
import { SaveSearchComponent } from '../save-search/save-search.component'
import { SearchHelpComponent } from '../search-help/search-help.component'
import { SearchHelpModule } from '../search-help/search-help.module'
import { SetIncludeFamilySearch } from './../../xs-store/review.actions'
import { MoveDocumentsComponent } from './../move-documents/move-documents.component'

@Component({
  selector: 'app-review-search-control-bar',
  templateUrl: './review-search-control-bar.component.html',
  styleUrls: ['./review-search-control-bar.component.scss'],
  animations: [inOut, fadeInOut]
})
export class ReviewSearchControlBarComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  /**
   * Subject to complete the subscriptions
   */
  private unsubscribed$ = new Subject<void>()

  public DEFAULT_SEARCH_EXPRESSION = 'FileId>0'

  @ViewChild('searchText') searchText: ElementRef

  @ViewChild('folderSelectorTemplate')
  private folderSelectorTemplate: TemplateRef<any>

  @ViewChild('searchHelpTemplate')
  private searchHelpTemplate: TemplateRef<any>

  /**
   * template ref of remove view all fields by template Id
   */
  @ViewChild('custodianMedia')
  private readonly tplCustodianMedia: TemplateRef<unknown>

  /**
   * User right to send the documents to analyze
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_VIEW_DASHBOARD)
  )
  allowAnalyze$: Observable<boolean>

  /**
   * Value injector in lazy loaded component
   */
  folderInjector: Injector

  /**
   *  Injector provider for lazy component
   */
  custodianMediaInjector: Injector

  public client: string

  /**
   * Toggle builder UI box when `advance query builder` button clicked.
   */
  showAdvanceFilterUi: boolean = <boolean>false

  /**
   * Whether user should be able to write on search input when query builder UI is shown
   */
  toggleInputWritable: boolean

  /**
   * Value can be either 'search' or 'reviewSet' based on the data to be loaded via search or review set.
   */
  reviewSourceType: string

  /**
   * handles text input event to perform UI toggling task or  element manipulation.
   */
  readonly forQueryBuilder = new EventEmitter()

  /**
   * Document restriction mode assigned to the user group
   */
  userDocRestrictonMode = RestrictionMode.None

  projectInfo: ProjectInfo

  /**
   * Folder list for search scope for the user having document restricted by folder
   */
  folderList: number[] = []

  /**
   * Restricted folder ids to user group
   */
  restrictedFolderIds: number[] = []

  /**
   * Loads the folder tree for folder scope selection
   */
  folderSelectorComponent: Promise<Type<FolderSelectorComponent>>

  /**
   * Lazy component to load on project scope (custodian/media)
   */
  custodianMediaComp: Promise<Type<CustodianMediaComponent>>

  // check condition to display project scope (custodian/media) when the page is loading
  isPageLoadShowProjectScope: boolean

  /**
   * Holds the breadcrumb of filters applied.
   */
  breadCrumbs: BreadCrumb[] = []

  // selected media list
  mediaList: number[] = []

  /**
   * Query that will be searched. This property is set:
   * 1. When user types the query from search textbox
   * 2. When documents are filtered
   * 3. When query sent from analyse to review.
   */
  searchQuery = new Subject<string>()

  /**
   * Include family hits `false` by default
   */
  includeFamilySearch: boolean

  private userDetails: User

  isExternalUser = false

  /**
   * Review set id to be loaded. This value is available, when review set is selected from launchpad.
   */
  reviewSetId: number

  /**
   * Flag to indicate if the search is loaded based on sql query mode.
   */
  isSqlMode: boolean

  /**
   * Information that is returned by search
   */
  searchResultParameter: InitialSearchResultParameter

  /**
   * search Duplicate Option, default = -1 (not set), 1: Show all hits in the selected scope (No DeDupe)
   */
  searchDuplicateOption: SearchDupOption

  searchDuplicateOptionFromPreviousSearch: SearchDupOption

  /**
   * stores the currently sorting field information.
   */
  currentSortFieldModel: SortingFieldModel

  /**
   * Dynamic Folder ID
   */
  dynamicFolderId: number

  /**
   * Dynamic Folder Type (Global or Local)
   */
  isDynamicFolderGlobal: boolean

  /**
   * Selected view type
   */
  selectedViewType: ReviewViewType

  /**
   * Holds the name of different temp table and search related informations used during search
   */
  tempTables: TempTableResponseModel

  searchTextAreaExpanded = false

  searchQueryFromOtherSource: boolean

  disableScope: boolean

  /**
   * Checks Whether the search input has length by removing leading,trailing spaces  expression text.
   */
  get isEmptyInput(): boolean {
    return !(this.searchText?.nativeElement?.value?.trim().length > 0)
  }

  get isDefaultExpression(): boolean {
    return (
      this.searchText?.nativeElement?.value?.trim() ===
      this.DEFAULT_SEARCH_EXPRESSION
    )
  }

  /**
   * Total number of documents searched
   */
  totalDocuments = 0

  searchHelpInjector: Injector

  searchHelpComponent: Promise<Type<SearchHelpComponent>>

  /**
   * No. of document selected from search result
   */
  selectedDocumentsCount: number

  reviewActions = ReviewSearchActions

  searchHelpModule: NgModuleFactory<SearchHelpModule>

  canShowReviewData: boolean

  isPageLoad: boolean

  /**
   * Document restriction mode refference
   */
  restrictionMode = RestrictionMode

  /**
   * Lazy component module to load for scope selection
   */
  folderSelectorModule: NgModuleFactory<FolderSelectorModule>

  /**
   * Array of selected document file ids
   */
  selectedDocuments: number[]

  // Size of search result grid per page.
  pageSize = 0

  // Selected page of search result grid.
  currentPage = 1

  // max no of pages
  maxPageSize = 1

  /**
   * User right to share the documents to other users
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_SHARE_DOCUMENT)
  )
  allowToShareDoc$: Observable<boolean>

  private shareDocumentModalRef: BsModalRef

  private deleteDocumentModalRef: BsModalRef

  private moveDocumentModalRef: BsModalRef

  /**
   * User right to bulk image
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_BULK_TIFF))
  allowToBulkImage$: Observable<boolean>

  /**
   * template ref of send to image
   */
  @ViewChild('sendToImage')
  private readonly sendToImageTemplateRef: TemplateRef<any>

  /**
   *  Injector provider for lazy component
   */
  sendToImageInjector: Injector

  /**
   * Lazy component to load on user demand for DocumentBulkImagingComponent
   */
  sendToImageComponent: Promise<Type<DocumentBulkImagingComponent>>

  /*********************************** PST */
  /**
   * template ref of responsive pst
   */
  @ViewChild('responsivePST')
  private readonly responsivePSTTemplateRef: TemplateRef<any>

  /**
   *  Injector provider for lazy component
   */
  responsivePSTInjector: Injector

  /**
   * Lazy component to load on user demand for DocumentBulkImagingComponent
   */
  reponsivePSTComponent: Promise<Type<ResponsivePstCreatorComponent>>

  /**
   * User right to create responsive PST
   */
  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_TO_GENERATE_RESPONSIVE_PST
    )
  )
  allowCreateResponsivePST$: Observable<boolean>

  /**
   * User right to transcribe
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_LAUNCH_TRANSCRIBING)
  )
  allowToTranscribe$: Observable<boolean>

  /**
   * template ref of audioTranscribe
   */
  @ViewChild('audioTranscribe')
  private readonly audioTranscribeTemplateRef: TemplateRef<any>

  /**
   *  Injector provider for lazy component
   */
  audioTranscribeInjector: Injector

  /**
   * Lazy component to load on user demand for AudioTranscribeComponent
   */
  audioTranscribeComponent: Promise<Type<AudioTranscribeComponent>>

  /************************************ */
  /**
   * Value to hold the Select all from all pages option and send to image
   */
  documentTableIsBatchSelected: boolean

  /**
   * Value to hold unselected documents during the 'Slect all from all pages' case
   */
  documentTableUnSelectedDocs: number[]

  /**
   * User right to produce the documents
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_EXPORT))
  allowProduction$: Observable<boolean>

  /**
   * Boolean flag that indicates the send to production action is triggered.
   */
  navigateToProduction: boolean

  /**
   * Template reference variable to show the batch check-out confirmation dialog
   */
  @ViewChild('checkOutBatch')
  private readonly checkOutBatchRef: TemplateRef<any>

  /**
   * Template reference variable to show the batch check-in confirmation dialog
   */
  @ViewChild('checkInBatch')
  private readonly checkInBatchRef: TemplateRef<any>

  /**
   * Flag that indicates if all documents in the review set batch are already reviewed.
   * Based on this flag, we can enable/disable the review set check-in button.
   */
  areAllDocumentsInBatchReviewed: boolean

  /**
   * Template reference variable to show the batch check-in warning dialog
   */
  @ViewChild('checkInBatchWarning')
  private readonly checkInBatchWarningRef: TemplateRef<any>

  /**
   * User right to print
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_PRINT))
  allowToPrint$: Observable<boolean>

  printLimit: Limit

  /** template ref of remove view all fields by template Id */
  @ViewChild('tpldocumentPrint')
  private readonly tpldocumentPrint: TemplateRef<unknown>

  /** Documents Print for lazy component */
  documentPrintComponent: Promise<Type<DocumentsPrintComponent>>

  /** Injector provider for lazy component */
  documentPrintInjector: Injector

  private downloadDocumentModalRef: BsModalRef

  /**
   * ---------------------slipsheet lazy loading component-----------------------------------
   * Lazy component'module to load on slipsheet
   */
  slipsheetModule: NgModuleFactory<DocumentsSlipsheetModule>

  slipsheetComponent: Promise<Type<DocumentsSlipsheetComponent>>

  /**
   *  Injector provider for lazy component
   */
  slipsheetInjector: Injector

  /**
   * template ref of remove view all fields by template Id
   */
  @ViewChild('tplSlipsheet')
  private readonly tplSlipsheet: TemplateRef<unknown>
  //----------------------slipsheet lazy loading component-----------------------------------

  private saveLayoutModalRef: BsModalRef

  private saveSearchModalRef: BsModalRef

  layoutMode = LayoutMode

  /**
   * Name of the currently loading review layout
   */
  selectedLayoutName: string

  /**
   * Search grid displayed column to download on CSV
   */
  columnsDataCSV: any = []

  /**
   * User right to create global dynamic folder
   */
  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_TO_ADD_GLOBAL_DYNAMIC_FOLDER
    )
  )
  allowToCreateGlobalDynamicFolder$: Observable<boolean>

  /**
   * User right to create local dynamic folder
   */
  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_TO_ADD_LOCAL_DYNAMIC_FOLDER
    )
  )
  allowToCreateLocalDynamicFolder$: Observable<boolean>

  /**
   * Value injector in lazy loaded component
   */
  dynamicFolderInjector: Injector

  /**
   * Loads the dynamic folder create component lazily
   */
  dynamicFolderCreateComponent: Promise<Type<DynamicFolderCreateComponent>>

  /**
   * Template reference for loading the dynamic folder create component.
   */
  @ViewChild('dynamicFolderCreate')
  private dynamicFolderCreate: TemplateRef<any>

  /**
   * User right to create auto folder
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_AUTO_FOLDER))
  allowToAutoFolder$: Observable<boolean>

  /**
   * User right to send or remove the documents to/from the folder
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_SEND_REMOVE_FOLDER)
  )
  allowToAddRemoveDocsToFolder$: Observable<boolean>

  /**
   * User right to create/edit review layout
   */
  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_MANAGE_REVIEW_LAYOUT
    )
  )
  allowToManageReviewLayout$: Observable<boolean>

  /**
   * User right to perform search
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_SEARCH))
  allowSearch$: Observable<boolean>

  /**
   * User right to view email thread
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_VIEW_MESSAGE_THREAD)
  )
  allowToViewMessageThread$: Observable<boolean>

  /**
   * User right to queue documents for entity extraction
   */
  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_TO_QUEUE_DOCUMENT_FOR_ENTITY_EXTRACTION
    )
  )
  allowToQueueForEntityExtraction$: Observable<boolean>

  /**Value injector lazy loaded auto folder */
  autoFolderInjector: Injector

  /** Loads the auto folder create component lazily*/
  autoFolderingComponent: Promise<Type<AutoFolderingComponent>>

  /** Template reference for loading the auto folder create component. */
  @ViewChild('autoFolderCreate')
  private autoFolderingTemplateRef: TemplateRef<any>

  /**
   * Key id used to represent tree structure in document table treelist
   */
  keyId: string

  /**
   * Parent id used to represent tree structure in document table treelist.
   * When its value is set null, treelist behaves as grid with no hierarchy
   */
  parentId: string

  /**
   * text to display for switching the review view
   */
  switchViewText: string

  /**
   * Status sent from the api to show the email thread view warning.
   */
  emailThreadViewStatus: string

  /**
   * Dialog to show the email thread view warning message.
   */
  @ViewChild('emailThreadViewWarning')
  emailThreadViewWarningRef: TemplateRef<any>

  enableEntiityExtraction: boolean = <boolean>false

  /**
   * Template reference for loading the dynamic folder create component.
   */
  @ViewChild('folderManagerTemplate')
  private folderManagerTemplate: TemplateRef<any>

  /**
   * Loads the folder tree component lazily
   */
  folderManagerComponent: Promise<Type<FolderManagerComponent>>

  /**
   * Lazy component'module to load on project scope (custodian/media)
   */
  //folderManagerModule: NgModuleFactory<FolderManagerModule>

  folderActionType = FolderActionType

  isSyntaxPresenterUiVisible = false

  isSyntaxPopoverContentHovered = false

  searchQueryValue: string

  rights = UserRights

  searchTerm$ = new Subject<string>()

  multiLineSearchTerm$ = new Subject<boolean>()

  numberOfLines: number

  clearSearchText$ = new Subject<boolean>()

  /**
   * Whether the search is happened so we can enable/disable reset button
   */
  isSearched$ = new Subject<boolean>()

  /**
   * If the review datasource is review set, get review set options of selected review set id.
   */
  selectedReviewSetInfo: ReviewSetInfo

  /**
   * tag info of the selected review tag. This is required to create a query for filtering all/none reviewed documents
   */
  reviewTagInfo: TagModel

  currentDocNumber: number = <number>0

  docNavAction = DocNavigation

  showPrintTab: boolean

  showOptimizationWarning = false

  get isReviewSourceSearch(): boolean {
    return this.reviewSetId <= 0
  }

  readonly searchUiRight = UserRights.ALLOW_SEARCH

  isOverlayCustomFieldContainerLoading: boolean

  openDuplicateOption = false

  constructor(
    private xsStore: XsStore,
    private store: Store<SearchState>,
    private rxStore: Store,
    private filterStore: Store<FilterState>,
    private documentStore: Store<Action>,
    private dialog: MatDialog,
    private injector: Injector,
    private router: Router,
    private route: ActivatedRoute,
    private communicator: HistoryDocumentCommunicatorService,
    private queryCommunicator: QueryTextCommunicatorService,
    private modalService: BsModalService,
    private sharedSearchstore: Store<SharedSearchState>,
    private transcriptService: MultiWindowTranscriptService,
    private configService: ConfigService,
    private toast: ToastrService,
    private multiWindowSelectionService: MultiWindowSelectionService,
    private vcds: VenMatDialogUtilityService,
    private compiler: Compiler,
    private reviewService: ReviewService,
    private documentService: DocumentsService,
    private overlayCustomFieldsFacade: OverlayCustomFieldsFacade,
    private sharedService: SharedService
  ) {
    this.readRouteQueryParam()
  }

  ngOnInit() {
    this.disableScope = true
    this.showOptimizationWarning = false
    this.isPageLoad = false
    this.initSlices()
    this.selectSearchSyntax()

    this.sharedService.currentBatchOptions
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((batchOptions) => {
        if (batchOptions) {
          if (
            batchOptions.totalGeneratedHtmlFiles === batchOptions.totalFiles
          ) {
            this.showOptimizationWarning = false
          } else {
            this.showOptimizationWarning = true
          }
        } else {
          this.showOptimizationWarning = false
        }
      })
  }

  ngAfterViewInit() {
    this.handleBuilderEvent()
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('selectedReviewLayout'))
      .pipe(
        filter((res) => !!res),
        take(1)
      )
      .subscribe((reviewLayout) => {
        this.initialSearch()
      })
    this.listenElementRefChanges()
    this.initGetCurrentFilterParams()

    this.store
      .pipe(select(isImageTypePdf))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((result: boolean) => {
        this.showPrintTab = result
      })

    this.mediaList = this.xsStore.selectSnapshot(
      StartupStateSelector.SliceOf('selectedMediaScope')
    )

    this.folderList = this.xsStore.selectSnapshot(
      StartupStateSelector.SliceOf('selectedFolderScope')
    )
  }

  listenElementRefChanges() {
    this.searchQuery
      .pipe(debounceTime(1000), takeUntil(this.unsubscribed$))
      .subscribe((term: string) => {
        const searchQuery: string = term

        // checks if scroll height of textarea is greater than height
        const enableMultiLine: boolean =
          this.searchText?.nativeElement?.offsetHeight <=
          this.searchText?.nativeElement?.scrollHeight
        this.multiLineSearchTerm$.next(enableMultiLine)
        this.clearSearchText$.next(searchQuery?.length > 0)
        // if no multiline is available and textarea is expanded then shrink text area
        if (!enableMultiLine && this.searchTextAreaExpanded) {
          this.toggleSearchTextArea()
        }
      })
  }

  /**
   * Sets query string to the expression input
   * @param q incoming query input
   */
  readonly setQuery = (q: string): void => {
    if (this.showAdvanceFilterUi) this.searchQuery.next(q)
  }

  readRouteQueryParam() {
    this.reviewSetId = +this.route.snapshot.queryParamMap.get('reviewSetId')
    this.isSqlMode = JSON.parse(
      this.route.snapshot.queryParamMap.get('queryModeEnabled')
    )
  }

  initSlices() {
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('includeFamilySearch'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        this.includeFamilySearch = res
      })

    this.xsStore
      .select(QueryBuilderSelector.sliceOf('searchDuplicateOption'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        this.searchDuplicateOption = res
      })

    this.store
      .pipe(select(getThemeClient), takeUntil(this.unsubscribed$))
      .subscribe((client: string) => {
        this.client = client
      })

    this.store
      .pipe(
        select(getProjectInfo),
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((projectInfo: ProjectInfo) => {
        this.projectInfo = projectInfo
        this.fetchReviewSetInfo()
      })

    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('reviewSourceType'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((sourceType: ReviewDataSourceType) => {
        this.reviewSourceType = sourceType.toString()
      })

    this.store
      .pipe(select(getSelectedDocumentsCount), takeUntil(this.unsubscribed$))
      .subscribe((count: number) => {
        this.selectedDocumentsCount = count
      })

    this.store
      .pipe(select(getSelectedDocuments), takeUntil(this.unsubscribed$))
      .subscribe((selectedDocuments: number[]) => {
        this.selectedDocuments = selectedDocuments
      })

    this.store
      .pipe(select(getUserDetails), takeUntil(this.unsubscribed$))
      .subscribe((userDetails: User) => {
        this.userDetails = userDetails
        this.isExternalUser =
          this.userDetails?.userRole?.toLowerCase() === 'external'
      })

    this.initBreadcrumbs()
    this.initSearchQuery()
    this.initSearchHistoryCommunicator()
    this.searchWhenUploadSendsQuery()
    this.initFilterWhenSync()
    this.initReviewTagInfo()
    this.initSearchResponse()
    this.initBatchSelectionFlag()
    this.initUnSelectedDocs()
    this.initSavedSearch()
    this.initBatchReviewCompletedFlag()
    this.initTiffLimitSetting()
    this.initSelectedLayoutName()
    this.initReviewViewType()
    this.initEntityExtraction()
    this.initCurrentPage()
    this.GetSearchDuplicateOpion()

    //need when open for first time
    if (!this.searchQueryFromOtherSource) this.getSearchDuplicateData()
  }

  async initialSearch() {
    this.store.dispatch(
      fromSharedSearchActions.updateSearchBaseExpression({
        payload: { expression: null }
      })
    )
    //restriction api not to be called for external user login
    if (this.userDetails.userRole.toLowerCase() !== 'external') {
      const documentRestriction: DocumentRestrictionModel =
        await this.getDocumentRestrictionInfo()
      if (documentRestriction?.isDocRestricted) {
        this.userDocRestrictonMode = documentRestriction.restrictionMode
        if (documentRestriction.restrictionMode === RestrictionMode.Folder)
          this.restrictedFolderIds = documentRestriction.restrictedFolderIds
      }
    }

    this.disableScope = false

    if (this.searchQueryFromOtherSource) return
    //remove all breadcrumbs as this is the initial search.
    //this.filterStore.dispatch(removeFilterBreadCrumb({ payload: { index: 1 } }))

    //This is executed when review page is loaded for the first time or review page is refreshed.
    // if call from navbar then show select project scope (custodian/media) when page load
    if (this.reviewSetId > 0) {
      this.searchQuery.next(this.DEFAULT_SEARCH_EXPRESSION)
      this.store.dispatch(
        fromSharedSearchActions.search({
          payload: {
            searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
            isResetBaseGuid: true,
            reviewSetId: this.reviewSetId,
            isInitialSearch: true
          }
        })
      )
    } else {
      const showCustodianMedia: boolean =
        await this.fetchDisplayCustodianMedia()
      this.isPageLoadShowProjectScope = showCustodianMedia

      const reviewNavigatedFrom = useNavigationState().isNavigatedByValue(
        ReviewNavigation.NAVIGATE_REVIEW
      )

      // external user then not show
      if (
        this.isPageLoadShowProjectScope &&
        !reviewNavigatedFrom &&
        this.userDetails.userRole.toLowerCase() !== 'external' &&
        !this.route.snapshot.queryParams['gl-window'] &&
        !this.getTagRuleData()
      ) {
        this.openProjectScope(true)
      } else {
        this.displayReviewDocumentsBasedOnPreferences()
      }
    }
  }

  displayReviewDocumentsBasedOnPreferences(): void {
    this.rxStore
      .pipe(
        select(hideSingleTagConfirmation),
        filter((c) => !this.isPageLoad),
        switchMap((userSettings: UserSettingModel) =>
          this.handleUserAndControlSettings(userSettings)
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((canShowReviewData: boolean) => {
        if (this.canShowReviewData != canShowReviewData) {
          this.canShowReviewData = canShowReviewData
          if (canShowReviewData) {
            this.handleShowReviewData()
          } else {
            this.showAdvanceFilterUi = true
            this.resetReviewData()
          }
          this.isPageLoad = true
        }
      })
  }

  handleUserAndControlSettings(
    userSettings: UserSettingModel
  ): Observable<boolean> {
    return this.store.pipe(
      select(getControlSetting('SHOW_ALL_DOCUMENT_IN_ONDEMAND_REVIEW')),
      map((isShowData: any) => {
        const canShowReviewData = this.shouldShowReviewData(
          userSettings,
          isShowData
        )
        return canShowReviewData
      })
    )
  }

  shouldShowReviewData(
    userSettings: UserSettingModel,
    isShowData: any
  ): boolean {
    // Determine whether to show review data based on user settings
    if (userSettings.DefaultLoadAllDocsInReview == null) {
      return isShowData
    } else {
      return userSettings.DefaultLoadAllDocsInReview
    }
  }

  handleShowReviewData(): void {
    const viewTagConflictData: ViewTagRuleConflictModel = this.getTagRuleData()
    const expression = viewTagConflictData
      ? `TagRuleId IN(${viewTagConflictData.conflictTagRuleIds.join(',')})`
      : this.DEFAULT_SEARCH_EXPRESSION

    if (viewTagConflictData) this.isPageLoadShowProjectScope = false
    this.searchQuery.next(expression)
    this.fetchReviewData(expression)
  }

  resetReviewData(): void {
    this.searchQuery.next('')
    const resetData = 'FileId = 0'
    this.fetchReviewData(resetData)
  }

  fetchReviewData(searchExpression: string): void {
    this.store.dispatch(
      fromSharedSearchActions.search({
        payload: {
          searchExpression: searchExpression,
          isResetBaseGuid: true,
          isInitialSearch: true,
          isSqlMode: JSON.parse(
            this.route.snapshot.queryParamMap.get('queryModeEnabled')
          )
        }
      })
    )
  }

  getTagRuleData() {
    const strModuleData = this.route.snapshot.queryParamMap.get('moduleData')
    if (StringUtils.isNullOrEmpty(strModuleData)) return null
    else {
      const jsondata = atob(strModuleData)
      const tagRuleData: ViewTagRuleConflictModel = JSON.parse(jsondata)
      return tagRuleData
    }
  }

  initBreadcrumbs() {
    this.filterStore
      .pipe(select(getBreadcrumbs), takeUntil(this.unsubscribed$))
      .subscribe((breadcrumbs) => {
        this.breadCrumbs = breadcrumbs.map((item, index) => ({
          ...item,
          itemIndex: index
        }))
      })
  }

  initReviewTagInfo() {
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('reviewTag'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((tagInfo: TagModel) => {
        this.reviewTagInfo = tagInfo
      })
  }

  initSearchHistoryCommunicator() {
    this.communicator.returnSearchTerm
      .pipe(
        filter((req) => !StringUtils.isNullOrEmpty(req?.searchQuery)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((searchQueryReq: SearchQueryRequest) => {
        this.searchQuery.next(searchQueryReq?.searchQuery)
        const searchInputParams: SearchInputParams = {
          searchExpression: !searchQueryReq.isSqlMode
            ? searchQueryReq?.searchQuery
            : '',
          isResetBaseGuid: true,
          includePC: searchQueryReq?.includePC ?? false,
          searchDuplicateOption: searchQueryReq.searchDuplicateOption,
          isSqlMode: searchQueryReq.isSqlMode,
          searchQuery: searchQueryReq.isSqlMode
            ? searchQueryReq?.searchQuery
            : '',
          searchHistoryId: searchQueryReq.searchId,
          navigationType: searchQueryReq.navigationType,
          folderList: searchQueryReq?.folderList,
          dynamicFolderId: searchQueryReq?.dynamicFolderId,
          isDynamicFolderGlobal: searchQueryReq?.isDynamicFolderGlobal
        }

        if (searchQueryReq?.mediaList?.length) {
          searchInputParams.medialist = searchQueryReq?.mediaList
          this.mediaList = searchQueryReq?.mediaList
        }

        this.store.dispatch(
          fromSharedSearchActions.search({
            payload: searchInputParams
          })
        )
      })

    this.communicator.returnLoadFileSearch
      .pipe(
        filter((value) => !StringUtils.isNullOrEmpty(value)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((loadFileHistory: SearchHistory) => {
        this.searchQuery.next(loadFileHistory.searchExpression)
        this.isSearched$.next(
          loadFileHistory?.searchExpression !== this.DEFAULT_SEARCH_EXPRESSION
            ? true
            : false
        )
        this.includeFamilySearch = loadFileHistory.includePC
        this.store.dispatch(
          fromSharedSearchActions.search({
            payload: {
              searchExpression: loadFileHistory.searchExpression,
              isSavedSearch: loadFileHistory.isSavedSearch,
              searchHistoryId: loadFileHistory.id,
              isLoadFile: loadFileHistory.isLoadFileSearch,
              isSearchFromHistory: true,
              isResetBaseGuid: true,
              includePC: loadFileHistory.includePC
            }
          })
        )
      })
  }

  initSearchQuery() {
    this.store
      .pipe(
        select(getSearchQuery),
        filter(
          (result) =>
            !!result && result.sourceModule !== SearchQueryModule.Review
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((searchQueryModel: SearchQueryModel) => {
        this.searchQueryFromOtherSource = true
        const { totalHitCount, pageSize } =
          searchQueryModel?.searchParameters?.searchResultIntialParameters
        const searchResponseData: SearchResponseModel = {
          error: null,
          searchResultIntialParameters: {
            totalHitCount: totalHitCount,
            pageSize: pageSize
          },
          tempTables: searchQueryModel.searchParameters?.tempTables
        }
        this.tempTables = searchQueryModel.searchParameters?.tempTables
        const breadCrumbs: BreadCrumb[] = searchQueryModel.searchInputs?.map(
          (input) => ({
            query: input.expression,
            filterText: input.displayText,
            itemIndex: input.currentIndex,
            isInitialSearch: input.isInitialSearch,
            docCount: input.documentCounts,
            isFilterSearch: input.isFilterSearch
          })
        )
        this.xsStore.dispatch(new SetReviewViewType(ReviewViewType.Search))
        this.filterStore.dispatch(
          updateBreadCrumbs({
            payload: { breadcrumbs: breadCrumbs }
          })
        )
        of(
          this.store.dispatch(
            fromSharedSearchActions.searchSuccess({
              payload: { searchResponse: searchResponseData }
            })
          )
        )
          .pipe(takeUntil(this.unsubscribed$))
          .subscribe(() => {
            this.store.dispatch(fromSharedSearchActions.postSearchReview())
            this.store.dispatch(fromSharedDocumentActions.unsetSearchQuery())
          })
      })
  }

  /**
   * When an user selects media from upload history section and sends to search,
   * we then receive that query and use to search here.
   */
  private searchWhenUploadSendsQuery() {
    this.xsStore
      .select(UploadStateSelector.SliceOf('initHistoryQuery'))
      .pipe(
        filter((value) => !!value && !Array.isArray(value?.query)),
        debounceTime(100),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((searchOption) => {
        this.searchQueryFromOtherSource = true
        this.searchQuery.next(searchOption.query as string)

        this.xsStore.dispatch(
          new SetIncludeFamilySearch(searchOption.includePc)
        )

        if (searchOption.searchDuplicateOption) {
          const searchDupOption = CONSTANT.SearchOption
          const dupOption = searchDupOption.find(
            (item) =>
              item.value == searchOption.searchDuplicateOption.toString()
          )
          this.xsStore.dispatch(new SetSearchDupOption(dupOption?.id))
        }

        this.store.dispatch(
          fromSharedSearchActions.search({
            payload: {
              searchExpression: searchOption.query as string,
              includePC: searchOption.includePc,
              dynamicFolderId: searchOption.dynamicFolderId,
              isDynamicFolderGlobal: searchOption.isDynamicFolderGlobal,
              isResetBaseGuid: true,
              searchDuplicateOption: searchOption.searchDuplicateOption
            }
          })
        )

        // so after we done, lets reset to its initial value
        this.xsStore.dispatch(new UploadStateResetAction('initHistoryQuery'))
      })
  }

  initSearchResponse() {
    this.sharedSearchstore
      .pipe(
        select(getSearchResponse),
        debounceTime(300),
        // filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((searchresponse: SearchResponseModel) => {
        if (!searchresponse?.searchResultIntialParameters) {
          this.searchResultParameter = null
          this.totalDocuments = 0
          this.pageSize = 0
          this.currentPage = 1
          return
        }
        this.tempTables = searchresponse.tempTables
        this.searchResultParameter = searchresponse.searchResultIntialParameters
        this.totalDocuments = this.searchResultParameter.totalHitCount
        this.pageSize = this.searchResultParameter.pageSize
        this.currentPage = this.searchResultParameter.currentPage
        this.maxPageSize =
          Math.ceil(this.totalDocuments / this.pageSize) > 0
            ? Math.ceil(this.totalDocuments / this.pageSize)
            : 1
        this.transcriptService.onDocumentPage = {
          pageSize: this.pageSize,
          currentPage: this.currentPage
        }
        // if (
        //   this.searchText?.nativeElement?.value?.trim().toLowerCase() ===
        //   this.DEFAULT_SEARCH_EXPRESSION.toLowerCase()
        // )
        //   this.searchQuery.next('')

        this.isSearched$.next(
          this.searchResultParameter?.searchExpression?.toLowerCase() !==
            this.DEFAULT_SEARCH_EXPRESSION.toLowerCase()
            ? true
            : false
        )
      })
  }

  initBatchSelectionFlag() {
    //for bulk imaging parameter
    this.store
      .pipe(select(getIsBatchSelection), takeUntil(this.unsubscribed$))
      .subscribe((documentTableIsBatchSelected: boolean) => {
        this.documentTableIsBatchSelected = documentTableIsBatchSelected
      })
  }

  initUnSelectedDocs() {
    //for bulk imaging parameter
    this.store
      .pipe(select(getUnselectedDocuments), takeUntil(this.unsubscribed$))
      .subscribe((documentTableUnSelectedDocs: number[]) => {
        this.documentTableUnSelectedDocs = documentTableUnSelectedDocs
      })
  }

  /**
   * Observes Saved SearchId and navigates to production
   * triggers savedSearchId from SendToProduction Method
   */
  initSavedSearch() {
    this.store
      .pipe(select(getSavedSearchId), takeUntil(this.unsubscribed$))
      .subscribe((savedSearchId: number) => {
        if (savedSearchId && this.navigateToProduction) {
          this.router.navigate(['/production'], {
            queryParams: { projectId: this.projectInfo.projectId }
          })
        }
      })
  }

  initBatchReviewCompletedFlag() {
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('isBatchReviewCompleted'))
      .pipe(
        //filter(res => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((result: boolean) => {
        this.areAllDocumentsInBatchReviewed = result
        if (result) {
          const chechkInRef = this.dialog.open(this.checkInBatchRef, {
            closeOnNavigation: true,
            autoFocus: false,
            width: '380px'
          })
          chechkInRef
            .beforeClosed()
            .pipe(
              filter((yes) => {
                return yes
              }),
              tap(() =>
                this.xsStore.dispatch(
                  new CheckInReviewBatchAction(this.projectInfo.projectId)
                )
              ),
              switchMap(() =>
                this.xsStore.select(
                  ReviewSetStateSelector.SliceOf('batchCheckInResponse')
                )
              ),
              filter((res: ResponseModel) => !!res?.message),
              debounceTime(400),
              take(1)
            )
            .subscribe((result: ResponseModel) => {
              this.handleBatchCheckIn(result)
            })
        }
      })
  }

  initTiffLimitSetting() {
    this.store
      .pipe(select(getTiffLimitSetting), takeUntil(this.unsubscribed$))
      .subscribe((printLimit: Limit) => {
        this.printLimit = printLimit
      })
  }

  initSelectedLayoutName() {
    this.xsStore
      .select(ReviewSetStateSelector.selectedLayoutName)
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((selectedLayoutName: string) => {
        this.selectedLayoutName = selectedLayoutName
      })
  }

  initReviewViewType() {
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('reviewViewType'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((viewType) => {
        this.selectedViewType = viewType
        if (this.selectedViewType === ReviewViewType.EmailThread) {
          this.keyId = '__Id'
          this.parentId = '__ParentId'
          this.switchViewText = 'Switch to Normal View'
        } else {
          this.keyId = '__FileID'
          this.parentId = '__ParentId'
          this.switchViewText = 'Switch to Email Thread View'
        }
      })
  }

  initEntityExtraction() {
    this.store
      .pipe(
        select(getControlSetting('ENABLE_ENTITY_EXTRACTION')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((isEnabled: boolean) => {
        this.enableEntiityExtraction = isEnabled
      })
  }

  initCurrentPage() {
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('currentDocNumber'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((currentDocNumber: number) => {
        this.currentDocNumber = currentDocNumber
      })
  }

  GetSearchDuplicateOpion() {
    this.xsStore
      .select(StartupStateSelector.SliceOf('searchParams'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((searchParams) => {
        this.searchDuplicateOptionFromPreviousSearch =
          searchParams?.searchDuplicateOption
      })
  }

  /**
   * Fetch user setting whether to display/popup custodian/media scope or not
   */
  fetchDisplayCustodianMedia(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.xsStore
        .dispatch(new fromCaseStateActions.FetchDisplayCustodianMedia())
        .pipe(
          switchMap(() => {
            return this.xsStore
              .select(CaseSelectors.displayCustodianMedias)
              .pipe(takeUntil(this.unsubscribed$))
          }),
          take(1)
        )
        .subscribe((displayCustodianMedias: boolean) => {
          resolve(displayCustodianMedias)
        })
    })
  }

  fetchReviewSetInfo() {
    if (this.reviewSetId > 0)
      this.xsStore
        .select(ReviewSetStateSelector.SliceOf('selectedReviewSetInfo'))
        .pipe(takeUntil(this.unsubscribed$))
        .subscribe(
          (reviewSetInfo) => (this.selectedReviewSetInfo = reviewSetInfo)
        )
  }

  /**
   * Fetch document restriction details for the user
   */
  private getDocumentRestrictionInfo(): Promise<DocumentRestrictionModel> {
    return new Promise((resolve, reject) => {
      this.xsStore
        .dispatch(
          new FetchUserDocumentRestrictionAction(this.projectInfo?.projectId)
        )
        .pipe(
          switchMap(() => {
            return this.xsStore
              .select(
                DocumentRestrictionStateSelector.SliceOf(
                  'documentRestrictionInfo'
                )
              )
              .pipe(debounceTime(200), takeUntil(this.unsubscribed$))
          })
        )
        .subscribe((res: DocumentRestrictionModel) => {
          resolve(res)
        })
    })
  }

  /**
   * Handles input click event to toggle advance filter UI
   */
  private readonly handleBuilderEvent = () => {
    this.forQueryBuilder
      .pipe(debounceTime(200), takeUntil(this.unsubscribed$))
      .subscribe({
        next: () => {
          // this.searchQuery.next('')
          this.showAdvanceFilterUi = true
        }
      })
  }

  /**
   * Custodian/Media OR Folder Scope selection
   */
  async openProjectScope(isPageInit: boolean) {
    if (this.userDocRestrictonMode === RestrictionMode.Folder) {
      const folderRef = this.dialog.open(this.folderSelectorTemplate, {
        closeOnNavigation: true,
        autoFocus: false,
        width: '60vw'
      })
      this.folderInjector = Injector.create({
        providers: [
          {
            provide: 'FolderData',
            useValue: {
              projectId: this.projectInfo.projectId,
              actionType: FolderActionType.Set_Folder_Scope,
              dialogRef: folderRef,
              folderIds:
                this.folderList.length > 0
                  ? this.folderList
                  : this.restrictedFolderIds
            } as FolderInjectorModel
          }
        ],
        parent: this.injector
      })

      await import(
        '@shared/components/folder/folder-selector/folder-selector.module'
      )

      this.folderSelectorComponent = import(
        '@shared/components/folder/folder-selector/folder-selector.component'
      ).then(({ FolderSelectorComponent }) => FolderSelectorComponent)

      folderRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((value) => {
          useNavigationState().clearNavigatedCache()
          if (
            this.isPageLoadShowProjectScope ||
            (value &&
              (difference(value, this.folderList).length > 0 ||
                difference(this.folderList, value).length > 0))
          ) {
            this.folderList = value
            this.isPageLoadShowProjectScope = false
            this.xsStore.dispatch(new SetSelectedFolderScope(this.folderList))
            // call search after folder scope changed
            this.searchQuery.next(this.DEFAULT_SEARCH_EXPRESSION)
            this.store.dispatch(
              fromSharedSearchActions.search({
                payload: {
                  searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
                  isResetBaseGuid: true,
                  folderList: this.folderList,
                  searchDuplicateOption: this.searchDuplicateOption
                }
              })
            )
          }
        })
    } else {
      await import('@shared/components/custodian-media/custodian-media.module')

      const projectScopeRef = this.dialog.open(this.tplCustodianMedia, {
        autoFocus: false,
        closeOnNavigation: true,
        width: '60vw'
      })
      /**
       * We use dynamic component loader on demand so we have our own injected DI
       */
      this.custodianMediaInjector = Injector.create({
        providers: [
          {
            provide: 'data',
            useValue: {
              projectId: this.projectInfo.projectId,
              userId: +localStorage.UserId,
              client: this.client,
              mediaList: this.mediaList,
              dialogRef: projectScopeRef
            }
          }
        ],
        parent: this.injector
      })

      // init the chunk
      this.custodianMediaComp = import(
        '@shared/components/custodian-media/custodian-media.component'
      ).then(({ CustodianMediaComponent }) => CustodianMediaComponent)

      projectScopeRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((value) => {
          useNavigationState().clearNavigatedCache()
          if (
            this.isPageLoadShowProjectScope ||
            (value &&
              (difference(value, this.mediaList).length > 0 ||
                difference(this.mediaList, value).length > 0))
          ) {
            if (value) this.mediaList = value
            this.isPageLoadShowProjectScope = false
            this.xsStore.dispatch(new SetSelectedMediaScope(this.mediaList))
            // call search after change media list or page and click close (not select)
            this.searchQuery.next(this.DEFAULT_SEARCH_EXPRESSION)
            this.store.dispatch(
              fromSharedSearchActions.search({
                payload: {
                  searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
                  isResetBaseGuid: true,
                  medialist: this.mediaList,
                  searchDuplicateOption: this.searchDuplicateOption
                }
              })
            )
          }
        })
    }

    useNavigationState().clearNavigatedCache()
  }

  reset(): void {
    this.xsStore.dispatch(new SetIncludeFamilySearch(false))
    this.getSearchDuplicateData()

    this.isSearched$.next(false)
    this.searchQuery.next(this.DEFAULT_SEARCH_EXPRESSION)
    this.store.dispatch(
      fromSharedSearchActions.search({
        payload: {
          searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
          isResetBaseGuid: true
        }
      })
    )
    this.updateSyncQuery()
    this.resetFlagsToDefault()
  }

  /**
   * Checks if the given search expression is Multiple Term Expression
   * @param expression search expression
   */
  private isMultipleTermExpression(expression: string) {
    return expression?.indexOf('\n') > 0
  }

  searchClicked(isPageInit: boolean): void {
    this.reviewService.setDocumentTableFieldSortingInStore$.next(() => {})
    this.#notifyAdvancedSearchUiReset()

    // when search was requested using button, hide the builder UI
    // if their value in the input and the UI state is still in show state,
    // it needs to be hide and do the searching.
    this.totalDocuments = 0
    this.showAdvanceFilterUi = false

    //change the current selected document to -1 to refresh the content like fulltext when search loads new data.
    this.multiWindowSelectionService.setCurrentDocumentId = -1
    this.store.dispatch(
      fromSharedSearchActions.search({
        payload: {
          // here, we have used default search if the syntax box is empty.
          searchExpression:
            this.searchText.nativeElement.value?.trim() ||
            this.DEFAULT_SEARCH_EXPRESSION,
          isResetBaseGuid: true,
          isLoadFile: this.isMultipleTermExpression(
            this.searchText.nativeElement.value?.trim()
          ),
          includePC: this.includeFamilySearch,
          searchDuplicateOption: this.searchDuplicateOption
        }
      })
    )
    this.updateSyncQuery()
    this.isSearched$.next(true)
  }

  onWarningClose(): void {
    this.showOptimizationWarning = false
  }

  /**
   * Make same filter expression between analyze * review page for multiple tab/window
   */
  private initFilterWhenSync(): void {
    fromEvent(window, 'storage')
      .pipe(
        // filter out with the specific key
        filter(
          (l: StorageEvent) =>
            l.key === CacheStorageKeysModel.FILTER_QUERY && !!l.newValue
        ),
        // parse the info
        map((l: StorageEvent) => JSON.parse(l.newValue ?? '') as PageSyncModel),
        // perform only if this event was raised from `ANALYZE`
        filter((p) => p?.commandFrom === CommandTypes.ANALYZE),
        debounceTime(500),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({
        next: (p) => [
          // reusing existing fn(s)
          (this.includeFamilySearch = p.includeFamily),
          (this.searchDuplicateOption = p.searchDuplicate),
          //this.onSearchQueryBuilt(p.query),
          this.store.dispatch(
            fromSharedSearchActions.search({
              payload: {
                searchExpression: p.query,
                includePC: p.includeFamily
              }
            })
          ),
          // once we have query value, clean up the storage  fro specific key
          localStorage.removeItem(CacheStorageKeysModel.FILTER_QUERY)
        ]
      })
  }

  /**
   * Updates the local storage with new values so can listen event from other
   * window to perform filter and sync in between
   */
  private updateSyncQuery(): void {
    // clean existing before new entry
    localStorage.removeItem(CacheStorageKeysModel.FILTER_QUERY)
    const obj: PageSyncModel = {
      commandFrom: CommandTypes.REVIEW,
      query: this.searchText.nativeElement.value?.trim()?.toLowerCase(),
      includeFamily: this.includeFamilySearch,
      searchDuplicate: this.searchDuplicateOption
    }
    localStorage.setItem(
      CacheStorageKeysModel.FILTER_QUERY,
      JSON.stringify(obj)
    )
  }

  toggleSearchTextArea() {
    this.searchTextAreaExpanded = !this.searchTextAreaExpanded
  }

  clearSearchText() {
    this.searchQuery.next('')
    this.queryCommunicator.setSearchText('')
    this.resetFlagsToDefault()
    this.#notifyAdvancedSearchUiReset()
  }

  openSearchGuide() {
    const modalRef = this.dialog.open(this.searchHelpTemplate)
    this.searchHelpInjector = Injector.create({
      providers: [
        {
          provide: 'searchHelpData',
          useValue: {
            modalRef: modalRef
          }
        }
      ],
      parent: this.injector
    })

    this.searchHelpComponent = import(
      '../search-help/search-help.component'
    ).then(({ SearchHelpComponent }) => SearchHelpComponent)
  }

  onActionClicked(action: ReviewSearchActions) {
    switch (action) {
      case ReviewSearchActions.SEND_TO_ANALYZE:
        this.sendToAnalyze()
        break
    }
  }

  handleBatchCheckIn(result: ResponseModel) {
    if (result.status === 'Success') {
      this.toast.success(result.message)
      if (result.data) {
        const reviewSetSummary: ReviewSetSummary =
          result.data as ReviewSetSummary
        if (reviewSetSummary.notStartedBatchCount > 0) {
          const checkOutRef = this.dialog.open(this.checkOutBatchRef, {
            closeOnNavigation: true,
            autoFocus: false,
            width: '380px'
          })
          checkOutRef
            .beforeClosed()
            .pipe(take(1))
            .subscribe((result: boolean) => {
              this.searchQuery.next(this.DEFAULT_SEARCH_EXPRESSION)
              if (result)
                this.store.dispatch(
                  fromSharedSearchActions.search({
                    payload: {
                      searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
                      isResetBaseGuid: true,
                      reviewSetId: this.reviewSetId
                    }
                  })
                )
              else this.xsStore.dispatch(new Navigate(['/launchpad']))
            })
        } else this.xsStore.dispatch(new Navigate(['/launchpad']))
      }
    }
  }

  initGetCurrentFilterParams() {
    this.filterStore
      .pipe(
        select(getCurrentFilterParameters),
        map((param) => param),
        filter((param) => !!param),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((filterParameters) => {
        this.performFilter(filterParameters)
      })
  }

  performFilter(filterParameters: FilterParams) {
    if (filterParameters) {
      const queryObj = this.constructSearchQuery(filterParameters)
      //this.applyInclusionOrExclusion(queryObj)
      const filterTxt = this.generateFilterText(
        filterParameters.filterType,
        filterParameters.fieldName,
        queryObj
      )

      this.store.dispatch(
        fromSharedSearchActions.search({
          payload: {
            searchExpression: queryObj.query,
            isResetBaseGuid: false,
            filterText: filterTxt,
            isForwardFilter: true,
            reviewSetId: this.reviewSetId,
            isSqlMode: JSON.parse(
              this.route.snapshot.queryParamMap.get('queryModeEnabled')
            ),
            isFilterSearch: true,
            searchDuplicateOption: this.searchDuplicateOptionFromPreviousSearch
          }
        })
      )
      this.store.dispatch(setFilterParameters({ payload: { params: null } }))
      //this.updateSyncQuery()
    }
  }

  constructSearchQuery(filterParameters: FilterParams) {
    let tempQuery = ''
    let filterText = ''

    const queryObj = this.performGenericFilter(filterParameters)
    tempQuery = queryObj.query
    filterText = queryObj.filterText

    tempQuery =
      '( ' + tempQuery.substring(0, tempQuery.length - 3).trim() + ' )'
    return { query: tempQuery, filterText: filterText }
  }

  performGenericFilter(filterParameters: FilterParams): any {
    let tempQuery = ''
    let filterText = ''
    filterParameters.fieldValues.forEach((value) => {
      if (value === '') {
        /** if value is empty on column CONTROL_NUMBER OR BATES NUMBER then empty string must be replaced
         * with IS NULL*/

        tempQuery += filterParameters.fieldName + ' IS NULL ' + ' OR '
      } else {
        if (filterParameters.fieldName === '__isReviewed') {
          if (value)
            tempQuery =
              tempQuery +
              ' ReviewSet=' +
              '"' +
              this.selectedReviewSetInfo.name +
              '"' +
              ' AND REVIEWSET_BATCH_ID = ' +
              this.searchResultParameter.batchId +
              ' AND TAGS(' +
              '"' +
              this.reviewTagInfo?.tagName +
              '"' +
              ')' +
              ' OR '
          else
            tempQuery =
              tempQuery +
              ' ReviewSet=' +
              '"' +
              this.selectedReviewSetInfo.name +
              '"' +
              ' AND REVIEWSET_BATCH_ID = ' +
              this.searchResultParameter.batchId +
              ' AND NOT TAGS(' +
              '"' +
              this.reviewTagInfo?.tagName +
              '"' +
              ')' +
              ' OR '
        } else if (filterParameters.fieldName === 'FAMILY_DATE_TIME') {
          /**
           * Here GroupDate is hard coded since the search id done from tbl_ex_FileInfo.[GroupDate] table
           * and the column there is GroupDate instead of FAMILY_DATE_TIME
           */
          tempQuery += 'GroupDate' + '=' + '"' + value + '"' + ' OR '
        } else
          tempQuery =
            tempQuery +
            filterParameters.fieldName +
            '=' +
            '"' +
            value.toString().split('"').join('\\"') +
            '"' +
            ' OR '
      }

      if (filterText.length > 0) {
        filterText = filterText + ', ' + value.toString()
      } else {
        filterText = value.toString()
      }
    })
    return { query: tempQuery, filterText: filterText }
  }

  generateFilterText(filterType: string, fieldName: string, queryObj: any) {
    let filterText = ''
    const displayFieldName =
      fieldName === '__isReviewed' ? 'IS_REVIEWED' : fieldName
    if (filterType === 'exclude') {
      filterText =
        '[' + displayFieldName + ' is not: ' + queryObj.filterText + ']'
    } else {
      filterText = '[' + displayFieldName + ' is : ' + queryObj.filterText + ']'
    }
    return filterText
  }

  // resets toolbar search actions to default state
  resetFlagsToDefault() {
    this.clearSearchText$.next(false)
    this.multiLineSearchTerm$.next(false)
  }

  //#region Send to analyze

  sendToAnalyze() {
    this.documentStore.dispatch(
      fromSharedDocumentActions.setSearchQuery({
        payload: {
          searchQueryModel: {
            searchQuery: this.searchText?.nativeElement.value,
            sourceModule: SearchQueryModule.Review,
            searchInputs: this.breadCrumbs
              // .filter((q) => q.filterText !== 'Home' || !(q.isInitialSearch == true && (q.query ?? 'fileid>0')?.toLocaleLowerCase() == 'fileid>0'))
              .filter((q) => q.filterText !== 'Home')
              .map(
                (q) =>
                  ({
                    expression: q.query,
                    displayText: q.filterText ?? q.query,
                    documentCounts: q.docCount,
                    isInitialSearch: q.isInitialSearch,
                    isFilterSearch: q.isFilterSearch
                  } as SearchInputs)
              ),
            searchParameters: {
              error: null,
              searchResultIntialParameters: null,
              tempTables: this.tempTables
            },
            searchDuplicateOption: this.searchDuplicateOption,
            mediaList: this.xsStore
              .selectSnapshot(
                StartupStateSelector.SliceOf('selectedMediaScope')
              )
              ?.map((mid) => mid.toString()),
            includeFamilySearch: this.includeFamilySearch
          }
        }
      })
    )
    this.router.navigate(['/analyze'], {
      queryParams: {
        projectId: this.projectInfo.projectId,
        docShareToken: this.route.snapshot.queryParamMap.get('docShareToken')
      }
    })
  }

  //#endregion

  onDeleteDocument = () => {
    this.deleteDocumentModalRef = this.modalService.show(
      DeleteDocumentComponent,
      Object.assign({
        initialState: {
          projectId: this.projectInfo.projectId,
          userId: +localStorage.UserId,
          selectedDocuments: this.selectedDocuments,
          unSelectedFileIds: this.documentTableUnSelectedDocs,
          isBatchSelected: this.documentTableIsBatchSelected,
          selectedDocumentsCount: this.selectedDocumentsCount,
          childFileToBeDeleted: 0,
          tempTables: this.tempTables
        },
        class: 'modal-lg',
        ignoreBackdropClick: true
      })
    )

    this.deleteDocumentModalRef.content.onClose.subscribe((result) => {
      if (result) {
        if (
          this.isEmptyInput ||
          (!this.isEmptyInput && this.showAdvanceFilterUi) ||
          (this.isEmptyInput && this.showAdvanceFilterUi)
        ) {
          this.store.dispatch(
            fromSharedSearchActions.search({
              payload: {
                searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
                isResetBaseGuid: true,
                medialist: this.mediaList
              }
            })
          )
        } else {
          this.searchClicked(false)
        }
      }
    })
  }

  //#region Share Document
  onShareClicked = () => {
    this.shareDocumentModalRef = this.modalService.show(
      DocumentsShareComponent,
      Object.assign(
        {},
        {
          initialState: {
            clientId: this.userDetails.clientId,
            projectId: this.projectInfo.projectId,
            userId: this.userDetails.userId,
            tempTableName: this.searchResultParameter.globalTempTableName,
            selectedDocuments: this.selectedDocuments
          },
          ignoreBackdropClick: true,
          class: 'modal-xl'
        }
      )
    )
  }
  //#endregion

  //#region Send to image

  /*BULK IMAGING STARTS*/
  onBulkImageClicked() {
    const sendToImagingDialogRef = this.dialog.open(
      this.sendToImageTemplateRef,
      {
        autoFocus: false,
        closeOnNavigation: true,
        minHeight: '40vh',
        width: 'calc(100% / 1.5)'
      }
    )
    this.sendToImageInjector = Injector.create({
      providers: [
        {
          provide: 'data',
          useValue: {
            projectId: this.projectInfo.projectId,
            tempTables: this.tempTables,
            userId: +localStorage.UserId,
            selectedFileIds: this.selectedDocuments,
            isBatchSelected: this.documentTableIsBatchSelected,
            unSelectedFileIds: this.documentTableUnSelectedDocs,
            dialogRef: sendToImagingDialogRef
          }
        }
      ],
      parent: this.injector
    })

    this.sendToImageComponent = import(
      '../document-bulk-imaging/document-bulk-imaging.component'
    ).then(({ DocumentBulkImagingComponent }) => DocumentBulkImagingComponent)
  }
  //#endregion

  // Move Document start
  onMoveDocumentClick() {
    const selectionData = {
      projectId: this.projectInfo.projectId,
      userId: +localStorage.UserId,
      selectedDocuments: cloneDeep(this.selectedDocuments) ?? [],
      unSelectedFileIds: cloneDeep(this.documentTableUnSelectedDocs) ?? [],
      isBatchSelected: this.documentTableIsBatchSelected,
      selectedDocumentsCount: this.selectedDocumentsCount,
      tempTables: this.tempTables
    }

    //Remove child documents from selection
    //if all the selected docs are child show error message
    //we can only move parent files. so, we need to filter non parent files
    this.documentService
      .getNonParentDocument$(this.projectInfo.projectId, {
        IsBatchSelected: selectionData.isBatchSelected,
        parentFileid: -1,
        SearchTempTable: this.tempTables.searchResultTempTable,
        SelectedFileIds: this.selectedDocuments,
        SessionId: '',
        UnSelectedFileIds: selectionData.unSelectedFileIds
      })
      .subscribe((res: ResponseModel) => {
        if (res) {
          if (res.data?.length > 0) {
            if (selectionData.isBatchSelected) {
              selectionData.unSelectedFileIds.push(...res.data)
            } else {
              selectionData.selectedDocuments =
                selectionData.selectedDocuments.filter(
                  (x) => res.data.indexOf(x) < 0
                )
            }
            selectionData.selectedDocumentsCount -= res.data?.length
          }

          this.dialog.open(MoveDocumentsComponent, {
            width: '600px',
            maxHeight: '80vh',
            autoFocus: false,
            disableClose: true,
            data: selectionData
          })
        }
      })
  }

  //#region Sent to production

  sendToProduction() {
    if (
      !this.configService.isVodEnabled &&
      this.projectInfo.isFilteringServiceCase
    ) {
      const searchExpressionList = []
      const searchExpressionCount = new SearchExpressionCount()
      searchExpressionCount.searchExpression =
        this.searchResultParameter.searchExpression
      searchExpressionCount.totalHitCount =
        this.searchResultParameter.totalHitCount
      searchExpressionList.push(searchExpressionCount)
      this.store.dispatch(
        fromSharedDocumentActions.setSearchExpressionListTemp({
          payload: { searchExpressionList }
        })
      )

      // This is the Ricoh case
      const productionTagModel = new SaveTagForProductionRequestModel()
      productionTagModel.moduleName = 'REVIEW'
      productionTagModel.productionTagName =
        'Production_' + moment().format('YYYYMMDD_HHmmss')
      productionTagModel.projectId = this.projectInfo.projectId
      productionTagModel.userId = this.userDetails.userId
      productionTagModel.searchTempTableName =
        this.tempTables.searchResultTempTable
      this.store.dispatch(
        fromSharedDocumentActions.saveTagsForProduction({
          payload: { saveTagRequestModel: productionTagModel }
        })
      )
    } else {
      this.navigateToProduction = true

      const saveSearchRequest: SaveSearchRequestModel = {
        searchGuid: this.tempTables ? this.tempTables.searchGuid : null,
        searchName: 'ProductionSave_' + moment().format('YYYYMMDD_HHmmss'),
        saveOnCustomField: true,
        isNewCustomField: true,
        customFieldId: null,
        customFieldName: null,
        applyAutoTagBasedOnSearchTerm: false,
        useExistingTag: false,
        tagGroupIdOfExistingSavedSearch: null
      }

      this.store.dispatch(
        fromSearchActions.saveSearch({
          payload: {
            projectId: this.projectInfo.projectId,
            saveSearchRequest,
            saveSearchForProduction: true
          }
        })
      )
    }
  }
  //#endregion

  //#region Mark as reviewed
  markAsReviewedBulk() {
    this.xsStore.dispatch(
      new MarkAsReviewedBulkAction(this.projectInfo.projectId)
    )
  }
  //#endregion

  //#region Check-In Batch
  doCheckInBatch() {
    const isBatchReviewCompleted: boolean = this.xsStore.selectSnapshot(
      ReviewSetStateSelector.SliceOf('isBatchReviewCompleted')
    )

    if (isBatchReviewCompleted) {
      const chechkInRef = this.dialog.open(this.checkInBatchRef, {
        closeOnNavigation: true,
        autoFocus: false,
        width: '380px'
      })

      chechkInRef
        .beforeClosed()
        .pipe(
          filter((yes) => {
            return yes
          }),
          tap(() =>
            this.xsStore.dispatch(
              new CheckInReviewBatchAction(this.projectInfo.projectId)
            )
          ),
          switchMap(() =>
            this.xsStore.select(
              ReviewSetStateSelector.SliceOf('batchCheckInResponse')
            )
          ),
          filter((res: ResponseModel) => !!res?.message),
          debounceTime(400),
          take(1)
        )
        .subscribe((result: ResponseModel) => {
          this.handleBatchCheckIn(result)
        })
    } else {
      const chechkInRef = this.dialog.open(this.checkInBatchWarningRef, {
        closeOnNavigation: true,
        autoFocus: false,
        width: '380px'
      })
    }
  }
  //#endregion

  //#region Print Document
  onPrintClicked = () => {
    //Check if the selected document count is less than the print document limit
    if (this.selectedDocumentsCount > this.printLimit.tiffPrintDocLimit) {
      if (this.printLimit.tiffPrintDocLimit.toString() === '-1') {
        this.openPrintPopUp()
      } else {
        this.store.dispatch(
          new GlobalErrorAction(
            new Error(
              this.selectedDocumentsCount +
                ' selected,' +
                this.printLimit.tiffPrintDocLimit +
                ' allowed to print at a time. Please select ' +
                this.printLimit.tiffPrintDocLimit +
                ' or less files or contact your administrator.'
            ),
            true,
            false,
            true
          )
        )
        return
      }
    } else {
      this.openPrintPopUp()
    }
  }

  async openPrintPopUp() {
    await import('../documents-print/documents-print.module')
    const documentPrintDialogRef = this.dialog.open(this.tpldocumentPrint, {
      autoFocus: false,
      closeOnNavigation: true,
      width: '80%',
      maxWidth: '900px',
      maxHeight: '630px'
    })

    this.documentPrintInjector = Injector.create({
      providers: [
        {
          provide: 'data',
          useValue: {
            projectId: this.projectInfo.projectId,
            tempTables: this.tempTables,
            userId: +localStorage.UserId,
            selectedFileIds: this.selectedDocuments,
            isBatchSelected: this.documentTableIsBatchSelected,
            unSelectedFileIds: this.documentTableUnSelectedDocs,
            selectedDocumentsCount: this.selectedDocumentsCount,
            dialogRef: documentPrintDialogRef
          }
        }
      ],
      parent: this.injector
    })

    this.documentPrintComponent = import(
      '../documents-print/documents-print.component'
    ).then(({ DocumentsPrintComponent }) => DocumentsPrintComponent)
  }
  //#endregion

  //#region  Print status
  onDownloadClicked = () => {
    this.downloadDocumentModalRef = this.modalService.show(
      DocumentDownloadComponent,
      Object.assign(
        {
          initialState: {
            projectId: this.projectInfo.projectId,
            userId: this.userDetails.userId
          }
        },
        { class: 'modal-lg', ignoreBackdropClick: true }
      )
    )
  }
  //#endregion

  //#responsive pst creator
  onResponsiveCreatorClicked(): void {
    // if (this.selectedDocumentsCount <= 0) {
    //   this.toast.error(
    //     'No files have been selected for creating Responsive PST'
    //   )
    //   return
    // }

    const responsivePSTRef = this.dialog.open(this.responsivePSTTemplateRef, {
      closeOnNavigation: true,
      autoFocus: false,
      // width: '600px',
      disableClose: true,
      minHeight: '40vh',
      width: 'calc(100% / 1.5)'
    })
    this.responsivePSTInjector = Injector.create({
      providers: [
        {
          provide: 'data',
          useValue: {
            projectId: this.projectInfo.projectId,
            tempTables: this.tempTables,
            userId: +localStorage.UserId,
            selectedFileIds: this.selectedDocuments,
            isBatchSelected: this.documentTableIsBatchSelected,
            unSelectedFileIds: this.documentTableUnSelectedDocs,
            selectedDocumentsCount: this.selectedDocumentsCount,
            dialogRef: responsivePSTRef
          }
        }
      ],
      parent: this.injector
    })

    this.reponsivePSTComponent = import(
      '../responsive-pst-creator/responsive-pst-creator.component'
    ).then(({ ResponsivePstCreatorComponent }) => ResponsivePstCreatorComponent)
  }
  //#end responsive pst creator

  onAudioTranscribeClicked(): void {
    const transcribeRef = this.dialog.open(this.audioTranscribeTemplateRef, {
      closeOnNavigation: true,
      autoFocus: false,
      disableClose: true,
      minHeight: '40vh',
      width: 'calc(100% / 1.5)'
    })
    this.audioTranscribeInjector = Injector.create({
      providers: [
        {
          provide: 'data',
          useValue: {
            projectId: this.projectInfo.projectId,
            tempTables: this.tempTables,
            userId: +localStorage.UserId,
            selectedFileIds: this.selectedDocuments,
            isBatchSelected: this.documentTableIsBatchSelected,
            unSelectedFileIds: this.documentTableUnSelectedDocs,
            selectedDocumentsCount: this.selectedDocumentsCount,
            dialogRef: transcribeRef
          }
        }
      ],
      parent: this.injector
    })

    this.audioTranscribeComponent = import(
      '../audio-transcribe/audio-transcribe.component'
    ).then(({ AudioTranscribeComponent }) => AudioTranscribeComponent)
  }

  onSlipSheetClicked(): void {
    const slipsheetDialogRef = this.dialog.open(this.tplSlipsheet, {
      autoFocus: false,
      closeOnNavigation: true,
      width: '80%',
      maxWidth: '900px',
      height: '90vh',
      maxHeight: '630px'
    })

    this.slipsheetInjector = Injector.create({
      providers: [
        {
          provide: 'data',
          useValue: {
            projectId: this.projectInfo.projectId,
            userId: +localStorage.UserId,
            selectedDocuments: this.selectedDocuments,
            selectedDocumentsCount: this.selectedDocumentsCount,
            unSelectedDocIds: this.documentTableUnSelectedDocs,
            isBatchSelected: this.documentTableIsBatchSelected,
            tempTables: this.tempTables,
            dialogRef: slipsheetDialogRef
          }
        }
      ],
      parent: this.injector
    })

    this.slipsheetComponent = import(
      '../documents-slipsheet/documents-slipsheet.component'
    ).then(({ DocumentsSlipsheetComponent }) => DocumentsSlipsheetComponent)

    slipsheetDialogRef
      .afterClosed()
      .pipe(take(1))
      .subscribe((isReplaceFulltext) => {
        if (isReplaceFulltext) {
          this.multiWindowSelectionService.onReloadFullText = true
        }
      })
  }
  //#endregion

  //#region Load Layout
  onSaveLayoutClicked = (mode: LayoutMode) => {
    this.reviewService.setDocumentTableFieldSortingInStore$.next(() => {})
    this.saveLayoutModalRef = this.modalService.show(
      LoadSaveLayoutComponent,
      Object.assign(
        {},
        {
          initialState: {
            client: this.client,
            userDetails: this.userDetails,
            mode: mode,
            projectId: this.projectInfo?.projectId
          },
          ignoreBackdropClick: true,
          class: 'modal-md'
        }
      )
    )
  }
  //#endregion

  //#region Save search
  onSaveSearchClicked = () => {
    if (this.totalDocuments === 0) {
      this.toast.warning('There are no search results to save.')
      return
    }
    this.saveSearchModalRef = this.modalService.show(
      SaveSearchComponent,
      Object.assign(
        {},
        {
          initialState: {
            client: this.client,
            dsid: this.projectInfo.dsid,
            projectId: this.projectInfo.projectId,
            userId: this.userDetails.userId,
            searchGuid: this.tempTables.searchGuid,
            saveOnCustomField: !this.isSqlMode
          },
          ignoreBackdropClick: true,
          class: 'modal-sm'
        }
      )
    )
  }
  //#endregion

  //#region save as dynamic folder
  /*BULK IMAGING ENDS*/
  async saveAsDynamicFolder() {
    const dynamicFolderRef = this.dialog.open(this.dynamicFolderCreate, {
      closeOnNavigation: true,
      autoFocus: false,
      width: '600px'
    })

    this.dynamicFolderInjector = Injector.create({
      providers: [
        {
          provide: 'dynamicFolderData',
          useValue: {
            projectId: this.projectInfo.projectId,
            isDynamicFolderPlaceHolder: false,
            searchExpression: StringUtils.isNullOrEmpty(
              this.searchText.nativeElement.value?.trim()
            )
              ? 'FileId>0'
              : this.searchText.nativeElement.value?.trim(),
            includeParentChild: this.includeFamilySearch,
            searchDuplicateOption: this.searchDuplicateOption,
            isLoadFileSearch: this.isMultipleTermExpression(
              this.searchText.nativeElement.value?.trim()
            ),
            selectedDynamicFolderId: 0,
            dialogRef: dynamicFolderRef
          } as DyanamicFolderCreateModel
        }
      ],
      parent: this.injector
    })

    if (!this.dynamicFolderCreateComponent) {
      await import(
        '@shared/components/dynamic-folder/dynamic-folder-create/dynamic-folder-create.module'
      )
      this.dynamicFolderCreateComponent = import(
        '@shared/components/dynamic-folder/dynamic-folder-create/dynamic-folder-create.component'
      ).then(({ DynamicFolderCreateComponent }) => DynamicFolderCreateComponent)
    }
  }
  //#endregion

  async openAutoFolderDialog(): Promise<void> {
    const autoFolderRef = this.dialog.open(this.autoFolderingTemplateRef, {
      width: '700px',
      autoFocus: false,
      closeOnNavigation: true,
      disableClose: true
    })

    this.autoFolderInjector = Injector.create({
      providers: [
        {
          provide: 'AutoFolderData',
          useValue: {
            projectId: this.projectInfo.projectId,
            parentPanel: FolderSourcePanel.ReviewMenuPanel,
            dialogRef: autoFolderRef,
            searchTempTable: this.tempTables.searchResultTempTable
          }
        }
      ],
      parent: this.injector
    })

    if (!this.autoFolderingComponent) {
      await import(
        '@shared/components/folder/auto-foldering/auto-foldering.module'
      )
      this.autoFolderingComponent = import(
        '@shared/components/folder/auto-foldering/auto-foldering.component'
      ).then(({ AutoFolderingComponent }) => AutoFolderingComponent)
    }
  }

  //#region Switch view
  switchView() {
    const nextViewType =
      this.selectedViewType == ReviewViewType.Search
        ? ReviewViewType.EmailThread
        : ReviewViewType.Search
    this.reviewService.setDocumentTableFieldSortingInStore$.next(() => {})

    if (nextViewType === ReviewViewType.EmailThread) {
      this.xsStore
        .dispatch([
          new GetEmailThreadViewStatus(this.projectInfo.projectId, {
            globalTempTable: this.tempTables?.searchResultTempTable
          })
          //new SetReviewViewType(nextViewType)
        ])
        .pipe(
          switchMap(() =>
            this.xsStore.select(
              ReviewSetStateSelector.SliceOf('emailThreadViewStatus')
            )
          ),
          filter((result) => !!result),
          take(1)
        )
        .subscribe((status: string) => {
          this.emailThreadViewStatus = status
          if (status !== 'MEDIA_HAS_EMAIL_THREAD')
            this.handleEmailThreadViewWarning(nextViewType)
          else {
            this.xsStore.dispatch(new SetReviewViewType(nextViewType))
            this.searchQuery.next(this.DEFAULT_SEARCH_EXPRESSION)
            this.store.dispatch(
              fromSharedSearchActions.search({
                payload: {
                  searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
                  isResetBaseGuid: true
                }
              })
            )
          }
        })
    } else
      this.xsStore
        .dispatch(new SetReviewViewType(nextViewType))
        .pipe(takeUntil(this.unsubscribed$))
        .subscribe(() => {
          this.searchQuery.next(this.DEFAULT_SEARCH_EXPRESSION)
          this.store.dispatch(
            fromSharedSearchActions.search({
              payload: {
                searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
                isResetBaseGuid: true
              }
            })
          )
        })
  }

  handleEmailThreadViewWarning(nextViewType: ReviewViewType) {
    const dialogRef = this.dialog.open(this.emailThreadViewWarningRef, {
      closeOnNavigation: true,
      autoFocus: false,
      width: '500px'
    })
    dialogRef
      .beforeClosed()
      .pipe(take(1))
      .subscribe((result: boolean) => {
        if (result) {
          this.searchQuery.next(this.DEFAULT_SEARCH_EXPRESSION)
          this.xsStore.dispatch(new SetReviewViewType(nextViewType))
          this.store.dispatch(
            fromSharedSearchActions.search({
              payload: {
                searchExpression: this.DEFAULT_SEARCH_EXPRESSION,
                isResetBaseGuid: true
              }
            })
          )
        }
      })
  }
  //#endregion

  //#region Entity Extraction
  confirmQueueEntitiesForExtraction() {
    const message =
      'Are you sure you want to queue the documents for entity extraction?'
    const headerText = 'Entity Extraction'
    const info = <IVenMatConfirmDialog>{
      headerText: headerText,
      confirmationText: message,
      isAlert: false
    }
    this.vcds.openConfirmDialog(info)
    this.vcds.confirmDialogAction
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((action) => {
        if (action == 'yes') {
          this.queueEntitiesForExtraction()
        } else if (action == 'no') {
          this.vcds.closeConfirmDialog()
        }
      })
  }

  queueEntitiesForExtraction() {
    this.xsStore
      .dispatch(
        new QueueEntityForExtraction(
          this.projectInfo.projectId,
          this.tempTables.computedSearchTempTable
        )
      )
      .pipe(
        switchMap(() =>
          this.xsStore.select(
            ReviewSetStateSelector.SliceOf('queueEntityExtractionResponse')
          )
        ),
        distinctUntilChanged(),
        debounceTime(800),
        takeUntil(this.unsubscribed$),
        take(1),
        catchError((res) => {
          this.toast.error(res?.error?.message)
          // close delete confirmation dialog
          this.vcds.closeConfirmDialog()
          return EMPTY
        })
      )
      .subscribe({
        next: (res) => {
          // close dialog component after successfully delete
          this.vcds.closeConfirmDialog()
        }
      })
  }
  //#endregion

  //#region send to folder

  async showFolderTreeDialog(folderingType: FolderActionType) {
    const sourceObs = combineLatest([
      this.rxStore.pipe(select(getSelectedDocuments)),
      this.rxStore.pipe(select(getUnselectedDocuments)),
      this.rxStore.pipe(select(getIsBatchSelection)),
      this.rxStore.pipe(select(getSearchTempTableName))
    ])

    const [
      selectedDocuments,
      unselectedDocuments,
      isBatchSelection,
      searchTempTableName
    ] = await sourceObs.pipe(take(1)).toPromise()

    // Import the module and component using a Promise
    const folderManagerModule = await import(
      '@shared/components/folder/folder-manager/folder-manager.module'
    )
    // const { FolderManagerComponent } = await import(
    //   '@shared/components/folder/folder-manager/folder-manager.component'
    // )

    const folderRef = this.dialog.open(this.folderManagerTemplate, {
      closeOnNavigation: true,
      autoFocus: false,
      width: '600px'
    })

    this.folderInjector = Injector.create({
      providers: [
        {
          provide: 'FolderData',
          useValue: {
            projectId: this.projectInfo.projectId,
            parentPanel: FolderSourcePanel.ReviewMenuPanel,
            actionType: folderingType,
            folderingModule: Module.Review,
            dialogRef: folderRef,
            mainTempTable: searchTempTableName,
            selectedFileIds: isBatchSelection
              ? unselectedDocuments
              : selectedDocuments,
            isBatchSelection: isBatchSelection
          } as FolderInjectorModel
        }
      ],
      parent: this.injector
    })

    this.folderManagerComponent = import(
      '@shared/components/folder/folder-manager/folder-manager.component'
    ).then(({ FolderManagerComponent }) => FolderManagerComponent)
  }

  //#endregion

  //#region convert to html
  /**
   * Opens up a doc conversion dialog container where
   * an user can update input params and submit to queue.
   * @param convertTo a type to convert selected doc.
   */
  readonly commandActon = (convertTo: OptionTypes): void => {
    const ref = this.dialog.open(ContainerComponent, {
      data: {
        optionType: convertTo,
        selectedDocIds: this.selectedDocuments,
        tempTables: this.tempTables,
        unSelectedDocIds: this.documentTableUnSelectedDocs,
        isBatchSelected: this.documentTableIsBatchSelected
      },
      autoFocus: false,
      closeOnNavigation: true,
      disableClose: true,
      minHeight: '40vh',
      width: 'calc(100% / 1.5)'
    })
  }
  //#endregion

  onPageChanged(currentDocNumber: number) {
    this.xsStore.dispatch(new SetcurrentDocNumber(null))
    this.store.dispatch(
      fromDocumentActions.navigateDocumentFromNumber({
        payload: { documentNumber: currentDocNumber }
      })
    )
  }

  navigateDocument(action: DocNavigation) {
    if (this.totalDocuments <= 0) return
    switch (action) {
      case DocNavigation.First:
        this.store.dispatch(
          fromDocumentActions.moveToFirstOrLastDocumentTablePage({
            payload: {
              action: 'first'
            }
          })
        )
        break
      case DocNavigation.Prev:
        this.store.dispatch(fromDocumentActions.moveBackDocument())
        break
      case DocNavigation.Next:
        this.store.dispatch(fromDocumentActions.moveToNextDocument())
        break
      case DocNavigation.Last:
        this.store.dispatch(
          fromDocumentActions.moveToFirstOrLastDocumentTablePage({
            payload: {
              action: 'last'
            }
          })
        )
        break
    }
  }

  public get isSendToAnalyzeDisable() {
    if (
      this.selectedDocumentsCount === 0 ||
      !this.projectInfo.enableNativeAutoPrefetch ||
      this.selectedViewType == ReviewViewType.EmailThread
    )
      return 'disabled'
    else return null
  }

  ngOnDestroy(): void {
    if (this.downloadDocumentModalRef) {
      this.downloadDocumentModalRef.hide()
    }

    if (this.saveSearchModalRef) {
      this.saveSearchModalRef.hide()
    }

    if (this.saveLayoutModalRef) {
      this.saveLayoutModalRef.hide()
    }

    if (this.shareDocumentModalRef) {
      this.shareDocumentModalRef.hide()
    }

    this.xsStore.dispatch([
      new ClearIndexedDb('HtmlParts'),
      new ClearIndexedDb('NativeRedaction')
    ])
    this.communicator.returnSearch({
      searchQuery: '',
      includePC: false,
      searchDuplicateOption: 0,
      isSqlMode: false,
      searchId: 0
    })

    this.communicator.returnLoadFile(null)

    if (this.deleteDocumentModalRef) {
      this.deleteDocumentModalRef.hide()
    }

    if (this.moveDocumentModalRef) {
      this.moveDocumentModalRef.hide()
    }
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  includeFamChange(event) {
    // updated state for search history purpose
    this.xsStore.dispatch(new SetIncludeFamilySearch(event?.target?.checked))
  }

  onOverlayCustomFieldClicked(): void {
    this.isOverlayCustomFieldContainerLoading = true
    import(
      '../../../shared/components/overlay-custom-fields/overlay-custom-fields-container/overlay-custom-fields-container.component'
    ).then(({ OverlayCustomFieldsContainerComponent }) => {
      this.overlayCustomFieldsFacade.storeSelectedFileIds({
        selectedFileIds: this.selectedDocuments,
        isAllSelected: this.documentTableIsBatchSelected,
        selectedDocumentsCount: this.selectedDocumentsCount,
        unselectedFileIds: this.documentTableUnSelectedDocs
      })
      this.isOverlayCustomFieldContainerLoading = false
      this.dialog.open(OverlayCustomFieldsContainerComponent, {
        closeOnNavigation: true,
        disableClose: true,
        minWidth: '95rem'
      })
    })
  }

  getSearchDuplicateData(): void {
    this.store.dispatch(
      new GetSearchDuplicateOption(this.projectInfo.projectId)
    )
    this.store
      .pipe(select(searchDuplicateOption), takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        if (res || res == 0) {
          const searchOption = CONSTANT.SearchOption
          const dupOption = searchOption.find((item) => item.id == res)
          this.xsStore.dispatch(new SetSearchDupOption(dupOption?.id))
        }
      })
  }

  #notifyAdvancedSearchUiReset(): void {
    if (!this.showAdvanceFilterUi) return

    this.xsStore.dispatch(new TriggerResetAdvancedSearchUiAction(true))
  }

  @DebounceTimer(100)
  onSearchInputKeydown(event: Event): void {
    const input = event.target as HTMLTextAreaElement
    const value = (input.value || '').trim()
    if (value) return

    this.#notifyAdvancedSearchUiReset()
  }

  toggleSyntaxPresenterUi(isVisible: boolean): void {
    this.isSyntaxPresenterUiVisible = isVisible
  }

  toggleSyntaxUiOnMouseLeave(): void {
    setTimeout(() => {
      if (!this.isSyntaxPopoverContentHovered) {
        this.toggleSyntaxPresenterUi(false)
      }
    }, 500)
  }

  onPopoverContentMouseEnter(): void {
    this.isSyntaxPopoverContentHovered = true
  }

  onPopoverContentMouseLeave(): void {
    this.isSyntaxPopoverContentHovered = false
    this.toggleSyntaxPresenterUi(false)
  }

  selectSearchSyntax(): void {
    this.searchQuery.pipe(takeUntil(this.unsubscribed$)).subscribe({
      next: (value: string) => {
        this.searchQueryValue = value
      }
    })
  }
}
