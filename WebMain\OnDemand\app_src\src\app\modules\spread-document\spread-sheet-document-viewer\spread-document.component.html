<ng-template #spreadSheetViewerTpl>
  <div
    style="height: calc(100% - 23px)"
    *appSpinner="showExcelSpinner$ | async; message: 'Loading Excel...'"
  >
    <div
      _ngcontent-vgk-c779=""
      style="height: 27px"
      class="align-items-center  bg-{{ config.themeClient }}-primary d-flex"
    >
      <input
        type="image"
        [disabled]="false"
        matTooltip="Redaction Mode"
        title="Redaction Mode"
        (click)="redactionModeMethod()"
        src="../../Images/show_redaction_mode.png"
        alt="Redaction Mode"
        style="width: 20px; margin: 0px 14px 0px 10px"
      />
      <input
        type="image"
        matTooltip="Save"
        [disabled]="!redactionMode"
        [ngStyle]="{ opacity: !redactionMode ? '0.6' : '1' }"
        (click)="save()"
        *ngIf="disableApplyNativeButton"
        src="../../Redaction/images/save.png"
      />
      <input
        type="image"
        [disabled]="redactionMode || !annotaitonObject"
        class="mx-3"
        [matTooltip]="
          !loadRedacted || !annotaitonObject
            ? 'Show Annotation'
            : 'Hide Annotation'
        "
        [ngStyle]="{
          opacity: !annotaitonObject || redactionMode ? '0.6' : '1'
        }"
        (click)="
          !loadRedacted || !annotaitonObject
            ? openRedacted(true)
            : openOriginalExcel()
        "
        [src]="
          !loadRedacted || !annotaitonObject
            ? '../../Redaction/images/show.svg'
            : '../../Redaction/images/hide.svg'
        "
      />
      <div class="d-flex w-100 justify-content-end pr-3">
        <input
          type="image"
          matTooltip="Reset"
          [disabled]="!redactionMode || !annotaitonObject"
          [ngStyle]="{
            opacity: !redactionMode || !annotaitonObject ? '0.6' : '1'
          }"
          (click)="reset()"
          src="../../Redaction/images/refresh_green.png"
          class="mx-3"
        />

        <div class="d-flex align-items-center flex-column px-2">
          <button
            class="btn-cancel btn-transparent border"
            *ngIf="showExcelSpinner$ | async"
            (click)="loadExcel('cancel')"
          >
            Cancel
          </button>
          <span class="spinner position-center" *ngIf="saveLoader"></span>
        </div>
      </div>
    </div>
    <div
      style="height: inherit !important; overflow: auto; display: flex"
      *ngIf="showExcelViewer; else noText"
    >
      <div
        class="d-flex align-items-center flex-column py-2 px-2"
        *ngIf="redactionMode"
      >
        <input
          type="image"
          class="cursor-pointer mt-3"
          matTooltip="Redact"
          (click)="contextMenuClick('Redact')"
          src="../../Redaction/images/Redaction.png"
        />
        <input
          type="image"
          class="cursor-pointer my-3"
          matTooltip="HighLight"
          (click)="highlightAction()"
          src="../../Redaction/images/highlighter.png"
          [ngStyle]="{ opacity: highlightButtonStatus ? '0.6' : '1' }"
          [disabled]="highlightButtonStatus"
        />
        <input
          type="image"
          class="cursor-pointer"
          matTooltip="Remove"
          (click)="contextMenuClick('UnRedact')"
          src="../../Redaction/images/delete.png"
        />
      </div>
      <ejs-spreadsheet
        #spreadsheet
        id="spreadsheet"
        [enableContextMenu]="false"
        [allowEditing]="true"
        [allowFiltering]="true"
        (select)="selectCell($event)"
        [showRibbon]="false"
      >
        <e-sheets>
          <e-sheet [isProtected]="true"></e-sheet>
        </e-sheets>
      </ejs-spreadsheet>
    </div>
  </div>

  <ng-template #noText>
    <div
      *ngIf="spreadsheetObj?.isOpen && !spreadsheetOriginal"
      class="d-flex justify-content-center align-items-center h-50"
    >
      <p class="p-2 bg-secondary text-white mb-0">Nothing to Show</p>
    </div>
  </ng-template>
</ng-template>

<app-spread-sheet-message
  [message]="excelErrorMessage"
  *ngIf="isFileSizeLimitExceeded || isLoadExcelError; else spreadSheetViewerTpl"
></app-spread-sheet-message>
