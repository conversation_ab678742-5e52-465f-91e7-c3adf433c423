import {
  fakeAsync,
  flushMicrotasks,
  TestBed,
  tick
} from '@angular/core/testing'
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot
} from '@angular/router'
import { RouterTestingModule } from '@angular/router/testing'
import { getControlSetting } from '@config/store/selectors'
import { Store } from '@ngrx/store'
import { of, throwError } from 'rxjs'
import { ReviewVersionGuard } from './review-version.guard'
class DummyComponent {}
describe('ReviewVersionGuard', () => {
  let guard: ReviewVersionGuard
  let router: Router
  let store: Store
  let routeSnapshot: ActivatedRouteSnapshot
  let stateSnapshot: RouterStateSnapshot

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule.withRoutes([
          { path: 'review', component: DummyComponent },
          { path: 'review2', component: DummyComponent },
          { path: 'review-next', component: DummyComponent }
          // ... other necessary routes
        ])
      ],
      providers: [
        ReviewVersionGuard,
        { provide: Store, useValue: { select: jest.fn() } }
      ]
    })

    guard = TestBed.inject(ReviewVersionGuard)
    router = TestBed.inject(Router)
    store = TestBed.inject(Store)
    stateSnapshot = { url: '/some-path' } as RouterStateSnapshot
    routeSnapshot = { queryParams: {} } as ActivatedRouteSnapshot

    jest
      .spyOn(router, 'createUrlTree')
      .mockImplementation((commands: any[]) => {
        return router.parseUrl(commands.join('/'))
      })
  })

  it('should do nothing for non-hash-based URLs', fakeAsync(() => {
    // GIVEN a non-hash-based URL
    jest.spyOn(store, 'select').mockReturnValue(of('1'))
    stateSnapshot.url = '/some-path-without-hash'
    const navigateSpy = jest.spyOn(router, 'navigate')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN no redirection should occur
    expect(navigateSpy).not.toHaveBeenCalled()
    flushMicrotasks()
  }))
  it('should handle URLs with unusual hash formats correctly', fakeAsync(() => {
    // GIVEN a URL with an unusual hash format
    jest.spyOn(store, 'select').mockReturnValue(of('1'))
    stateSnapshot.url = '/unexpected/format?query=param'
    const navigateSpy = jest.spyOn(router, 'navigate')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN the guard should process the URL correctly
    expect(navigateSpy).not.toHaveBeenCalled()
    flushMicrotasks()
  }))
  it('should handle simultaneous changes in VOD_VERSION and ENABLE_REVIEW_2', fakeAsync(() => {
    // GIVEN simultaneous changes in VOD_VERSION and ENABLE_REVIEW_2
    jest.spyOn(store, 'select').mockImplementation((selector) => {
      if (selector === getControlSetting('VOD_VERSION')) {
        return of('2') // Changes to '2'
      }
      if (selector === getControlSetting('ENABLE_REVIEW_2')) {
        return of('1') // Changes to '1'
      }
      return of(null)
    })
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review2'])

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review2
    expect(navigateSpy).toHaveBeenCalledWith(['/review2'])
    flushMicrotasks()
  }))
  it('should redirect to /review when VOD_VERSION is 1', fakeAsync(() => {
    // GIVEN VOD_VERSION is set to '1'
    jest.spyOn(store, 'select').mockReturnValue(of('1'))
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review'])

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review
    expect(navigateSpy).toHaveBeenCalledWith(['/review'])
    flushMicrotasks()
  }))
  it('should redirect to /review2 when VOD_VERSION is 2 and ENABLE_REVIEW_2 is enabled', fakeAsync(() => {
    // GIVEN VOD_VERSION is set to '2' and ENABLE_REVIEW_2 is '1'
    jest.spyOn(store, 'select').mockImplementation((selector) => {
      if (selector === getControlSetting('VOD_VERSION')) {
        return of('2')
      }
      if (selector === getControlSetting('ENABLE_REVIEW_2')) {
        return of('1')
      }
      return of(null)
    })
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review2'])

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review2
    expect(navigateSpy).toHaveBeenCalledWith(['/review2'])
  }))
  it('should redirect to /review when VOD_VERSION is 2 and ENABLE_REVIEW_2 is not enabled', fakeAsync(() => {
    // GIVEN VOD_VERSION is set to '2' and ENABLE_REVIEW_2 is not '1'
    jest.spyOn(store, 'select').mockImplementation((selector) => {
      if (selector === getControlSetting('VOD_VERSION')) {
        return of('2')
      }
      if (selector === getControlSetting('ENABLE_REVIEW_2')) {
        return of('0')
      }
      return of(null)
    })
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review'])

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review
    expect(navigateSpy).toHaveBeenCalledWith(['/review'])
    flushMicrotasks()
  }))
  it('should redirect to /review-next when VOD_VERSION is 3', fakeAsync(() => {
    // GIVEN VOD_VERSION is set to '3'
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review-next'])

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review-next
    expect(navigateSpy).toHaveBeenCalledWith(['/review-next'])
    flushMicrotasks()
  }))
  it('should redirect to /review-next when VOD_VERSION is 3 without reviewSetId', fakeAsync(() => {
    // GIVEN VOD_VERSION is set to '3' without reviewSetId
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    routeSnapshot.queryParams = {}
    const navigateSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review-next
    expect(navigateSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: {},
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should redirect to /review-next when VOD_VERSION is 3 with reviewSetId', fakeAsync(() => {
    // GIVEN VOD_VERSION is set to '3' and reviewSetId is present
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    routeSnapshot.queryParams = { reviewSetId: '123' }
    const navigateSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review
    expect(navigateSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: { reviewSetId: '123' },
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should redirect to /review for an unknown VOD_VERSION', fakeAsync(() => {
    // GIVEN VOD_VERSION is unknown
    jest.spyOn(store, 'select').mockReturnValue(of('unknown'))
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review'])

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review by default
    expect(navigateSpy).toHaveBeenCalledWith(['/review'])
    flushMicrotasks()
  }))
  it('should not redirect when the current path matches the target path', fakeAsync(() => {
    // GIVEN VOD_VERSION is set and current path matches the target path
    jest.spyOn(store, 'select').mockReturnValue(of('1'))
    stateSnapshot.url = '/review'
    const navigateSpy = jest.spyOn(router, 'navigate')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN no redirection should occur
    expect(navigateSpy).not.toHaveBeenCalled()
    flushMicrotasks()
  }))
  it('should redirect to /review-next with query parameters preserved', fakeAsync(() => {
    // GIVEN VOD_VERSION is set to '3' with query parameters
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    routeSnapshot.queryParams = { param: 'test' }
    stateSnapshot.url = '/review-next?param=test'
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review-next'], { queryParams: { param: 'test' } })

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review-next and preserve query parameters
    expect(navigateSpy).toHaveBeenCalledTimes(1)
    expect(navigateSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: { param: 'test' }
    })
    flushMicrotasks()
  }))
  it('should redirect to /review when the store select returns null', fakeAsync(() => {
    // GIVEN store selection fails or returns undefined
    jest.spyOn(store, 'select').mockReturnValue(of(null))
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review'])

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review by default
    expect(navigateSpy).toHaveBeenCalledWith(['/review'])
    flushMicrotasks()
  }))
  it('should handle URLs with hash and query parameters for VOD_VERSION 3', fakeAsync(() => {
    // GIVEN VOD_VERSION is set to '3' and URL has hash and query parameters
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    stateSnapshot.url = '/review-next?query=param' // Mocking URL with hash and query
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review-next'], { queryParams: { query: 'param' } })

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review-next
    expect(navigateSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: { query: 'param' }
    })
    flushMicrotasks()
  }))
  it('should redirect to /review by default when store selection undefined', fakeAsync(() => {
    // GIVEN store selection fails or returns undefined
    jest.spyOn(store, 'select').mockReturnValue(of(undefined))
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review'])

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review by default
    expect(navigateSpy).toHaveBeenCalledWith(['/review'])
    flushMicrotasks()
  }))
  it('should not redirect to /review2 if ENABLE_REVIEW_2 is enabled but VOD_VERSION is not 2', fakeAsync(() => {
    // GIVEN ENABLE_REVIEW_2 is '1' but VOD_VERSION is not '2'
    jest.spyOn(store, 'select').mockImplementation((selector) => {
      if (selector === getControlSetting('VOD_VERSION')) {
        return of('1') // Not '2'
      }
      if (selector === getControlSetting('ENABLE_REVIEW_2')) {
        return of('1')
      }
      return of(null)
    })
    const navigateSpy = jest.spyOn(router, 'navigate')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should not redirect to /review2
    expect(navigateSpy).not.toHaveBeenCalledWith(['/review2'])
    flushMicrotasks()
  }))
  it('should redirect to /review when VOD_VERSION is 2 but ENABLE_REVIEW_2 is not set', fakeAsync(() => {
    // GIVEN VOD_VERSION is '2' but ENABLE_REVIEW_2 is not set
    jest.spyOn(store, 'select').mockImplementation((selector) => {
      if (selector === getControlSetting('VOD_VERSION')) {
        return of('2')
      }
      if (selector === getControlSetting('ENABLE_REVIEW_2')) {
        return of(null) // Not set
      }
      return of('2')
    })
    const navigateSpy = jest.spyOn(router, 'createUrlTree')
    // router.navigate(['/review'])

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review
    expect(navigateSpy).toHaveBeenCalledWith(['/review'], {
      queryParams: {},
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should react to dynamic changes in route parameters', fakeAsync(() => {
    // GIVEN dynamic changes in route parameters
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    routeSnapshot.queryParams = { updated: 'yes' }
    stateSnapshot.url = '/review-next?updated=yes'
    const expectedNavigationExtras = {
      queryParams: { updated: 'no' },
      replaceUrl: true
    }
    const navigateSpy = jest.spyOn(router, 'navigate')
    router.navigate(['/review-next'], expectedNavigationExtras)

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect and preserve updated query parameters
    expect(navigateSpy).toHaveBeenCalledWith(
      ['/review-next'],
      expectedNavigationExtras
    )
    flushMicrotasks()
  }))
  it('should handle errors during store selection', fakeAsync(() => {
    // GIVEN an error occurs during store selection
    stateSnapshot.url = '/nothing'
    jest.spyOn(store, 'select').mockImplementation(() => throwError('Error'))
    const consoleSpy = jest.spyOn(console, 'warn')
    const navigateSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).subscribe()

    tick(100)

    // THEN it should log an error and not cause navigation
    expect(consoleSpy).toHaveBeenCalledWith(
      'Error in ReviewVersionGuard, falling back to default review: ',
      'Error'
    )
    expect(navigateSpy).toHaveBeenCalledWith(['/review'], {
      queryParams: {},
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should handle errors by logging and redirecting to the default version', fakeAsync(() => {
    // Temporarily mock console.error to prevent output in the test terminal
    const consoleErrorMock = jest
      .spyOn(console, 'warn')
      .mockImplementation(() => {})

    // GIVEN an error occurs during store selection
    stateSnapshot.url = '/non-review'
    jest
      .spyOn(store, 'select')
      .mockImplementation(() => throwError(new Error('Error')))
    const consoleSpy = jest.spyOn(console, 'warn')
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).subscribe()

    tick(100)

    // THEN it should log an error
    expect(consoleSpy).toHaveBeenCalledWith(
      'Error in ReviewVersionGuard, falling back to default review: ',
      'Error'
    )

    // AND it should create a UrlTree for the default redirection
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review'], {
      queryParams: {},
      replaceUrl: true
    })

    flushMicrotasks()

    // Restore console.error to its original state
    consoleErrorMock.mockRestore()
  }))
  it('should redirect to the default version on error for different URL patterns', fakeAsync(() => {
    // GIVEN errors with different URL patterns
    stateSnapshot.url = '/review'
    jest
      .spyOn(store, 'select')
      .mockImplementation(() => throwError(new Error('Error')))
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')
    const testUrls = [
      '/error-path#review',
      '/error-path#review2',
      '/error-path#review-next'
    ]

    testUrls.forEach((url) => {
      stateSnapshot.url = url
      // WHEN canActivate is called
      guard.canActivate(routeSnapshot, stateSnapshot).subscribe()

      tick(100)
    })

    // THEN it should always redirect to the default version
    testUrls.forEach(() => {
      expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review'], {
        queryParams: {},
        replaceUrl: true
      })
    })
    flushMicrotasks()
  }))
  it('should not redirect if the current path matches the target path', fakeAsync(() => {
    // Setup for the scenario where currentPath equals targetPath
    const targetPath = '/review' // Example target path
    stateSnapshot.url = `/${targetPath}` // Mock URL to match targetPath
    jest.spyOn(store, 'select').mockImplementation(() => of('1')) // Mock store selection

    const navigateSpy = jest.spyOn(router, 'navigate')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).subscribe()

    tick(100)

    // THEN it should not perform any redirection
    expect(navigateSpy).not.toHaveBeenCalled()

    flushMicrotasks()
  }))
  it('should handle reviewSetId in case-insensitive manner for VOD_VERSION 3', fakeAsync(() => {
    // GIVEN VOD_VERSION is '3' and reviewSetId is present in different cases
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    routeSnapshot.queryParams = { ReviewSetID: '123' } // Different casing
    const navigateSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review
    expect(navigateSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: { ReviewSetID: '123' },
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should redirect to /review-next for VOD_VERSION 3 with unrelated query parameters', fakeAsync(() => {
    // GIVEN VOD_VERSION is '3' and an unrelated query parameter is present
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    routeSnapshot.queryParams = { otherParam: 'value' }
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review-next
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: { otherParam: 'value' },
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should handle multiple query parameters correctly for VOD_VERSION 3 with reviewSetId', fakeAsync(() => {
    // GIVEN VOD_VERSION is '3' with reviewSetId and other query parameters
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    routeSnapshot.queryParams = { reviewSetId: '123', anotherParam: 'test' }
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: { reviewSetId: '123', anotherParam: 'test' },
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should redirect to /review-next for VOD_VERSION 3 with empty reviewSetId', fakeAsync(() => {
    // GIVEN VOD_VERSION is '3' and reviewSetId is empty
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    routeSnapshot.queryParams = { reviewSetId: '' }
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: { reviewSetId: '' },
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should prioritize VOD_VERSION over ENABLE_REVIEW_2 when VOD_VERSION is 3', fakeAsync(() => {
    // GIVEN VOD_VERSION is '3' and ENABLE_REVIEW_2 is enabled
    jest.spyOn(store, 'select').mockImplementation((selector) => {
      if (selector === getControlSetting('VOD_VERSION')) {
        return of('3')
      }
      if (selector === getControlSetting('ENABLE_REVIEW_2')) {
        return of('1')
      }
      return of('3')
    })
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review-next, prioritizing VOD_VERSION
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: {},
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should redirect to /review for a numeric VOD_VERSION outside expected range', fakeAsync(() => {
    // GIVEN VOD_VERSION is set to a number outside the expected range (e.g., 4)
    jest.spyOn(store, 'select').mockReturnValue(of('4'))
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review by default
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review'], {
      queryParams: {},
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should handle empty VOD_VERSION', fakeAsync(() => {
    // GIVEN VOD_VERSION is an empty string
    jest.spyOn(store, 'select').mockReturnValue(of(''))
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review by default
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review'], {
      queryParams: {},
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should redirect to /review for non-numeric VOD_VERSION string', fakeAsync(() => {
    // GIVEN VOD_VERSION is a non-numeric string (e.g., 'abc')
    jest.spyOn(store, 'select').mockReturnValue(of('abc'))
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review by default
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review'], {
      queryParams: {},
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should redirect to /review-next for negative or zero VOD_VERSION', fakeAsync(() => {
    // GIVEN VOD_VERSION is a negative number or zero
    ;['0', '-1', '-2'].forEach((version) => {
      jest.spyOn(store, 'select').mockReturnValue(of(version))
      const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

      // WHEN canActivate is called
      guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

      tick(100)

      // THEN it should redirect to /review by default
      expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review'], {
        queryParams: {},
        replaceUrl: true
      })
    })
    flushMicrotasks()
  }))
  it('should handle VOD_VERSION as a float value', fakeAsync(() => {
    // GIVEN VOD_VERSION is a float value
    jest.spyOn(store, 'select').mockReturnValue(of('2.5'))
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review by default
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review'], {
      queryParams: {},
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should handle complex interaction of multiple query parameters', fakeAsync(() => {
    // GIVEN VOD_VERSION is set with complex query parameters
    jest.spyOn(store, 'select').mockReturnValue(of('3'))
    routeSnapshot.queryParams = { reviewSetId: '123', extraParam: 'value' }
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should correctly handle the complex query parameters
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review-next'], {
      queryParams: { reviewSetId: '123', extraParam: 'value' },
      replaceUrl: true
    })
    flushMicrotasks()
  }))
  it('should redirect to default route when VOD_VERSION route is 9812378126387123', fakeAsync(() => {
    // GIVEN VOD_VERSION is set but corresponding route is absent
    jest.spyOn(store, 'select').mockReturnValue(of('9812378126387123')) // Assuming 'review4' route doesn't exist
    const createUrlTreeSpy = jest.spyOn(router, 'createUrlTree')

    // WHEN canActivate is called
    guard.canActivate(routeSnapshot, stateSnapshot).toPromise()

    tick(100)

    // THEN it should redirect to /review by default
    expect(createUrlTreeSpy).toHaveBeenCalledWith(['/review'], {
      queryParams: {},
      replaceUrl: true
    })
    flushMicrotasks()
  }))
})
