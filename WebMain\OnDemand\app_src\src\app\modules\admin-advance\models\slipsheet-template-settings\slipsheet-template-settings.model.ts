export enum StampLocation {
  TopLeft = 0,
  TopCenter,
  TopRight,
  BottomLeft,
  BottomCenter,
  BottomRight,
  Center
}

export enum SlipSheetType {
  usePlaceHolderText = 0,
  usePlaceHolderFile
}

//Specifies style information applied to text.
export enum FontStyle {
  // Summary:
  //     Normal text.
  Regular = 0,
  // Summary:
  //     Bold text.
  Bold = 1,
  // Summary:
  //     Italic text.
  Italic = 2,
  // Summary:
  //     Underlined text.
  Underline = 4,
  // Summary:
  //     Text with a line through the middle.
  Strikeout = 8
}

export interface SlipSheetQueryParams {
  slipsheetTemplateId: number
}

export class SlipsheetTemplateModel {
  slipsheetTemplateId?: number

  slipsheetTemplateName?: string

  slipSheetType?: SlipSheetType

  placeHolderText?: string

  placeHolderPosition?: string

  placeHolderFile?: string

  placeHolderTextFont?: string

  createdOn?: string

  updatedOn?: string

  editable?: boolean
}

export class SlipSheetTemplateField {
  FieldName: string
}

export class FontName {
  Name: string
}
