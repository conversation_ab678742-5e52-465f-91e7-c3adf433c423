export enum ActionType {
  CREATE_CASE = 'CREATE_CASE',
  ANALYZE = 'ANALYZE',
  REVIEW = 'REVIEW',
  PRODUCE_NEW = 'PRODUCE_NEW',
  PRODUCE_STATUS = 'PRODUCE_STATUS',
  UPLOAD_NEW = 'UPLOAD_NEW',
  UPLOAD_INVITE = 'UPLOAD_INVITE',
  UPLOAD_REPROCESS = 'UPLOAD_REPROCESS',
  DIRECT_EXPORT = 'DIRECT_EXPORT',
  REPRODUCE = 'REPRODUCE',
  ENTER_REVIEW_SET_REVIEW = 'ENTER_REVIEW_SET_REVIEW',
  REVIEW_SET_CREATE = 'REVIEW_SET_CREATE',
  REVIEW_SET_EDIT = 'REVIEW_SET_EDIT',
  REVIEW_SET_DELETE = 'REVIEW_SET_DELETE',
  REVIEW_SET_CLONE = 'REVIEW_SET_CLONE'
}

export interface LaunchpadNextEvent {
  actionType: ActionType
  content: any
}
