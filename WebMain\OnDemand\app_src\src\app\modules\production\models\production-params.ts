import { CommaSeparatedNumberConverter } from '@shared/utils/json-converters'
import { SelectiveEndorsementSetting } from '@stores/models'
import {
  Any,
  <PERSON>sonConverter,
  JsonCustomConvert,
  JsonObject,
  JsonProperty
} from 'json2typescript'

/* Utilities */

// TODO This is suppose to be in a separate file.
// But moving to a different file creates circular dependencies issue.
// Need to figure something out.

@JsonConverter
export class ColonSeparatedStringToUserModelConverter
  implements JsonCustomConvert<UploadInvitationUserModel[]>
{
  serialize(userModels: UploadInvitationUserModel[]): any {
    if (!userModels) {
      return null
    }
    const models: string[] = []
    for (const userModel of userModels) {
      const model =
        userModel.userId +
        ':' +
        userModel.email +
        ':' +
        userModel.userName +
        ':' +
        userModel.groupName
      models.push(model)
    }
    return models
  }

  deserialize(models: string[]): UploadInvitationUserModel[] {
    if (!models) {
      return null
    }
    const userModels: UploadInvitationUserModel[] = []
    for (const model of models) {
      const userModel = new UploadInvitationUserModel()
      const values = model.split(':')
      userModel.userId = Number(values[0])
      userModel.email = values[1]
      userModel.userName = values[2]
      userModel.groupName = values[3]
      userModels.push(userModel)
    }
    return userModels
  }
}

/**
 * Font Style : Specifies style information applied to text
 */
export enum FontStyle {
  Regular = 0,
  Bold = 1,
  Italic = 2,
  Underline = 4,
  Strikeout = 8
}

export interface FontSetting {
  name: string
  size: number
  style: FontStyle
}

/* Model Classes */
/**
 * #23817 Sanh Huynh add batesType, controlNumberFieldId, isSystemBates
 */

@JsonObject('ProductionUIOptionsProduction')
export class ProductionUIOptions {
  @JsonProperty('BateStamping', Boolean, true) bateStamping: boolean = null

  @JsonProperty('CrossReferenceNative', Boolean, true)
  crossReferenceNative: boolean = null

  @JsonProperty('BateStampingSelectedValue', String, true)
  bateStampingSelectedValue: string = null

  @JsonProperty('BatesType', Number, true) batesType: number = null

  @JsonProperty('BatesControlNumberSetting_PrefixString', String, true)
  batesNumberPrefix: string = null

  @JsonProperty('BatesControlNumberSetting_ControlNumber', Number, true)
  batesNumberStartNumber: number = null

  @JsonProperty('BatesControlNumberSetting_PaddingLength', Number, true)
  batesNumberPadding: number = null

  @JsonProperty('ControlNumberFieldId', Number, true)
  controlNumberFieldId: number = null

  @JsonProperty('IsSystemBates', Boolean, true) isSystemBates: boolean = null

  @JsonProperty('BurnRedactionOnImages', Boolean, true)
  burnRedactionOnImages: boolean = null

  @JsonProperty('BurnAllRedactionSets', Boolean, true)
  burnAllRedactionSets: boolean = null

  @JsonProperty('BurnRedactionSelection', [Number], true)
  burnRedactionSelection: number[] = null

  @JsonProperty('CustomStamping', Boolean, true) customStamping: boolean = null

  @JsonProperty('CustomStampingSelectedValue', String, true)
  customStampingSelectedValue: string = null

  @JsonProperty('CustomStampingText', String, true)
  customStampingText: string = null

  @JsonProperty('FontName', String, true)
  fontName: string = null

  @JsonProperty('FontSize', Number, true)
  fontSize: number = null

  @JsonProperty('FontStyle', Number, true)
  fontStyle: FontStyle = null

  @JsonProperty('ExportFile', String, true) productionName: string = null

  @JsonProperty('isReExport', Boolean, true) isReExport: boolean = null

  @JsonProperty('originalExportId', Number, true)
  originalExportId: number = null

  @JsonProperty('ExportFormatList', [String], true)
  exportFormatList: string[] = null

  @JsonProperty('ExportFullTextFiles', Boolean, true)
  exportFullTextFiles: boolean = null

  @JsonProperty('ExportNativeFiles', Boolean, true)
  exportNativeFiles: boolean = null

  @JsonProperty('ExportParentChildAsSinglePDF', Boolean, true)
  exportParentChildAsSinglePDF: boolean = null

  @JsonProperty('ExportSinglePageColorImageInJpg', Boolean, true)
  exportSinglePageColorImageInJpg: boolean = null

  @JsonProperty('ExportTiff', Boolean, true) exportTiff: boolean = null

  @JsonProperty('FilterByTags', CommaSeparatedNumberConverter, true)
  filterByTags: number[] = null

  @JsonProperty('FilterProducedDocs', Boolean, true)
  filterProducedDocs: boolean = null

  @JsonProperty('FolderIDs', CommaSeparatedNumberConverter, true)
  folderIDs: number[] = null

  @JsonProperty('FolderOptionOperator', String, true)
  folderOptionOperator: string = null

  @JsonProperty('FulltextPlaceholderText', String, true)
  fulltextPlaceholderText: string = null

  @JsonProperty('ImageExportOption', String, true)
  imageExportOption: string = null

  @JsonProperty('ImagePlaceholderText', String, true)
  imagePlaceholderText: string = null

  @JsonProperty('NativePlaceholderText', String, true)
  nativePlaceholderText: string = null

  @JsonProperty('SavedSearchId', Number, true) savedSearchId: number = null

  @JsonProperty('ImageSetId', Number, true) imageSetId: number = null

  @JsonProperty('SelectedExportTemplateId', Number, true)
  selectedExportTemplateId: number = null

  @JsonProperty('SelectedExportFieldTemplateId', Number, true)
  selectedExportFieldTemplateId: number = null

  @JsonProperty('SelectedMedias', CommaSeparatedNumberConverter, true)
  selectedMedias: number[] = null

  @JsonProperty('TagIDs', CommaSeparatedNumberConverter, true)
  tagIDs: number[] = null

  @JsonProperty('TagOperator', String, true) tagOperator: string = null

  @JsonProperty('TiffFileFormatList', [String], true)
  tiffFileFormatList: string[] = null

  @JsonProperty('exportSource', Number, true) exportSource: number = null

  @JsonProperty('sourceOption_IncludeParentChild', Boolean, true)
  sourceOptionIncludeParentChild: boolean = null

  @JsonProperty('sourceOption_ArchivesFiles', Boolean, true)
  sourceOptionArchivesFiles: boolean = null

  @JsonProperty('sourceOption_UseOriginalPath', Boolean, true)
  useOriginalPath: boolean = null

  @JsonProperty('sourceOption_IncludeOriginalFileExtension', Boolean, true)
  includeOriginalFileExtension: boolean = null

  @JsonProperty('TagScopeBrands', [SelectiveEndorsementSetting], true)
  tagScopeBrands: SelectiveEndorsementSetting[] = null

  @JsonProperty('CreateTiffJob', Boolean, true) createTiffJob: boolean = null

  @JsonProperty('exportType', String, true) exportType: string = null

  @JsonProperty('IsSortByCustodian', Boolean, true)
  isSortByCustodian?: boolean = null

  @JsonProperty('SortOrderCustodian', String, true)
  sortOrderCustodian?: string = null

  @JsonProperty('IsSort1Enabled', Boolean, true) isSort1Enabled?: boolean = null

  @JsonProperty('SortBy1', String, true) sortBy1?: string = null

  @JsonProperty('SortOrder1', String, true) sortOrder1?: string = null

  @JsonProperty('IsSort2Enabled', Boolean, true) isSort2Enabled?: boolean = null

  @JsonProperty('SortBy2', String, true) sortBy2?: string = null

  @JsonProperty('SortOrder2', String, true) sortOrder2?: string = null

  @JsonProperty('IsSort3Enabled', Boolean, true) isSort3Enabled?: boolean = null

  @JsonProperty('SortBy3', String, true) sortBy3?: string = null

  @JsonProperty('SortOrder3', String, true) sortOrder3?: string = null
}

@JsonObject('SourceMediaProduction')
export class SourceMedia {
  @JsonProperty('CustodianId', Number, true) custodianId: number = null

  @JsonProperty('CustodianName', String, true) custodianName: string = null

  @JsonProperty('DuplicateCount', Number, true) duplicateCount: number = null

  @JsonProperty('MediaId', Number, true) mediaId: number = null

  @JsonProperty('MediaName', String, true) mediaName: string = null

  @JsonProperty('OriginalCount', Number, true) originalCount: number = null

  @JsonProperty('ProjectName', String, true) projectName: string = null

  @JsonProperty('SELECTMEDIA', Boolean, true) selectMedia: boolean = null
}

@JsonObject('ExportFieldTemplateProduction')
export class ExportFieldTemplate {
  @JsonProperty('Id', Number, true) id: number = null

  @JsonProperty('IsDefaultTemplate', Boolean, true)
  isDefaultTemplate: boolean = null

  @JsonProperty('IsEditable', Boolean, true) isEditable: boolean = null

  @JsonProperty('Name', String, true) name: string = null

  @JsonProperty('TemplateProjectId', Number, true)
  templateProjectId: number = null

  @JsonProperty('TemplateType', String, true) templateType: string = null
}

@JsonObject('ExportFieldDetailsProduction')
export class ExportFieldDetails {
  @JsonProperty('DESCRIPTION', String, true) description: string = null

  @JsonProperty('FIELD NAME', String, true) fieldName: string = null

  @JsonProperty('GROUP NAME', String, true) groupName: string = null

  @JsonProperty('PRODUCTION FIELD NAME', String, true)
  productionFieldName: string = null
}

@JsonObject('FieldProduction')
export class Field {
  @JsonProperty('CustomFieldValue', String, true)
  customFieldValue: string = null

  @JsonProperty('ExportFieldId', Number, true) exportFieldId: number = null

  @JsonProperty('ExportFieldName', String, true) exportFieldName: string = null

  @JsonProperty('FieldDescription', String, true)
  fieldDescription: string = null

  @JsonProperty('FieldId', Number, true) fieldId: number = null

  @JsonProperty('GroupName', String, true) groupName: string = null

  @JsonProperty('IsCustomField', Boolean, true) isCustomField: boolean = null

  @JsonProperty('IsCustomFieldValueEditable', Boolean, true)
  isCustomFieldValueEditable: boolean = null

  @JsonProperty('Name', String, true) name: string = null

  @JsonProperty('OrderWeight', Number, true) orderWeight: number = null

  @JsonProperty('IsVenioField', Boolean, true) isVenioField: boolean = null
}

@JsonObject('SourceTagProduction')
export class SourceTag {
  @JsonProperty('ID', String, true) id: string = null

  @JsonProperty('IsGroup', Boolean, true) isGroup: boolean = null

  @JsonProperty('Name', String, true) name: string = null

  @JsonProperty('ParentID', String, true) parentId: string = null

  @JsonProperty('TagGroupID', Number, true) tagGroupId: number = null

  @JsonProperty('TagID', Number, true) tagId: number = null

  @JsonProperty('TotalTagCount', Number, true) totalTagCount: number = null
}

@JsonObject('SavedSearchProduction')
export class SavedSearch {
  @JsonProperty('SearchID', Number, true) searchId: number = null

  @JsonProperty('SearchName', String, true) searchName: string = null

  @JsonProperty('TotalHitCount', Number, true) totalHitCount: number = null
}

export interface SourceImageSet {
  imageSetId: number
  imageSetName: string
  totalHitCount: number
}

@JsonObject('SourceFolderProduction')
export class SourceFolder {
  @JsonProperty('AccessType', String, true) accessType: string = null

  @JsonProperty('FolderID', Number, true) folderId: number = null

  @JsonProperty('FolderLineage', String, true) folderLineage: string = null

  @JsonProperty('FolderName', String, true) folderName: string = null

  @JsonProperty('ID', Number, true) id: number = null

  @JsonProperty('ParentID', Number, true) parentID: number = null

  @JsonProperty('SystemFolder', String, true) systemFolder: string = null

  @JsonProperty('Total File Count', Number, true) totalFileCount: number = null
}

/**
 * #23817 custom field
 */
@JsonObject('CustomField')
export class CustomField {
  @JsonProperty('FieldId', Number, true) fieldId: number = null

  @JsonProperty('FieldName', String, true) fieldName: string = null
}

@JsonObject('ProductionStatusProduction')
export class ProductionStatus {
  @JsonProperty('ArchiveStatus', String, true) archiveStatus: string = null

  @JsonProperty('CompressedFileLocation', String, true)
  compressedFileLocation: string = null

  @JsonProperty('CreatedBy', String, true) createdBy: string = null

  @JsonProperty('DownloadCount', Number, true) downloadCount: number = null

  @JsonProperty('ExportCreatedOn', String, true) exportCreatedOn: string = null

  @JsonProperty('ExportEndDate', String, true) exportEndDate: string = null

  @JsonProperty('ExportFulltext', Boolean, true) exportFullText: boolean = null

  @JsonProperty('ExportId', Number, true) exportId: number = null

  @JsonProperty('ExportName', String, true) exportName: string = null

  @JsonProperty('ExportNative', Boolean, true) exportNative: boolean = null

  @JsonProperty('ExportStartDate', String, true) exportStartDate: string = null

  @JsonProperty('ExportTiff', Boolean, true) exportTiff: boolean = null

  @JsonProperty('ExportedBy', Any, true) exportedBy: any = null

  @JsonProperty('ExportedFromVoD', String, true) exportedFromVoD: string = null

  @JsonProperty('FulltextCompleted', Number, true)
  fulltextCompleted: number = null

  @JsonProperty('ImageCompleted', Number, true) imageCompleted: number = null

  @JsonProperty('IsReExport', String, true) isReExport: string = null

  @JsonProperty('IsZipped', Boolean, true) isZipped: boolean = null

  @JsonProperty('JobId', Number, true) jobId: number = null

  @JsonProperty('NativeCompleted', Number, true) nativeCompleted: number = null

  @JsonProperty('ProcessingMachineName', String, true)
  processingMachineName: string = null

  @JsonProperty('ProjectId', Number, true) projectId: number = null

  @JsonProperty('Status', String, true) status: string = null

  @JsonProperty('TotalDocumentCount', Number, true)
  totalDocumentCount: number = null

  @JsonProperty('TotalPageCount', Number, true) totalPageCount: number = null

  @JsonProperty('IsRelativityImportEnabled', Boolean, true)
  isRelativityImportEnabled: boolean = null

  @JsonProperty('RelativityFulltextCompleted', Number, true)
  relativityFulltextCompleted: number = null

  @JsonProperty('RelativityNativeCompleted', Number, true)
  relativityNativeCompleted: number = null

  @JsonProperty('RelativityImageCompleted', Number, true)
  relativityImageCompleted: number = null

  @JsonProperty('IsDownloadProgress', Boolean, true)
  isDownloadProgress: boolean = null

  @JsonProperty('Connector', String, true)
  connector: string = null
}

@JsonObject('ProductionSummaryProduction')
export class ProductionSummary {
  @JsonProperty('FulltextFileCount', Number, true)
  fulltextFileCount: number = null

  @JsonProperty('FulltextFileSize', String, true)
  fulltextFileSize: string = null

  @JsonProperty('ImageFileCount', Number, true) imageFileCount: number = null

  @JsonProperty('ImageFileSize', String, true) imageFileSize: string = null

  @JsonProperty('ImagePageCount', Number, true) imagePageCount: number = null

  @JsonProperty('NativeFileCount', Number, true) nativeFileCount: number = null

  @JsonProperty('NativeFileSize', String, true) nativeFileSize: string = null

  @JsonProperty('TotalFileCount', Number, true) totalFileCount: number = null
}

@JsonObject('ProductionParamsProduction')
export class ProductionParams {
  @JsonProperty('ExportFieldTemplateDataTable', [ExportFieldTemplate], true)
  exportFieldTemplates: ExportFieldTemplate[] = null

  @JsonProperty('ExportTemplateDataTable', [ExportFieldTemplate], true)
  exportTemplates: ExportFieldTemplate[] = null

  @JsonProperty('MediaSourceGridData', [SourceMedia], true)
  sourceMedias: SourceMedia[] = null

  @JsonProperty('ProductionStatus', [ProductionStatus], true)
  productionStatus: ProductionStatus[] = null

  @JsonProperty('ProductionUIOptions', ProductionUIOptions, true)
  productionUIOptions: ProductionUIOptions = null

  @JsonProperty('Tags', [SourceTag], true) sourceTags: SourceTag[] = null

  @JsonProperty('InvalidFileNameChars', String, true)
  invalidFileNameChars: string = null
}

@JsonObject('CreateExportFieldTemplateRequestModelProduction')
export class CreateExportFieldTemplateRequestModel {
  @JsonProperty('Field', [Field], true) fields: Field[] = null

  @JsonProperty('Template', ExportFieldTemplate, true)
  template: ExportFieldTemplate = null
}

@JsonObject('UploadInvitationUserModelProduction')
export class UploadInvitationUserModel {
  @JsonProperty('Email', String, true) email: string = null

  @JsonProperty('GroupName', String, true) groupName: string = null

  @JsonProperty('UserID', Number, true) userId: number = null

  @JsonProperty('UserName', String, true) userName: string = null
}

@JsonObject('ProductionDownloadInvitationProduction')
export class ProductionDownloadInvitation {
  @JsonProperty('exportId', Number, true) exportId: number = null

  @JsonProperty('invitedExtUserInfo', [String], true)
  invitedExtUserInfo: string[] = null

  @JsonProperty(
    'invitedIntUserInfo',
    ColonSeparatedStringToUserModelConverter,
    true
  )
  invitedIntUserInfo: UploadInvitationUserModel[] = null

  @JsonProperty('productionDownloadExpirationPeriod', String, true)
  productionDownloadExpirationPeriod: string = null

  @JsonProperty('productionDownloadInstruction', String, true)
  productionDownloadInstruction: string = null

  @JsonProperty('recipientUserIds', [Number], true)
  recipientUserIds: number[] = null
}
