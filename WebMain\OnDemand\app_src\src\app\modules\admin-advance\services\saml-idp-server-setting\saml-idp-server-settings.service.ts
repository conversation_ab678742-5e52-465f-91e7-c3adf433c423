import {
  GroupRequestPayload,
  IdPGroupInsertRequestModel,
  SamlSettingModel
} from '@admin-advance/models/saml-idp-server/saml-idp-server.model'
import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ConfigService } from '@config/services/config.service'
import { cloneDeep } from 'lodash'
import { Observable } from 'rxjs'

declare let encryptStr: any

@Injectable()
export class SamlIdpServerSettingsService {
  key = '92fdce43453434c36f8e3fbcb2b2e4fb'

  private get _url() {
    return this.configService.getApiUrl() + '/idp-settings'
  }

  constructor(private configService: ConfigService, private http: HttpClient) {}

  readonly fetchParseFromXmlMetafile = <T>(
    xmlMetafile: string
  ): Observable<T> => {
    const fd = new FormData()
    fd.append('XmlFile', new Blob([xmlMetafile]))
    return this.http.post<T>(`${this._url}/parse-meta-file`, fd)
  }

  readonly fetchIdpGroups = <T>(payload: GroupRequestPayload): Observable<T> =>
    this.http.post<T>(`${this._url}/load-idp-groups`, payload)

  readonly insertIdpGroups = <T>(
    payload: IdPGroupInsertRequestModel
  ): Observable<T> =>
    this.http.post<T>(`${this._url}/insert-idp-groups`, payload)

  readonly checkMissingIdpGroups = <T>(
    payload: GroupRequestPayload
  ): Observable<T> =>
    this.http.post<T>(`${this._url}/check-missing-idp-groups`, payload)

  readonly fetchSamlIdpServerSettings = <T>(
    clientId?: number
  ): Observable<T> => {
    const url = clientId ? `${this._url}?clientId=${clientId}` : this._url
    return this.http.get<T>(url)
  }

  readonly applySamlIdpServerSettings = <T>(
    payload: SamlSettingModel
  ): Observable<T> => {
    const samlSettingModel = cloneDeep(payload)
    const encryptedToken = encryptStr(samlSettingModel.ssoToken, this.key)
    samlSettingModel.sessionId = this.key
    samlSettingModel.ssoToken = encryptedToken
    return this.http.post<T>(this._url, samlSettingModel)
  }

  readonly fetchIdpUsers = <T>(payload: GroupRequestPayload): Observable<T> =>
    this.http.post<T>(`${this._url}/users`, payload)

  readonly GetIdPGroupMappingSettingsStatus = <T>(): Observable<T> =>
    this.http.get<T>(`${this._url}/check-idp-group-mapping-status`)
}
