<div>
  <div class="modal-header">
    <div class="d-flex align-items-center">
      <img
        src="assets/images/icon-warning-error-theme-triangle.svg"
        alt="Warning Icon"
        style="width: 20px; height: 20px; margin-right: 8px"
      />
      <h4 class="modal-title mb-0">Search</h4>
    </div>
    <button type="button" class="close" (click)="onCancel()" aria-label="Close">
      <span aria-hidden="true" class="fa fa-times"></span>
    </button>
  </div>
  <div class="modal-body">
    <div class="d-flex flex-column p-3">
      <div class="d-flex ml-3">
        <div class="d-flex flex-column flex-grow-1 w-100">
          <div
            class="
              custom-warning-subtle
              px-3
              py-2
              rounded
              mb-3
              small
              custom-bg-color
            "
          >
            Following media included in search are not completely indexed.
            Searching on fulltext &amp;
            <span class="font-weight-bold custom-highlight-color">
              indexed fields
            </span>
            may give inconsistent search results.
          </div>

          <div>
            <dx-data-grid
              [dataSource]="unIndexMediaList"
              [showBorders]="true"
              [showRowLines]="true"
              [allowColumnResizing]="true"
              [columnResizingMode]="'nextColumn'"
              [columnMinWidth]="50"
              [columnAutoWidth]="true"
              [rowAlternationEnabled]="true"
              height="400px"
              keyExpr="sn"
            >
              <dxi-column
                dataField="sn"
                caption="#"
                [allowFiltering]="false"
                width="50"
              >
              </dxi-column>

              <dxi-column
                dataField="custodianName"
                caption="Custodian"
                [allowFiltering]="false"
              >
              </dxi-column>

              <dxi-column
                dataField="mediaName"
                caption="Media"
                [allowFiltering]="false"
              >
              </dxi-column>

              <dxi-column
                dataField="mediaStatus"
                caption="Status"
                [allowFiltering]="false"
              >
              </dxi-column>

              <dxo-load-panel [enabled]="true"></dxo-load-panel>

              <dxo-scrolling
                showScrollbar="always"
                mode="virtual"
              ></dxo-scrolling>
            </dx-data-grid>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <div class="text-right">
      <button type="submit" class="btn btn-primary" (click)="proceed()">
        Ok
      </button>
      <button type="button" class="btn btn-grey" (click)="onCancel()">
        Cancel
      </button>
    </div>
  </div>
</div>
