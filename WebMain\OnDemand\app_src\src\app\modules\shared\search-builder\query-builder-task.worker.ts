import { expose } from 'comlink'
import {
  CachedValueType,
  DataItems,
  FilterPayloadModel,
  TermValueType
} from './query-builder-static.model'

/**
 * performs query generation task in the worker and returns finalized data as a promise.
 * @param input input data to transform and generate query out of it.
 */
export function generateQueryWorkerTask(data: {
  items: DataItems[]
  controlCachedValues: CachedValueType[]
  operators: { main: string }
  history: string
}): any {
  const controlItems = data.items as DataItems[]
  const cachedValues = data.controlCachedValues as CachedValueType[]

  const setNodeValuesRecursively = (items: DataItems[]): void => {
    for (let i = 0; i < items.length; i++) {
      const el = items[i]
      if (el?.children) setNodeValuesRecursively(el.children)
      if (el.nodes) {
        el.nodes.forEach((node) => {
          const cachedValue = cachedValues.find((c) => c.nodeUuid === node.uuid)
          if (cachedValue) {
            const { values: v } = cachedValue
            const operator = v?.operator?.operator
            if (operator) {
              if (operator.includes('NULL')) {
                v.primaryOperandValue = undefined
                v.secondaryOperandValue = undefined
                v.multiValueOperator = undefined
              }
              if (!operator.includes('BETWEEN')) {
                v.secondaryOperandValue = undefined
              }
              if (
                operator.includes('LIKE') &&
                typeof v?.primaryOperandValue === 'string'
              ) {
                v.primaryOperandValue = `*${v.primaryOperandValue ?? ''}*`
                v.operator.operator = 'LIKE'
              }
              if (
                operator.toLowerCase().includes('with') &&
                typeof v?.primaryOperandValue === 'string'
              ) {
                v.primaryOperandValue =
                  operator.toLowerCase().search('begins_with') > -1
                    ? `${v.primaryOperandValue ?? ''}*`
                    : `*${v.primaryOperandValue ?? ''}`
                v.operator.operator = 'LIKE'
              }
            }
            node.values = v ?? {}
          }
        })
      }
    }
  }

  setNodeValuesRecursively(controlItems)

  const handleValue = (
    fieldName: string,
    value: any,
    multiValueOperator: any,
    fileSizeType?: string
  ) => {
    const fileSizeValue =
      fileSizeType === 'kb'
        ? +value * Math.pow(1024, 1)
        : fileSizeType === 'mb'
        ? +value * Math.pow(1024, 2)
        : fileSizeType === 'gb'
        ? +value * Math.pow(1024, 3)
        : value
    const isMulti = Array.isArray(value)
    if (isMulti) {
      return `${fieldName} (${value
        .map((p) => `"${p['Name']}"`.trim())
        .join(` ${multiValueOperator} `)})`
    } else {
      const isObject = typeof value === 'object'
      const isNum = typeof value === 'number'
      return `"${
        isObject
          ? value?.['Name'] ?? ''
          : fileSizeType
          ? Math.abs(fileSizeValue)
          : isNum
          ? +value
          : value
      }"`
    }
  }

  const wrap = (s) => `${s ? '(' : ''}${s}${s ? ')' : ''}`
  const isUnary = (s) => ['NOT'].includes(s)
  const getNodes = ({
    values: {
      fieldValue,
      fileSizeType,
      operator,
      primaryOperandValue,
      secondaryOperandValue,
      multiValueOperator
    }
  }) => {
    const flaggedFieldForOperatorAssignee =
      fieldValue.FieldName.match(/tags|folders/i)
    const operatorString = !flaggedFieldForOperatorAssignee
      ? operator?.operator?.trim() || ''
      : ''

    return !secondaryOperandValue?.toString()
      ? `${
          Array.isArray(primaryOperandValue) && operatorString.includes('!=')
            ? 'NOT'
            : `${
                !flaggedFieldForOperatorAssignee
                  ? fieldValue?.FieldName || ''
                  : ''
              } ${operatorString}`
        }${
          primaryOperandValue?.toString()
            ? ` ${handleValue(
                fieldValue?.FieldName,
                primaryOperandValue,
                multiValueOperator,
                fileSizeType
              )}`
            : ''
        }`
      : `${fieldValue?.FieldName} ${operatorString}${
          operatorString === 'BETWEEN' && secondaryOperandValue?.toString()
            ? ` ${handleValue(
                fieldValue?.FieldName,
                primaryOperandValue,
                multiValueOperator,
                fileSizeType
              )} AND ${handleValue(
                fieldValue?.FieldName,
                secondaryOperandValue,
                multiValueOperator,
                fileSizeType
              )}`
            : ''
        }`
  }
  const getChildren = ({ operator, nodes = [], children = [] }) => {
    const values = []
      .concat(nodes.map(getNodes))
      .concat(children.map(getChildren))
      .filter((term) => term?.trim())

    return isUnary(operator)
      ? ` ${values.length > 0 ? operator : ''} ${values.join(' ')}`
      : wrap(values.join(` ${operator} `))
  }
  const q = controlItems
    .map(getChildren)
    .join('')
    .replace(/\r?\n|\r/g, '')

  const mo =
    data.operators.main === 'NOT'
      ? ' AND ' + data.operators.main
      : data.operators.main
  const query = `${mo} ${q}`
  return query.replace(/\r?\n|\r/g, '')
}

/**
 * Generates query out of selected terms.
 * @param input payload to transform and generate query.
 */
export function generateQueryWorkerSubTask(
  data: FilterPayloadModel<string[]>
): any {
  try {
    if (!data?.terms) return ''

    const q = data.terms
      .reduce((query, item, index, array) => {
        return (query += `
        ${index === 0 ? '(' : ''}${item} ${
          index >= 0 && index < array.length - 1 ? data.operators.term : ''
        }${index === array.length - 1 ? ')' : ''}`)
      }, '')
      ?.trim()
      .replace(/(?:\r\n|\r|\n)/g, '')
      .replace(/\s+/g, ' ')
    const mo =
      data.operators.main === 'NOT'
        ? ' AND ' + data.operators.main
        : data.operators.main

    const query = ` ${mo} ${q}`
    return query.replace(/\r?\n|\r/g, '')
  } catch (e) {
    console.error(e)
  }
}

/**
 * Checks if a string is a single, balanced parenthesized expression.
 * e.g., "(foo)" -> true, "((foo))" -> true, "(foo) AND (bar)" -> false, "foo" -> false
 */
function isSingleBalancedParenthesized(str: string): boolean {
  const trimmed = str.trim()
  if (!(trimmed.startsWith('(') && trimmed.endsWith(')'))) {
    return false
  }

  let balance = 0
  for (let i = 0; i < trimmed.length; i++) {
    if (trimmed[i] === '(') {
      balance++
    } else if (trimmed[i] === ')') {
      balance--
    }
    // If balance is 0 before reaching the end, it means it's not a *single* enclosing pair
    // e.g. "(foo) AND (bar)" would hit balance = 0 after "(foo)"
    if (balance === 0 && i < trimmed.length - 1) {
      return false
    }
  }
  return balance === 0 // Should be 0 if it was perfectly balanced from start to end
}
/**
 * Wraps a string with parentheses if it's not empty and not already a single balanced parenthesized expression.
 */
function wrapIfNotAlready(str: string): string {
  const trimmed = str.trim()
  if (!trimmed) return trimmed // Don't wrap empty or whitespace-only strings

  if (isSingleBalancedParenthesized(trimmed)) {
    return trimmed // Already perfectly wrapped
  }
  return `(${trimmed})`
}
/**
 * Generates query out of terms.
 * @param terms payload to transform and generate query.
 */
export function generateQueryWorkerTerms(terms: TermValueType): any {
  const termKeys = Object.keys(terms)
  try {
    if (!termKeys.some((key) => !!terms[key])) return ''

    const multiTermsValue = terms.multiTerms
      ?.trim()
      .replace(/\s+/gi, ' ')
      .split(' ')
      .filter((s) => s)
      .reduce((q, t, index, arr) => {
        return (q += `${index === 0 ? '(' : ''}${t}${
          index < arr.length - 1 ? ' AND ' : ''
        }${index === arr.length - 1 ? ')' : ''}`)
      }, '')
      .replace(/\s*\(\s*\)\s*/, '')
      .trim()

    const textField =
      terms.filterOn === 1 //ExpansionField.CONTENTS
        ? 'FULLTEXT'
        : terms.filterOn === 2 //ExpansionField.ATTRIBUTES
        ? 'ATTRIBUTES'
        : ''

    const searchQueryValue = termKeys // Renamed for clarity
      .filter(
        (key) =>
          terms[key] &&
          String(terms[key]).trim() && // Ensure value is not just whitespace
          terms[key] !== 'OR' &&
          terms[key] !== 'AND' &&
          key !== 'operator' &&
          key !== 'multiTerms' &&
          key !== 'filterOn'
      )
      .reduce((accum, key, index, arr) => {
        const valueStr = String(terms[key]).trim()
        if (!valueStr) return accum // Skip if value is empty after trim

        let term = ''
        switch (key) {
          case 'begins':
            {
              // Split by spaces and process each word separately
              const parts = valueStr.split(' ').filter((s) => !!s.trim())
              if (parts.length > 1) {
                // Multiple words - join with OR and add asterisk (no quotes)
                const joinedParts = parts.map((part) => `${part}*`).join(' OR ')
                term = `(${joinedParts})`
              } else if (parts.length === 1) {
                // Single word - just add asterisk (no quotes)
                term = `${parts[0]}*`
              }
            }
            break
          case 'contains':
            {
              // Split by spaces and process each word separately
              const parts = valueStr.split(' ').filter((s) => !!s.trim())
              if (parts.length > 1) {
                // Multiple words - join with AND and add asterisks (no quotes)
                const joinedParts = parts
                  .map((part) => `*${part}*`)
                  .join(' AND ')
                term = `(${joinedParts})`
              } else if (parts.length === 1) {
                // Single word - just add asterisks (no quotes)
                term = `*${parts[0]}*`
              }
            }
            break
          case 'ends':
            {
              // Split by spaces and process each word separately
              const parts = valueStr.split(' ').filter((s) => !!s.trim())
              if (parts.length > 1) {
                // Multiple words - join with OR and add asterisk (no quotes)
                const joinedParts = parts.map((part) => `*${part}`).join(' OR ')
                term = `(${joinedParts})`
              } else if (parts.length === 1) {
                // Single word - just add asterisk (no quotes)
                term = `*${parts[0]}`
              }
            }
            break
          case 'phrase':
            {
              // For phrases, if user already included quotes, use as-is; otherwise, wrap in quotes
              const trimmed = valueStr.trim()
              if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
                term = trimmed
              } else {
                term = `"${trimmed}"`
              }
            }
            break
          case 'singleTerm':
            {
              const singleTermParts = valueStr
                .split(' ')
                .filter((s) => !!s.trim())
              if (singleTermParts.length > 0) {
                // singleTerm is (A OR B), already wrapped. No quotes, just join with OR
                term = `(${singleTermParts.join(' OR ')})`
              }
            }
            break
          default:
            // For any other case, just use the value as is (no quotes)
            term = valueStr
        }

        if (term) {
          if (accum) {
            accum += ' AND '
          }
          accum += term
        }
        return accum
      }, '')
      .trim()

    // Combine searchQueryValue and multiTermsValue
    let queryBody = ''
    const bodyParts = []
    if (searchQueryValue) bodyParts.push(searchQueryValue)
    if (multiTermsValue) bodyParts.push(multiTermsValue)

    queryBody = bodyParts.filter((p) => p).join(' AND ') // Filter out empty parts before join

    if (!queryBody) {
      // If after all processing, there's no query body
      // Decide what to do: return empty, or let operator prefix nothing?
      // For now, let's check if an operator exists. If not, empty is fine.
      if (!terms.operator || !String(terms.operator).trim()) return ''
      // If operator exists but queryBody is empty, it might form "AND NOT " which is incomplete
      // Depending on desired behavior, could return '' or throw error or form specific query
    }

    let finalAssembledQuery = queryBody

    // Apply textField wrapping
    if (textField && queryBody) {
      // Only wrap if textField and queryBody exist
      finalAssembledQuery = `${textField} ${wrapIfNotAlready(queryBody)}`
    }

    // Conditional wrapping based on original multiTerms presence (or other logic)
    // The original `if (multiTerms)` (variable) was a bit indirect.
    // Let's use the original terms.multiTerms as a more direct condition if that was the intent.
    // This step is often to ensure the entire block is treated as a unit for an outer operator like NOT.
    if (terms.multiTerms?.trim()) {
      // If the *input* had multiTerms
      finalAssembledQuery = wrapIfNotAlready(finalAssembledQuery)
    }

    // Handle the main operator
    const operatorValue = terms.operator ? String(terms.operator).trim() : ''
    let fullQueryString = ''

    if (operatorValue) {
      const effectiveOperator =
        operatorValue === 'NOT' ? 'AND NOT' : operatorValue
      if (finalAssembledQuery) {
        // Only add operator if there's a query to operate on
        // For NOT, ensure the thing being negated is a single group
        if (operatorValue === 'NOT') {
          fullQueryString = `${effectiveOperator} ${wrapIfNotAlready(
            finalAssembledQuery
          )}`
        } else {
          fullQueryString = `${effectiveOperator} ${finalAssembledQuery}` // AND/OR usually connect to something preceding
        }
      } else {
        // Operator exists but no query. What to do? e.g. "AND NOT " by itself.
        // This case needs careful consideration based on system requirements.
        // For now, if query is empty, don't prepend operator.
        // Or, if an operator *must* be there, consider returning only the operator if it's part of a larger chain.
        // This function seems to build a *segment*, so `AND ` might be valid if appended to.
        // If the function is meant to return a *complete* and valid query part:
        if (operatorValue === 'NOT' && !finalAssembledQuery) {
          // "AND NOT" with nothing to negate is problematic.
          // return ''; // or handle as an error/specific case
        } else {
          fullQueryString = effectiveOperator // e.g. "AND " or "OR "
        }
      }
    } else {
      fullQueryString = finalAssembledQuery
    }

    // Final cleanup
    return fullQueryString
      .replace(/\r+|\n+/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
  } catch (e) {
    console.error(e)
    return ''
  }
}

/**
 * performs background task to remove invalid line from the tet file
 * and returns only valid
 * @param input payload to transform vand give valit output
 */
export function excludeInvalidLinesFormText(input: {
  text: string
  exclude: Array<{ searchExpression: string }>
}): any {
  input.exclude = Array.isArray(input.exclude) ? input.exclude ?? [] : []
  try {
    const invalid: string[] = input.exclude.map((l) =>
      l.searchExpression?.trim()
    )
    return input.text
      .split(/\n/gi)
      .filter((t) => !!t?.trim() && !invalid.includes(t?.trim()))
      .join('\n')
  } catch (e) {
    console.error(e)
  }
}

/**
 * Generates query of tag history
 * @param input payload to transform into query
 */
export function generateTagHistoryQuery(
  data: FilterPayloadModel<number[]>
): any {
  try {
    if (!data?.terms) return ''
    const qq =
      data.operators.term === 'AND'
        ? data.terms
            .map((id) => `TAG_BATCH_ID = ${id}`)
            .join(` ${data.operators.term} `)
        : data.operators.term === 'OR'
        ? `TAG_BATCH_ID IN(${data.terms.join(',')})`
        : ''

    const mo =
      data.operators.main === 'NOT'
        ? ' AND ' + data.operators.main
        : data.operators.main
    const query = `${mo} (${qq})`.replace(/(?:\r\n|\r|\n)/g, '')
    return query.replace(/\r?\n|\r/g, '')
  } catch (e) {
    console.error(e)
  }
}

/**
 * Generates query of reviewset batch
 * @param input payload to transform into query
 */
export function generateReviewSetQuery(data: FilterPayloadModel<any[]>): any {
  try {
    if (!data?.terms) return ''
    const all = data.terms
    // Split the items into parents and children based on the `isParent` flag.
    // eslint-disable-next-line prefer-const
    let [parents, children] = all.reduce(
      ([parents, children], node) =>
        node.isParent
          ? [[...parents, node], children]
          : [parents, [...children, node]],
      [[], []]
    )

    // Generate an array of queries based on the parent-child relationships.
    const queries = parents.flatMap((parent) => {
      // Filter the children that match the parent's `treeID`.
      const matchingChildren = children.filter(
        (child) => child.parentID === parent.treeID
      )
      // If no matching children were found, return an empty array.
      if (matchingChildren.length === 0) return []
      // Get the IDs of the matching children.
      const childIds = new Set(matchingChildren.map((child) => child.id))
      // Remove the matching children from the list of children.
      children = children.filter((child) => !childIds.has(child.id))
      // Return a query string for the parent.
      const query = `Reviewset="${parent.name}"${
        data?.showOnlyReviewed ? ` AND TAGS("${parent.tagName}")` : ''
      }`
      return [query]
    })

    // Get the IDs of the children that do not have a parent.
    const uniqueChildIds = new Set(
      children
        .filter((child) => !parents.some((parent) => parent.id === child.id))
        .map((child) => child.id)
    )

    // If there are any unique child IDs, add them to the list of queries.
    if (uniqueChildIds.size > 0) {
      const query = `REVIEWSET_BATCH_ID IN (${[...uniqueChildIds].join(',')})`
      if (data?.showOnlyReviewed) {
        const uniqueChildTagNames = new Set(
          children
            .filter(
              (child) => !parents.some((parent) => parent.id === child.id)
            )
            .map((child) => `"${child.tagName}"`)
        )
        queries.push(
          `${query} AND TAGS(${[...uniqueChildTagNames].join(' OR ')})`
        )
      } else queries.push(query)
    }

    // Join the queries into a single string.
    const queryArray = queries.join(` ${data.operators.term} `)

    const mo =
      data.operators.main === 'NOT'
        ? ' AND ' + data.operators.main
        : data.operators.main
    const query = `${mo} (${queryArray})`.replace(/(?:\r\n|\r|\n)/g, '')

    // Remove any line breaks from the query string and return it.
    return query.replace(/\r?\n|\r/g, '')
  } catch (e) {
    console.error(e)
  }
}

/**
 * Generates query of non processable terms
 * @param input payload to transform into query
 */
export function generateNonProcessableQuery(
  data: FilterPayloadModel<any[][]>
): any {
  try {
    if (!data?.terms) return ''
    const qq = data.terms
      .reduce((d, elem) => (d = d.concat(elem)), [])
      .map((k) => k.value)
      .join(` ${data.operators.term} `)

    const mo =
      data.operators.main === 'NOT'
        ? ' AND ' + data.operators.main
        : data.operators.main
    const query = `${mo} (${qq})`.replace(/(?:\r\n|\r|\n)/g, '')
    return query.replace(/\r?\n|\r/g, '')
  } catch (e) {
    console.error(e)
  }
}

/**
 * Generates query of folder search.
 * @param input payload to transform and generate query.
 */
export function generateFolderOrTagSearchQuery(input: {
  payload: FilterPayloadModel<string[]>
  isTag: boolean
}): any {
  // Variable workerData is assigned as a constant in the worker context.
  const data: FilterPayloadModel<string[]> = input.payload
  const attribute = input.isTag ? 'TAGS' : 'FOLDERS'

  try {
    if (!data?.terms) return ''

    const q = data.terms
      .reduce((query, item, index, array) => {
        return (query += `
        ${index === 0 ? attribute + '(' : ''}"${item}" ${
          index >= 0 && index < array.length - 1 ? data.operators.term : ''
        }${index === array.length - 1 ? ')' : ''}`)
      }, '')
      ?.trim()
      .replace(/(?:\r\n|\r|\n)/g, '')
      .replace(/\s+/g, ' ')
    const mo =
      data.operators.main === 'NOT'
        ? ' AND ' + data.operators.main
        : data.operators.main
    const query = `${mo} ${q}`
    return query.replace(/\r?\n|\r/g, '')
  } catch (e) {
    console.error(e)
  }
}

expose({
  generateQueryWorkerTask,
  generateQueryWorkerSubTask,
  generateQueryWorkerTerms,
  excludeInvalidLinesFormText,
  generateTagHistoryQuery,
  generateReviewSetQuery,
  generateNonProcessableQuery,
  generateFolderOrTagSearchQuery
})
