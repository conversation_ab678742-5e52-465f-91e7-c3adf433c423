import { NumberEnumConverter } from '@shared/utils/json-converters'

export enum ServiceType {
  PRINT_SERVICE = 0,
  PDF_SERVICE = 1,
  VODR_STANDARD_CONCORDANCE_SERVICE = 2,
  VODR_STANDARD_SUMMATION_SERVICE = 3,
  FILTERING_SERVICE = 4,
  VODR_IMPORT_TO_RELATIVITY_SERVICE = 5
}
export enum SERVICE_TYPE_CONSTANTS {
  VODR_CONCORDANCE_SERVICE = 'VODR_CONCORDANCE_SERVICE',
  VODR_SUMMATION_SERVICE = 'VODR_SUMMATION_SERVICE',
  VODR_PDF_SERVICE = 'VODR_PDF_SERVICE',
  VODR_PRINT_SERVICE = 'VODR_PRINT_SERVICE',
  VODR_ADD_DATA_EXISTING_PROJECT = 'VODR_ADD_DATA_EXISTING_PROJECT',
  VODR_FILTERING_SERVICE = 'VODR_FILTERING_SERVICE',
  VODR_IMPORT_TO_RELATIVITY_SERVICE = 'VODR_IMPORT_TO_RELATIVITY_SERVICE'
}
export const ServiceTypeDescription = new Map<ServiceType, string>([
  [ServiceType.PRINT_SERVICE, SERVICE_TYPE_CONSTANTS.VODR_PRINT_SERVICE],
  [ServiceType.PDF_SERVICE, SERVICE_TYPE_CONSTANTS.VODR_PDF_SERVICE],
  [
    ServiceType.VODR_STANDARD_CONCORDANCE_SERVICE,
    SERVICE_TYPE_CONSTANTS.VODR_CONCORDANCE_SERVICE
  ],
  [
    ServiceType.VODR_STANDARD_SUMMATION_SERVICE,
    SERVICE_TYPE_CONSTANTS.VODR_SUMMATION_SERVICE
  ],
  [
    ServiceType.FILTERING_SERVICE,
    SERVICE_TYPE_CONSTANTS.VODR_FILTERING_SERVICE
  ],
  [
    ServiceType.VODR_IMPORT_TO_RELATIVITY_SERVICE,
    SERVICE_TYPE_CONSTANTS.VODR_IMPORT_TO_RELATIVITY_SERVICE
  ]
])
export class ServiceTypeEnumConverter extends NumberEnumConverter<ServiceType> {
  constructor() {
    super(ServiceType)
  }
}

export enum DataRetentionType {
  RETAIN_30DAYS_THEN_REMOVE = 0,
  DONT_RETAIN_DELETE_IMMEDIATELY = 1,
  RETAIN_AT_ADDITIONAL_COST = 2
}
export const DataRetentionTypeDescription = new Map<DataRetentionType, string>([
  [
    DataRetentionType.RETAIN_30DAYS_THEN_REMOVE,
    'Retain for 30 days then remove'
  ],
  [
    DataRetentionType.DONT_RETAIN_DELETE_IMMEDIATELY,
    'Do not retain, delete immediately'
  ],
  [
    DataRetentionType.RETAIN_AT_ADDITIONAL_COST,
    'Retain beyond 30 days at additional cost'
  ]
])
export class DataRetentionTypeEnumConverter extends NumberEnumConverter<DataRetentionType> {
  constructor() {
    super(DataRetentionType)
  }
}

export enum DeduplicationOption {
  GLOBAL = 0,
  CUSTODIAN_LEVEL = 1,
  NONE = 2
}
export const DeduplicationOptionDescription = new Map<
  DeduplicationOption,
  string
>([
  [DeduplicationOption.GLOBAL, 'Global'],
  [DeduplicationOption.CUSTODIAN_LEVEL, 'Custodian Level'],
  [DeduplicationOption.NONE, 'None']
])
export class DeduplicationOptionEnumConverter extends NumberEnumConverter<DeduplicationOption> {
  constructor() {
    super(DeduplicationOption)
  }
}

export enum CSVExcelHandling {
  PLACEHOLDER_ONLY = 0,
  CAP_TO_100_PAGES_THEN_PLACEHOLDER = 1,
  PROCESS_ALL_PAGES = 2
}
export const CSVExcelHandlingDescription = new Map<CSVExcelHandling, string>([
  [CSVExcelHandling.PLACEHOLDER_ONLY, 'Placeholder Only'],
  [
    CSVExcelHandling.CAP_TO_100_PAGES_THEN_PLACEHOLDER,
    'Cap to 100 Pages, then Placeholder'
  ],
  [CSVExcelHandling.PROCESS_ALL_PAGES, 'Process all Pages']
])
export class CSVExcelHandlingEnumConverter extends NumberEnumConverter<CSVExcelHandling> {
  constructor() {
    super(CSVExcelHandling)
  }
}

export enum DirectExportImageType {
  NONE = 0,
  TIFF = 1,
  PDF = 2
}

export const DirectExportImageTypeDescription = new Map<
  DirectExportImageType,
  string
>([
  [DirectExportImageType.NONE, 'None'],
  [DirectExportImageType.TIFF, 'TIFF'],
  [DirectExportImageType.PDF, 'PDF']
])

export class DirectExportImageTypeEnumConverter extends NumberEnumConverter<DirectExportImageType> {
  constructor() {
    super(DirectExportImageType)
  }
}

export enum ConversionType {
  DEFAULT = 0,
  BLACK_AND_WHITE = 1,
  COLOR = 2,
  GRAYSCALE = 3,
  COLOR_FOR_COLOR = 4
}
export const ConversionTypeDescription = new Map<ConversionType, string>([
  [ConversionType.DEFAULT, 'Default'],
  [ConversionType.BLACK_AND_WHITE, 'Black And White'],
  [ConversionType.COLOR, 'Color'],
  [ConversionType.GRAYSCALE, 'Grayscale'],
  [ConversionType.COLOR_FOR_COLOR, 'Color For Color']
])
export class ConversionTypeEnumConverter extends NumberEnumConverter<ConversionType> {
  constructor() {
    super(ConversionType)
  }
}

export enum PrefixDelimiter {
  NONE = 0,
  SPACE = 1,
  DASH = 2,
  UNDERSCORE = 3,
  PERIOD = 4
}
export const PrefixDelimiterDescription = new Map<PrefixDelimiter, string>([
  [PrefixDelimiter.NONE, ''],
  [PrefixDelimiter.SPACE, ' '],
  [PrefixDelimiter.DASH, '-'],
  [PrefixDelimiter.UNDERSCORE, '_'],
  [PrefixDelimiter.PERIOD, '.']
])
export class PrefixDelimiterEnumConverter extends NumberEnumConverter<PrefixDelimiter> {
  constructor() {
    super(PrefixDelimiter)
  }
}

export enum ExportLocation {
  HAND_DELIVERY = 0,
  VoDR_HOME = 1
}
export class ExportLocationEnumConverter extends NumberEnumConverter<ExportLocation> {
  constructor() {
    super(ExportLocation)
  }
}

export enum EndorsementLocation {
  TL = 0,
  TC = 1,
  TR = 2,
  LL = 3,
  LC = 4,
  LR = 5
}
export const EndorsementLocationDescription = new Map<
  EndorsementLocation,
  string
>([
  [EndorsementLocation.TL, 'TL'],
  [EndorsementLocation.TC, 'TC'],
  [EndorsementLocation.TR, 'TR'],
  [EndorsementLocation.LL, 'LL'],
  [EndorsementLocation.LC, 'LC'],
  [EndorsementLocation.LR, 'LR']
])
export class EndorsementLocationEnumConverter extends NumberEnumConverter<EndorsementLocation> {
  constructor() {
    super(EndorsementLocation)
  }
}

export enum SelectiveEndorsementType {
  CONTROL_NUMBER = 0,
  MESSAGE = 1
}
export const SelectiveEndorsementTypeDescription = new Map<
  SelectiveEndorsementType,
  string
>([
  [SelectiveEndorsementType.CONTROL_NUMBER, 'Control Number'],
  [SelectiveEndorsementType.MESSAGE, 'Message']
])
export class SelectiveEndorsementTypeEnumConverter extends NumberEnumConverter<SelectiveEndorsementType> {
  constructor() {
    super(SelectiveEndorsementType)
  }
}

export enum PDFType {
  SEARCHABLE = 0,
  IMAGE_ONLY = 1
}
export const PDFTypeDescription = new Map<PDFType, string>([
  [PDFType.SEARCHABLE, 'Searchable'],
  [PDFType.IMAGE_ONLY, 'Image Only']
])
export class PDFTypeEnumConverter extends NumberEnumConverter<PDFType> {
  constructor() {
    super(PDFType)
  }
}

export enum PDFFamilyFileHandling {
  PARENTCHILD_AS_ONE_DOC = 0,
  PARENTCHILD_AS_SEPARATE_DOC = 1
}
export const PDFFamilyFileHandlingDescription = new Map<
  PDFFamilyFileHandling,
  string
>([
  [PDFFamilyFileHandling.PARENTCHILD_AS_ONE_DOC, 'Parent/Child as 1 Doc'],
  [
    PDFFamilyFileHandling.PARENTCHILD_AS_SEPARATE_DOC,
    'Parent and Children Separate'
  ]
])
export class PDFFamilyFileHandlingEnumConverter extends NumberEnumConverter<PDFFamilyFileHandling> {
  constructor() {
    super(PDFFamilyFileHandling)
  }
}

export enum PDFFileNamingConvention {
  NAME_AFTER_CONTROL_NUMBER = 0,
  ORIGINAL_FILE_NAME = 1
}
export const PDFFileNamingConventionDescription = new Map<
  PDFFileNamingConvention,
  string
>([
  [
    PDFFileNamingConvention.NAME_AFTER_CONTROL_NUMBER,
    'Name After Control Number'
  ],
  [
    PDFFileNamingConvention.ORIGINAL_FILE_NAME,
    'Maintain Original File Name and Folder Structure'
  ]
])
export class PDFFileNamingConventionEnumConverter extends NumberEnumConverter<PDFFileNamingConvention> {
  constructor() {
    super(PDFFileNamingConvention)
  }
}

export enum PaperType {
  REGULAR = 0,
  THREE_HOLE_DRILL = 1,
  TWO_HOLE_DRILL = 2
}
export const PaperTypeDescription = new Map<PaperType, string>([
  [PaperType.REGULAR, 'Regular'],
  [PaperType.THREE_HOLE_DRILL, '3-Hole Drill (Side)'],
  [PaperType.TWO_HOLE_DRILL, '2-Hole Drill (Top)']
])
export class PaperTypeEnumConverter extends NumberEnumConverter<PaperType> {
  constructor() {
    super(PaperType)
  }
}

export enum FamilyFileHandling {
  PARENTCHILD_AS_ONE_DOC = 0,
  PARENTCHILD_AS_SEPARATE_DOC = 1
}
export const FamilyFileHandlingDescription = new Map<
  FamilyFileHandling,
  string
>([
  [FamilyFileHandling.PARENTCHILD_AS_ONE_DOC, 'Parent/Child as 1 Doc'],
  [
    FamilyFileHandling.PARENTCHILD_AS_SEPARATE_DOC,
    'Parent and Children Separate'
  ]
])
export class FamilyFileHandlingEnumConverter extends NumberEnumConverter<FamilyFileHandling> {
  constructor() {
    super(FamilyFileHandling)
  }
}

export enum DocumentSeparator {
  BLANK_BLUE_SLIPSHEET = 0,
  METADATA_SLIPSHEET = 1,
  STAPLE_CLIP = 2
}
export const DocumentSeparatorDescription = new Map<DocumentSeparator, string>([
  [DocumentSeparator.BLANK_BLUE_SLIPSHEET, 'Blank Blue Slipsheet'],
  [
    DocumentSeparator.METADATA_SLIPSHEET,
    'Metadata Slipsheet - Original Filename and Original Path'
  ],
  [DocumentSeparator.STAPLE_CLIP, 'Staple/Clip']
])
export class DocumentSeparatorEnumConverter extends NumberEnumConverter<DocumentSeparator> {
  constructor() {
    super(DocumentSeparator)
  }
}

export enum PaperSide {
  SINGLE_SIDED = 0,
  DOUBLE_SIDED = 1
}
export const PaperSideDescription = new Map<PaperSide, string>([
  [PaperSide.SINGLE_SIDED, 'Single Sided'],
  [PaperSide.DOUBLE_SIDED, 'Double Sided']
])
export class PaperSideEnumConverter extends NumberEnumConverter<PaperSide> {
  constructor() {
    super(PaperSide)
  }
}

export enum PrintSetOption {
  SINGLE_COPY_SET = 0,
  NUMBER_OF_SET = 1
}
export const PrintSetOptionDescription = new Map<PrintSetOption, string>([
  [PrintSetOption.SINGLE_COPY_SET, 'Single Copy Set'],
  [PrintSetOption.NUMBER_OF_SET, '# of Sets']
])
export class PrintSetOptionEnumConverter extends NumberEnumConverter<PrintSetOption> {
  constructor() {
    super(PrintSetOption)
  }
}

export enum BindingType {
  NONE = 0,
  BINDERS = 1,
  FILES_FOLDER = 2,
  REDWELDS = 3,
  VELO = 4,
  GBC = 5
}
export const BindingTypeDescription = new Map<BindingType, string>([
  [BindingType.NONE, 'None'],
  [BindingType.BINDERS, '3-Ring Binders'],
  [BindingType.FILES_FOLDER, 'File Folders'],
  [BindingType.REDWELDS, 'Redwelds'],
  [BindingType.VELO, 'Velo'],
  [BindingType.GBC, 'GBC']
])
export class BindingTypeEnumConverter extends NumberEnumConverter<BindingType> {
  constructor() {
    super(BindingType)
  }
}

export enum BinderSize {
  ONE = 0,
  TWO = 1,
  THREE = 2,
  FOUR = 3,
  FIVE = 4
}
export const BinderSizeDescription = new Map<BinderSize, string>([
  [BinderSize.ONE, '1'],
  [BinderSize.TWO, '2'],
  [BinderSize.THREE, '3'],
  [BinderSize.FOUR, '4'],
  [BinderSize.FIVE, '5']
])
export class BinderSizeEnumConverter extends NumberEnumConverter<BinderSize> {
  constructor() {
    super(BinderSize)
  }
}

export enum BinderColor {
  BLACK = 0,
  WHITE = 1
}
export const BinderColorDescription = new Map<BinderColor, string>([
  [BinderColor.BLACK, 'BLACK'],
  [BinderColor.WHITE, 'WHITE']
])
export class BinderColorEnumConverter extends NumberEnumConverter<BinderColor> {
  constructor() {
    super(BinderColor)
  }
}
