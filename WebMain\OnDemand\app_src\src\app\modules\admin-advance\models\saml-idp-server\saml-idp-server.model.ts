import { IdPProvider } from './saml-idp-server.enum'

export type SamlGridUiTypes =
  | 'venioAdminLevels'
  | 'venioApplicationAccess'
  | 'venioUsers'
  | 'venioUserGroups'
  | 'formData'

export interface SamlGridUiSettingData {
  checkboxLabel: string
  gridLevelLabel: string
  gridUiType: SamlGridUiTypes
  enable: boolean
}

export enum LoadGroupTypes {
  'TOKEN' = 'TOKEN',
  'FILE' = 'FILE',
  'PROFILE' = 'PROFILE'
}

export type SamlGridUiDataType = {
  type: SamlGridUiTypes
  samlIdpSettings: AdminLevelModel[] | Partial<SamlSettingModel>
}

export interface XmlFileParseResponseModel {
  certificate: string
  groupAPIURL: string
  groupExtractionToken: string
  idPSLOUrl: string
  idpIssuer: string
  idpssoUrl: string
  metaDataXML: string
  signatureAlgorithm: string
  signatureValue: string
}

export interface GroupRequestPayload {
  token: string
  link: string
  groupType: 'OKTA_GROUP' | 'APP_GROUP' | 'BUILT_IN'
  tenantId: string
  applicationClientId: string
  applicationObjectId: string
  providerName: IdPProvider // Using the enum here
  clientId: number
  fetchGroupsFromDatabase: boolean
}

export interface GroupResponseModel {
  profile: {
    name: string
  }
  id: string
  type: 'OKTA_GROUP' | 'APP_GROUP' | 'BUILT_IN'
}
export interface AdminLevelModel {
  venioadminlevel: string
  groupsid: string
}
export interface SamlSettingModel {
  apilink: string
  checkEditActiveUsers: boolean
  checkEditAdminLevel: boolean
  checkEditApplicationLevel: boolean
  checkEditProjectLevel: boolean
  enableIDP: boolean
  groupAPIURL: string
  idpUser: string
  metaDataXML: string
  ssO_URL: string
  idpssoUrl: string
  ssoToken: string
  token: string
  venioAdminLevels: AdminLevelModel[]
  venioApplicationAccess: AdminLevelModel[]
  venioUserGroups: AdminLevelModel[]
  venioUsers: AdminLevelModel[]
  providerName: IdPProvider
  clientId: number
  tenantId: string
  applicationClientId: string
  applicationObjectId: string
  idPGroupRequest: GroupRequestPayload
  sessionId: string
}

export interface GroupResponse {
  idPGroups: GroupResponseModel[]
  missingIdPGroups: MissingIdPGroupsInfo
}

export interface MissingIdPGroupsInfo {
  hasMissingIdPGroups: boolean
  message: string
}

export interface IdPGroupInsertRequestModel {
  idPGroups: GroupResponseModel[]
  clientId: number
}

export interface IdPGroupMappingStatus {
  providerName: string
  clientId: number
  hasAnySettingDisabled: boolean
  chkADGroupAdminLevel: boolean
  chkADGroupProjectLevel: boolean
  chkADGroupApplicationLevel: boolean
  chkADGroupActiveUsers: boolean
}
