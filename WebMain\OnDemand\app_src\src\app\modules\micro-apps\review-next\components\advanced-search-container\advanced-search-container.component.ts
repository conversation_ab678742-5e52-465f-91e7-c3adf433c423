import { OverlayModule } from '@angular/cdk/overlay'
import { CommonModule } from '@angular/common'
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  NgModule,
  OnDestroy,
  OnInit
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { MatRadioModule } from '@angular/material/radio'
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome'
import { select, Store as RxStore } from '@ngrx/store'
import { Select, Store } from '@ngxs/store'
import { UserRights } from '@root/helpers/user-rights'
import { searchDuplicateOption } from '@root/modules/application-nav/store/selectors/application-nav.selectors'
import {
  IframeMessengerService,
  MessageType
} from '@root/modules/micro-apps/config/iframe-messenger.service'
import { AppIdentitiesTypes } from '@root/modules/micro-apps/models/app-identities-types'
import { SharedDirectivesModule } from '@shared/directives/shared-directives.module'
import { SearchQueryBuilderModule } from '@shared/search-builder/search-query-builder.module'
import { TriggerResetAdvancedSearchUiAction } from '@shared/search-builder/sotre/query-builder.actions'
import { DebounceTimer } from '@shared/utils'
import { StartupStateSelector } from '@stores/selectors'
import { combineLatest, Observable, Subject } from 'rxjs'
import { filter, takeUntil } from 'rxjs/operators'
import {
  getSearchDuplicationOption,
  SearchDupOptions,
  SearchDupOptionUtils
} from './search-duplication-data'

@Component({
  selector: 'app-advanced-search-container',
  templateUrl: './advanced-search-container.component.html',
  styleUrls: ['./advanced-search-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdvancedSearchContainerComponent implements OnDestroy, OnInit {
  public readonly toDestroy$ = new Subject<void>()

  showAdvanceFilterUi = true

  showDuplicationOption = false

  duplicationOptions = getSearchDuplicationOption.slice()

  selectedDuplicateOption: SearchDupOptions

  defaultDuplicateOption: SearchDupOptions

  readonly searchUiRight = UserRights.ALLOW_SEARCH

  searchTerm = ''

  includeFamily = false

  // The selected command event from the child component which may be any type of command event
  private readonly selectedCommandEvent: any

  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_SHOW_ALL_HITS_IN_SELECTED_SCOPE
    )
  )
  allowToShowAllHitsInSelectedScope$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_SHOW_ONLY_ONE_INSTANCE_PER_CUSTODIAN_IN_SELECTED_SCOPE
    )
  )
  allowToShowOnlyOneInstancePerCustodianInSelectedScope$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_HIDE_PROJECT_LEVEL_DUPLICATES
    )
  )
  allowToHideProjectLevelDuplicates$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_HIDE_CUSTODIAN_LEVEL_DUPLICATES
    )
  )
  allowToHideCustodianLevelDuplicates$: Observable<boolean>

  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_SHOW_ONLY_ONE_INSTANCE_IN_SELECTED_SCOPE
    )
  )
  allowToShowOnlyOneInstanceInSelectedScope$: Observable<boolean>

  constructor(
    private iframeMessengerService: IframeMessengerService,
    private changeDetectorRef: ChangeDetectorRef,
    private store: Store,
    private rxStore: RxStore,
    @Inject('CHILD_DATA')
    public childData: unknown
  ) {
    this.selectedCommandEvent = this.childData['selectedCommandEvent']
  }

  ngOnInit(): void {
    this.#selectProjectSearchDuplicateOption()
    this.setPermittedDuplicateOptions()
    this.#setSearchSettings()
    this.#setDynamicFolderSearchSettings()
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  setQuery(query: string): void {
    this.changeDetectorRef.markForCheck()
    // Only update the search term if the UI is shown
    // This prevents the reset action from overwriting the search term
    // that was just used for searching
    if (this.showAdvanceFilterUi) {
      this.searchTerm = query
    }
  }

  #selectProjectSearchDuplicateOption(): void {
    this.rxStore
      .pipe(
        select(searchDuplicateOption),
        filter(
          (option) => typeof option !== 'undefined' && typeof option !== null
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((searchOption: SearchDupOptions) => {
        this.defaultDuplicateOption = searchOption
      })
  }

  private setPermittedDuplicateOptions() {
    combineLatest([
      this.allowToShowAllHitsInSelectedScope$,
      this.allowToShowOnlyOneInstancePerCustodianInSelectedScope$,
      this.allowToHideProjectLevelDuplicates$,
      this.allowToHideCustodianLevelDuplicates$,
      this.allowToShowOnlyOneInstanceInSelectedScope$
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(
        ([
          allowToShowAllHitsInSelectedScope,
          allowToShowOnlyOneInstancePerCustodianInSelectedScope,
          allowToHideProjectLevelDuplicates,
          allowToHideCustodianLevelDuplicates,
          allowToShowOnlyOneInstanceInSelectedScope
        ]) => {
          if (!allowToShowAllHitsInSelectedScope) {
            const index = this.duplicationOptions.findIndex((s) => s.value == 1)
            if (index > -1 && this.defaultDuplicateOption !== 1) {
              this.duplicationOptions.splice(index, 1)
            }
          }
          if (!allowToShowOnlyOneInstancePerCustodianInSelectedScope) {
            const index = this.duplicationOptions.findIndex((s) => s.value == 2)
            if (index > -1 && this.defaultDuplicateOption !== 2) {
              this.duplicationOptions.splice(index, 1)
            }
          }
          if (!allowToHideProjectLevelDuplicates) {
            const index = this.duplicationOptions.findIndex((s) => s.value == 3)
            if (index > -1 && this.defaultDuplicateOption !== 3) {
              this.duplicationOptions.splice(index, 1)
            }
          }
          if (!allowToHideCustodianLevelDuplicates) {
            const index = this.duplicationOptions.findIndex((s) => s.value == 4)
            if (index > -1 && this.defaultDuplicateOption !== 4) {
              this.duplicationOptions.splice(index, 1)
            }
          }
          if (!allowToShowOnlyOneInstanceInSelectedScope) {
            const index = this.duplicationOptions.findIndex((s) => s.value == 0)
            if (index > -1 && this.defaultDuplicateOption !== 0) {
              this.duplicationOptions.splice(index, 1)
            }
          }
        }
      )
  }

  #setSearchSettings(): void {
    const isDynamicFolderOption =
      this.selectedCommandEvent?.['selectedFolder']?.['searchSettings']
    // If the advanced search launched from a dynamic folder, we use the search settings from the folder
    if (isDynamicFolderOption) return

    // If it was normal search, we use the search settings whatever the user selected
    this.changeDetectorRef.markForCheck()
    this.selectedDuplicateOption = this.childData['searchDuplicateOption']
    this.includeFamily = this.childData['includePC']
  }

  /**
   * Updates the search settings based on the selected folder's search settings.
   * This method ensures that any changes in the search settings of the dynamically
   * selected folder within a child micro app are reflected accurately.
   */
  #setDynamicFolderSearchSettings(): void {
    this.changeDetectorRef.markForCheck()
    const settings =
      this.selectedCommandEvent?.['selectedFolder']?.['searchSettings']

    if (!settings) return

    const { searchDuplicateOption, includeParentChild } = settings

    this.includeFamily = includeParentChild
    this.selectedDuplicateOption = SearchDupOptionUtils.getNumberFromString(
      searchDuplicateOption
    )
  }

  search(): void {
    this.changeDetectorRef.markForCheck()

    // First hide the UI before sending the search message
    // This ensures the UI is hidden before any reset actions are triggered
    this.showAdvanceFilterUi = false

    // Send the search message with the current search term
    this.#sendSearchChangeMessage(true)

    // After the search is sent, reset the advanced search UI
    // The clearAdvancedSearch method will preserve the search term
    // since showAdvanceFilterUi is now false
    this.clearAdvancedSearch()
  }

  clearAdvancedSearch(): void {
    // Store the current search term before resetting the UI
    // This ensures we don't lose the search term that was just used
    const currentSearchTerm = this.searchTerm

    // Reset the UI first
    this.#notifyAdvancedSearchUiReset()

    // Only clear the search term if the UI is shown
    // This prevents clearing the search term that was just used for searching
    if (this.showAdvanceFilterUi) {
      this.searchTerm = ''
    } else {
      // If the UI is hidden (after search), keep the search term
      this.searchTerm = currentSearchTerm
    }
  }

  /**
   * The advanced search UI has lazy chunks that are loaded lazily.
   * When all are loaded, we need to tell the view to update the changes.
   * @param areAllLoaded - whether all the lazy chunks are loaded
   */
  @DebounceTimer(500)
  allAdvancedChunksLoaded(areAllLoaded: boolean): void {
    this.changeDetectorRef.markForCheck()
    if (!areAllLoaded) return
    this.changeDetectorRef.detectChanges()
  }

  /**
   * Handles changes to command events from child components.
   * If the changes affect a dynamic folder expression setting,
   * this method updates the corresponding search expression for the selected command event.
   * The updated expression is then returned.
   * This allows the changes to be propagated back to the child component.
   * @returns {Object} - updated search expression command event
   */
  #getUpdatedSearchExpressionCommandEvent(): any {
    // The object must have a selected command event to update the search expression
    if (!this.searchTerm.trim().length || !this.selectedCommandEvent)
      return undefined

    // Create the updated search expression command event
    return {
      ...this.selectedCommandEvent,
      selectedFolder: {
        ...this.selectedCommandEvent?.['selectedFolder'],
        searchSettings: {
          ...this.selectedCommandEvent?.['selectedFolder']?.['searchSettings'],
          includeParentChild: this.includeFamily,
          searchDuplicateOption: SearchDupOptionUtils.getStringFromNumber(
            this.selectedDuplicateOption
          ),
          searchExpression: {
            ...this.selectedCommandEvent?.['selectedFolder']?.[
              'searchSettings'
            ]?.['searchExpression'],
            expression: this.searchTerm
          }
        }
      }
    }
  }

  #sendSearchChangeMessage(isAdvancedSearch = false): void {
    const selectedCommandEvent = this.#getUpdatedSearchExpressionCommandEvent()
    this.iframeMessengerService.sendMessage({
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.SEARCH_CHANGE,
        content: {
          includeFamily: this.includeFamily,
          searchDuplicateOption: this.selectedDuplicateOption,
          searchTerm: this.searchTerm,
          triggerSearch: !!this.searchTerm?.trim(),
          // Whether the search is from advanced search
          isAdvancedSearch,
          // If a child element has a selected command event that is being propagated up to the parent,
          // we need to pass this event along to the child by updating
          // its folder settings with the new search expression.
          selectedCommandEvent
        }
      }
    })
  }

  #notifyAdvancedSearchUiReset(): void {
    this.store.dispatch(new TriggerResetAdvancedSearchUiAction(true))
  }
}

@NgModule({
  imports: [
    SearchQueryBuilderModule,
    SharedDirectivesModule,
    CommonModule,
    FontAwesomeModule,
    OverlayModule,
    FormsModule,
    MatRadioModule
  ],
  declarations: [AdvancedSearchContainerComponent]
})
export class LazyAdvancedSearchContainerModule {}
