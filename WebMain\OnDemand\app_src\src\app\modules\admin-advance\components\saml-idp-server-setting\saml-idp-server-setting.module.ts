import { SamlIdpServerFacade } from '@admin-advance/store'
import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatCheckboxModule } from '@angular/material/checkbox'
import { MatDialogModule } from '@angular/material/dialog'
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'
import { MatRadioModule } from '@angular/material/radio'
import { MatSelectModule } from '@angular/material/select'
import { MatTooltipModule } from '@angular/material/tooltip'
import { RouterModule } from '@angular/router'
import { UiButtonModule } from '@shared/components/ui-button/ui-button.component'
import { RouteBreadcrumbModule } from '@shared/route-breadcrumb/route-breadcrumb.module'
import {
  DxDataGridModule,
  DxLoadIndicatorModule,
  DxLoadPanelModule
} from 'devextreme-angular'
import { SamlCommonGridComponent } from './child/saml-common-grid/saml-common-grid.component'
import { SamlIdpServerSettingFormComponent } from './child/saml-idp-server-setting-form/saml-idp-server-setting-form.component'
import { SamlIdpServerComponentService } from './saml-idp-server-component.service'
import { SamlIdpServerSettingComponent } from './saml-idp-server-setting.component'

@NgModule({
  declarations: [
    SamlIdpServerSettingComponent,
    SamlCommonGridComponent,
    SamlIdpServerSettingFormComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild([
      { path: '', component: SamlIdpServerSettingComponent }
    ]),
    RouteBreadcrumbModule,
    MatCheckboxModule,
    DxDataGridModule,
    FormsModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatRadioModule,
    UiButtonModule,
    DxLoadIndicatorModule,
    DxLoadPanelModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatDialogModule
  ],
  providers: [SamlIdpServerComponentService, SamlIdpServerFacade]
})
export class SamlIdpServerSettingModule {}
