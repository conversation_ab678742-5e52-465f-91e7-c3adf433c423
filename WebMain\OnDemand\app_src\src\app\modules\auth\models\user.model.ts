import { JsonObject, JsonProperty } from 'json2typescript'

export interface UserModel {
  UserId: number
  ModuleString: string
  ClientId: number
  UserName: string
  DocShareUserRole: string
  WebServiceURL: string
  IsIdPlogin: boolean
  IsExternalUserEnabled: boolean
  ModuleName: string
  ENABLE_MIXED_MODE: boolean
  UserTypeMixedMode: string
  IsADUser: boolean
  IsIdPUser?: boolean
  RightList: string
  SettingsInfo: string
  ProductionFlow: string
  ProjectId: string
  IsClientTrial: boolean
  TrialPeriod: number
  ThirdPartyBillingEnabled: boolean
  LoginTokenId: string
  ActiveSessionLastChecked: Date
  UserFullName: string
  Admin: number
  LOGINDETAILSPKID: number
  VenioAdminLevel: string
  UserRole: string
  AuthToken: string
  Script: string
  HasOnDemandAccess: boolean
  ReviewOnly: boolean
  PasswordExpired: boolean
  Remark: string
  EmailAddress: string
  NotifyForPasswordChange: boolean
  PasswordExpiryInDays: number
}

@JsonObject('ClientAuth')
export class Client {
  @JsonProperty('ClientId', Number, true) clientId: number = null

  @JsonProperty('ClientName', String, true) clientName: string = null

  @JsonProperty('Address', String, true) address: string = null

  @JsonProperty('ContactPersonLastName', String, true)
  contactPersonLastName: string = null

  @JsonProperty('Email', String, true) email: string = null

  @JsonProperty('Phone', String, true) phone: string = null

  @JsonProperty('Mobile', String, true) mobile: string = null

  @JsonProperty('Fax', String, true) fax: string = null

  @JsonProperty('OnDemandFlag', Boolean, true) onDemandFlag: boolean = null

  @JsonProperty('ClientFileRepositoryRootFolderName', String, true)
  clientFileRepositoryRootFolderName: string = null

  @JsonProperty('IsClientTrial', Boolean, true) isClientTrial: boolean = null

  @JsonProperty('AccountNo', String, true) accountNo: string = null

  @JsonProperty('Memo', String, true) memo: string = null

  @JsonProperty('IsInternalClient', Boolean, true)
  isInternalClient: boolean = null

  @JsonProperty('AutoSendCaseCompletedNotification', Boolean, true)
  autoSendCaseCompletedNotification: boolean = null

  @JsonProperty('ThirdPartyBilling', Boolean, true)
  thirdPartyBilling: boolean = null
}

@JsonObject('UserAuth')
export class User {
  @JsonProperty('ADUserGUID', String, true) adUserGUID: string = null

  @JsonProperty('ADUserSID', String, true) adUserSID: string = null

  @JsonProperty('Address', String, true) address: string = null

  @JsonProperty('ClientId', Number, true) clientId: number = null

  @JsonProperty('Email', String, true) email: string = null

  @JsonProperty('FailedLoginAttempts', Number, true)
  failedLoginAttempts: number = null

  @JsonProperty('Fax', String, true) fax: string = null

  @JsonProperty('ForceUserToChangePassword', Boolean, true)
  forceUserToChangePassword: boolean = null

  @JsonProperty('FullName', String, true) fullName: string = null

  @JsonProperty('GlobalRoleId', Number, true) globalRoleId: number = null

  @JsonProperty('GlobalRoleName', String, true) globalRoleName: string = null

  @JsonProperty('HasDesktopAccess', Boolean, true)
  hasDesktopAccess: boolean = null

  @JsonProperty('HasOnDemandAccess', Boolean, true)
  hasOnDemandAccess: boolean = null

  @JsonProperty('HasReviewAccess', Boolean, true)
  hasReviewAccess: boolean = null

  @JsonProperty('HasTouchAccess', Boolean, true) hasTouchAccess: boolean = null

  @JsonProperty('HasWebECAAccess', Boolean, true)
  hasWebECAAccess: boolean = null

  @JsonProperty('IsADUser', Boolean, true) isADUser: boolean = null

  @JsonProperty('IsUserAdmin', Boolean, true) isUserAdmin: boolean = null

  @JsonProperty('IsUserApproved', Boolean, true) isUserApproved: boolean = null

  @JsonProperty('IsUserDeactivated', Boolean, true)
  isUserDeactivated: boolean = null

  @JsonProperty('IsUserLocked', Boolean, true) isUserLocked: boolean = null

  @JsonProperty('Mobile', String, true) mobile: string = null

  @JsonProperty('Phone', String, true) phone: string = null

  @JsonProperty('ShowNotification', Boolean, true)
  showNotification: boolean = null

  @JsonProperty('UserID', Number, true) userId: number = null

  @JsonProperty('UserLockType', String, true) userLockType: string = null

  @JsonProperty('UserLockValidUpto', String, true)
  userLockValidUpto: string = null

  @JsonProperty('UserName', String, true) userName: string = null

  @JsonProperty('UserRole', String, true) userRole: string = null

  @JsonProperty('EULAAcceptance', Boolean, true) eulaAcceptance: boolean = null

  @JsonProperty('ActiveSessionId', String, true) activeSessionId: string = null

  @JsonProperty('ClientName', String, true) clientName: string = null

  @JsonProperty('DisablePasswordReset', Boolean, true)
  disablePasswordReset: boolean = null

  @JsonProperty('PasswordExpiryInDays', Number, true)
  passwordExpiryInDays: number = null

  @JsonProperty('NotifyForPasswordChange', Boolean, true)
  notifyForPasswordChange: boolean = null
}

export interface UsersModel {
  userID?: number
  fullName?: string
  userName?: string
  address?: string
  email?: string
  phone?: string
  mobile?: string
  fax?: string
  globalRoleId?: number
  isUserLocked?: boolean
  isUserDeactivated?: boolean
  failedLoginAttempts?: number
  userLockValidUpto?: string
  userLockType?: string
  forceUserToChangePassword?: boolean
  hasDesktopAccess?: boolean
  hasWebECAAccess?: boolean
  hasReviewAccess?: boolean
  hasTouchAccess?: boolean
  isADUser?: boolean
  adUserSID?: string
  adUserGUID?: string
  isIdPUser?: boolean
  hasOnDemandAccess?: boolean
  clientId?: number
  showNotification?: boolean
  isUserApproved?: boolean
  userRole?: string
  globalRoleName?: string
  isUserAdmin?: boolean
  userCaseAssignmentModel?: {
    userId?: number
    caseGroupAssignedInfoList?: Array<{
      projectId?: number
      projectName?: string
      clientMatterNumber?: string
      caseName?: string
      assigned?: boolean
      groupId?: number
      availableGroups?: Array<{
        groupId?: number
        groupName?: string
      }>
    }>
  }
  eulaAcceptance?: boolean
  activeSessionId?: string
  disablePasswordReset?: boolean
}
