import { DragDropModule } from '@angular/cdk/drag-drop'
import { OverlayModule } from '@angular/cdk/overlay'
import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatAutocompleteModule } from '@angular/material/autocomplete'
import { MatDatepickerModule } from '@angular/material/datepicker'
import { MatDialogModule } from '@angular/material/dialog'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatMenuModule } from '@angular/material/menu'
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'
import { MatSelectModule } from '@angular/material/select'
import { MatTooltipModule } from '@angular/material/tooltip'
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome'
import { SharedDirectivesModule } from '@shared/directives/shared-directives.module'
import { SearchDuplicateOptionModule } from '@shared/search-builder/child/search-duplicate-option/search-duplicate-option/search-duplicate-option.module'
import { SearchQueryBuilderModule } from '@shared/search-builder/search-query-builder.module'
import { GridsterModule } from 'angular-gridster2'
import {
  DevExtremeModule,
  DxChartModule,
  DxDataGridModule,
  DxFunnelModule,
  DxLoadIndicatorModule,
  DxLoadPanelModule,
  DxPieChartModule
} from 'devextreme-angular'
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search'
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar'
import { WidgetSelectorComponent } from './chhild/widget-selector.component'
import { DynamicWidgetComponent } from './dynamic-widget/dynamic-widget.component'
import { MainControlsWidgetComponent } from './main-controls/main-controls-widget.component'
import { MediaProcessingStatusDialogComponent } from './media-processing-status-dialog/media-processing-status-dialog/media-processing-status-dialog.component'
import { MultiSearchStatusComponent } from './multi-search-status/multi-search-status.component'
import { NetworkGraphComponent } from './social-network-widget/n-graph.component'
import { StackedFunnelWidgetComponent } from './stacked-funnel-widget/stacked-funnel-widget.component'
import { SummaryWidgetComponent } from './summary-widget/summary-widget.component'
import { TimelineWidgetComponent } from './timeline-widget/timeline-widget.component'
import { WidgetControlsComponent } from './widget-controls/widget-controls.component'
import { WidgetExistPipe } from './widget-exist.pipe'
import { WidgetsComponent } from './widgets.component'

@NgModule({
  declarations: [
    WidgetControlsComponent,
    MainControlsWidgetComponent,
    TimelineWidgetComponent,
    WidgetsComponent,
    WidgetSelectorComponent,
    WidgetExistPipe,
    NetworkGraphComponent,
    StackedFunnelWidgetComponent,
    SummaryWidgetComponent,
    MultiSearchStatusComponent,
    DynamicWidgetComponent,
    MediaProcessingStatusDialogComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    DxChartModule,
    MatTooltipModule,
    GridsterModule,
    DxLoadIndicatorModule,
    DxLoadPanelModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    PerfectScrollbarModule,
    DragDropModule,
    MatExpansionModule,
    MatSelectModule,
    NgxMatSelectSearchModule,
    SharedDirectivesModule,
    OverlayModule,
    DxFunnelModule,
    MatMenuModule,
    DxDataGridModule,
    DxPieChartModule,
    MatAutocompleteModule,
    SearchQueryBuilderModule,
    FontAwesomeModule,
    SearchDuplicateOptionModule,
    DevExtremeModule
  ]
})
export class AnalyzeWidgetsModule {}
