import {
  <PERSON><PERSON>iew<PERSON>nit,
  ChangeDetector<PERSON><PERSON>,
  <PERSON>mponent,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild
} from '@angular/core'
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators
} from '@angular/forms'
import { MatAccordion } from '@angular/material/expansion'
import { ActivatedRoute, Router } from '@angular/router'
import { ConfigService } from '@config/services/config.service'
import { ConfigState } from '@config/store/reducers'
import { getThemeClient } from '@config/store/selectors'
import { select, Store } from '@ngrx/store'
import { Select, Store as XSStore } from '@ngxs/store'
import { UserRights } from '@root/helpers/user-rights'
import { ApiSettingPrioritiesModel } from '@root/modules/launchpad/models/api-setting-priorities.model'
import { ControlNames } from '@root/modules/launchpad/pages/case-setting/components/edai-eca/edai-eca.model'
import { CaseSettingFacade } from '@root/modules/launchpad/store/case-setting/case-setting.facade'
import { CaseSettingsTagManagementService } from '@root/services/case-settings-tag-management.service'
import { animateHeight } from '@shared/animation'
import { DebounceTimer } from '@shared/utils'
import {
  FetchGlobalGroupsAction,
  FetchProjectDetails,
  FetchProjectList
} from '@stores/actions'
import { StartupStateSelector } from '@stores/selectors'
import { DxDataGridComponent } from 'devextreme-angular'
import _, { cloneDeep, Dictionary } from 'lodash'
import moment from 'moment'
import { BsModalService } from 'ngx-bootstrap/modal'
import { ToastrService } from 'ngx-toastr'
import { combineLatest, Observable, Subject } from 'rxjs'
import { debounceTime, filter, map, switchMap, takeUntil } from 'rxjs/operators'
import { ConfirmationDialogComponent } from 'src/app/modules/launchpad/components/confirmation-dialog/confirmation-dialog.component'
import { NavigateToAction } from 'src/app/modules/shared/store/actions/shared.actions'
import { getControlSetting } from '../../../config/store/selectors'
import {
  ADGroupMapping,
  AdvanceOcrOptionLanguages,
  DateRestriction,
  DedupingAlgorithm,
  DisclaimerList,
  EmbeddedFilterType,
  ExcludeJobsDic,
  FileTypeTimeOut,
  HashField,
  ImageFileExtension,
  MetaIndexingFields,
  NativeFileTypeList,
  OCRExtensions,
  OCRExtensionType,
  ProjectSetting,
  ProjectSetupInfo,
  ProjectTemplateModel,
  SplitOptions,
  TiffFileTypesPageLimit,
  TranscribeAccessKeys,
  TranscribeFiles,
  TranscribeSupportedFiles
} from '../../models/case-template-settings.model'
import {
  AutoTiffOcrFileTypeModel,
  CaseQueryParams,
  CaseSettingFormValueType,
  CaseSettingTypes,
  FullTextOptionsModel,
  InternalDomainModel,
  SearchDuplicateOptionModel
} from '../../models/case.model'
import {
  INITIAL_UI_ITEMS,
  SlipSheetContentModel
} from '../../models/slip-sheet.model'
import { TiffColorOption } from '../../models/tiff-color-option.model'
import {
  CreateCase,
  CreateCaseSuccess,
  CreateCaseTemplate,
  CreateCaseTemplateSuccess,
  FetchCaseInfoEdit,
  FetchCaseTemplateSettings,
  FetchProjectTemplates,
  FetchTemplateInfoEdit,
  GetDefaultProjectTemplate,
  GetSupportedFileTypesForTranscribing,
  GetTimeZones,
  GetTranscribeAccessKeys,
  ResetCaseInfoEdit,
  ResetTemplateInfoEdit,
  UpdateCase,
  UpdateCaseSuccess,
  UpdateCaseTemplate,
  UpdateCaseTemplateSuccess
} from '../../store/actions'
import {
  CaseSettingStateResetAction,
  CheckADGroupsPrjLevel,
  CheckIDPGroupsPrjLevelAction,
  IsCaseEditingAction,
  NativeFileOptionHasEcaEnabledAction
} from '../../store/case-setting/case-setting.actions'
import { CaseSettingSelector } from '../../store/case-setting/case-setting.selectors'
import { CaseState } from '../../store/reducers/case.reducers'
import {
  getAccessKeys,
  getCaseInfoEdit,
  getErrorMessage,
  getProjectTemplateList,
  getSupportedFileTypesForTranscribing,
  getTimeZones,
  newCase,
  newCaseTemplate,
  projectTemplateSettings,
  updateCase,
  updateCaseTemplateInfo
} from '../../store/selectors/case.selectors'

@Component({
  selector: 'app-case-setting',
  templateUrl: './case-setting.component.html',
  styleUrls: [
    './case-setting.component.scss',
    '../../../../app.component.scss'
  ],
  animations: [animateHeight]
})
export class CaseSettingComponent implements OnInit, AfterViewInit, OnDestroy {
  private createCaseModel: ProjectSetupInfo = new ProjectSetupInfo()

  caseForm: FormGroup

  private unsubscribed$ = new Subject<void>()

  timeZoneList: any

  selectedTimeZone: any

  showSpinner = false

  client: string

  projectTemplates: ProjectTemplateModel[]

  // ref to UI
  colorOption = TiffColorOption

  templateName: string

  templateNote: string

  /**
   * OCR languages: bind the retrieved data to display in the view, when user selects, we post selected values to the API
   */
  ocrLanguages: AdvanceOcrOptionLanguages[]

  /*
   * search options
   */
  searchOptions: SearchDuplicateOptionModel[] = []

  hashOptions: HashField[]

  /**
   *  query params for listing the value with project id
   */
  qParams: CaseQueryParams

  /**
   * case is for edit
   */
  isEdit = false

  /**
   * Control setting flag to enable/disable entity extraction
   */
  enableEntityExtraction = false

  /**
   * Control setting value to hold ingestion engine
   */
  isLegacyIngestionEngine = false

  /**
   * Control setting flag to enable/disable index only case
   */
  isTOALicensed = false

  isFullFeatureLicensed = false

  isNativeAutoPrefetchEnabled = false

  isNativeFileOptionOnlyIndex = false

  transcribeSupportedFiles: TranscribeSupportedFiles[]

  transcribingEngines: string[]

  //create array of string of supported file names only
  //output array to store the files type checked on ui. Used for sending data to backend
  supportedFilesArray: TranscribeFiles[] = []

  transcribeAccessKeys: TranscribeAccessKeys[]

  accessKeyMissingMessage = ''

  selectedEngine: string //string

  // Save the last selected values in 'Convert Image Option'
  LastDefaultTiffColorOption: TiffColorOption

  LastAutoGenerateTiff = false

  @ViewChild('hashFieldGrid', { static: false })
  hashFieldGrid: DxDataGridComponent

  selectedHashFieldRows: any //HashField[] //number[] = []

  SecondaryHash = 'MD5'

  selectedHashFieldIdOnly: number[] = []

  @ViewChild('ocrFileTypeGrid', { static: false })
  ocrFileTypeGrid: DxDataGridComponent

  @ViewChild('matAccordion')
  matAccordion: MatAccordion

  ocrFileOptions: OCRExtensions[]

  selectedOCRFileType: number[]

  selectedOCRExtensionType: OCRExtensionType[] = []

  disableOCROPtions = false

  /**
   * Project data to pass into child/lazy component and patch form data there.
   */
  formData: Partial<{ [key in CaseSettingTypes]: any }> = {}

  filetypePageLimitList: TiffFileTypesPageLimit[] = []

  pageLimitdb: Array<any> = [
    // 'Max Pages',
    // 'All Pages',
    10, 100, 200, 300, 400, 500, 1000, 2000
  ]

  isTextHidden = false

  isFieldHidden = true

  disableImageOptions = false

  /**
   * Control setting flag to enable/disable pdf imaging
   */
  enablePDFImaging = false

  @ViewChild('fileTypeGrid', { static: false })
  fileTypeGrid: DxDataGridComponent

  placeholderTextTimeout = 'Default'

  //featureFlagCaseMigration = false

  isValidDataFromChild = true

  //metadata indexing
  @ViewChild('indexMetaGrid', { static: false })
  indexMetaGrid: DxDataGridComponent

  indexDataSource: MetaIndexingFields[]

  selectedMetaDataField: any //number[]

  disclaimerListData: DisclaimerList[] = []

  stopWords: string

  dateFilterListData: DateRestriction[] = []

  imagefileExtensionListData: ImageFileExtension[] = []

  imagefileExtensionListDataDic: Dictionary<ImageFileExtension> = {}

  _isProjectTemplateLoading: boolean

  isLoadingTagTree = true // indicates if the tag tree is still loading even after template is loaded

  get isProjectTemplateLoading(): boolean {
    return this._isProjectTemplateLoading || this.isLoadingTagTree
  }

  ingestionAdvancedsetingJobs: ExcludeJobsDic[] = []

  adGroupMapListData: ADGroupMapping[] = []

  showADSettings: boolean

  ingestionFileTypeTimeOut: FileTypeTimeOut[] = []

  originalngestionFileTypeTimeOut: FileTypeTimeOut[] = []

  idpGroupMapListData: ADGroupMapping[] = []

  showIDPSettings: boolean

  showSocialMedia = false

  /*Create Template */
  isTemplateCreateModule = false

  //Flag to identify edit template mode
  isTemplateEditModule = false

  //Case or Case Template title will be displayed in UI
  titleText = ''

  //flag to identify if page is template mode or case mode
  isTemplateRelated = false

  //create or update button name for Case or Template
  buttonName = 'Case'

  /**
   * Select the right to 'allowToCreateEditProjectTemplate' from the store.
   */
  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_CREATE_EDIT_PROJECT_TEMPLATE
    )
  )
  //right to restrict user from opening template create/edit operation
  allowCreateEditCaseTemplate$: Observable<boolean>

  //to restrict user from updating DefaultProjectTemplate
  isDefaultTemplateEdit = false

  //flag to identigy if user has right to access template page
  isAccessDeniedTemplate = false

  nativeFileHandling = true

  readonly pageOptions: Array<any> = [1, 10, 100, 500, 1000, 2000]

  internalDomains: InternalDomainModel[] = []

  selectedInternalDomains: string[] = []

  /**
   * Flag that determines if EDAI ECA feature is enabled at system level
   */
  public enableEdaiEcaFeature = false

  public get isEdaiECAFormValid(): boolean {
    if (!this.caseForm) return false

    const controls = this.caseForm.controls

    const hasNoControls = [
      ControlNames.isEnabled,
      ControlNames.background,
      ControlNames.relevantDesc,
      ControlNames.nonRelevantDesc
    ].every((controlName) => !controls[controlName])

    // Since the controls are being added during component creation,
    // if the ECA feature is disabled, then the controls will not be added.
    // So, we need to check if the controls are present before checking the values.
    if (hasNoControls) return true

    const isEnabled = controls[ControlNames.isEnabled].value
    const background = controls[ControlNames.background].value || ''
    const relevantDesc = controls[ControlNames.relevantDesc].value || ''
    const nonRelevantDesc = controls[ControlNames.nonRelevantDesc].value || ''
    // IF EDAI ECA feature is enabled and the isEnabled flag is true, then check if the text areas are empty.
    // All text areas must have some text to be valid when EDAI ECA feature is enabled and isEnabled flag is true.
    return isEnabled && this.enableEdaiEcaFeature
      ? background.trim() && relevantDesc.trim() && nonRelevantDesc.trim()
      : true
  }

  constructor(
    private fb: FormBuilder,
    private configStore: Store<ConfigState>,
    private modalService: BsModalService,
    private store: Store<CaseState>,
    private xsStore: XSStore,
    private route: ActivatedRoute,
    private ref: ChangeDetectorRef,
    private service: ConfigService,
    private toast: ToastrService,
    private router: Router,
    private caseSettingFacade: CaseSettingFacade,
    private caseSettingsTagMgmtService: CaseSettingsTagManagementService
  ) {
    this.store.dispatch(new GetTimeZones())

    this.isTemplateCreateModule =
      router.routerState.snapshot.url.includes('/create/template')

    this.isTemplateEditModule =
      router.routerState.snapshot.url.includes('/edit/template')

    this.isTemplateRelated =
      router.routerState.snapshot.url.includes('/template')
    this.checkTemplatePermission()
    if (this.isTemplateRelated)
      this.store.dispatch(new GetDefaultProjectTemplate())
    if (!this.isTemplateRelated)
      this.store.dispatch(new FetchProjectTemplates())

    this.qParams = route.snapshot.queryParams as CaseQueryParams

    if (this.qParams.projectId) {
      this.isEdit = true
      this.store.dispatch(new ResetCaseInfoEdit())
      this.store.dispatch(new FetchCaseInfoEdit(this.qParams.projectId))
      this.xsStore.dispatch(new IsCaseEditingAction(this.qParams.projectId > 0))
    }
    if (this.qParams.templateId) {
      this.isEdit = true
      this.store.dispatch(new ResetTemplateInfoEdit())
      this.store.dispatch(new FetchTemplateInfoEdit(this.qParams.templateId))
      //in template edit case,controls should not be disabled.so sending false
      this.xsStore.dispatch(new IsCaseEditingAction(false))
    }
  }

  private storeApiPrioritySettingsData(payload: {
    ProjectLevelFileIdentificationPriority: ApiSettingPrioritiesModel[]
    ExtractorOperationPriorities: ApiSettingPrioritiesModel[]
  }): void {
    this.caseSettingFacade.storeApiPrioritySettingsFromTemplateData(payload)
  }

  private setModifiedApiPriorityData(): void {
    combineLatest([
      this.caseSettingFacade.selectExtractorOperationPriorities$,
      this.caseSettingFacade.selectFileIdentificationPriority$
    ])
      .pipe(
        debounceTime(100),
        filter((data) => !!data),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(
        ([
          ExtractorOperationPriorities,
          ProjectLevelFileIdentificationPriority
        ]) => {
          this.createCaseModel = {
            ...this.createCaseModel,
            MultipleApiSettings: {
              ExtractorOperationPriorities,
              ProjectLevelFileIdentificationPriority
            }
          } as ProjectSetupInfo
        }
      )
  }

  /**
   * Handle template values to pass and patch on children lazy components.
   * @param p
   */
  private readonly handleProjectTemplateDataPatchToChildren = (): void => {
    const obs = [
      this.store.pipe(
        select(getCaseInfoEdit),
        map((res) => res?.data)
      ),
      this.store.pipe(select(projectTemplateSettings))
    ]
    obs[this.isEdit ? 0 : 1]
      .pipe(
        filter((p) => !!p),
        debounceTime(1000),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((p) => {
        this.formData = {
          FILE_TYPE_SETTING: {
            fileTypeAdvanceFilterOpt: p['fileTypeAdvanceFilterOpt'],
            FileTypeFilterInclude: p['FileTypeFilterInclude'],
            FileTypeFilters: p['FileTypeFilters']
          },
          IMAGE_FILE_TYPE_SETTING: {
            NativeExcelImagingOptions: p['NativeExcelImagingOptions'] || null,
            dicTiffFileTypeSetting: p['dicTiffFileTypeSetting'] || null,
            NativeWordImagingOptions: p['NativeWordImagingOptions'] || null,
            OutlookImagingOptions: p['OutlookImagingOptions'] || null,
            NativePowerpointImagingOptions:
              p['NativePowerpointImagingOptions'] || null,
            FallbackToStellent: p['FallbackToStellent']
          },
          FULL_TEXT_OPTIONS: {
            ShowMetaInFullText: p['ShowMetaInFullText'],
            fullTextAdvanceOption: p['fullTextAdvanceOption'],
            AutoTiffOCRFileTypes: p['AutoTiffOCRFileTypes'],
            ProjectSettings: p['ProjectSettings'],
            ExtractTextFromMasterView: p['ExtractTextFromMasterView']
          } as FullTextOptionsModel,
          SLIP_SHEET: {
            ...p['SlipSheetSettings'],
            FileTypeFilters: p['FileTypeFilters']
          },
          ADVANCE_SETTINGS: {
            TiffPrintPageLimit: p['TiffPrintPageLimit'],
            TiffPrintDocLimit: p['TiffPrintDocLimit'],
            ShowIngestionErrorsWarningInExport:
              p['ShowIngestionErrorsWarningInExport'],
            CreateDefaultRedactionSets: p['CreateDefaultRedactionSets'],
            ProjectSettings: p['ProjectSettings'],
            _projectTagSettings: p['_projectTagSettings'],
            _privilegedTermSettings: p['_privilegedTermSettings'],
            defaultCustomFields: p['defaultCustomFields'],
            listFoldering: p['listFoldering']
          }
          // more...
        }

        this.storeApiPrioritySettingsData(p['MultipleApiSettings'])

        this.patchEdaiECAValueToForm(p['ProjectSettings'])
      })
  }

  /**
   * Map only selected auto tiff OCR file type when submitting the case creation form.
   * @param data collection of AutoTiffOcrFileTypeModel
   */
  private readonly mapSelectedAutoTiffOCRFileTypes = (
    data: Array<AutoTiffOcrFileTypeModel>
  ): Array<AutoTiffOcrFileTypeModel> => {
    if (
      !this.createCaseModel['fullTextAdvanceOption']['AutomaticallyTiffAndOCR']
    ) {
      return
    }

    this.createCaseModel['AutoTiffOCRFileTypes'] = (data || [])
      .filter((c) => c.IsEnabled)
      .map(
        (c) =>
          ({
            ToolsDisplayName: c.ToolsDisplayName,
            IsEnabled: c.IsEnabled
          } as AutoTiffOcrFileTypeModel)
      )
  }

  private cloneAndReassignCaseModel(formData: unknown): void {
    const cloned = cloneDeep(this.createCaseModel)
    Object.assign(cloned.ProjectSettings, {
      ...cloned.ProjectSettings,
      ...formData['ProjectSettings']
    })
    this.createCaseModel = cloned
  }

  private assignCaseModelAfterSimplified = (data): void => {
    const valueReplacer = (key, value) =>
      typeof value === 'object' && value === null ? undefined : value
    const simplifiedFormData = JSON.parse(JSON.stringify(data, valueReplacer))
    this.createCaseModel = {
      ...this.createCaseModel,
      ...simplifiedFormData
    }
  }

  private readonly assignAdvanceSettingFormValue = (formData: any): void => {
    if (!(formData && this.createCaseModel)) return
    this.cloneAndReassignCaseModel(formData)
    delete formData.ProjectSettings
    this.assignCaseModelAfterSimplified(formData)
  }

  private readonly assignFulltextSettingFormValue = (formData): void => {
    if (!(formData && this.createCaseModel)) return
    this.cloneAndReassignCaseModel(formData)
    delete formData.ProjectSettings
    this.assignCaseModelAfterSimplified(formData)
  }

  @DebounceTimer(1)
  readonly changedFormValue = (
    values: Partial<CaseSettingFormValueType>
  ): void => {
    const copiedFormData = cloneDeep(values)
    this.assignAdvanceSettingFormValue(copiedFormData?.ADVANCE_SETTINGS)
    this.assignFulltextSettingFormValue(copiedFormData?.FULL_TEXT_OPTIONS)

    this.createCaseModel = {
      ...this.createCaseModel,
      ...copiedFormData.FILE_TYPE_SETTING,
      ...copiedFormData.SLIP_SHEET,
      ...copiedFormData.IMAGE_FILE_TYPE_SETTING
      // more props to add later
    }
  }

  ngOnInit() {
    this.selectEdaiEcaFeature()
    this.searchDuplicateOptions()
    this.hashFieldOptions()
    this.store.dispatch(new GetSupportedFileTypesForTranscribing())
    this.store.dispatch(new GetTranscribeAccessKeys())

    this.initForm()
    this.initializeAudioTranscribe()
    this.getAccessKeys()
    //template
    if (this.isTemplateCreateModule || this.isTemplateEditModule) {
      this.titleText = ' Template '
      this.buttonName = ' Template '
    }

    if (this.isEdit) {
      this.caseForm.get('projectTemplateId').disable()
    }

    this.service
      .fetchLicenseStatus$('FEATURE', 'TOA')
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((isEnabled: boolean) => {
        this.isTOALicensed = isEnabled
        this.isNativeFileOptionOnlyIndex = isEnabled
      })

    this.service
      .fetchLicenseStatus$('FEATURE', 'FULL FEATURE')
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((isEnabled: boolean) => {
        this.isFullFeatureLicensed = isEnabled
        //Always make Extract native as default selection when Both (TOA-Text Only Analysis) And Full Feature are given in license
        this.caseForm
          .get('nativeFileHandling')
          .setValue(this.isFullFeatureLicensed)
        this.nativeFileHandling = this.caseForm.get('nativeFileHandling').value
      })

    this.store
      .pipe(
        select(getControlSetting('ENABLE_ENTITY_EXTRACTION')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((isEnable: boolean) => {
        this.enableEntityExtraction = isEnable
      })

    this.store
      .pipe(
        select(getControlSetting('INGESTION_ENGINE_TYPE')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((ingestionEngine: number) => {
        this.isLegacyIngestionEngine = ingestionEngine === 0
        this.caseForm
          .get('isLegacyIngestionEngine')
          .setValue(this.isLegacyIngestionEngine)

        this.updateLegacyIngestionEngineValues()
      })

    this.store
      .pipe(
        select(getControlSetting('ENABLE_PDF_IMAGING')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((isEnable: boolean) => {
        this.enablePDFImaging = isEnable
      })

    this.caseSettingsTagMgmtService.loadingTagTree$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((isLoadingTagTree: boolean) => {
        this.isLoadingTagTree = isLoadingTagTree
      })

    // this.store
    //   .pipe(
    //     select(getControlSetting('ENABLE_NEW_CASE_SETTINGS_FEATUREFLAG')),
    //     takeUntil(this.unsubscribed$)
    //   )
    //   .subscribe((isEnable: boolean) => {
    //     this.featureFlagCaseMigration = isEnable
    //   })

    //If Template is edited then getCaseInfoEdit will be returning Template Details
    //If Case is edited then getCaseInfoEdit will be returning Project Details
    if (this.isEdit) {
      this.store
        .pipe(
          select(getCaseInfoEdit),
          debounceTime(1000),
          takeUntil(this.unsubscribed$)
        )
        .subscribe((res: any) => {
          if (!this.isEdit) return
          if (res) {
            this.showSpinner = false
            this.isNativeAutoPrefetchEnabled = !res.data.IndexOnlyCase
            this.filetypePageLimitList = res.data.TiffFileClassPageLimit.map(
              (el) => ({ ...el })
            )
            //edit template
            this.filetypePageLimitList.forEach((element) => {
              //to show default placeholder when timeout is -1
              if (element.TiffTimeOutPeriod == -1)
                element.TiffTimeOutPeriod = undefined

              if (element.PageLimit == -1 || element.PageLimit == 0) {
                const ctrlId = '#imagePageLimit' + element.FileTypeGroupID
                const displayValue =
                  element.PageLimit == -1 ? 'Max Pages' : 'All Pages'
                setTimeout(() => {
                  if (
                    (document.querySelector(ctrlId) as HTMLInputElement) !==
                    null
                  ) {
                    ;(
                      document.querySelector(ctrlId) as HTMLInputElement
                    ).value = displayValue
                  }
                }, 1000)
              }
            })
            const projectDomains: string[] =
              res.data.ProjectSettings.INTERNAL_DOMAIN?.split(';')
            this.internalDomains = projectDomains
              ?.filter((d) => d.trim() != '')
              ?.map((d) => ({ name: d, id: d }))
            this.selectedInternalDomains = this.internalDomains?.map(
              (d) => d.id
            )
            // //defaultingestionfiletypetimeout
            this.originalngestionFileTypeTimeOut =
              res.data.IngestionFileTypeTimeOut

            this.caseForm.patchValue({
              templateNote: res.data.ProjectTemplateNote,
              caseName: this.isTemplateRelated
                ? res.data.ProjectTemplateName
                : res.data.ProjectName,
              clientMatterNumber: res.data.MatterNumber,
              searchDupOption: res.data.SearchDuplicateOption,
              timeZone: res.data.TzTimeZone,
              nativeFileHandling: !res.data.IndexOnlyCase,
              discoveryExceptionHandling:
                res.data.ProjectSettings.EXCEPTION_HANDLING,
              autoQueueForEntityExtraction:
                res.data.ProjectSettings.AUTO_QUEUE_FILES_FOR_ENTITY_EXTRACTION,
              passwords: res.data.Passwords,
              // imageConversionOption: {
              //   autoGenerateImgForIngestedFile: res.data?.AutoGenerateTiff,
              //   notifyAfterImgGenComplete: res.data?.NotifyAfterImageGeneration,
              //   defaultTiffColorOption: res.data.DefaultTiffColorOption
              // },
              autoGenerateImgForIngestedFile:
                res.data.AdvancedTiffOption.AutogenerateTiff, //AutoGenerateTiff,
              notifyAfterImgGenComplete: res.data?.NotifyAfterImageGeneration,
              defaultTiffColorOption: res.data.DefaultTiffColorOption,
              enableTranscriptViewer: res.data.EnableTranscriptViewer,
              internalDomain: this.internalDomains,
              searchTerm: res.data.SearchTerm,
              // autoFolderRelativePathDuringIngestion:
              //   res.data.ProjectSettings.AUTO_FOLDER_RELATIVE_PATH,
              isLegacyIngestionEngine: res.data.IngestionEngine === 0,
              transcribeSettings: res.data.ProjectSettings.TRANSCRIBING_ENGINE,
              autoQueueTranscribe: res.data.AutoQueueTranscribe,
              deNistingFlag: res.data.DeNistingFlag,
              fileTypeFlag: res.data.FileTypeFlag,
              indexFullText: res.data.IndexFullText,
              indexEmailHeader: res.data.IndexEmailHeader,
              indexMetaData: res.data.IndexMetaData,

              filterDuplicateFile: res.data.DedupingAlgo,
              languageIdentifier: res.data.LanguageIdentifier,
              allowEmailAnalysis: res.data.AllowEmailAnalysis,
              allowAutoLaunchEmailThreading:
                res.data.AllowAutoLaunchEmailThreading,
              computeInclusiveEmail: res.data.ComputeInclusiveEmail,
              autoGenerateMissingEmail:
                res.data.ProjectSettings.AUTO_GENERATE_MISSING_EMAILS,
              autoQueueOCR: res.data.AutoQueueOCR,

              convertToMHT: res.data.ProjectSettings.CONVERT_TO_MHT,
              preserverMHT: res.data.ProjectSettings.PRESERVE_MHT, //res.data.ProjectSettings.PRESERVE_MHT,
              autoQueueNative: res.data.AutoQueueNative,
              nativeOptionPST_MSG: res.data.PreferedFileTypeNative.filter(
                (x) => x.FileType === NativeFileTypeList.OUTLOOK_MSG
              )[0].DefaultFileType,
              nativeOptionMBOX_EML: res.data.PreferedFileTypeNative.filter(
                (x) => x.FileType === NativeFileTypeList.MIMEOUTLOOKEML
              )[0].DefaultFileType,
              nativeOptionNSF_DXL: res.data.PreferedFileTypeNative.filter(
                (x) => x.FileType === NativeFileTypeList.DXL_MAILMESSAGE
              )[0].DefaultFileType,
              doNotComputeLanguageIdentificationForSpreadsheets:
                !res.data.DoNotComputeLanguageIdentificationForSpreadsheets,
              allowOCR: res.data.AllowOCR,
              enableImage: res.data.AllowTiff,
              imageTypeTiff: !res.data.IsImageTypePDF,
              defaultMaxPage: res.data.MaxPages,
              defaultTimeOut: res.data.DefaultTiffTimeoutPeriod,
              generateBates: res.data.AdvancedTiffOption?.GenerateBates,
              prefixList: res.data.AdvancedTiffOption.PrefixType.toUpperCase(),
              prefixText: res.data.AdvancedTiffOption.PrefixText,
              prefixField: res.data.AdvancedTiffOption.PrefixField,
              startNumber: res.data.AdvancedTiffOption.StartingNum,
              padding: res.data.AdvancedTiffOption.Padding,
              brandingBates: res.data.AdvancedTiffOption.BrandingBates
            })

            this.enableDisableOnExtractNative()
            this.patchTiffDefaultValues(res.data)
            if (res.data.AllowOCR) {
              this.disableOCROPtions = false
            } else {
              this.disableOCROPtions = true
            }

            this.selectedEngine =
              res.data.ProjectSettings[ProjectSetting.TRANSCRIBING_ENGINE]
            this.onTranscribeEngineChanged(this.selectedEngine)

            res.data?.transcribeAllowedList?.forEach((x) => {
              this.supportedFilesArray.find((z) => z.fileName == x).selected =
                true
            })

            const ocrLang: ProjectSetupInfo = new ProjectSetupInfo() //CaseTemplateSettings = new CaseTemplateSettings()
            const ocrLanguages = res.data.AdvanceOcrOptionLanguages?.map(
              (c) => {
                return {
                  LangName: c.LangName,
                  Selected: c.Selected
                }
              }
            )
            ocrLang.AdvanceOcrOptionLanguages = ocrLanguages
            this.patchOcrLanguages(ocrLang)

            const legacyEngineCtrl = this.caseForm.get(
              'isLegacyIngestionEngine'
            )
            legacyEngineCtrl.setValue(res.data.IngestionEngine === 0)
            if (!this.isTemplateEditModule) legacyEngineCtrl.disable()

            // this.selectedHashFieldRows = []
            // this.hashFieldGrid.instance.deselectAll()
            // this.selectedHashFieldRows = res.data.SelectedHashField

            if (res.data != null && res.data.SelectedHashField != null) {
              this.selectedHashFieldIdOnly = []

              this.hashFieldGrid?.instance.deselectAll()
              res.data.SelectedHashField.forEach((element) => {
                this.selectedHashFieldIdOnly.push(element)
              })

              setTimeout(() => {
                this.hashFieldGrid?.instance.selectRows(
                  this.selectedHashFieldIdOnly,
                  true
                )
              }, 300)

              this.selectedHashFieldRows = res.data.SelectedHashField
            }
            const filterDuplicateCtrl = this.caseForm.get('filterDuplicateFile')
            if (!this.isTemplateEditModule) filterDuplicateCtrl.disable()

            if (res.data.HashAlgorithmSecondary != null)
              this.caseForm.get('hasSecondary').setValue(1)
            const filterDuplicateSecondaryCtrl =
              this.caseForm.get('hasSecondary')
            if (!this.isTemplateEditModule)
              filterDuplicateSecondaryCtrl.disable()

            const imageTypeTiffCtrl = this.caseForm.get('imageTypeTiff')
            if (!this.isTemplateEditModule) imageTypeTiffCtrl.disable()
            if (res.data != null && res.data.OcrFileExtension != null) {
              this.ocrFileOptions = res.data.OcrFileExtension

              this.selectedOCRFileType = []
              this.ocrFileTypeGrid?.instance.deselectAll()
              res.data.OcrFileExtension.forEach((element) => {
                if (element.Selected)
                  this.selectedOCRFileType.push(element.ExtNo)
              })

              setTimeout(() => {
                this.ocrFileTypeGrid?.instance.selectRows(
                  this.selectedOCRFileType,
                  true
                )
              }, 300)
            }

            if (res.data != null && res.data.MetaIndexingFields != null) {
              this.indexDataSource = res.data.MetaIndexingFields
              this.selectedMetaDataField = []
              this.indexMetaGrid?.instance.deselectAll()
              res.data.MetaIndexingFields.forEach((element) => {
                if (element.Selected)
                  this.selectedMetaDataField.push(element.FieldId)
              })

              setTimeout(() => {
                this.indexMetaGrid?.instance.selectRows(
                  this.selectedMetaDataField,
                  true
                )
              }, 300)
            }

            //Advanced OCR Settings
            this.caseForm.patchValue({
              enableOCRThresholdTextOption:
                res.data.OcrForDocumentsWithFewerText,
              oCRPDFSetting: !res.data.OcrForDocumentsWithFewerText
                ? '1'
                : res.data.OCRThresholdAverageCharacters < 1
                ? '2'
                : '1',
              oCRThresholdAverageCharacters:
                !res.data.OcrForDocumentsWithFewerText &&
                res.data.OCRThresholdAverageCharacters < 1
                  ? 20
                  : res.data.OCRThresholdAverageCharacters,
              oCRMinCharInPDfInAtleastOnePage:
                !res.data.OcrForDocumentsWithFewerText &&
                res.data.OCRMinCharInPDfInAtleastOnePage < 1
                  ? 10
                  : res.data.OCRMinCharInPDfInAtleastOnePage,
              oCROtherSetting: res.data.IncludePartialOcredFile,
              maxOcrTryCount: res.data.maxOcrTryCount
            })

            this._isProjectTemplateLoading = false

            if (this.isTemplateEditModule) {
              const templateName = this.caseForm.get('caseName').value
              this.caseForm.get('caseName').disable()
              if (templateName === 'DefaultProjectTemplate')
                this.isDefaultTemplateEdit = true
              else this.isDefaultTemplateEdit = false
            }

            this.patchEdaiECAValueToForm(res.data.ProjectSettings)
          }
        })
    }

    this.store
      .pipe(
        select(getTimeZones),
        takeUntil(this.unsubscribed$),
        filter((timeZoneList: string) => !!timeZoneList)
      )
      .subscribe((timeZoneList: any) => {
        this.timeZoneList = timeZoneList
      })

    //If Template is to be created then projectTemplateSettings will be returning DefaultCaseTemplate Details
    //If Case is to be created then projectTemplateSettings will be returning Selected Project template Details, However by default it will be also returning first template retruned from API. i.e DefaultProjectTemplate
    if (!this.isEdit) {
      this.store
        .pipe(
          select(projectTemplateSettings),
          takeUntil(this.unsubscribed$),
          filter((settings: ProjectSetupInfo) => !!settings)
        )
        .subscribe((settings: ProjectSetupInfo) => {
          this.showSpinner = false

          this.filetypePageLimitList = settings.TiffFileClassPageLimit.map(
            (el) => ({ ...el })
          )

          this.filetypePageLimitList.forEach((element) => {
            if (element.PageLimit == -1 || element.PageLimit == 0) {
              const ctrlId = '#imagePageLimit' + element.FileTypeGroupID
              const displayValue =
                element.PageLimit == -1 ? 'Max Pages' : 'All Pages'
              setTimeout(() => {
                if (
                  (document.querySelector(ctrlId) as HTMLInputElement) !== null
                ) {
                  ;(document.querySelector(ctrlId) as HTMLInputElement).value =
                    displayValue
                }
              }, 1000)
            }
          })

          //defaultingestionfiletypetimeout
          this.originalngestionFileTypeTimeOut =
            settings.IngestionFileTypeTimeOut

          //Get the current selected template
          this.LastDefaultTiffColorOption = settings.DefaultTiffColorOption
          this.LastAutoGenerateTiff =
            settings.AdvancedTiffOption.AutogenerateTiff //.AutoGenerateTiff

          this.enableDisableOnExtractNative()
          if (!this.isEdit) {
            if (settings && settings.SearchDuplicateOption != null) {
              this.caseForm.patchValue({
                searchDupOption: settings.SearchDuplicateOption
              })
            }
            // patch template derived default values to the form
            this.patchTiffDefaultValues(settings)
            // patch OCR languages
            this.patchOcrLanguages(settings)

            if (settings != null && settings.TzTimeZone != null) {
              this.caseForm.patchValue({
                timeZone: settings.TzTimeZone
              })
            } else {
              this.caseForm.patchValue({
                timeZone: this.timeZoneList[0].TzTimeZone
              })
            }

            /*Start: Transacribe setting display based on template*/
            if (
              settings != null &&
              settings.ProjectSettings[ProjectSetting.TRANSCRIBING_ENGINE] !=
                null
            ) {
              this.caseForm.patchValue({
                autoQueueTranscribe: settings.AutoQueueTranscribe,
                transcribeSettings:
                  settings.ProjectSettings[ProjectSetting.TRANSCRIBING_ENGINE]
              })
              this.selectedEngine =
                settings.ProjectSettings[ProjectSetting.TRANSCRIBING_ENGINE]

              this.onTranscribeEngineChanged(this.selectedEngine)
              settings.transcribeAllowedList.forEach((x) => {
                this.supportedFilesArray.find((z) => z.fileName == x).selected =
                  true
              })
            } else {
              this.caseForm.patchValue({
                autoQueueTranscribe: false
              })
              this.selectedEngine = 'DeepSpeech'
              this.onTranscribeEngineChanged(this.selectedEngine)
            }
            /*End: Transacribe setting display based on template*/

            this.caseForm.patchValue({
              deNistingFlag: settings.DeNistingFlag,
              fileTypeFlag: settings.FileTypeFlag,
              indexFullText: settings.IndexFullText,
              indexEmailHeader: settings.IndexEmailHeader,
              indexMetaData: settings.IndexMetaData
            })
            this.caseForm.patchValue({
              filterDuplicateFile: settings.DedupingAlgo,
              languageIdentifier: settings.LanguageIdentifier,
              allowEmailAnalysis: settings.AllowEmailAnalysis,
              allowAutoLaunchEmailThreading:
                settings.AllowAutoLaunchEmailThreading,
              computeInclusiveEmail: settings.ComputeInclusiveEmail,
              autoGenerateMissingEmail:
                settings.ProjectSettings[
                  ProjectSetting.AUTO_GENERATE_MISSING_EMAILS
                ],
              autoQueueOCR: settings.AutoQueueOCR,
              // autoFolderRelativePathDuringIngestion:
              //   settings.ProjectSettings[
              //     ProjectSetting.AUTO_FOLDER_RELATIVE_PATH
              //   ],
              preserverMHT:
                settings.ProjectSettings[ProjectSetting.PRESERVE_MHT], //settings.PreserverMHT,
              convertToMHT:
                settings.ProjectSettings[ProjectSetting.CONVERT_TO_MHT],
              autoQueueNative: settings.AutoQueueNative,
              nativeOptionPST_MSG: settings.PreferedFileTypeNative.filter(
                (x) => x.FileType === NativeFileTypeList.OUTLOOK_MSG
              )[0].DefaultFileType,
              nativeOptionMBOX_EML: settings.PreferedFileTypeNative.filter(
                (x) => x.FileType === NativeFileTypeList.MIMEOUTLOOKEML
              )[0].DefaultFileType,
              nativeOptionNSF_DXL: settings.PreferedFileTypeNative.filter(
                (x) => x.FileType === NativeFileTypeList.DXL_MAILMESSAGE
              )[0].DefaultFileType,
              doNotComputeLanguageIdentificationForSpreadsheets:
                !settings.DoNotComputeLanguageIdentificationForSpreadsheets,
              allowOCR: settings.AllowOCR,
              enableImage: settings.AllowTiff,
              imageTypeTiff: !settings.IsImageTypePDF,
              defaultMaxPage:
                settings.MaxPages == 0 ? 'All Pages' : settings.MaxPages,
              defaultTimeOut: settings.DefaultTiffTimeoutPeriod,
              generateBates: settings.AdvancedTiffOption?.GenerateBates,
              prefixList: settings.AdvancedTiffOption.PrefixType.toUpperCase(),
              prefixText: settings.AdvancedTiffOption.PrefixText,
              prefixField: settings.AdvancedTiffOption.PrefixField,
              startNumber: settings.AdvancedTiffOption.StartingNum,
              padding: settings.AdvancedTiffOption.Padding,
              brandingBates: settings.AdvancedTiffOption.BrandingBates,
              discoveryExceptionHandling:
                settings.ProjectSettings.EXCEPTION_HANDLING,
              autoQueueForEntityExtraction:
                settings.ProjectSettings.AUTO_QUEUE_FILES_FOR_ENTITY_EXTRACTION,
              enableTranscriptViewer:
                settings.ProjectSettings[ProjectSetting.ENABLE_TRANSCRIPT]
            })

            this.enableDisableOnExtractNative()
            this.patchTiffDefaultValues(settings)

            if (settings.AllowOCR) {
              this.disableOCROPtions = false
            } else {
              this.disableOCROPtions = true
            }

            if (settings != null && settings.HashAlgorithmSecondary != null) {
              this.caseForm.patchValue({ hasSecondary: true })
            } else {
              this.caseForm.patchValue({ hasSecondary: false })
            }

            //to clear on selectin chane of template id
            // this.selectedHashFieldRows = []
            // this.hashFieldGrid.instance.deselectAll()
            // this.selectedHashFieldRows = settings.SelectedHashField

            if (settings != null && settings.SelectedHashField != null) {
              this.selectedHashFieldIdOnly = []

              this.hashFieldGrid?.instance.deselectAll()
              settings.SelectedHashField.forEach((element) => {
                this.selectedHashFieldIdOnly.push(element)
              })

              setTimeout(() => {
                this.hashFieldGrid?.instance.selectRows(
                  this.selectedHashFieldIdOnly,
                  true
                )
              }, 300)

              this.selectedHashFieldRows = settings.SelectedHashField
            }

            if (settings != null && settings.OcrFileExtension != null) {
              this.ocrFileOptions = settings.OcrFileExtension
              this.selectedOCRFileType = []
              this.ocrFileTypeGrid?.instance.deselectAll()
              settings.OcrFileExtension.forEach((element) => {
                if (element.Selected)
                  this.selectedOCRFileType.push(element.ExtNo)
              })

              setTimeout(() => {
                this.ocrFileTypeGrid?.instance.selectRows(
                  this.selectedOCRFileType,
                  true
                )
              }, 300)
            }

            if (settings != null && settings.MetaIndexingFields != null) {
              this.indexDataSource = settings.MetaIndexingFields
              this.selectedMetaDataField = []
              this.indexMetaGrid?.instance.deselectAll()
              settings.MetaIndexingFields.forEach((element) => {
                if (element.Selected)
                  this.selectedMetaDataField.push(element.FieldId)
              })

              setTimeout(() => {
                this.indexMetaGrid?.instance.selectRows(
                  this.selectedMetaDataField,
                  true
                )
              }, 300)
            }
            //Advanced OCR Settings
            this.caseForm.patchValue({
              enableOCRThresholdTextOption:
                settings.OcrForDocumentsWithFewerText,
              oCRPDFSetting: !settings.OcrForDocumentsWithFewerText
                ? '1'
                : settings.OCRThresholdAverageCharacters < 1
                ? '2'
                : '1',
              oCRThresholdAverageCharacters:
                !settings.OcrForDocumentsWithFewerText &&
                settings.OCRThresholdAverageCharacters < 1
                  ? 20
                  : settings.OCRThresholdAverageCharacters,
              oCRMinCharInPDfInAtleastOnePage:
                !settings.OcrForDocumentsWithFewerText &&
                settings.OCRMinCharInPDfInAtleastOnePage < 1
                  ? 10
                  : settings.OCRMinCharInPDfInAtleastOnePage,
              oCROtherSetting: settings.IncludePartialOcredFile,
              maxOcrTryCount: settings.maxOcrTryCount
            })
          }
          this._isProjectTemplateLoading = false
        })
    }

    // Navigate to the upload section with project ID as a query param.
    this.navigateToUpload()

    // Cleanup after receiving error response when creating a case.
    this.displayResponseError()

    // Load the selected template in UI on selection change of case template.
    this.caseForm.controls.projectTemplateId.valueChanges
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((newValue) => {
        if (newValue > 0) {
          this._isProjectTemplateLoading = true
          if (this.matAccordion !== undefined) this.matAccordion?.closeAll()
          this.caseSettingsTagMgmtService.isLoadingTagTree(true)
          this.store.dispatch(new FetchCaseTemplateSettings(newValue))
        }
      })
    this.caseForm.controls.indexFullText.valueChanges.subscribe((newValue) => {
      if (!newValue) {
        this.caseForm.get('indexEmailHeader').setValue(false)
        this.caseForm.get('indexEmailHeader').disable()
      } else this.caseForm.get('indexEmailHeader').enable()
    })

    this.caseForm.controls.convertToMHT.valueChanges.subscribe((newValue) => {
      if (newValue) {
        this.caseForm.get('preserverMHT').setValue(false)
        this.caseForm.get('preserverMHT').disable()
      } else this.caseForm.get('preserverMHT').enable()
    })

    this.caseForm.controls.languageIdentifier.valueChanges.subscribe(
      (newValue) => {
        if (!newValue) {
          this.caseForm
            .get('doNotComputeLanguageIdentificationForSpreadsheets')
            .setValue(false)
          this.caseForm
            .get('doNotComputeLanguageIdentificationForSpreadsheets')
            .disable()
        } else
          this.caseForm
            .get('doNotComputeLanguageIdentificationForSpreadsheets')
            .enable()
      }
    )

    this.caseForm.controls.filterDuplicateFile.valueChanges.subscribe(
      (newValue) => {
        if (newValue) {
          this.SecondaryHash = 'MD5'
        } else this.SecondaryHash = 'SHA1'
      }
    )

    this.caseForm.controls.allowAutoLaunchEmailThreading.valueChanges.subscribe(
      (newValue) => {
        if (!newValue) {
          this.caseForm.get('computeInclusiveEmail').setValue(false)
          this.caseForm.get('autoGenerateMissingEmail').setValue(false)
        }
      }
    )

    this.caseForm.controls.computeInclusiveEmail.valueChanges.subscribe(
      (newValue) => {
        if (newValue) {
          this.caseForm.get('allowAutoLaunchEmailThreading').setValue(true)
        }
      }
    )

    this.caseForm.controls.autoGenerateMissingEmail.valueChanges.subscribe(
      (newValue) => {
        if (newValue) {
          this.caseForm.get('allowAutoLaunchEmailThreading').setValue(true)
        }
      }
    )

    this.caseForm.controls.allowOCR.valueChanges.subscribe((newValue) => {
      if (!newValue) {
        this.caseForm.get('autoQueueOCR').setValue(false)
        this.caseForm.get('autoQueueOCR').disable()
        this.caseForm.get('enableOCRThresholdTextOption').disable()
        this.caseForm.get('oCRPDFSetting').disable()
        this.caseForm.get('oCRThresholdAverageCharacters').disable()
        this.caseForm.get('oCRMinCharInPDfInAtleastOnePage').disable()
        this.caseForm.get('maxOcrTryCount').disable()
        this.caseForm.get('oCROtherSetting').disable()
        this.disableOCROPtions = true
      } else {
        this.caseForm.get('autoQueueOCR').enable()
        this.caseForm.get('enableOCRThresholdTextOption').enable()
        this.caseForm.get('oCRPDFSetting').enable()
        this.caseForm.get('oCRThresholdAverageCharacters').disable()
        this.caseForm.get('oCRMinCharInPDfInAtleastOnePage').disable()
        this.caseForm.get('maxOcrTryCount').enable()
        this.caseForm.get('oCROtherSetting').enable()
        this.disableOCROPtions = false
      }
      this.controlOcrLanguages()
    })

    this.caseForm.controls.generateBates.valueChanges.subscribe((newValue) => {
      if (newValue) {
        this.caseForm.get('prefixList').enable()
        this.caseForm.get('prefixText').enable()
        this.caseForm.get('prefixField').enable()
        this.caseForm.get('startNumber').enable()
        this.caseForm.get('padding').enable()
        this.caseForm.get('brandingBates').enable()
      } else {
        this.caseForm.get('prefixList').disable()
        this.caseForm.get('prefixText').disable()
        this.caseForm.get('prefixField').disable()
        this.caseForm.get('startNumber').disable()
        this.caseForm.get('padding').disable()
        this.caseForm.get('brandingBates').disable()
      }
    })

    this.caseForm.controls.prefixList.valueChanges.subscribe((newValue) => {
      if (newValue === 'TEXT') {
        this.caseForm.get('prefixText').enable()
        this.caseForm.get('prefixField').disable()
        this.isTextHidden = false
        this.isFieldHidden = true
      } else {
        this.caseForm.get('prefixText').disable()
        this.caseForm.get('prefixField').enable()
        this.isTextHidden = true
        this.isFieldHidden = false
      }
    })

    this.caseForm.controls.enableImage.valueChanges.subscribe((newValue) => {
      if (newValue) {
        this.disableImageOptions = false
        this.caseForm.get('imageTypeTiff').enable()
        this.caseForm.get('defaultTimeOut').enable()
        this.caseForm.get('defaultMaxPage').enable()
        this.caseForm.get('defaultTiffColorOption').enable()
      } else {
        this.disableImageOptions = true
        this.caseForm.get('imageTypeTiff').disable()
        this.caseForm.get('defaultTimeOut').disable()
        this.caseForm.get('defaultMaxPage').disable()
        this.caseForm.get('defaultTiffColorOption').disable()
      }
    })

    this.caseForm.controls.enableOCRThresholdTextOption.valueChanges.subscribe(
      (newValue) => {
        if (newValue) {
          this.caseForm.get('oCRPDFSetting').enable()
          //this.caseForm.get('oCRPDFSetting').setValue('1')
          this.caseForm.get('oCRThresholdAverageCharacters').enable()
          this.caseForm.get('oCRMinCharInPDfInAtleastOnePage').disable()
        } else {
          this.caseForm.get('oCRPDFSetting').disable()
          this.caseForm.get('oCRThresholdAverageCharacters').disable()
          this.caseForm.get('oCRMinCharInPDfInAtleastOnePage').disable()
        }
      }
    )

    this.caseForm.controls.oCRPDFSetting.valueChanges.subscribe((newValue) => {
      if (newValue == '1') {
        this.caseForm.get('oCRThresholdAverageCharacters').enable()
        this.caseForm.get('oCRMinCharInPDfInAtleastOnePage').disable()
      } else {
        this.caseForm.get('oCRThresholdAverageCharacters').disable()
        this.caseForm.get('oCRMinCharInPDfInAtleastOnePage').enable()
      }
    })

    this.caseForm.controls.isLegacyIngestionEngine.valueChanges.subscribe(
      (newValue) => {
        if (newValue) {
          this.showSocialMedia = false
        } else this.showSocialMedia = true
      }
    )

    this.caseForm.controls.nativeFileHandling.valueChanges
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((newValue) => {
        this.nativeFileHandling = newValue
        this.enableDisableOnExtractNative()
      })

    this.caseForm.valueChanges
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((val) => {
        this.createCaseModel.ProjectName = val.caseName
        this.createCaseModel.ProjectTemplateId = val.projectTemplateId
        this.createCaseModel.MatterNumber = val.clientMatterNumber
        this.createCaseModel.SearchDuplicateOption = val.searchDupOption
        this.createCaseModel.EnableNativeFileHandling = val.nativeFileHandling

        this.createCaseModel.ProjectSettings = {}
        //this.createCaseModel.EnableDiscoveryExceptionHandling =val.discoveryExceptionHandling
        this.createCaseModel.ProjectSettings[
          ProjectSetting.EXCEPTION_HANDLING
        ] = val.discoveryExceptionHandling
        //this.createCaseModel.AutoQueueForEntityExtraction =val.autoQueueForEntityExtraction
        this.createCaseModel.ProjectSettings.AUTO_QUEUE_FILES_FOR_ENTITY_EXTRACTION =
          val.autoQueueForEntityExtraction
        this.createCaseModel.NotifyAfterImageGeneration =
          val.notifyAfterImgGenComplete
        //val.imageConversionOption?.notifyAfterImgGenComplete
        // Edited: Sanh Huynh; Date: Mar 23, 2020; Ref #23584 - Color Tiffing is not working
        this.createCaseModel.DefaultTiffColorOption = val.defaultTiffColorOption
        this.createCaseModel.EnableTranscriptViewer = val.enableTranscriptViewer
        this.createCaseModel.ProjectSettings[ProjectSetting.INTERNAL_DOMAIN] =
          val.internalDomain?.join(';')
        this.createCaseModel.SearchTermList = val.searchTerm
          ?.split('\n')
          .filter((t) => t !== '')
        this.createCaseModel.TzTimeZone = val.timeZone

        // this.createCaseModel.AdvancedTiffOption.AutogenerateTiff =
        //   val.autoGenerateImgForIngestedFile;
        ;(this.createCaseModel.ImageConversionOption = {
          AutoGenerateImagesAfterIngestion: val?.autoGenerateImgForIngestedFile,
          // val.imageConversionOption?.autoGenerateImgForIngestedFile,
          PasswordList: val.passwords?.split('\n'),
          TimeZone: val.timeZone
        }),
          //(this.createCaseModel.TranscribeEngine = val.transcribeSettings),
          (this.createCaseModel.ProjectSettings.TRANSCRIBING_ENGINE =
            val.transcribeSettings),
          (this.createCaseModel.AutoQueueTranscribe = val.autoQueueTranscribe),
          // (this.createCaseModel.AutoFolderRelativePathDuringIngestion =
          //   val.autoFolderRelativePathDuringIngestion),
          // (this.createCaseModel.ProjectSettings.AUTO_FOLDER_RELATIVE_PATH =
          //   val.autoFolderRelativePathDuringIngestion),
          (this.createCaseModel.IngestionEngine = val.isLegacyIngestionEngine
            ? 0
            : 1)

        this.createCaseModel.DeNistingFlag = val.deNistingFlag
        this.createCaseModel.FileTypeFlag = val.fileTypeFlag
        this.createCaseModel.IndexFullText = val.indexFullText
        this.createCaseModel.IndexEmailHeader = val.indexEmailHeader
        this.createCaseModel.IndexMetaData = val.indexMetaData

        this.createCaseModel.DedupingAlgo = val.filterDuplicateFile
        this.createCaseModel.LanguageIdentifier = val.languageIdentifier
        this.createCaseModel.AllowEmailAnalysis = val.allowEmailAnalysis
        this.createCaseModel.AllowAutoLaunchEmailThreading =
          val.allowAutoLaunchEmailThreading
        this.createCaseModel.ComputeInclusiveEmail = val.computeInclusiveEmail
        //this.createCaseModel.MissingEmailGenerate = val.autoGenerateMissingEmail
        this.createCaseModel.ProjectSettings.AUTO_GENERATE_MISSING_EMAILS =
          val.autoGenerateMissingEmail
        this.createCaseModel.AutoQueueOCR = val.autoQueueOCR
        //this.createCaseModel.ProjectSettings = val.preserverMHT
        this.createCaseModel.ProjectSettings.PRESERVE_MHT = val.preserverMHT
        //this.createCaseModel.ConvertToMHT = val.convertToMHT
        this.createCaseModel.ProjectSettings.CONVERT_TO_MHT = val.convertToMHT
        this.createCaseModel.AutoQueueNative = val.autoQueueNative

        this.createCaseModel.NativeOptionPST_MSG = {
          FileType: NativeFileTypeList.OUTLOOK_MSG,
          DefaultFileType: val.nativeOptionPST_MSG
        }
        this.createCaseModel.NativeOptionMBOX_EML = {
          FileType: NativeFileTypeList.MIMEOUTLOOKEML,
          DefaultFileType: val.nativeOptionMBOX_EML
        }
        this.createCaseModel.NativeOptionNSF_DXL = {
          FileType: NativeFileTypeList.DXL_MAILMESSAGE,
          DefaultFileType: val.nativeOptionNSF_DXL
        }
        this.createCaseModel.DoNotComputeLanguageIdentificationForSpreadsheets =
          !val.doNotComputeLanguageIdentificationForSpreadsheets
        this.createCaseModel.AllowOCR = val.allowOCR

        this.createCaseModel.AllowTiff = val.enableImage
        this.createCaseModel.MaxPages = val.defaultMaxPage
        this.createCaseModel.DefaultTiffTimeoutPeriod = val.defaultTimeOut

        this.createCaseModel.AdvancedTiffOption = {
          GenerateBates: val.generateBates,
          PrefixType: val.prefixList,
          PrefixText: val.prefixText,
          PrefixField: val.prefixField,
          StartingNum: val.startNumber,
          Padding: val.padding,
          BrandingBates: val.brandingBates,
          IgnoreAutoTiffJobsForMediaProcessingStatus:
            val.notifyAfterImgGenComplete,
          AutogenerateTiff: val.autoGenerateImgForIngestedFile
        }
      })

    this.configStore
      .pipe(select(getThemeClient), takeUntil(this.unsubscribed$))
      .subscribe((client: string) => {
        this.client = client
      })

    if (!this.isTemplateRelated) {
      this.store
        .pipe(select(getProjectTemplateList), takeUntil(this.unsubscribed$))
        .subscribe((projectTemplateList: ProjectTemplateModel[]) => {
          this.projectTemplates = projectTemplateList
          if (projectTemplateList != null && projectTemplateList.length > 0) {
            this.caseForm.patchValue({
              projectTemplateId: this.projectTemplates[0].Id
            })
            this.store.dispatch(
              new FetchCaseTemplateSettings(this.projectTemplates[0].Id)
            )
          }
        })

      /**/

      /**/
      this.store
        .pipe(
          select(updateCase),
          filter((p) => !!p),
          takeUntil(this.unsubscribed$)
        )
        .subscribe({
          next: (o) => {
            this.showSpinner = false
            this.toDefault()
            this.store.dispatch(
              new NavigateToAction(`/admin/case/manage`, true)
            )
            this.store.dispatch(new UpdateCaseSuccess(null))
            this.xsStore.dispatch(
              new FetchProjectDetails(this.qParams?.projectId)
            )
          }
        })
    }

    if (this.isTemplateRelated) {
      this.store
        .pipe(
          select(updateCaseTemplateInfo),
          filter((p) => !!p),
          takeUntil(this.unsubscribed$)
        )
        .subscribe({
          next: (o) => {
            this.showSpinner = false
            this.toDefault()
            this.store.dispatch(
              new NavigateToAction(
                `/admin/system/template/manage/manage-case-template`,
                true
              )
            )
            this.store.dispatch(new UpdateCaseTemplateSuccess(null))
          }
        })
    }

    this.xsStore
      .dispatch(new CheckADGroupsPrjLevel())
      .pipe(
        switchMap(() =>
          this.xsStore.select(CaseSettingSelector.sliceOf('checkADPrjLevel'))
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((res) => {
        this.showADSettings = res.data
      })

    this.xsStore
      .dispatch(new CheckIDPGroupsPrjLevelAction())
      .pipe(
        switchMap(() =>
          this.xsStore.select(CaseSettingSelector.sliceOf('checkIDPPrjLevel'))
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((res) => {
        this.showIDPSettings = res.data
      })

    this.nativeFileHandlingControlChanges()
    this.setModifiedApiPriorityData()
  }

  initForm() {
    const currentDate: Date = new Date()
    this.caseForm = this.fb.group({
      caseName: ['', [Validators.required]],
      projectTemplateId: [null],
      clientMatterNumber: [''],
      searchDupOption: [''],
      timeZone: [null],
      nativeFileHandling: [true],
      discoveryExceptionHandling: [true],
      passwords: [''],
      autoQueueForEntityExtraction: [false],
      autoFolderRelativePathDuringIngestion: [false],
      //imageConversionOption: this.fb.group({
      autoGenerateImgForIngestedFile: [false],
      notifyAfterImgGenComplete: false,
      defaultTiffColorOption: ['BLACK_AND_WHITE'],
      //}),
      searchTerm: [''],
      enableTranscriptViewer: [false],
      internalDomain: [],
      ocrLanguages: this.fb.array([], this.minSelectedCheckboxes(1)),
      transcribeSettings: [this.selectedEngine],
      autoQueueTranscribe: [false],
      supportedFiles: [],
      isLegacyIngestionEngine: [this.isLegacyIngestionEngine],
      deNistingFlag: [true],
      fileTypeFlag: [true],
      indexFullText: [true],
      indexEmailHeader: [true],
      indexMetaData: [true],
      filterDuplicateFile: ['SHA1'],
      languageIdentifier: [true],
      allowEmailAnalysis: [true],
      allowAutoLaunchEmailThreading: [false],
      computeInclusiveEmail: [false],
      autoGenerateMissingEmail: [false],
      autoQueueOCR: [false],
      preserverMHT: [false],
      convertToMHT: [false],
      autoQueueNative: [false],
      nativeOptionPST_MSG: ['msg'],
      nativeOptionMBOX_EML: ['eml'],
      nativeOptionNSF_DXL: ['html'],
      hasSecondary: [false],
      doNotComputeLanguageIdentificationForSpreadsheets: [false],
      allowOCR: [true],
      generateBates: [false],
      prefixList: ['TEXT'],
      prefixText: ['IMG'],
      prefixField: ['DOCUMENT_UNIQUE_IDENTIFIER'],
      startNumber: [1],
      padding: [8],
      brandingBates: [false],
      enableImage: [true],
      imageTypeTiff: [true],
      defaultTimeOut: [5],
      defaultMaxPage: [1000],
      showHiddenRowsColums: [true],
      showHiddenText: [true],
      showTrackChanges: [false],
      showSpreadSheetGridLines: [true],
      embeddedEmail: ['EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES'],
      excludeEmailNonRecognizable: [true],
      excludeEmailAllEmbedded: [true],
      embeddedEdoc: ['EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES'],
      excludeEdocNonRecognizable: [true],
      excludeEdocAllEmbedded: [true],
      embeddedPPT: ['EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES'],
      excludePPTNonRecognizable: [true],
      excludePPTAllEmbedded: [true],
      autoQueueNDDSig: [false],
      nDDMinThershold: [80],
      nDDMinChar: [512],
      includeMetadataForNDD: [false],
      filterDisclaimer: [false],
      disclaimerText: [''],
      dateFilterdocumentType: ['BOTH'],
      dateTypeList: ['GROUP_DATE'],
      dateOperatorList: ['BETWEEN'],

      startdateDateFilter: [moment(currentDate).format('YYYY-MM-DD')],

      enddateDateFilter: [moment(currentDate).format('YYYY-MM-DD')],
      overrideFileTypeAssignment: [false],
      imageFileExtension: new FormControl({ value: '', disabled: true }),
      imagingEngine: new FormControl({ value: '', disabled: true }),
      treatAsEdoc: [false],
      fileIdentificationFilterList: [''],
      enableOCRThresholdTextOption: [false],
      oCRPDFSetting: ['1'],
      oCRThresholdAverageCharacters: [20],
      oCRMinCharInPDfInAtleastOnePage: [10],
      maxOcrTryCount: [2],
      oCROtherSetting: [true],
      fileExtensionFilterInclude: [true],
      fileExtFilterList: [''],
      fileExtfilterLevel: ['FIRST_LEVEL'],
      applyContainerFile: [false],
      minValidDateTime: [moment(new Date('01/01/1970'))],
      NsfDateTimeFormat: ['MM/DD/YYYY'],
      DO_NOT_EXTRACT_FUTURE_DATETIME: [true],
      SetMeetingDate: [true],
      AutoCopyCrashDocumentFlag: [false],
      DefaultIngestionTimeout: [5],
      ExtractInternetEmailHeader: [true],
      PopulateCustodianDedup: [true],
      PopulateDuplicateFilePath: [true],
      NSF_VIEW_EXTRACTION_OPTION: [0],

      DUPLICATE_COMPUTING_OPTION: ['CUSTODIAN_PRIORITY'],
      DoNotQueue: [false],
      FileCopyOpt: ['COPY_CREATE_SECONDCOPY_OF_SCANNED_FILES'],
      ExcludeLevel: ['ProjectLevel'],

      SplitType: ['NUMBER_OF_MESSAGES'],
      SplitValueConversation: [5],
      SplitValueConversationDropDown: ['CONVERSATION_IN_NUMBER_OF_DAYS'],
      SplitValueGap: [5],
      SplitValueGapDropDown: ['CONVERSATION_GAP_IN_DAYS'],
      NUMBER_OF_MESSAGES_VALUE: [500],

      templateNote: [''],
      newStopWord: ''
    })

    this.xsStore.dispatch(new FetchGlobalGroupsAction())
  }

  disableExtractNativeOption() {
    if (this.isTOALicensed && this.isFullFeatureLicensed && !this.isEdit) return
    if (!this.isEdit) {
      if (this.isTOALicensed && !this.isFullFeatureLicensed) return true
      else return false
    } else {
      if (this.isNativeAutoPrefetchEnabled) return false
      else return true
    }
  }

  disableECAOption() {
    if (this.isTOALicensed && !this.isEdit) return
    if (!this.isEdit) {
      if (!this.isTOALicensed && this.isFullFeatureLicensed) return true
      else return false
    } else {
      if (this.isNativeAutoPrefetchEnabled) return true
      else return false
    }
  }

  changeNativeFileOption(e: { target: { value: any } }) {
    this.isNativeFileOptionOnlyIndex = !this.isNativeFileOptionOnlyIndex
    if (!this.isEdit) {
      this.enableDisableOnExtractNative()
    }
  }

  //#region search duplicate options

  searchDuplicateOptions(): void {
    this.searchOptions = [
      {
        id: 1,
        option: 'Show all hits in the selected scope (No DeDupe)'
      },
      {
        id: 0,
        option: 'Show only one instance in the selected scope (DynamicDeDupe™)'
      },
      {
        id: 2,
        option:
          'Show only one instance per custodian in the selected scope (DynamicDeDupe™)'
      },
      {
        id: 3,
        option: 'Hide project level duplicates (StaticDeDupe™)'
      },
      {
        id: 4,
        option: 'Hide custodian level duplicates (StaticDeDupe™)'
      }
    ]
  }

  hashFieldOptions(): void {
    this.hashOptions = [
      { HashId: 0, HashName: 'Attachment Name' },
      { HashId: 1, HashName: 'Bcc' },
      { HashId: 2, HashName: 'Cc' },
      { HashId: 3, HashName: 'From' },
      { HashId: 4, HashName: 'Subject' },
      { HashId: 5, HashName: 'To' },
      { HashId: 6, HashName: 'Sent Date' },
      { HashId: 7, HashName: 'Attachment CRC Hash' }
    ]
  }

  //#endregion

  ngAfterViewInit() {
    this.store
      .pipe(
        select(projectTemplateSettings),
        debounceTime(1000),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({
        next: (t) => {
          if (!this.isEdit) {
            this.patchOcrLanguages(t)
          }
        }
      })

    if (!this.isEdit) {
      this.caseForm.get('caseName').setValue('')
    }

    this.handleProjectTemplateDataPatchToChildren()
  }

  openExportFieldDetailsPreviewModal() {
    const selectedTemplate = this.projectTemplates.filter(
      (e) => e.Id === this.createCaseModel.ProjectTemplateId
    )[0]
    if (selectedTemplate) {
      this.templateName = selectedTemplate.Name
      this.templateNote =
        selectedTemplate.Note === ''
          ? 'Template note not available.'
          : selectedTemplate.Note
    }
    this.showConfirmationModal(this.templateName, this.templateNote)
  }

  /**
   * Handle patching for EDAI ECA feature.
   */
  private patchEdaiECAValueToForm(data: any): void {
    // If the project is not enabled for EDAI ECA feature, no patching is required.
    if (!this.enableEdaiEcaFeature || !this.isEdit) return

    const enableControl = this.caseForm.get(ControlNames.isEnabled)
    const backgroundControl = this.caseForm.get(ControlNames.background)
    const relevantDescControl = this.caseForm.get(ControlNames.relevantDesc)
    const nonRelevantDescControl = this.caseForm.get(
      ControlNames.nonRelevantDesc
    )

    enableControl?.setValue(data[ControlNames.isEnabled])
    backgroundControl?.setValue(data[ControlNames.background])
    relevantDescControl?.setValue(data[ControlNames.relevantDesc])
    nonRelevantDescControl?.setValue(data[ControlNames.nonRelevantDesc])

    enableControl.updateValueAndValidity()
    backgroundControl.updateValueAndValidity()
    relevantDescControl.updateValueAndValidity()
    nonRelevantDescControl.updateValueAndValidity()

    // For editing the project setting, these controls should be disabled.
    enableControl.disable()
    backgroundControl.disable()
    relevantDescControl.disable()
    nonRelevantDescControl.disable()
  }

  private selectEdaiEcaFeature(): void {
    this.configStore
      .pipe(
        select(getControlSetting('ENABLE_EDAI_ECA')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((isEnabled: boolean) => {
        this.enableEdaiEcaFeature = isEnabled
      })
  }

  /**
   * Navigates to the file upload section of current case which was created successfully.
   */
  private navigateToUpload() {
    if (!this.isTemplateRelated) {
      // we receive created project object from the backend so the project ID must be valid.
      this.store
        .pipe(
          select(newCase),
          filter((p) => p instanceof Array && p[0].ProjectId > 0),
          map((o) => o[0]),
          takeUntil(this.unsubscribed$)
        )
        .subscribe({
          next: (o) => {
            this.xsStore.dispatch(new FetchProjectList())
            this.showSpinner = false
            this.toDefault()
            // The query param of project ID being used in upload component to fetch created object from the API
            this.store.dispatch(
              new NavigateToAction('/upload?projectId=' + o.ProjectId, true)
            )

            /* We are having issue from the state when the object keeps on track,
             Once we receive success response with object and redirecting to the different component,
             we no longer need the same object when we come back to create another case here.
           **/
            this.store.dispatch(new CreateCaseSuccess(null))
          }
        })
    } else {
      this.store
        .pipe(
          select(newCaseTemplate),
          filter((p) => !!p),
          takeUntil(this.unsubscribed$)
        )
        .subscribe({
          next: (o) => {
            this.showSpinner = false
            this.toDefault()

            this.store.dispatch(
              new NavigateToAction(
                `/admin/system/template/manage/manage-case-template`,
                true
              )
            )
            this.store.dispatch(new CreateCaseTemplateSuccess(null))
          }
        })
    }
  }

  /**
   * Removes the UI blocker.
   */
  private displayResponseError() {
    this.store
      .select(getErrorMessage)
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe(() => {
        this.showSpinner = false

        if (this.selectedHashFieldIdOnly.length > 0) {
          setTimeout(() => {
            this.hashFieldGrid?.instance.selectRows(
              this.selectedHashFieldIdOnly,
              true
            )
          }, 300)
        }

        if (this.selectedOCRExtensionType.length > 0) {
          const ocrIds = []

          this.selectedOCRExtensionType.forEach((el) => {
            ocrIds.push(el.ExtNo)
          })

          setTimeout(() => {
            this.ocrFileTypeGrid?.instance.selectRows(ocrIds, true)
          }, 300)
        }
      })
    // .subscribe({ next: (_) => {(this.showSpinner = false)
    // } })
  }

  ngOnDestroy(): void {
    if (!this.isAccessDeniedTemplate) this.toDefault()

    this.xsStore.dispatch(new CaseSettingStateResetAction('isCaseEditing'))
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
    this.caseSettingFacade.resetApiPrioritySettingsState()
  }

  onCreateCase() {
    const langs = this.getSelectedOcrLanguages
    const files = this.getSelectedTranscribeFiles

    /*-----------------------------------------------------------------------------
      prevent post if no OCR language is selected.
      There is also a validation which will force an user to select a language.
    ------------------------------------------------------------------------------*/
    if (!this.disableOCROPtions) {
      if (!(langs.length > 0)) {
        return false
      }
    }

    this.selectedHashFieldRows =
      this.hashFieldGrid?.instance.getSelectedRowsData()

    if (this.selectedHashFieldRows.length <= 0) {
      this.toast.error('Please select at least one hash field.')
      return
    }

    if (this.createCaseModel.SlipSheetSettings?.GenerateSlipSheet) {
      const message = this.validateSlipsheetSettings(
        this.createCaseModel.SlipSheetSettings
      )
      if (message) {
        this.toast.error(message)
        return
      }
    }

    this.selectedHashFieldIdOnly = []
    this.selectedHashFieldRows.forEach((element) => {
      this.selectedHashFieldIdOnly.push(element.HashId)
    })

    this.selectedOCRFileType =
      this.ocrFileTypeGrid?.instance.getSelectedRowsData()
    if (!this.disableOCROPtions) {
      if (this.selectedOCRFileType.length <= 0) {
        this.toast.error('Please select at least one ocr fle type.')
        return false
      }
    }

    this.selectedOCRExtensionType = []
    this.selectedOCRFileType.forEach((element: any) => {
      const ocrFileTypeClass = {
        ExtNo: element.ExtNo,
        ExtensionName: element.ExtensionName,
        Selected: true
      }
      this.selectedOCRExtensionType.push(ocrFileTypeClass)
    })

    this.selectedMetaDataField =
      this.indexMetaGrid?.instance.getSelectedRowsData()

    this.createCaseModel.MetaIndexingFields = this.selectedMetaDataField

    this.createCaseModel.OcrFileExtension = this.selectedOCRExtensionType
    this.createCaseModel.OcrAllowedList = this.selectedOCRExtensionType

    // assign selected values to the payload property.
    this.createCaseModel.LanguageSettingsForOCR = langs
    this.createCaseModel.transcribeAllowedList = files
    if (this.createCaseModel.AutoQueueTranscribe) {
      const isAnyFileTypeSelected =
        this.createCaseModel.transcribeAllowedList.some(
          (item) => item !== undefined
        )
      if (
        this.createCaseModel.transcribeAllowedList &&
        !isAnyFileTypeSelected
      ) {
        this.toast.error('Please select file type for auto Transcribing.')
        return false
      }
    }
    this.createCaseModel.PreferedFileTypeNative = []
    this.createCaseModel.PreferedFileTypeNative.push(
      this.createCaseModel.NativeOptionPST_MSG
    )
    this.createCaseModel.PreferedFileTypeNative.push(
      this.createCaseModel.NativeOptionMBOX_EML
    )
    this.createCaseModel.PreferedFileTypeNative.push(
      this.createCaseModel.NativeOptionNSF_DXL
    )

    if (this.caseForm.get('hasSecondary').value)
      this.createCaseModel.HashAlgorithmSecondary =
        this.createCaseModel.DedupingAlgo === DedupingAlgorithm.MD5
          ? DedupingAlgorithm.SHA1
          : DedupingAlgorithm.MD5
    else this.createCaseModel.HashAlgorithmSecondary = null

    this.createCaseModel.SelectedHashField = []
    this.createCaseModel.SelectedHashField = this.selectedHashFieldIdOnly

    const filetypePageLimitListCpy = _.cloneDeep(this.filetypePageLimitList)
    filetypePageLimitListCpy.forEach((element) => {
      if (element.PageLimit.toString() === 'All Pages') element.PageLimit = 0

      if (element.PageLimit.toString() === 'Max Pages') element.PageLimit = -1
    })

    this.createCaseModel.TiffFileClassPageLimit = filetypePageLimitListCpy
    //this.createCaseModel.TiffFileClassPageLimit = this.filetypePageLimitList

    if (this.createCaseModel.AllowTiff == undefined)
      this.createCaseModel.AllowTiff = this.caseForm.getRawValue()?.enableImage

    this.createCaseModel.IsImageTypePDF =
      !this.caseForm.getRawValue()?.imageTypeTiff

    if (this.createCaseModel.DefaultTiffTimeoutPeriod == undefined)
      this.createCaseModel.DefaultTiffTimeoutPeriod =
        this.caseForm.getRawValue()?.defaultTimeOut

    if (this.createCaseModel.MaxPages == undefined)
      this.createCaseModel.MaxPages =
        this.caseForm.getRawValue()?.defaultMaxPage
    else {
      if (this.caseForm.getRawValue()?.defaultMaxPage === 'All Pages') {
        this.createCaseModel.MaxPages = 0
      }
    }
    if (this.createCaseModel.DefaultTiffColorOption == undefined)
      this.createCaseModel.DefaultTiffColorOption =
        this.caseForm.getRawValue()?.defaultTiffColorOption

    if (this.createCaseModel.AllowOCR == undefined)
      this.createCaseModel.AllowOCR = this.caseForm.getRawValue()?.allowOCR

    //advanced ocr handling
    this.createCaseModel.OcrForDocumentsWithFewerText =
      this.caseForm.getRawValue()?.enableOCRThresholdTextOption

    const CtrlAvg = this.caseForm.get('oCRThresholdAverageCharacters')
    const CtrlOnePage = this.caseForm.get('oCRMinCharInPDfInAtleastOnePage')
    if (this.createCaseModel.OcrForDocumentsWithFewerText) {
      if (CtrlAvg.enabled && CtrlOnePage.disabled) {
        if (this.caseForm.getRawValue()?.oCRThresholdAverageCharacters < 1) {
          this.toast.error(
            'OCR PDF documents with average character per page less than should be greater than 0'
          )
          return false
        }
        this.createCaseModel.OCRThresholdAverageCharacters =
          this.caseForm.getRawValue()?.oCRThresholdAverageCharacters
        this.createCaseModel.OCRMinCharInPDfInAtleastOnePage = 0
      } else {
        if (this.caseForm.getRawValue()?.oCRMinCharInPDfInAtleastOnePage < 1) {
          this.toast.error(
            'OCR PDF documents if at least one page has characters less than should be greater than 0'
          )
          return false
        }
        this.createCaseModel.OCRThresholdAverageCharacters = 0
        this.createCaseModel.OCRMinCharInPDfInAtleastOnePage =
          this.caseForm.getRawValue()?.oCRMinCharInPDfInAtleastOnePage
      }
    } else {
      this.createCaseModel.OCRThresholdAverageCharacters = 0
      this.createCaseModel.OCRMinCharInPDfInAtleastOnePage = 0
    }

    this.createCaseModel.maxOcrTryCount =
      this.caseForm.getRawValue()?.maxOcrTryCount
    this.createCaseModel.IncludePartialOcredFile =
      this.caseForm.getRawValue()?.oCROtherSetting

    this.getSettingsFromChildComponents(this.createCaseModel)

    this.createCaseModel.ADGroupMapping = this.adGroupMapListData
    this.createCaseModel.IDPGroupMapping = this.idpGroupMapListData
    this.createCaseModel.customFields = this.createCaseModel.defaultCustomFields

    if (!this.isValidDataFromChild) return

    // map fulltext auto tiff ocr file types
    this.mapSelectedAutoTiffOCRFileTypes(
      this.createCaseModel['AutoTiffOCRFileTypes']
    )

    this.createCaseModel.ProjectSettings[ProjectSetting.STOP_WORDS] =
      this.stopWords

    // Map EDAI ECA control values to the model.
    // Only maps if the feature is enabled and the controls are defined.
    ;[
      ProjectSetting.ENABLE_EDAI_ECA,
      ProjectSetting.EDAI_ECA_BACKGROUND,
      ProjectSetting.EDAI_ECA_RELEVANT_DESCRIPTION,
      ProjectSetting.EDAI_ECA_NON_RELEVANT_DESCRIPTION
    ]
      .filter(
        (controlName) =>
          typeof this.caseForm.get(controlName) !== undefined &&
          this.caseForm.get(controlName) !== null
      )
      .forEach((controlName) => {
        this.createCaseModel.ProjectSettings[controlName] =
          this.caseForm.get(controlName).value
      })

    this.showSpinner = true
    if (!this.isTemplateRelated) {
      if (!this.isEdit) {
        this.store.dispatch(
          new CreateCase(Object.assign({}, this.createCaseModel))
        )
      } else {
        this.store.dispatch(
          new UpdateCase(
            this.qParams?.projectId,
            Object.assign({}, this.createCaseModel)
          )
        )
      }
    } else {
      this.createCaseModel.ProjectTemplateName =
        this.caseForm.get('caseName').value
      this.createCaseModel.ProjectTemplateNote =
        this.caseForm.get('templateNote').value
      //template create case
      if (this.isTemplateCreateModule) {
        this.store.dispatch(
          new CreateCaseTemplate(Object.assign({}, this.createCaseModel))
        )
      } else {
        //edit tempalte
        this.store.dispatch(
          new UpdateCaseTemplate(
            this.qParams?.templateId,
            Object.assign({}, this.createCaseModel)
          )
        )
      }
    }
  }

  private validateSlipsheetSettings(
    slipSheetSettings: SlipSheetContentModel
  ): string {
    if (!slipSheetSettings.slipSheetSetting) return '' // if slipsheet is null then no need to check further

    let fileTypeSelected = false // flat to indicate that at least one file type is selected

    // Iterate over each array in slipSheetSetting and validate
    for (const key in slipSheetSettings.slipSheetSetting) {
      if (
        Object.prototype.hasOwnProperty.call(
          slipSheetSettings.slipSheetSetting,
          key
        )
      ) {
        const items = slipSheetSettings.slipSheetSetting[key] // Array of SlipSheetCommonContentModel
        if (!items) continue

        for (const item of items) {
          if (!item || !item.EnableSlipsheet) continue

          fileTypeSelected = true

          if (
            item.SlipsheetType === 'LoadFromTemplate' &&
            (!item.SlipSheetTemplateId || item.SlipSheetTemplateId < 1)
          ) {
            const uiItem = INITIAL_UI_ITEMS.find((item) => item.type === key)
            return `Please provide the placeholder template for '${uiItem?.title}' in Slipsheet settings.`
          }

          if (
            item.SlipsheetType === 'SlipSheetText' &&
            (!item.SlipSheetText || !item.SlipSheetTextLocation)
          ) {
            const uiItem = INITIAL_UI_ITEMS.find((item) => item.type === key)
            return `Please provide the placeholder text for '${uiItem?.title}' in Slipsheet settings.`
          }

          if (
            item.SlipsheetType === 'SlipSheetTiffFile' &&
            !item.TiffFileName
          ) {
            const uiItem = INITIAL_UI_ITEMS.find((item) => item.type === key)
            return `Please provide the placeholder file for '${uiItem?.title}' in Slipsheet settings.`
          }
        }
      }
    }

    // At least one file type should be selected when slipsheet is enabled
    if (!fileTypeSelected)
      return 'Please select at least one file type in the Slipsheet settings.'

    return ''
  }

  private toDefault() {
    this.caseForm.reset()
    this.ocrLanguages = []
  }

  trackByFn = (item, _) => item.LangName

  /**
   * Returns controls from the FormArray
   */
  ocrControls = () => (<FormArray>this.caseForm.get('ocrLanguages')).controls

  /**
   * Show/Hide error message based on formArray valid state.
   */
  hasSelectedOcrLanguage = () => this.caseForm.get('ocrLanguages').valid

  /**
   * Check if case name is entered. If case name is not entered disable the Create Case button
   */
  //hasCaseName = () => this.caseForm.get('caseName').valid

  //For template edit , caseName will represent template name and template name is not editable so it hasto be in disabled state.
  //Disabled control was returning valid as false so logic of .invalid  false is used
  hasCaseName = () => !this.caseForm.get('caseName').invalid

  get isTagGroupListEmpty(): boolean {
    return !this.createCaseModel?.['TagGroupTagList']
  }

  //  Set default values`in Image-Conversion-Option (Edgardo)
  private SetDefaultOnNativeFileChange() {
    const fg = this.caseForm //.get('imageConversionOption') as FormGroup

    const CtrlAuto = this.caseForm.get('autoGenerateImgForIngestedFile')
    CtrlAuto.reset(false)

    const CtrlNotify = this.caseForm.get('notifyAfterImgGenComplete')
    //notifyAfterImgGenComplete control should not be disabled during template mode
    if (!this.isTemplateRelated)
      this.caseForm.getRawValue()?.nativeFileHandling
        ? CtrlNotify.enable()
        : CtrlNotify.disable()

    const imgCtrlTiff = this.caseForm.get('defaultTiffColorOption')
    //Get current values with previous values
    if (!this.isNativeFileOptionOnlyIndex) {
      if (CtrlAuto.value != this.LastAutoGenerateTiff) {
        this.caseForm
          .get('autoGenerateImgForIngestedFile')
          .setValue(this.LastAutoGenerateTiff)
      }
      if (
        imgCtrlTiff.value != this.LastDefaultTiffColorOption &&
        this.LastDefaultTiffColorOption != null
      ) {
        this.caseForm
          .get('defaultTiffColorOption')
          .setValue(this.LastDefaultTiffColorOption)
      }
    }
  }

  //  Enable/disable UI element when `autoGenerateImgForIngestedFile` (Edgardo)
  private enableDisableOnExtractNative() {
    // const fg = this.caseForm.get('imageConversionOption') as FormGroup

    const nfCtrlAuto = this.caseForm.get('autoGenerateImgForIngestedFile')

    //autoGenerateImgForIngestedFile control should not be disabled during template mode
    if (!this.isTemplateRelated)
      this.caseForm.getRawValue()?.nativeFileHandling
        ? nfCtrlAuto.enable()
        : nfCtrlAuto.disable()

    const advSettingGenerateBate = this.caseForm.get('generateBates')

    const imageControl = this.caseForm.get('enableImage')
    const imageTypeTiffCtrl = this.caseForm.get('imageTypeTiff')
    const defaultTiffColorOptionCtrl = this.caseForm.get(
      'defaultTiffColorOption'
    )
    const defaultMaxPageCtrl = this.caseForm.get('defaultMaxPage')
    const defaultTimeOutCtrl = this.caseForm.get('defaultTimeOut')

    //FOR TOA , ALLOWOCR TO BE CHECKED AND  DISABLED
    const ocrControl = this.caseForm.get('allowOCR')

    if (!this.caseForm.getRawValue()?.nativeFileHandling) {
      imageControl.setValue(true)
      imageControl.disable()
      advSettingGenerateBate.setValue(false)
      advSettingGenerateBate.disable()

      imageTypeTiffCtrl.disable()
      defaultTiffColorOptionCtrl.disable()
      defaultMaxPageCtrl.disable()
      defaultTimeOutCtrl.disable()

      ocrControl.setValue(true)
      ocrControl.disable()

      this.disableImageOptions = true
    } else {
      imageControl.enable()
      this.disableImageOptions = false
      advSettingGenerateBate.enable()
      imageTypeTiffCtrl.enable()
      defaultTiffColorOptionCtrl.enable()
      defaultMaxPageCtrl.enable()
      defaultTimeOutCtrl.enable()

      ocrControl.enable()
    }
    this.nativeFileHandling = this.caseForm.get('nativeFileHandling').value

    if (this.isEdit) {
      // Not allow changing the 'Image Conversion Options' while managing an existing case
      const nfCtrlNotify = this.caseForm.get('notifyAfterImgGenComplete')
      const imgCtrlTiff = this.caseForm.get('defaultTiffColorOption')

      if (!this.isNativeAutoPrefetchEnabled) {
        nfCtrlAuto.disable()
        nfCtrlNotify.disable()
        imgCtrlTiff.disable()
      } else {
        nfCtrlAuto.enable()
        this.enableDisableOnAutoImageChange()
      }
    } else {
      // Cteate Case: Set as defaul values
      this.SetDefaultOnNativeFileChange()
    }

    this.updateLegacyIngestionEngineValues()
  }

  private nativeFileHandlingControlChanges = (): void => {
    this.caseForm
      .get('nativeFileHandling')
      .valueChanges.pipe(debounceTime(400), takeUntil(this.unsubscribed$))
      .subscribe((isNative: boolean) => {
        //'Auto image and OCR ..' checkbox should not be disabled in case of template
        if (!this.isTemplateRelated)
          this.xsStore.dispatch(
            new NativeFileOptionHasEcaEnabledAction(!isNative)
          )
      })
  }

  private updateLegacyIngestionEngineValues() {
    if (!this.isEdit) {
      this.caseForm
        .get('nativeFileHandling')
        .valueChanges.pipe(debounceTime(400), takeUntil(this.unsubscribed$))
        .subscribe({
          next: (isChecked: boolean) => {
            const legacyEngineCtrl = this.caseForm.get(
              'isLegacyIngestionEngine'
            )
            if (isChecked) {
              legacyEngineCtrl.setValue(this.isLegacyIngestionEngine)
            } else {
              legacyEngineCtrl.setValue(false)
            }

            isChecked ? legacyEngineCtrl.enable() : legacyEngineCtrl.disable()
          }
        })
    }
  }

  /**
   * Patch default values from case template derived from backend.
   * @param t  Template setting model
   */
  private patchTiffDefaultValues(ts: ProjectSetupInfo) {
    // init change event to enable/disable UI
    this.enableDisableOnAutoImageChange()

    if (!ts) {
      return
    }

    // const fg = this.caseForm.get('imageConversionOption') as FormGroup
    // if (!fg) {
    //   return
    // }

    //const adv = t //.AdvancedTiffOption
    //const ts = t //.TiffSetting

    // Enable auto generate image option only if 'Extract Native' option is selected and
    // both Enable tiff and Auto Generate Image for ingested documents option is enabled in project template
    //this.isNativeFileOptionOnlyIndex was check which is if TOA license installed. Added !.isNativeAutoPrefetchEnabled. If native extract than auto queue tiff setting should reflect.
    const valAutoGenerate =
      this.isNativeFileOptionOnlyIndex && !this.nativeFileHandling
        ? false
        : this.caseForm.getRawValue()?.nativeFileHandling &&
          ts?.AdvancedTiffOption.AutogenerateTiff &&
          ts?.AllowTiff

    if (!this.isTemplateRelated)
      this.caseForm
        .get('autoGenerateImgForIngestedFile')
        .setValue(valAutoGenerate)

    this.caseForm
      .get('notifyAfterImgGenComplete')
      .setValue(
        ts.AdvancedTiffOption.IgnoreAutoTiffJobsForMediaProcessingStatus
      )

    // Select BLACK_AND_WHITE as default tiff color option if the template doesnot have information about tiff color option

    if (ts.DefaultTiffColorOption == null && this.isNativeFileOptionOnlyIndex) {
      this.caseForm.get('defaultTiffColorOption').setValue(1)
    } else {
      this.caseForm
        .get('defaultTiffColorOption')
        .setValue(ts.DefaultTiffColorOption)
    }

    /*** */
    if (ts.IsImageTypePDF) this.caseForm.get('imageTypeTiff').setValue(false)
    else this.caseForm.get('imageTypeTiff').setValue(true)
    /*** */

    this.caseForm.updateValueAndValidity()
  }

  /**
   * Enable/disable UI element when `autoGenerateImgForIngestedFile` value change
   */
  private enableDisableOnAutoImageChange() {
    // We have a child form group in parent form
    // const fg = this.caseForm //.get('imageConversionOption') as FormGroup
    // if (!fg) {
    //   return
    // }
    // this.caseForm
    //   .get('autoGenerateImgForIngestedFile')
    //   .valueChanges.pipe(debounceTime(400), takeUntil(this.unsubscribed$))
    //   .subscribe({
    //     next: (isChecked) => {
    //       // Controls
    //       const imgCtrl = this.caseForm.get('defaultTiffColorOption')
    //       const nfCtrl = this.caseForm.get('notifyAfterImgGenComplete')
    //       // Enable/disable based in checked value
    //       isChecked ? imgCtrl.enable() : imgCtrl.disable()
    //       isChecked ? nfCtrl.enable() : nfCtrl.disable()
    //       // Reset to default
    //       nfCtrl.reset(false)
    //     }
    //   })
  }

  /**
   * Add controls to the form array (Checkbox input)
   */
  private patchOcrLanguages(t: ProjectSetupInfo) {
    //CaseTemplateSettings
    if (
      !(
        t &&
        t.AdvanceOcrOptionLanguages &&
        t.AdvanceOcrOptionLanguages.length > 0
      )
    ) {
      return
    }

    /*-----------------------------------------------------
     store array data to use later.
     NOTE: The property `ocrLanguages` being used to filter selected languages for API payload.
     ------------------------------------------------*/

    // we want to sort alphabetically so user may feel easy to search.
    // built-in support for things like language-specific sort ordering, ignoring cases or diacritics
    const sorted = Array.from(t.AdvanceOcrOptionLanguages).sort((a, b) => {
      return a.LangName.localeCompare(b.LangName, 'en', { sensitivity: 'base' })
    })

    // clear existing to de-dupe when NGRX state may there to re-emit
    this.ocrLanguages = []

    this.ocrLanguages = sorted

    const ctrl = this.caseForm.get('ocrLanguages') as FormArray

    if (!ctrl) {
      return
    }

    // clear existing
    ctrl.clear()
    let checkbox: FormControl

    setTimeout(() => {
      sorted.forEach((o, l) => {
        checkbox = new FormControl(
          !this.isEdit || this.isTemplateEditModule
            ? o?.LangName?.trim().toLowerCase() === 'english' || o?.Selected // english must be marked as checked by default in case of create
            : o?.Selected
        )
        if (this.disableOCROPtions && !this.isNativeFileOptionOnlyIndex)
          checkbox.disable()
        ctrl.push(checkbox)
      })
    }, 100)
  }

  //enable or disable ocr wise languagees to be enable /disable
  private controlOcrLanguages() {
    const ctrl = this.caseForm.get('ocrLanguages') as FormArray
    if (!ctrl) {
      return
    }

    if (this.disableOCROPtions && !this.isNativeFileOptionOnlyIndex)
      ctrl.disable()
    else ctrl.enable()
  }

  /**
   * minimum validation:
   * we show a message to notify the user they must check at least one item.
   */
  private minSelectedCheckboxes(min = 1) {
    const validator: any = (formArray: FormArray) => {
      const totalSelected = formArray.controls
        // get a list of checkbox values (boolean)
        ?.map((control) => control.value)
        // total up the number of checked checkboxes
        .reduce((prev, next) => (next ? prev + next : prev), 0)

      // if the total is not greater than the minimum, return the error message
      return totalSelected >= min ? null : { required: true }
    }

    return validator
  }

  /**
   * Returns selected languages only.
   * NOTE: The patched array of `FormArray` and the local property `ocrLanguages` must have the same order to filter selected languages.
   * @see patchOcrLanguages method for ordering and assigning the values to both local property and FormArray
   */
  private get getSelectedOcrLanguages(): string[] {
    const selected = this.caseForm.value.ocrLanguages
      ?.map((v: string, i: number) =>
        v ? this.ocrLanguages[i].LangName : null
      )
      .filter((v: string) => v !== null)
    return selected
  }

  private get getSelectedTranscribeFiles(): string[] {
    const selected = this.supportedFilesArray?.map((file) => {
      if (file.selected == true) return file.fileName
    })

    return selected
  }

  showConfirmationModal(title: string, message: string): Observable<boolean> {
    const modal = this.modalService.show(ConfirmationDialogComponent, {
      class: 'modal-dialog-centered'
    })
    ;(<ConfirmationDialogComponent>modal.content).showConfirmationModal(
      title,
      message,
      true
    )
    return (<ConfirmationDialogComponent>modal.content).onClose
  }

  initializeAudioTranscribe() {
    //fetch the available engine from control settings
    this.store
      .pipe(
        select(getControlSetting('TRANSCRIBING_ENGINES')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((engines: string) => {
        //since engines are comma seperated we have to split them
        const engineArray: string[] = engines?.split(',')
        this.transcribingEngines = engineArray?.map((eng) => {
          //make deepspeech selected by default
          if (eng.toLowerCase() === 'deepspeech') this.selectedEngine = eng
          return eng.trim()
        })
      })
    //get the supported file for the engine types as
    this.store
      .pipe(
        select(getSupportedFileTypesForTranscribing),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((fileTypes: TranscribeSupportedFiles[]) => {
        this.ref.markForCheck()
        if (fileTypes && fileTypes != null) {
          this.transcribeSupportedFiles = fileTypes
        }
      })
    //populate the current supported files according to the default selected engine
    if (this.selectedEngine != null && this.selectedEngine.length > 0) {
      this.caseForm.patchValue({
        transcribeSettings: this.selectedEngine
      })
      this.onTranscribeEngineChanged(this.selectedEngine)
    }
  }

  onTranscribeEngineChanged(currentEngine) {
    let supportedFiles: string[] = []
    this.transcribeSupportedFiles?.forEach((element) => {
      if (element.Engine === currentEngine) {
        supportedFiles = element.SupportedFiles
      }
    })
    this.supportedFilesArray = []
    supportedFiles?.forEach((el) => {
      this.supportedFilesArray.push({ fileName: el, selected: false })
    })
    if (currentEngine !== 'DeepSpeech') {
      this.transcribeAccessKeys.forEach((element) => {
        if (
          currentEngine === element.Engine ||
          currentEngine === element.Engine + ' Transcribe'
        ) {
          if (!element.AccessKey) {
            this.accessKeyMissingMessage =
              currentEngine + ' Keys are not configured'
          } else this.accessKeyMissingMessage = ''
        }
      })
    } else this.accessKeyMissingMessage = ''
  }

  onSelectedFilesChange(name: string, selected: boolean) {
    this.supportedFilesArray.forEach((file) => {
      if (name === file.fileName) {
        file.selected = selected
      }
    })
  }

  getAccessKeys() {
    this.store
      .pipe(select(getAccessKeys), takeUntil(this.unsubscribed$))
      .subscribe((accessKeys) => {
        this.transcribeAccessKeys = accessKeys
      })
  }

  /**
   * update checkbox value on ocrlanguage formarray
   * quickfix for regression bug pass, need to re-write formarray portion */

  onCheckboxChange(event: Event, index: number) {
    const myForm = (<FormArray>this.caseForm.get('ocrLanguages')).at(index)
    myForm.patchValue(event?.target['checked'])
  }

  validateStartNumber(event: any) {
    const startNumberCtrl = this.caseForm.get('startNumber')
    if (
      parseInt(startNumberCtrl.value + event.key) > 2147483647 ||
      event.key === '-'
    ) {
      return false
    }
    if (!startNumberCtrl.value && event.key === '0') {
      return false
    }
    const charCode = event.which ? event.which : event.keyCode
    return !(charCode > 31 && (charCode < 48 || charCode > 57))
  }

  validatePadding(event: any) {
    const paddingCtrl = this.caseForm.get('padding')
    if (parseInt(paddingCtrl.value + event.key) > 99 || event.key === '-') {
      return false
    }
    if (!paddingCtrl.value && event.key === '0') {
      return false
    }
    const charCode = event.which ? event.which : event.keyCode
    return !(charCode > 31 && (charCode < 48 || charCode > 57))
  }

  checkMaxMinTimeout(event: any, idValue: string, timeValue: any, data: any) {
    const ctrlId = '#timeout' + idValue
    if (timeValue < 1 || timeValue > 999) {
      this.toast.error('Please insert the time between 1 and 999')
      setTimeout(() => {
        ;(document.querySelector(ctrlId) as HTMLInputElement).value = null
      }, 1000)
    }

    const ctrlIdPL = '#imagePageLimit' + data.data.FileTypeGroupID
    if (data.data.PageLimit == -1) {
      setTimeout(() => {
        ;(document.querySelector(ctrlIdPL) as HTMLInputElement).value =
          'Max Pages'
      }, 1000)
    }
    if (data.data.PageLimit == 0) {
      setTimeout(() => {
        ;(document.querySelector(ctrlIdPL) as HTMLInputElement).value =
          'All Pages'
      }, 1000)
    }
  }

  onBlurCheckMinMaxValue() {
    const defaultTimeOut = this.caseForm.get('defaultTimeOut')
    if (defaultTimeOut.value < 1 || defaultTimeOut.value > 999) {
      this.toast.error('Please insert the time between 1 and 999')
      this.caseForm.get('defaultTimeOut').setValue(5)
      return
    }
  }

  validateTimeout(event: any, idValue: string, timeValue: any) {
    const ctrlId = '#timeout' + idValue

    //this.tiffTimeoutRetain=(document.querySelector(ctrlId) as HTMLInputElement).value

    const charCode = event.which ? event.which : event.keyCode
    return !(charCode > 31 && (charCode < 48 || charCode > 57))
  }

  validateDefaultTimeout(event: any) {
    const charCode = event.which ? event.which : event.keyCode
    return !(charCode > 31 && (charCode < 48 || charCode > 57))
  }

  validateDefaultMaxPage(event: any) {
    const charCode = event.which ? event.which : event.keyCode
    return !(charCode > 31 && (charCode < 48 || charCode > 57))
  }

  SetPageLimitDefaultValue(data: any) {
    const ctrlId = '#imagePageLimit' + data.data.FileTypeGroupID
    if (data.data.PageLimit == -1) {
      setTimeout(() => {
        ;(document.querySelector(ctrlId) as HTMLInputElement).value =
          'Max Pages'
      }, 1000)
    }
    if (data.data.PageLimit == 0) {
      setTimeout(() => {
        ;(document.querySelector(ctrlId) as HTMLInputElement).value =
          'All Pages'
      }, 1000)
    }
  }

  GetSelectedPageLimit(event: any, data: any, value: any) {
    const defaultMaxControl = this.caseForm.get('defaultMaxPage')
    const ctrlId = '#imagePageLimit' + data.data.FileTypeGroupID

    const regExp = /[a-zA-Z]/g
    const testString = data.data.PageLimit

    if (
      regExp.test(testString) &&
      testString !== 'Max Pages' &&
      testString !== 'All Pages'
    ) {
      this.toast.error(
        "Page Limit is required in file type class '" +
          data.data.FileTypeGroup +
          "'."
      )
      setTimeout(() => {
        data.data.PageLimit = 'All Pages'
        ;(document.querySelector(ctrlId) as HTMLInputElement).value =
          'All Pages'
      }, 1000)
    }

    if (data.data.PageLimit.length == 0) {
      this.toast.error(
        "Page Limit is required in file type class '" +
          data.data.FileTypeGroup +
          "'."
      )
      setTimeout(() => {
        data.data.PageLimit = 'All Pages'
        ;(document.querySelector(ctrlId) as HTMLInputElement).value =
          'All Pages'
      }, 1000)
      return false
    }

    if (data.data.PageLimit > defaultMaxControl.value) {
      this.toast.error(
        "Page Limit cannot exceed 'Max Pages' value (" +
          defaultMaxControl.value +
          ") in file type class '" +
          data.data.FileTypeGroup +
          "'."
      )
      setTimeout(() => {
        data.data.PageLimit = 'All Pages'
        ;(document.querySelector(ctrlId) as HTMLInputElement).value =
          'All Pages'
      }, 1000)
      return false
    }
  }

  getSettingsFromChildComponents(psi: ProjectSetupInfo) {
    this.isValidDataFromChild = true

    this.getSettingsFromHTMLConversionSetting(psi)

    this.getSettingsFromEmbeddedItemSetting(psi)

    this.getSettingsFromNDDSetting(psi)

    this.getSettingsFromDateFilter(psi)

    this.getSettingsFromImageFileExtension(psi)

    this.getSettingsFromArchiveFileExtension(psi)

    this.getSettingsFromFileFilterExtension(psi)

    this.getSettingsFromIngestion(psi)

    this.getSettingsFromIngestionAdvancedSetting(psi)

    this.getSettingFromSocialMedia(psi)
  }

  getSettingsFromHTMLConversionSetting(psi: ProjectSetupInfo) {
    psi.ShowHiddenRowColumnSheets = this.caseForm.get(
      'showHiddenRowsColums'
    ).value
    psi.ShowHiddenText = this.caseForm.get('showHiddenText').value
    psi.ShowTrackChanges = this.caseForm.get('showTrackChanges').value
    psi.ShowSpreadsheetGridLines = this.caseForm.get(
      'showSpreadSheetGridLines'
    ).value
  }

  getSettingsFromEmbeddedItemSetting(psi: ProjectSetupInfo) {
    /*EMAIL*/
    const embeddedEmailVal = this.caseForm.get('embeddedEmail').value

    if (embeddedEmailVal === 'EXCLUDE_NONE')
      psi.EmbeddedFilterSettingsEmail = EmbeddedFilterType.EXCLUDE_NONE
    else if (embeddedEmailVal === 'EXCLUDE_ALL_EMBEDDED_FILES')
      psi.EmbeddedFilterSettingsEmail =
        EmbeddedFilterType.EXCLUDE_ALL_EMBEDDED_FILES
    else {
      const excludeEmailNonRecognizableVal = this.caseForm.get(
        'excludeEmailNonRecognizable'
      ).value
      const excludeEmailAllEmbeddedVal = this.caseForm.get(
        'excludeEmailAllEmbedded'
      ).value
      if (excludeEmailNonRecognizableVal && excludeEmailAllEmbeddedVal)
        psi.EmbeddedFilterSettingsEmail =
          EmbeddedFilterType.EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES
      else if (excludeEmailNonRecognizableVal && !excludeEmailAllEmbeddedVal)
        psi.EmbeddedFilterSettingsEmail =
          EmbeddedFilterType.EXCLUDE_ALL_EMBEDDED_UNKNOWN_FILES
      else if (!excludeEmailNonRecognizableVal && excludeEmailAllEmbeddedVal)
        psi.EmbeddedFilterSettingsEmail =
          EmbeddedFilterType.EXCLUDE_ALL_EMBEDDED_IMAGE_FILES
      else {
        this.toast.error(
          'Please select at least one custom email embedded filter option'
        )
        this.isValidDataFromChild = false
        return
      }
    }

    /*EDOC*/
    const embeddedEdocVal = this.caseForm.get('embeddedEdoc').value
    if (embeddedEdocVal === 'EXCLUDE_NONE')
      psi.EmbeddedFilterSettingsEdoc = EmbeddedFilterType.EXCLUDE_NONE
    else if (embeddedEdocVal === 'EXCLUDE_ALL_EMBEDDED_FILES')
      psi.EmbeddedFilterSettingsEdoc =
        EmbeddedFilterType.EXCLUDE_ALL_EMBEDDED_FILES
    else {
      const excludeEdocNonRecognizableVal = this.caseForm.get(
        'excludeEdocNonRecognizable'
      ).value
      const excludeEdocAllEmbeddedVal = this.caseForm.get(
        'excludeEdocAllEmbedded'
      ).value
      if (excludeEdocNonRecognizableVal && excludeEdocAllEmbeddedVal)
        psi.EmbeddedFilterSettingsEdoc =
          EmbeddedFilterType.EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES
      else if (excludeEdocNonRecognizableVal && !excludeEdocAllEmbeddedVal)
        psi.EmbeddedFilterSettingsEdoc =
          EmbeddedFilterType.EXCLUDE_ALL_EMBEDDED_UNKNOWN_FILES
      else if (!excludeEdocNonRecognizableVal && excludeEdocAllEmbeddedVal)
        psi.EmbeddedFilterSettingsEdoc =
          EmbeddedFilterType.EXCLUDE_ALL_EMBEDDED_IMAGE_FILES
      else {
        this.toast.error(
          'Please select at least one custom edoc embedded filter option'
        )
        this.isValidDataFromChild = false
        return
      }
    }

    /*PPT*/
    const embeddedPPTVal = this.caseForm.get('embeddedPPT').value
    if (embeddedPPTVal === 'EXCLUDE_NONE')
      psi.EmbeddedFilterSettingsPPT = EmbeddedFilterType.EXCLUDE_NONE
    else if (embeddedPPTVal === 'EXCLUDE_ALL_EMBEDDED_FILES')
      psi.EmbeddedFilterSettingsPPT =
        EmbeddedFilterType.EXCLUDE_ALL_EMBEDDED_FILES
    else {
      const excludePPTNonRecognizableVal = this.caseForm.get(
        'excludePPTNonRecognizable'
      ).value
      const excludePPTAllEmbeddedVal = this.caseForm.get(
        'excludePPTAllEmbedded'
      ).value
      if (excludePPTNonRecognizableVal && excludePPTAllEmbeddedVal)
        psi.EmbeddedFilterSettingsPPT =
          EmbeddedFilterType.EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES
      else if (excludePPTNonRecognizableVal && !excludePPTAllEmbeddedVal)
        psi.EmbeddedFilterSettingsPPT =
          EmbeddedFilterType.EXCLUDE_ALL_EMBEDDED_UNKNOWN_FILES
      else if (!excludePPTNonRecognizableVal && excludePPTAllEmbeddedVal)
        psi.EmbeddedFilterSettingsPPT =
          EmbeddedFilterType.EXCLUDE_ALL_EMBEDDED_IMAGE_FILES
      else {
        this.toast.error(
          'Please select at least one custom powerpoint embedded filter option'
        )
        this.isValidDataFromChild = false
        return
      }
    }
    if (this.isValidDataFromChild) this.isValidDataFromChild = true
  }

  getSettingsFromNDDSetting(psi: ProjectSetupInfo) {
    const disclaimerVal: string[] = []

    psi.AutoQueueNDDSig = this.caseForm.get('autoQueueNDDSig').value
    const nddThreasholdVal = this.caseForm.get('nDDMinThershold').value
    if (parseInt(nddThreasholdVal) < 40 || parseInt(nddThreasholdVal) > 100) {
      this.toast.error(
        'Minimum threshold value for near duplicates can only be within range of 40 and 100'
      )
      this.isValidDataFromChild = false
      return
    }
    psi.NDDMinThershold = this.caseForm.get('nDDMinThershold').value

    const nddMinCharVal = this.caseForm.get('nDDMinChar').value
    if (parseInt(nddMinCharVal) < 100) {
      this.toast.error(
        'Minimum character length for near duplicates cannot be less than 100 characters'
      )
      this.isValidDataFromChild = false
      return
    }

    psi.NDDMinChar = this.caseForm.get('nDDMinChar').value
    psi.IncludeMetadataForNDD = this.caseForm.get('includeMetadataForNDD').value

    if (this.caseForm.get('filterDisclaimer').value) {
      if (this.disclaimerListData.length <= 0) {
        this.toast.error(
          'Please provide the disclaimer to remove while generating hash signature'
        )
        this.isValidDataFromChild = false
        return
      }
    }

    if (this.disclaimerListData.length > 0)
      this.disclaimerListData.forEach((element) => {
        disclaimerVal.push(element.DisclaimerText)
      })

    psi.NDDDisclaimerList = disclaimerVal
    if (this.isValidDataFromChild) this.isValidDataFromChild = true
  }

  getDisclaimerList(disclaimerListFromchild: DisclaimerList[]) {
    this.disclaimerListData = disclaimerListFromchild
  }

  getStopWords(stopWords: string) {
    this.stopWords = stopWords
  }

  getDateFilterList(dateFilterListFromchild: DateRestriction[]) {
    this.dateFilterListData = dateFilterListFromchild
  }

  getSettingsFromDateFilter(psi: ProjectSetupInfo) {
    psi.DateResctrictions = this.dateFilterListData
    if (this.isValidDataFromChild) this.isValidDataFromChild = true
  }

  getImageFileExtensionList(imageFileExtListFromchild: ImageFileExtension[]) {
    this.imagefileExtensionListData = imageFileExtListFromchild
  }

  getSettingsFromImageFileExtension(psi: ProjectSetupInfo) {
    const dict = {}
    psi.OverrideFileTypeAssignment = this.caseForm.get(
      'overrideFileTypeAssignment'
    ).value
    if (this.imagefileExtensionListData.length > 0)
      this.imagefileExtensionListData.forEach((element) => {
        dict[element.Extension] = element.ImagerPlugin
      })
    psi.dicTiffFileExtensionSetting = dict
  }

  getSettingsFromArchiveFileExtension(psi: ProjectSetupInfo) {
    const filterExtensionVal = this.caseForm
      .get('fileIdentificationFilterList')
      .value.toUpperCase()
      .split('\n')

    let finalExtensionList: string[] = []
    filterExtensionVal.forEach((item) => {
      const rowData = item.split(',') //to handle data in comma separated format in single row
      if (rowData.length > 0) {
        rowData.forEach((element) => {
          if (element.length > 0) {
            //skip first .
            let newElement
            element.substring(0, 1) == '.'
              ? (newElement = element.substring(1))
              : (newElement = element)
            finalExtensionList.push(newElement)
          }
        })
      } else {
        //skip first .
        if (item.length > 0) {
          let newItem
          item.substring(0, 1) == '.'
            ? (newItem = item.substring(1))
            : (newItem = item)
          finalExtensionList.push(newItem)
        }
      }
    })

    //Remove Duplicate
    finalExtensionList = finalExtensionList.filter(
      (n, i) => finalExtensionList.indexOf(n) === i
    )

    psi.ArchivalFileExtension = {
      TreatAsEdoc: this.caseForm.get('treatAsEdoc').value,
      Extensions: finalExtensionList
    }
  }

  validateOCR(event: any, controlName: string) {
    //maxOcrTryCount
    const paddingCtrl = this.caseForm.get(controlName)
    if (
      (controlName == 'maxOcrTryCount' &&
        parseInt(paddingCtrl.value + event.key) > 10) ||
      event.key === '-'
    ) {
      return false
    }
    if (
      parseInt(paddingCtrl.value + event.key) > 2147483647 ||
      event.key === '-'
    ) {
      return false
    }

    if (!paddingCtrl.value && event.key === '0') {
      return false
    }
    const charCode = event.which ? event.which : event.keyCode
    return !(charCode > 31 && (charCode < 48 || charCode > 57))
  }

  setMaxValue() {
    this.caseForm.get('oCRThresholdAverageCharacters').setValue(2147483647)
  }

  getSettingsFromFileFilterExtension(psi: ProjectSetupInfo) {
    psi.FileExtensionFilterInclude = this.caseForm.get(
      'fileExtensionFilterInclude'
    ).value

    psi.fileExtensionAdvanceFilterOpt = {
      FilterLevel: this.caseForm.get('fileExtfilterLevel').value,
      ApplyContainerFile: !this.caseForm.get('applyContainerFile').value
    }

    const filterExtensionVal = this.caseForm
      .get('fileExtFilterList')
      .value.toUpperCase()
      .split('\n')

    let finalExtensionList: string[] = []
    filterExtensionVal.forEach((item) => {
      const rowData = item.split(',') //to handle data in comma separated format in single row
      if (rowData.length > 0) {
        rowData.forEach((element) => {
          if (element.length > 0) {
            //skip first .
            let newElement
            element.substring(0, 1) == '.'
              ? (newElement = element.substring(1))
              : (newElement = element)
            finalExtensionList.push(newElement)
          }
        })
      } else {
        //skip first .
        if (item.length > 0) {
          let newItem
          item.substring(0, 1) == '.'
            ? (newItem = item.substring(1))
            : (newItem = item)
          finalExtensionList.push(newItem)
        }
      }
    })

    //Remove Duplicate
    finalExtensionList = finalExtensionList.filter(
      (n, i) => finalExtensionList.indexOf(n) === i
    )

    psi.ExtensionList = finalExtensionList
  }

  getJobListADV(ingestionAdvancedsetingFromchild: ExcludeJobsDic[]) {
    this.ingestionAdvancedsetingJobs = ingestionAdvancedsetingFromchild
  }

  getSettingsFromIngestion(psi: ProjectSetupInfo) {
    psi.minValidDateTime = this.caseForm.get('minValidDateTime').value
    psi.NsfDateTimeFormat = this.caseForm.get('NsfDateTimeFormat').value

    psi.ProjectSettings.DO_NOT_EXTRACT_FUTURE_DATETIME = this.caseForm.get(
      'DO_NOT_EXTRACT_FUTURE_DATETIME'
    ).value

    psi.SetMeetingDate = this.caseForm.get('SetMeetingDate').value
    psi.AutoCopyCrashDocumentFlag = this.caseForm.get(
      'AutoCopyCrashDocumentFlag'
    ).value
    psi.DefaultIngestionTimeout = this.caseForm.get(
      'DefaultIngestionTimeout'
    ).value

    psi.ExtractInternetEmailHeader = this.caseForm.get(
      'ExtractInternetEmailHeader'
    ).value

    psi.PopulateDuplicateFilePath = this.caseForm.get(
      'PopulateDuplicateFilePath'
    ).value

    psi.PopulateCustodianDedup = this.caseForm.get(
      'PopulateCustodianDedup'
    ).value

    psi.ProjectSettings.NSF_VIEW_EXTRACTION_OPTION = this.caseForm.get(
      'NSF_VIEW_EXTRACTION_OPTION'
    ).value

    psi.ProjectSettings.AUTO_FOLDER_RELATIVE_PATH = this.caseForm.get(
      'autoFolderRelativePathDuringIngestion'
    ).value

    //ingestion timeout
    if (
      this.ingestionFileTypeTimeOut != undefined &&
      this.ingestionFileTypeTimeOut.length > 0
    ) {
      //TO handle undefined  as timeout
      let dictTime = []
      dictTime = this.ingestionFileTypeTimeOut

      dictTime.forEach((element) => {
        if (element.TimeOut == undefined) {
          element.TimeOut = 'Default'
        }
      })

      this.ingestionFileTypeTimeOut = dictTime

      psi.IngestionFileTypeTimeOut = this.ingestionFileTypeTimeOut
    } else psi.IngestionFileTypeTimeOut = this.originalngestionFileTypeTimeOut
  }

  getTimeOutItem(ingestionTimeOut: FileTypeTimeOut[]) {
    this.ingestionFileTypeTimeOut = ingestionTimeOut
  }

  getSettingsFromIngestionAdvancedSetting(psi: ProjectSetupInfo) {
    const dict = {}
    this.ingestionAdvancedsetingJobs.forEach((element) => {
      dict[element.JobName] = element.SelectedFlag
    })

    psi.DuplicateComputingOption =
      this.caseForm.getRawValue()?.DUPLICATE_COMPUTING_OPTION
    psi.FileCopyOpt = this.caseForm.getRawValue()?.FileCopyOpt
    psi.DuplicateExcludeJobsSettings = {
      ExcludeJob: this.caseForm.get('DoNotQueue').value,
      ExcludeLevel: this.caseForm.get('ExcludeLevel').value,
      ExcludedJobs: dict
    }
  }

  getADGroupMappingList(adGroupMapList: ADGroupMapping[]) {
    this.adGroupMapListData = adGroupMapList
  }

  getSettingFromSocialMedia(psi: ProjectSetupInfo) {
    // if(this.isEdit && (psi.IngestionEngine==1))
    this.getSplitType(psi)
  }

  getSplitType(psi: ProjectSetupInfo) {
    const newPsi = _.cloneDeep(psi)
    const socialMediaCtrl = this.caseForm.get('SplitType')
    let splitOptions: SplitOptions
    switch (socialMediaCtrl.value) {
      case 'ENTIRE_CONVERSATION':
        newPsi.SocialMediaSettings.SplitOptions = {
          SplitType: socialMediaCtrl.value,
          SplitValue: 1
        }
        break
      case 'INDIVIDUAL_MESSAGE':
        newPsi.SocialMediaSettings.SplitOptions = {
          SplitType: socialMediaCtrl.value,
          SplitValue: 1
        }
        break
      case 'NUMBER_OF_MESSAGES':
        if (this.caseForm.get('NUMBER_OF_MESSAGES_VALUE').value < 1) {
          this.toast.error(
            'Please enter a valid number in Split Option for Social Media Setting. The number should be greater than 0(zero)'
          )
          this.isValidDataFromChild = false
          return
        }
        splitOptions = {
          SplitType: socialMediaCtrl.value,
          SplitValue: this.caseForm.get('NUMBER_OF_MESSAGES_VALUE').value
        }

        newPsi.SocialMediaSettings.SplitOptions = splitOptions

        break

      case 'DURATION':
        if (this.caseForm.get('SplitValueConversation').value < 1) {
          this.toast.error(
            'Please enter a valid number in Split Option for Social Media Setting. The number should be greater than 0(zero)'
          )
          this.isValidDataFromChild = false
          return
        }
        newPsi.SocialMediaSettings.SplitOptions = {
          SplitType: this.caseForm.get('SplitValueConversationDropDown').value,
          SplitValue: this.caseForm.get('SplitValueConversation').value
        }
        break
      case 'GAP':
        if (this.caseForm.get('SplitValueGap').value < 1) {
          this.toast.error(
            'Please enter a valid number in Split Option for Social Media Setting. The number should be greater than 0(zero)'
          )
          this.isValidDataFromChild = false
          return
        }
        newPsi.SocialMediaSettings.SplitOptions = {
          SplitType: this.caseForm.get('SplitValueGapDropDown').value,
          SplitValue: this.caseForm.get('SplitValueGap').value
        }
        break
    }
    if (this.isValidDataFromChild) this.isValidDataFromChild = true

    psi.SocialMediaSettings = newPsi.SocialMediaSettings
  }

  getIDPGroupMappingList(idpGroupMapList: ADGroupMapping[]) {
    this.idpGroupMapListData = idpGroupMapList
  }

  private checkTemplatePermission() {
    if (this.isTemplateRelated)
      this.allowCreateEditCaseTemplate$
        .pipe(takeUntil(this.unsubscribed$))
        .subscribe((res) => {
          if (!res) {
            this.isAccessDeniedTemplate = true
            this.toast.error('Access Denied')
            this.router.navigateByUrl('/admin/system/dashboard')
          }
        })
  }

  onCustomTagCreating(event) {
    const newItem = {
      id: event.text,
      name: event.text
    }
    this.internalDomains = [...this.internalDomains, newItem]
    event.customItem = newItem
  }

  onMultiTagPreparing(args) {
    args.cancel = true
  }
}
