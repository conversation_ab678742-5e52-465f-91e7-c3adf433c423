import { Injectable } from '@angular/core'
import { Actions, Effect, ofType } from '@ngrx/effects'
import { Action, select, Store } from '@ngrx/store'
import { ConfirmationModalComponent } from '@shared/components/confirmation-modal/confirmation-modal.component'
import { BsModalService } from 'ngx-bootstrap/modal'
import { from, Observable, timer } from 'rxjs'
import {
  catchError,
  map,
  mergeMap,
  switchMap,
  takeWhile,
  tap,
  withLatestFrom
} from 'rxjs/operators'
import { ErrorService } from '../../../../services/error.service'
import {
  GlobalErrorAction,
  GlobalSuccessAction
} from '../../../../store/actions'
import { CustomField } from '../../models/custom-field'
import { CustodianConfig } from '../../models/import-config'
import { ImportConfigService } from '../../services/import-config.service'
import { SharedService } from '../../services/shared-service.service'
import {
  AddCustomFieldError,
  AddCustomFields,
  AddCustomFieldsSuccess,
  AddVenioFields,
  AddVenioToLoadFileFieldMapping,
  CheckImportTemplateExistance,
  ConfigureImageMappingField,
  ConfigureImageProcessOption,
  ConfigureLoadFilePath,
  ConfigureLoadFileProcessOption,
  ConfigureMapping,
  CreateImportTemplate,
  FetchCustomDelimiterAction,
  FetchImportConfig,
  FetchImportConfigSuccess,
  FetchImportProgress,
  FetchImportProgressFailure,
  FetchImportProgressSuccess,
  FetchImportStatus,
  FetchImportStatusFailure,
  FetchImportStatusSuccess,
  FetchImportSummary,
  FetchImportTemplateConfig,
  FetchImportTemplates,
  FetchImportTemplateSuccess,
  HideCustomFieldsSpinner,
  HideImportSpinner,
  HideValidateFilePathsSpinner,
  HideValidationSpinner,
  ImportConfigActionTypes,
  MapCustodian,
  RefreshImportTicker,
  RemoveVenioToLoadFileFieldMapping,
  SetCustomDelimiterAction,
  SetImportTemplate,
  SetImportTemplateExistance,
  StartImport,
  StartPollingForProgress,
  StartSchedulerForImportPushUpdates,
  StopPollingForProgress,
  UpdateFieldMappingFromSummary,
  UpdateFullTextMappingFieldFromSummary,
  UpdateImportConfigLoadFileFormatFromSummary,
  UpdateImportTemplate,
  UpdateNativeMappingFieldFromSummary,
  ValidateFieldMapping,
  ValidateFilePaths,
  ValidateFilePathsFailure,
  ValidateFilePathsSuccess,
  ValidateLoadFilesAndResources
} from '../actions'
import { ImportConfigState } from '../reducers/import-config.reducer'
import { getConfigCustodian } from '../selectors/import-config.selectors'

@Injectable()
export class ImportConfigEffects {
  constructor(
    private actions$: Actions,
    private store: Store<ImportConfigState>,
    private modalService: BsModalService,
    private sharedService: SharedService,
    private service: ImportConfigService,
    private errorService: ErrorService
  ) {}

  @Effect()
  fetchImportConfig$: Observable<Action> = this.actions$.pipe(
    ofType<FetchImportConfig>(ImportConfigActionTypes.FetchImportConfig),
    switchMap((action) => {
      return this.service
        .fetchImportConfig(action.payload.projectId, action.payload.importId)
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new FetchImportConfigSuccess(res.data),
                new ConfigureMapping(res.data.venioToLoadFileMapping),
                new FetchImportSummary({
                  importId: action.payload.importId,
                  projectId: action.payload.projectId
                })
              ]
            }
            return []
          }),
          catchError((err) => from([new GlobalErrorAction(err, true, true)]))
        )
    })
  )

  @Effect()
  configureLoadFilePath$: Observable<Action> = this.actions$.pipe(
    ofType<ConfigureLoadFilePath>(
      ImportConfigActionTypes.ConfigureLoadFilePath
    ),
    switchMap(() => {
      return [
        new UpdateImportConfigLoadFileFormatFromSummary(),
        new UpdateFieldMappingFromSummary(),
        new UpdateNativeMappingFieldFromSummary(),
        new UpdateFullTextMappingFieldFromSummary()
      ]
    })
  )

  @Effect()
  mapCustodian$: Observable<Action> = this.actions$.pipe(
    ofType<MapCustodian>(ImportConfigActionTypes.MapCustodian),
    withLatestFrom(this.store.pipe(select(getConfigCustodian))),
    switchMap(([action, custodian]: [MapCustodian, CustodianConfig]) => {
      if (
        custodian &&
        custodian.custodianFromLoadFileField &&
        custodian.custodianLoadFileField
      ) {
        return [
          new AddVenioToLoadFileFieldMapping(
            'CUSTODIAN',
            custodian.custodianLoadFileField
          )
        ]
      }
      return []
    })
  )

  @Effect()
  configureMapping$: Observable<Action> = this.actions$.pipe(
    ofType<ConfigureMapping>(ImportConfigActionTypes.ConfigureMapping),
    map((action) => action.mapping),
    mergeMap((mapping) => {
      const addActions = []
      Object.keys(mapping).forEach((venioField) => {
        addActions.push(
          new AddVenioToLoadFileFieldMapping(venioField, mapping[venioField])
        )
      })
      addActions.push(new MapCustodian())
      return addActions
    })
  )

  @Effect()
  configureImageMappingField$: Observable<Action> = this.actions$.pipe(
    ofType<ConfigureImageMappingField>(
      ImportConfigActionTypes.ConfigureImageMappingField
    ),
    map((action) => action.payload),
    mergeMap((field) => {
      return [
        new RemoveVenioToLoadFileFieldMapping('DOCUMENT_UNIQUE_IDENTIFIER'),
        new AddVenioToLoadFileFieldMapping('DOCUMENT_UNIQUE_IDENTIFIER', field)
      ]
    })
  )

  @Effect()
  validateLoadFiles$: Observable<Action> = this.actions$.pipe(
    ofType<ValidateLoadFilesAndResources>(
      ImportConfigActionTypes.ValidateLoadFilesAndResources
    ),
    switchMap((action) => {
      return this.service
        .validateLoadFilesAndResources(
          action.payload.projectId,
          action.payload.importId,
          action.payload.importConfig,
          action.payload.isOverlay
        )
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new GlobalSuccessAction(res.message, res.data, false, false)
              ]
            }
            return []
          }),
          catchError((err) => from([new GlobalErrorAction(err, true, true)]))
        )
    })
  )

  @Effect()
  validateFilePaths$: Observable<Action> = this.actions$.pipe(
    ofType<ValidateFilePaths>(ImportConfigActionTypes.ValidateFilePaths),
    switchMap((action) => {
      return this.service
        .validateFilePaths(
          action.projectId,
          action.importId,
          action.importConfig,
          action.isOverlay
        )
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new GlobalSuccessAction(res.message, res.data, false, false),
                new HideValidateFilePathsSpinner(),
                new ValidateFilePathsSuccess(res.message, res.data)
              ]
            }
            return []
          }),
          catchError((err) =>
            from([
              new GlobalErrorAction(err, false, true),
              new HideValidateFilePathsSpinner(),
              new ValidateFilePathsFailure(
                this.errorService.getServerMessage(err),
                this.errorService.getServerStack(err)
              )
            ])
          )
        )
    })
  )

  @Effect()
  validateFieldMapping$: Observable<Action> = this.actions$.pipe(
    ofType<ValidateFieldMapping>(ImportConfigActionTypes.ValidateFieldMapping),
    switchMap((action) => {
      return this.service
        .validateFieldMapping(
          action.payload.projectId,
          action.payload.importId,
          action.payload.importConfig,
          action.payload.isOverlay,
          action.payload.overlay
        )
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new GlobalSuccessAction(res.message, res.data, false, false),
                new HideValidationSpinner(),
                new FetchImportStatus(
                  action.payload.projectId,
                  action.payload.importId
                )
              ]
            }
            return []
          }),
          catchError((err) => {
            return from([
              new GlobalErrorAction(err, true, true),
              new HideValidationSpinner()
            ])
          })
        )
    })
  )

  @Effect()
  startImport: Observable<Action> = this.actions$.pipe(
    ofType<StartImport>(ImportConfigActionTypes.StartImport),
    switchMap((action) => {
      return this.service
        .startImport(
          action.payload.projectId,
          action.payload.importId,
          action.payload.importConfig,
          action.payload.overlay,
          action.payload.isOverlay
        )
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new GlobalSuccessAction(res.message, res.data, false, false),
                new HideImportSpinner(),
                new FetchImportStatus(
                  action.payload.projectId,
                  action.payload.importId
                )
              ]
            }
            return []
          }),
          catchError((err) =>
            from([
              new GlobalErrorAction(err, true, true),
              new HideImportSpinner()
            ])
          )
        )
    })
  )

  @Effect()
  createImportTemplate$: Observable<Action> = this.actions$.pipe(
    ofType<CreateImportTemplate>(ImportConfigActionTypes.CreateImportTemplate),
    switchMap((action) => {
      return this.service
        .createImportTemplate(
          action.payload.templateConfig,
          action.payload.projectId,
          action.payload.importId
        )
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new GlobalSuccessAction(
                  res.message,
                  res.data,
                  true,
                  true,
                  true
                ),
                new FetchImportTemplates(action.payload.projectId)
              ]
            }
            return []
          }),
          catchError((err) =>
            from([new GlobalErrorAction(err, true, true, true)])
          )
        )
    })
  )

  @Effect()
  updateImportTemplate$: Observable<Action> = this.actions$.pipe(
    ofType<UpdateImportTemplate>(ImportConfigActionTypes.UpdateImportTemplate),
    switchMap((action) => {
      return this.service
        .updateImportTemplate(
          action.payload.templateConfig,
          action.payload.projectId,
          action.payload.templateId,
          action.payload.importId
        )
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new GlobalSuccessAction(res.message, res.data, true, true, true)
              ]
            }
            return []
          }),
          catchError((err) =>
            from([new GlobalErrorAction(err, true, true, true)])
          )
        )
    })
  )

  @Effect()
  fetchTemplateConfig$: Observable<Action> = this.actions$.pipe(
    ofType<FetchImportTemplateConfig>(
      ImportConfigActionTypes.FetchImportTemplateConfig
    ),
    switchMap((action) => {
      return this.service
        .fetchImportTemplateConfig(
          action.payload.projectId,
          action.payload.importTemplate.templateId,
          action.payload.importId
        )
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              // compute which path to use
              const computedLoadFilePath =
                action.payload.loadFileUpdate?.hasUpdated &&
                action.payload.loadFileUpdate?.filePath
                  ? action.payload.loadFileUpdate.filePath
                  : res.data.loadFile.filePath

              return [
                new FetchImportConfigSuccess(res.data),
                new ConfigureMapping(res.data.venioToLoadFileMapping),
                new FetchImportSummary({
                  importId: action.payload.importId,
                  projectId: action.payload.projectId
                }),
                new SetImportTemplate({
                  templateId: action.payload.importTemplate?.templateId,
                  templateName: action.payload.importTemplate?.templateName,
                  templateDesc: action.payload.importTemplate?.templateDesc,
                  importConfig: res.data
                }),
                new ConfigureLoadFileProcessOption(
                  action.payload.processLoadFile
                ),
                new ConfigureImageProcessOption(action.payload.processImage),
                new ConfigureLoadFilePath(computedLoadFilePath)
              ]
            }
            return []
          }),
          catchError((err) => from([new GlobalErrorAction(err, true, true)]))
        )
    })
  )

  @Effect()
  fetchImportTemplates$: Observable<Action> = this.actions$.pipe(
    ofType<FetchImportTemplates>(ImportConfigActionTypes.FetchImportTemplates),
    switchMap((action) => {
      return this.service.fetchImportTemplates(action.projectId).pipe(
        mergeMap((res: any) => {
          if (res.status === 'Success') {
            return [new FetchImportTemplateSuccess(res.data)]
          }
          return []
        }),
        catchError((err) => from([new GlobalErrorAction(err, true, true)]))
      )
    })
  )

  @Effect()
  checkImportTemplateExistance$: Observable<Action> = this.actions$.pipe(
    ofType<CheckImportTemplateExistance>(
      ImportConfigActionTypes.CheckImportTemplateExistance
    ),
    switchMap((action) => {
      return this.service
        .checkImportTemplateExistance(action.importTemplateName)
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [new SetImportTemplateExistance(res.data)]
            }

            return []
          }),
          catchError((err) => from([new GlobalErrorAction(err, false, true)]))
        )
    })
  )

  @Effect()
  addCustomFields: Observable<Action> = this.actions$.pipe(
    ofType<AddCustomFields>(ImportConfigActionTypes.AddCustomFields),
    switchMap((action) => {
      return this.service
        .addCustomFields(action.projectId, action.customFields)
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new GlobalSuccessAction(res.message, res.data, false, false),
                new AddVenioFields(this.getCustomVenioFields(res.data)),
                new ConfigureMapping(this.getCustomFieldsMapping(res.data)),
                new AddCustomFieldsSuccess(res.data),
                new HideCustomFieldsSpinner()
              ]
            }
            return []
          }),
          catchError((err) =>
            from([
              new GlobalErrorAction(err, false, true),
              new AddCustomFieldError(
                this.errorService.getServerMessage(err),
                this.errorService.getServerStack(err)
              ),
              new HideCustomFieldsSpinner()
            ])
          )
        )
    })
  )

  @Effect()
  fetchImportStatus: Observable<Action> = this.actions$.pipe(
    ofType<FetchImportStatus>(ImportConfigActionTypes.FetchImportStatus),
    switchMap((action) => {
      return this.service
        .fetchImportStatus(action.projectId, action.importId)
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new FetchImportStatusSuccess(res.data),
                new GlobalSuccessAction(res.message, res.data, false, false)
              ]
            }
            return []
          }),
          catchError((err) =>
            from([
              new FetchImportStatusFailure(),
              new GlobalErrorAction(err, false, true)
            ])
          )
        )
    })
  )

  @Effect()
  fetchImportProgress: Observable<Action> = this.actions$.pipe(
    ofType<FetchImportProgress>(ImportConfigActionTypes.FetchImportProgress),
    switchMap((action) => {
      return this.service
        .fetchImportProgress(action.projectId, action.importId)
        .pipe(
          mergeMap((res: any) => {
            if (res.status === 'Success') {
              return [
                new FetchImportProgressSuccess(res.data),
                new GlobalSuccessAction(res.message, res.data, false, false)
              ]
            }
            return []
          }),
          catchError((err) =>
            from([
              new FetchImportProgressFailure(),
              new GlobalErrorAction(err, false, true)
            ])
          )
        )
    })
  )

  @Effect()
  refreshImportTicker: Observable<Action> = this.actions$.pipe(
    ofType<RefreshImportTicker>(ImportConfigActionTypes.RefreshImportTicker),
    switchMap(() => {
      return this.service.refreshImportTicker().pipe(
        mergeMap((res: any) => {
          if (res.status === 'Success') {
            return [
              new GlobalSuccessAction(res.message, res.data, false, false)
            ]
          }
          return []
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, true)]))
      )
    })
  )

  @Effect()
  startPollingForProgress: Observable<Action> = this.actions$.pipe(
    ofType<StartPollingForProgress>(
      ImportConfigActionTypes.StartPollingForProgress
    ),
    map((action) => {
      this.sharedService.sharedProperties.isPollingForProgress = true
      return action
    }),
    switchMap((action) =>
      timer(0, 3000).pipe(
        takeWhile(
          () => this.sharedService.sharedProperties.isPollingForProgress
        ),
        switchMap(() =>
          from([
            new FetchImportProgress(action.projectId, action.importId),
            new FetchImportStatus(action.projectId, action.importId)
          ])
        )
      )
    )
  )

  @Effect({ dispatch: false })
  stopPollingForProgress: Observable<Action> = this.actions$.pipe(
    ofType<StopPollingForProgress>(
      ImportConfigActionTypes.StopPollingForProgress
    ),
    tap(() => {
      this.sharedService.sharedProperties.isPollingForProgress = false
    })
  )

  @Effect()
  startSchedulerForImportPushUpdates: Observable<Action> = this.actions$.pipe(
    ofType<StartSchedulerForImportPushUpdates>(
      ImportConfigActionTypes.StartSchedulerForImportProgress
    ),
    switchMap(() =>
      timer(0, 30000).pipe(switchMap(() => from([new RefreshImportTicker()])))
    )
  )

  @Effect({ dispatch: false })
  validateFilePathSuccess: Observable<Action> = this.actions$.pipe(
    ofType<ValidateFilePathsSuccess>(
      ImportConfigActionTypes.ValidateFilePathsSuccess
    ),
    tap((action: ValidateFilePathsSuccess) => {
      const modal = this.modalService.show(
        ConfirmationModalComponent,
        Object.assign({}, { ignoreBackdropClick: true })
      )
      ;(<ConfirmationModalComponent>modal.content).showConfirmationModal(
        'Validation Successful',
        action.message,
        null,
        'Close'
      )
    })
  )

  @Effect({ dispatch: false })
  validateFilePathFailure: Observable<Action> = this.actions$.pipe(
    ofType<ValidateFilePathsFailure>(
      ImportConfigActionTypes.ValidateFilePathsFailure
    ),
    tap((action: ValidateFilePathsFailure) => {
      const modal = this.modalService.show(
        ConfirmationModalComponent,
        Object.assign({}, { ignoreBackdropClick: true })
      )

      let error

      if (typeof action.errorData === 'string') {
        try {
          error = JSON.parse(action.errorData)
        } catch (e) {
          error = action.errorData
        }
      } else if (typeof action.errorData === 'object') {
        error = action.errorData
      } else {
        error = null
      }
      const errorMessage = action.errorMessage
      ;(<ConfirmationModalComponent>modal.content).showConfirmationModal(
        'Validation Failed',
        errorMessage,
        null,
        'Close'
      )
    })
  )

  getCustomFieldsMapping(customFields: CustomField[]) {
    const mapping = {}
    for (const customField of customFields) {
      const loadFileField = customField.identifier
      const venioField = customField.fieldName
      mapping[venioField] = loadFileField
    }
    return mapping
  }

  getCustomVenioFields(customFields: CustomField[]) {
    const fields: string[] = []
    for (const customField of customFields) {
      const venioField = customField.fieldName
      fields.push(venioField)
    }
    return fields
  }

  @Effect()
  fetchCustomDelimiters$: Observable<Action> = this.actions$.pipe(
    ofType<FetchCustomDelimiterAction>(
      ImportConfigActionTypes.FetchCustomDelimiterAction
    ),
    switchMap((action) => {
      return this.service.fetchdelimiters().pipe(
        mergeMap((res: any) => {
          if (res.status === 'Success') {
            return [new SetCustomDelimiterAction(res.data)]
          }
          return []
        }),
        catchError((err) => from([new GlobalErrorAction(err, true, true)]))
      )
    })
  )
}
