import { SamlIdpServerSettingTestingModule } from '@admin-advance/components/saml-idp-server-setting/saml-idp-server-setting-testing.module'
import { SamlIdpServerSettingComponent } from '@admin-advance/components/saml-idp-server-setting/saml-idp-server-setting.component'
import { IdPProvider } from '@admin-advance/models'
import {
  SamlGridUiTypes,
  SamlSettingModel
} from '@admin-advance/models/saml-idp-server/saml-idp-server.model'
import { SamlIdpServerFacade } from '@admin-advance/store'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import {
  ComponentFixture,
  fakeAsync,
  flush,
  TestBed,
  tick
} from '@angular/core/testing'
import { ResponseModel } from '@shared/models'
import { ToastrModule, ToastrService } from 'ngx-toastr'
import { of } from 'rxjs'

describe('SamlIdpServerSettingComponent', () => {
  let component: SamlIdpServerSettingComponent
  let fixture: ComponentFixture<SamlIdpServerSettingComponent>
  let samlIdpServerFacade: SamlIdpServerFacade
  let toastrService: ToastrService
  let encryptStr: any // Avoid TypeScript errors

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      teardown: { destroyAfterEach: true },
      declarations: [SamlIdpServerSettingComponent],
      imports: [SamlIdpServerSettingTestingModule, ToastrModule.forRoot({})],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(SamlIdpServerSettingComponent)
    component = fixture.componentInstance
    samlIdpServerFacade = TestBed.inject(SamlIdpServerFacade)
    toastrService = TestBed.inject(ToastrService)
    fixture.detectChanges()
  })

  beforeAll(() => {
    ;(global as any).encryptStr = jest.fn(
      (data: string) => `mock-encrypted(${data})`
    )
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it.each([
    ['should notify disable', 'unchecked', false],
    ['should notify enable', 'checked', true]
  ])(
    `%s SAML-Idp UI when 'Enable SAML-Idp Server Based Authentication' is %s`,
    (should, happened, expected) => {
      // GIVEN Enable SAML-Idp Server Based Authentication checkbox
      component.isEnableAllUi = false

      // WHEN Enable SAML-Idp Server Based Authentication is changed
      component.enableAllUi(expected)

      // THEN should toggle enable/disable accordingly
      expect(component.isEnableAllUi).toBe(expected)
    }
  )

  it(`should notify to subcomponent group data loader when load groups notifies to groups loading`, () => {
    // GIVEN groups loader status
    const expected = true
    component.isLoading = false

    // WHEN group loading notifies to parent
    component.onLoadingChanged(expected)

    // THEN should notify to subcomponent
    expect(component.isLoading).toBe(expected)
  })

  it(`should dispatch apply groups action with form data payload when user clicks on apply button`, () => {
    // GIVEN form data
    const mockApplySettingPayload: SamlSettingModel = {
      token: 'mock token',
      enableIDP: undefined,
      venioAdminLevels: [],
      venioApplicationAccess: [],
      venioUserGroups: [],
      venioUsers: [],
      apilink: undefined,
      ssoToken: 'mock token',
      ssO_URL: undefined,
      clientId: 0,
      idPGroupRequest: {
        applicationClientId: undefined,
        applicationObjectId: undefined,
        clientId: 0,
        groupType: 'OKTA_GROUP',
        link: undefined,
        providerName: IdPProvider.OKTA,
        tenantId: undefined,
        token: 'mock token',
        fetchGroupsFromDatabase: false
      },
      providerName: IdPProvider.OKTA
    } as SamlSettingModel

    const storeObjectMock: Partial<{ [key in SamlGridUiTypes]: unknown }> = {
      formData: mockApplySettingPayload
    }
    samlIdpServerFacade.selectStoreSamlIdpServerData$ = of(storeObjectMock)

    // WHEN load groups button clicked
    const applySamlIdpServerSettingDataDataSpy = jest.spyOn(
      samlIdpServerFacade,
      'applySamlIdpServerSettingData'
    )
    component.ngAfterViewInit()
    component.onApplySaml()

    // THEN should load groups data according to payload
    expect(component.isSamlApplying).toBeTruthy()
    expect(applySamlIdpServerSettingDataDataSpy).toHaveBeenCalledTimes(1)
    expect(applySamlIdpServerSettingDataDataSpy).toHaveBeenCalledWith(
      mockApplySettingPayload
    )
  })

  it(`should dispatch reset saml state action and stored states when SAML-Idp component get destroyed`, () => {
    // GIVEN store saml states
    const resetSamlServerAllStatesSpy = jest.spyOn(
      samlIdpServerFacade,
      'resetSamlServerAllStates'
    )

    // WHEN SAML-Idp server component get destroyed
    component.ngOnDestroy()

    // THEN reset SAML-Idp store states
    expect(resetSamlServerAllStatesSpy).toHaveBeenCalled()
  })

  it(`should should success message when SAML-Idp settings is applied with valid form data`, fakeAsync(() => {
    // GIVEN valid SAML-Idp form data
    const successResponse: ResponseModel = { message: 'success' }

    // GIVEN form data
    const mockApplySettingPayload: SamlSettingModel = {
      token: 'mock token',
      enableIDP: undefined,
      venioAdminLevels: [],
      venioApplicationAccess: [],
      venioUserGroups: [],
      venioUsers: [],
      apilink: undefined,
      ssoToken: 'mock token',
      ssO_URL: undefined,
      clientId: 0,
      idPGroupRequest: {
        applicationClientId: undefined,
        applicationObjectId: undefined,
        clientId: 0,
        groupType: 'OKTA_GROUP',
        link: undefined,
        providerName: IdPProvider.OKTA,
        tenantId: undefined,
        token: 'mock token',
        fetchGroupsFromDatabase: false
      },
      providerName: IdPProvider.OKTA
    } as SamlSettingModel

    const storeObjectMock: Partial<{ [key in SamlGridUiTypes]: unknown }> = {
      formData: mockApplySettingPayload
    }
    samlIdpServerFacade.selectStoreSamlIdpServerData$ = of(storeObjectMock)

    const applySamlIdpServerSettingDataSpy = jest.spyOn(
      samlIdpServerFacade,
      'applySamlIdpServerSettingData'
    )
    samlIdpServerFacade.selectSamlIdpServerApplyResponse$ = of(successResponse)
    const successToastSpy = jest.spyOn(toastrService, 'success')

    // WHEN settings are applied
    component.ngAfterViewInit()
    component.onApplySaml()

    tick(100)
    flush()

    // THEN should show success message
    expect(applySamlIdpServerSettingDataSpy).toHaveBeenCalledTimes(1)
    expect(component.isSamlApplying).toBeTruthy()
    expect(successToastSpy).toHaveBeenCalledWith(successResponse.message)
  }))
})
