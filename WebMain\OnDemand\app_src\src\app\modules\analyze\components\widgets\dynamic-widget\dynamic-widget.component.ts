import {
  AfterV<PERSON>wInit,
  ChangeDetector<PERSON>ef,
  Component,
  Inject,
  <PERSON><PERSON><PERSON><PERSON>,
  Query<PERSON><PERSON>,
  ViewChildren
} from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { Store } from '@ngxs/store'
import { SearchDupOption } from '@shared/models'
import { QueryBuilderSelector } from '@shared/search-builder/sotre/query-builder.selector'
import { CsvBuilder } from '@shared/utils'
import { GridsterItem, GridsterItemComponent } from 'angular-gridster2'
import {
  DxChartComponent,
  DxDataGridComponent,
  DxPieChartComponent
} from 'devextreme-angular'
import { exportDataGrid } from 'devextreme/excel_exporter'
import * as ExcelJS from 'exceljs'
import saveAs from 'file-saver'
import { jsPDF } from 'jspdf'
import autoTable from 'jspdf-autotable'
import { cloneDeep } from 'lodash'
import { debounceTime, filter, mergeMap, takeUntil, tap } from 'rxjs/operators'
import {
  ActionConfigModel,
  ActionEventTarget,
  SearchInputs,
  WidgetInputs,
  WidgetItem,
  WidgetTypes
} from '../../../models'
import { WidgetCommonExtension } from '../../../models/widget-common.extension'
import {
  AnalyzeStateSelectors,
  FetchWidgetResultAction,
  PreserveSelectedQueriesAction,
  SearchAction
} from '../../../store/ngxs'
import useLocalStore from './local-state.extension'

@Component({
  selector: 'app-dynamic-widget',
  templateUrl: './dynamic-widget.component.html',
  styleUrls: ['./dynamic-widget.component.scss', '../widgets.component.scss']
})
export class DynamicWidgetComponent
  extends WidgetCommonExtension
  implements AfterViewInit, OnDestroy
{
  /**
   * Toggles dev-express loading indicator.
   */
  loadingVisible = true

  /**
   * The main data source to bind with dev-express chart.
   */
  dataSource: Array<{ [key: string]: any }>

  /**
   * Extends subtitle text info as widget updates and get changed.
   */
  get subExtendedSubtitle(): string {
    const val = this.widget.item.topLimit
    return val ? `(${val})` : ''
  }

  /**
   * Dev-express chart instance for timeline data.
   * Use internally to set datasource to optimize performance when
   * using `ChangeDetectionStrategy.OnPush` strategy. Propagate changes
   * only when needed.
   */
  @ViewChildren('dxUi')
  dxComp: QueryList<
    DxChartComponent | DxPieChartComponent | DxDataGridComponent
  >

  /**
   * since we have collection of rendered `gridster` component with our own `uuid`,
   * we find that item to toggle css rules for fullscreen feature.
   */
  currentGridItemComponent: GridsterItemComponent

  /**
   * Find outs the client height of `gridster` component and applies it to the chart.
   * By doing so, we don't have to be fixed with dynamic height.
   * CAUTION: Since we're binding it within a property, Do not perform a heavy task in it.
   */
  get dynamicHeight(): number {
    return (
      this.widget?.gridsterItemComponent?.find(
        (g) => g.item.uuid === this.widget?.item.uuid
      )?.el?.clientHeight - 48
    )
  }

  /**
   * Token to store query of selected nodes.
   * This will be use when inclusive/exclusive actions are invoked.
   * @see handleActionEvent
   */
  private currentQuery: SearchInputs

  /**
   * Initially true to animate. There may be some situation which needs to off the animation.
   */
  isDxAnimation = true

  searchDuplicateOption: SearchDupOption

  constructor(
    @Inject('WIDGET_CONTENT')
    public widget: ActionConfigModel,
    public store: Store,
    private cdr: ChangeDetectorRef,
    private route: ActivatedRoute
  ) {
    super(store, widget)
    this.currentGridItemComponent = widget.gridsterItemComponent.find(
      (c) => c.item.uuid === widget.item.uuid
    )
  }

  ngAfterViewInit(): void {
    this.GetSearchDuplicationOption()
    this.handleGridsterResizeRefreshLib()
    this.initSearchEventHandler()
    this.handleExcludingWidgetActionControls(this.widget.item.widgetType)
  }

  /**
   * Only render the  `SIZE` column.
   * Conditional column (dev-express) to render
   */
  readonly onlyForFilesizeCol = (): boolean =>
    this.dataSource?.some((s) => s.Filesize)

  /**
   * returns caption for grid column depending on the widget. Will return User and Group for User Access List widget and default values for others.
   * @param column name of column in the data source
   * @param defaultName default name for columns
   * @returns column name that will be displayed in the grid
   */
  readonly getCaption = (column: string, defaultName: string): string => {
    let caption: string = defaultName
    switch (this.widget.item.widgetType) {
      case WidgetTypes.USER_ACCESS_LIST:
        if (column.toLowerCase() === 'key') {
          caption = 'User'
        } else if (column.toLowerCase() === 'value') {
          caption = 'Group'
        }
        break
      default:
        caption = defaultName
        break
    }
    return caption
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()

    // when a widget is removed but the queries are already preserved by selecting it,
    // we need to remove it too. Just supply the widget title to remove it.
    this.removeUnselected(this.widget.item.title)
    //  remove from local store
    const { cachedDxComponent } = useLocalStore()
    const index = cachedDxComponent.findIndex((s) => s.uuid === this._uuid)
    if (index > -1) {
      cachedDxComponent.splice(index, 1)
    }
  }

  /**
   * Some conditional controls to be toggled based on user interaction.
   * @param uuid current element uuid.
   * @param isExtend Whether to extend the control controls with input.
   */
  private readonly toggleControls = (uuid: string, isExtend?: boolean) =>
    this.syncWidgetControls(
      uuid,
      ['INCLUSIVE_FILTER', 'EXCLUSIVE_FILTER'],
      isExtend ? 'EXTEND' : 'FILTER'
    )

  /**
   * When selection was reset or unselected, must cleanup from store.
   * @param title Act as a key to store the current query.
   */
  private readonly removeUnselected = (title: string): void => {
    this.store.dispatch(
      new PreserveSelectedQueriesAction(title, {
        expression: '',
        displayText: title
      })
    )
  }

  /**
   *  Static and dynamic query handler. It'll take k/v pair input data and
   *  prepares query for static/dynamic widget.
   * @param data Input k/v pair data.
   *  NOTE: The demo commented query below are the finalized output format.
   *  Once the output is created, the query will then pass to the store or dispatches an
   *  action to search from this component.
   *  @see PreserveSelectedQueriesAction
   *  @see handleActionEvent
   */
  private readonly prepareQueries = (data: any[]): SearchInputs => {
    const type = this.widget.item.widgetType
    let q: string
    const hasVal = data.some((s) => s)
    switch (type) {
      // creates query of `EXCEPTION FILES`
      // output: `(IsUnknown="True" OR IsProcessingError="True" OR IsUnSupported="True")`
      case WidgetTypes.EXCEPTION_FILES_CANNED:
        {
          q = data.reduce((accu, d, index, arr) => {
            const searchKey = d.Key.includes('Unsupported Files')
              ? 'IsUnSupported'
              : d.Key.includes('Processing Error Files')
              ? 'IsProcessingError'
              : d.Key.includes('Unknown Files')
              ? 'IsUnknown'
              : d.Key.includes('Password Protected Files')
              ? 'IsPasswordProtected'
              : d.Key.includes('Corrupted Files')
              ? 'IsCorrupted'
              : d.Key.includes('Zero Byte Files')
              ? 'IsZeroByte'
              : ''
            // or even more custom property

            const OrLastParen =
              index >= 0 && arr.length - 1 !== index ? ' OR ' : ''
            return (accu += `${searchKey}="True"${OrLastParen}`)
          }, '')
        }
        break
      // creates query of `TAG`
      // Output: `TAGS("Tracking-test-review") OR TAGS("Non-Responsive") OR TAGS("Privileged") `
      case WidgetTypes.TAG_CANNED:
        {
          q = data.reduce((accu, d, index, arr) => {
            const OrLastParen =
              index >= 0 && arr.length - 1 !== index ? 'OR ' : ''
            return (accu += `TAGS("${d.Key}") ${OrLastParen}`)
          }, '')
        }
        break
      // creates query of `E-MAIL / ATTACHMENTS`
      // Output: `(parentdoctype="email" AND IsAttachment="True" AND IsDuplicate="False") OR (parentdoctype="email" AND IsEmail="True" AND IsDuplicate="False" AND IsAttachment="False")`
      case WidgetTypes.EMAIL_CANNED:
        {
          q = data.reduce((accu, d, index, arr) => {
            const searchKey = d.Key.includes('Duplicate')
              ? '(parentdoctype="email" AND IsDuplicate="True")'
              : d.Key.includes('Attachment')
              ? '(parentdoctype="email" AND IsAttachment="True" AND IsDuplicate="False")'
              : d.Key.includes('Email')
              ? '(parentdoctype="email" AND IsEmail="True" AND IsDuplicate="False" AND IsAttachment="False")'
              : ''

            const OrLastParen =
              index >= 0 && arr.length - 1 !== index ? ' OR ' : ''
            return (accu += `${searchKey}${OrLastParen}`)
          }, '')
        }
        break
      // creates query of `EDOC`
      // Output: `(parentdoctype="edoc" AND IsDuplicate="True")`
      case WidgetTypes.EDOC_CANNED:
        {
          q = data.reduce((accu, d, index, arr) => {
            const searchKey = `(parentdoctype="edoc" AND IsDuplicate="${
              d.Key.includes('Original') ? 'False' : 'True'
            }")`

            const OrLastParen =
              index >= 0 && arr.length - 1 !== index ? ' OR ' : ''
            return (accu += `${searchKey}${OrLastParen}`)
          }, '')
        }
        break
      // creates query of `FILE TYPE`
      // Output: ``file_type_group="RTF" OR file_type_group="Bitmap" OR file_type_group="Multimedia"
      case WidgetTypes.FILE_TYPE_CANNED:
        {
          q = data.reduce((accu, d, index, arr) => {
            const searchKey = `file_type_group="${d.Key}"`
            const OrLastParen =
              index >= 0 && arr.length - 1 !== index ? ' OR ' : ''
            return (accu += `${searchKey}${OrLastParen}`)
          }, '')
        }
        break
      // OUTPUT: FOLDERS("\FOL1" OR "\FOL2")
      case WidgetTypes.FOLDER_CANNED:
        {
          const fl = data.map((d) => `"${d['FolderLineage']}"`).join(' OR ')
          q = `FOLDERS(${fl})`
        }
        break
      // default Output: `EDOC_AUTHOR="Doug Kinney" OR EDOC_AUTHOR="ECT" OR EDOC_AUTHOR="CWSD"`
      default:
        q = data.reduce((accu, d, index, arr) => {
          // A widget by dynamic field will have the title as KEY.
          // The title is the actual base key for column to filter.
          // CAUTION: DO not supply static widget title here.
          const searchKey =
            d.Key.length > 0
              ? `${this.widget.item.title}="${d.Key}"`
              : `${this.widget.item.title} IS NULL`
          const OrLastParen =
            index >= 0 && arr.length - 1 !== index ? ' OR ' : ''
          return (accu += `${searchKey}${OrLastParen}`)
        }, '')
    }

    return {
      expression: hasVal ? `(${q})` : '',
      displayText: data.map((d) => d.Key).join(',')
    } as SearchInputs
  }

  /**
   * Handles dev-express component change events. such as grid, chart etc.,
   */
  readonly handleDxSelectionChanged = (event: any): void => {
    // some widget may not have point/selection features so it should simply return
    if (
      this.widget.item.widgetType === WidgetTypes.PROCESSING_SUMMARY_CANNED ||
      this.widget.item.widgetType === WidgetTypes.USER_ACCESS_LIST
    )
      return

    const { getDxComponent } = useLocalStore()

    /**
     *  Refreshes the preserved query output after select/unselect
     *  The store will add if expression text or will remove on  empty text.
     * @param data k/v pair input data to generate query.
     */
    const refreshPreservedQueries = (data: any[]) => {
      const q = this.prepareQueries(data)
      this.currentQuery = q
      this.store.dispatch(
        new PreserveSelectedQueriesAction(this.widget.item.title, q)
      )
    }

    const comp = getDxComponent(this.widget.item.uuid)

    // handler for grid
    if (comp instanceof DxDataGridComponent) {
      const selected = event.selectedRowKeys
      const data = this.dataSource.filter((d) => selected.includes(d.Key))
      const hasValue = selected.some((s) => s)
      this.toggleControls(this._uuid, hasValue)
      refreshPreservedQueries(data)
    }

    // chart which has series.
    if (
      comp instanceof DxPieChartComponent ||
      (comp instanceof DxChartComponent && this.whitelistWidgetOnly())
    ) {
      const target = event.target
      target.isSelected() ? target.clearSelection() : target.select()
      const selected =
        comp instanceof DxPieChartComponent
          ? event.component.getAllSeries()[0].getAllPoints()
          : event.component.getAllSeries()
      const points = selected
        .filter((p) => p.isSelected())
        .map((p) => p.data || p['_data'][0]?.data)
      this.toggleControls(
        this._uuid,
        points.some((p) => p)
      )
      refreshPreservedQueries(points)
    }
  }

  /**
   * Some widget may have limited action controls.
   * @param type a widget type
   */
  private readonly handleExcludingWidgetActionControls = (
    type: WidgetTypes
  ) => {
    switch (type) {
      case WidgetTypes.PROCESSING_SUMMARY_CANNED:
        this.toggleControls(this._uuid, false)
    }
  }

  /**
   * These are the fixed type to fetch data which may have different structures.
   * Only dynamic data  have k/v pair.
   * @param type
   */
  private readonly oneOfType = (type: WidgetTypes) => {
    return (
      type === WidgetTypes.EXCEPTION_FILES_CANNED ||
      type === WidgetTypes.TAG_CANNED ||
      type === WidgetTypes.FOLDER_CANNED ||
      type === WidgetTypes.EDOC_CANNED ||
      type === WidgetTypes.FILE_TYPE_CANNED ||
      type === WidgetTypes.EMAIL_CANNED ||
      type === WidgetTypes.PROCESSING_SUMMARY_CANNED ||
      type === WidgetTypes.DOMAIN ||
      type === WidgetTypes.REVIEW_INFO ||
      type === WidgetTypes.DYNAMIC_COMPATIBLE_MODE ||
      type === WidgetTypes.USER_ACCESS_LIST
    )
  }

  /**
   * Server response keys for specific widgets.
   * @param type a type of a widget.
   */
  private readonly keyOf = (type: WidgetTypes) => {
    return type === WidgetTypes.EXCEPTION_FILES_CANNED
      ? 'exceptionFileDocs'
      : type === WidgetTypes.TAG_CANNED
      ? 'Tags'
      : type === WidgetTypes.FOLDER_CANNED
      ? 'Folders'
      : type === WidgetTypes.EDOC_CANNED
      ? 'Edocs'
      : type === WidgetTypes.FILE_TYPE_CANNED
      ? 'FileTypes'
      : type === WidgetTypes.EMAIL_CANNED
      ? 'EmailAttachments'
      : type === WidgetTypes.PROCESSING_SUMMARY_CANNED
      ? 'funnel'
      : 'Data'
  }

  /**
   * The widgets who does not includes node/rows selection.
   */
  private readonly whitelistWidgetOnly = (): boolean => {
    const wt = this.widget.item.widgetType
    return !(wt === WidgetTypes.PROCESSING_SUMMARY_CANNED)
  }

  /**
   * Current awaiter for time.
   */
  private awaiter: any

  /**
   * Refreshes the dx-element when the gridster item size changed.
   * CAUTION: Be careful when changing it. Please see view events where it has been
   * call with mouse event which calls infinitely.
   * @param comp component to get refresh.
   * @see handleGridsterResizeRefreshLib
   */
  refreshDxInstance(
    comp: DxChartComponent | DxPieChartComponent | DxDataGridComponent
  ): void {
    const { isGridResized, updateResize } = useLocalStore()
    // only refresh at once when this value is toggled frm he gridster resize event.
    if (isGridResized) {
      if (this.awaiter) clearTimeout(this.awaiter)
      this.awaiter = setTimeout(() => {
        comp.instance.refresh()
        // once we're done, set to false.
        updateResize(false)
        clearTimeout(this.awaiter)
      }, 800)
    }
  }

  private readonly GetSearchDuplicationOption = () => {
    this.store
      .select(QueryBuilderSelector.sliceOf('searchDuplicateOption'))
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        this.searchDuplicateOption = res
      })
  }

  /**
   * Refreshes the dev-express instance when gridster get resize.
   */
  private readonly handleGridsterResizeRefreshLib = () => {
    // Since we use shared component to load lazily and populate multiple data for
    // various widgets, some of its children instances are not isolated thus, we use our own
    // local state to maintain reactivity
    const { cachedDxComponent, updateResize } = useLocalStore()

    // the component instance of angular os one while we have multiple fn caller as shared `ref`
    this.dxComp.changes
      .pipe(
        filter((comp) => comp.first),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (comp) => {
          // since we use our own store, we've to maintain its reactivity.
          // store `dx-*` ref to our local store so we can access it to manipulate.
          const index = cachedDxComponent.findIndex(
            (s) => s.uuid === this.widget.item.uuid
          )
          const payload = { uuid: this.widget.item.uuid, comp: comp.first }
          if (index > -1) cachedDxComponent[index] = payload
          else cachedDxComponent.push(payload)
        }
      })

    let wait: any
    const instanceHandler = (item: GridsterItem & WidgetItem): void => {
      // local store state to use
      updateResize(true)

      if (wait) clearTimeout(wait)
      wait = setTimeout(() => {
        // when resize happens and there's still selected node,
        // toggle the selection controls
        this.toggleControls(item.uuid, false)
        this.removeUnselected(item.title)
        clearTimeout(wait)
      }, 800)
    }
    // when  `gridster` get resized, we need t refresh the `dx-*` UI.
    this.currentGridItemComponent.gridster.options.itemResizeCallback =
      instanceHandler
    this.currentGridItemComponent.gridster.options.itemChangeCallback =
      instanceHandler
  }

  /**
   * When component retrieves fresh data from the API, the dx* elements should restore the
   * user selected stats to default.
   */
  private readonly clearLocalStats = (): void => {
    const uuid = this.widget.item.uuid
    const { getDxComponent } = useLocalStore()
    const comp = getDxComponent(uuid)
    if (
      comp instanceof DxChartComponent ||
      comp instanceof DxPieChartComponent ||
      comp instanceof DxDataGridComponent
    ) {
      if (comp.instance.getDataSource())
        // clear the selected chart series.
        comp.instance?.clearSelection()
    }

    this.currentQuery = undefined
    // once search is done, filter out to restore
    this.toggleControls(uuid, false)

    // clear out previous query of selected node.
    this.removeUnselected(this.widget.item.title)
  }

  /**
   * Some widget may have different data structure with custom property or value check.
   * @param data incoming data to manipulate.
   */
  private readonly widgetBasedDataStructure = (data: any[]) => {
    switch (this.widget.item.widgetType) {
      case WidgetTypes.EXCEPTION_FILES_CANNED:
        // exception grid ignores rows who have no value.
        data = data.filter((s) => s.Value > 0)
        break
      case WidgetTypes.FOLDER_CANNED:
        data = data.map((d) => ({
          ...d,
          Key: d.FolderLineage
        }))
        break
      // If needed, add more task for other widgets.
    }
    return data ?? []
  }

  /**
   * Widget action payload
   */
  private payload = () => {
    const hasType = this.oneOfType(this.widget.item.widgetType)
    // widget payload
    const payload: WidgetInputs = {
      // we have some static widget types and dynamic types.
      WidgetName: hasType
        ? this.widget.item.widgetType
        : this.widget.item.title,
      PageNumber: 1,
      TopLimit: this.widget.item.topLimit
    }
    if (!hasType) {
      /**
       * API expect this property have a value as `DYNAMIC`
       * when an user choose to render data of dynamic field.
       */
      payload.WidgetType = WidgetTypes.DYNAMIC
    }
    return payload
  }

  /**
   * Uses the search response event to load widget data.
   */
  private readonly initSearchEventHandler = () => {
    const { loadWidgetData } = this.useSearchResponse()

    loadWidgetData()
      .pipe(
        tap(() => {
          this.cdr.markForCheck()
          this.loadingVisible = true
          // once we receive the search params fro widgets, new event to fetch widget data.
          this.store.dispatch(new FetchWidgetResultAction(this.payload()))
        }),
        // once the widget data arrives, we then flip the source to select slice.
        mergeMap(() =>
          // store keys are dynamically added. See store.
          this.store.select(
            AnalyzeStateSelectors.sliceOfDynamic(this.payload().WidgetName)
          )
        ),
        // the data must be defined to fill
        filter(
          (d) => !!d?.input && d.input.WidgetName === this.payload().WidgetName
        ),
        debounceTime(500),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (d) => {
          this.loadingVisible = false
          // we're done loading.
          this.dxComp.notifyOnChanges()
          this.dataSource = []
          this.clearLocalStats()
          this.syncDataExportFormats(this.widget.item)
          const key = this.keyOf(this.widget.item.widgetType)
          // init data
          this.dataSource = this.widgetBasedDataStructure(
            cloneDeep(d.data.value[key])
          )
        }
      })
  }

  public customizeArgumentLabel = (arg: any) => {
    if (this.widget.item.widgetType === WidgetTypes.FOLDER_CANNED) {
      const folders = arg.value.split('\\')
      return folders[folders.length - 1]
    } else return arg.value
  }

  /**
   * Returns customized tooltip text for timeline series.
   * @param arg tooltip event input object
   */
  customizeToolTip(arg: any): unknown {
    return {
      text: `${arg.argumentText} : <b>${arg.valueText}</b>`
    }
  }

  /**
   * Dev express component customized label.
   * @param arg incoming value of event.
   */
  readonly customizedLabel = (arg: any) =>
    arg.argument + ' ( ' + arg.valueText + ' )'

  /**
   * Control action click event handler with emitted value as an arg.
   * @param event emitted value as an arg. Such as action type, widget object etc.,
   */
  handleActionEvent = (event: ActionEventTarget): void => {
    switch (event.actionType) {
      case 'EXCLUSIVE_FILTER':
      case 'INCLUSIVE_FILTER':
        //  mutate expression to append `exclusive` operator.
        this.store.dispatch(
          new SearchAction({
            displayText:
              event.actionType === 'EXCLUSIVE_FILTER'
                ? `${this.widget.item.title} is : NOT ${this.currentQuery.displayText}`
                : `${this.widget.item.title} is : ${this.currentQuery.displayText}`,
            expression:
              event.actionType === 'EXCLUSIVE_FILTER'
                ? ` NOT ${this.currentQuery.expression}`
                : this.currentQuery.expression,
            isFilterSearch: true,
            isForwardFilter: true,
            searchDuplicateOption: this.searchDuplicateOption
          })
        )
        break
      case 'EXPORT_DATA':
        this.handleExport(event.value)
        break
      case 'TOGGLE_FULLSCREEN':
        this.clearLocalStats()
        this.dxComp.first.instance.refresh()
        break
    }

    // once the conditional action is invoked, restore it.
    if (event.actionType !== 'WIDGET_UPDATE') {
      // hide ins/ex buttons
      this.toggleControls(this._uuid, false)
    }
  }

  /**
   * Combines name of the widget title with datetime to
   * make unique name to avoid naming conflict for same file download.
   */
  private get fileName(): string {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }
    const dateString: string = new Date()
      .toLocaleDateString('en-US', options) // date in format: '02/18/2024, 10:25:09 AM'
      .replace(/\//g, '-') // Replace slashes with hyphens. output: '02-18-2024, 10:25:09 AM'
      .replace(', ', '__') // Replace comma with double underscore. output: '02-18-2024__10:25:09 AM'
      .replace(/\s/g, '') // Remove spaces. output: '02-18-2024__10:25:09AM'
      .replace(/:/g, '_') // Remove colons. output: '02-18-2024__10_25_09AM'

    return `${this.widget?.item?.title}_${dateString}`
  }

  /**
   * Exports dx-grid data to the excel-sheet format.
   */
  private readonly gridExport = (): void => {
    const { getDxComponent } = useLocalStore()
    const comp = getDxComponent(this._uuid)
    const self = this
    const c = comp as DxDataGridComponent
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Main sheet')
    exportDataGrid({
      component: c.instance,
      worksheet: worksheet,
      selectedRowsOnly: c.selectedRowKeys.some((s) => s),
      customizeCell: (options) => {
        const o = options
        o.excelCell.font = { name: 'Arial', size: 12 }
        o.excelCell.alignment = { horizontal: 'left' }
      }
    }).then(function () {
      workbook.xlsx.writeBuffer().then((buffer) => {
        saveAs(
          new Blob([buffer], { type: 'application/octet-stream' }),
          `${self.fileName}.xlsx`
        )
      })
    })
  }

  /**
   * Export data from dx-grid to pdf file
   * @param data datasource to export to pdf.
   */
  private exportToPdf(data: any[]): void {
    const pdf = new jsPDF()

    // Define the header row
    const head: any[] = []
    if (data.length > 0) {
      const firstRow = data[0]
      const columns: string[] = []
      Object.keys(firstRow).forEach((columnName) => {
        let newColumnName = columnName
        if (columnName.toLowerCase() === 'key') {
          newColumnName = this.getCaption('Key', this.widget.item.title ?? '')
        } else if (columnName.toLowerCase() === 'value') {
          newColumnName = this.getCaption('Value', 'COUNT')
        } else if (columnName.toLowerCase() === 'filesize') {
          newColumnName = 'SIZE(MB)'
        }
        columns.push(newColumnName)
      })
      head.push(columns)
    }

    // define row entries
    const rows = data.map((row) => {
      const rowData: any[] = []
      Object.keys(row).forEach((columnName) => rowData.push(row[columnName]))
      return rowData
    })

    // Add data to the PDF
    autoTable(pdf, { head: head, body: rows, startY: 20 })

    // Save the PDF
    pdf.save(`${this.fileName}.pdf`)
  }

  /**
   * Handle data exports based on format types.
   * @param m widget config model.
   */
  private readonly handleExport = (m: ActionConfigModel) => {
    const { getDxComponent } = useLocalStore()
    const comp = getDxComponent(this.widget.item.uuid)
    const format = m.item.exportDataFormat.selectedType
    const p = comp as DxPieChartComponent | DxChartComponent
    const c = comp as DxDataGridComponent
    const selected = this.dataSource.filter((d) =>
      c.selectedRowKeys?.includes(d.Key)
    )
    const ds = selected.some((s) => s)
      ? selected
      : (c.instance.getDataSource().items() as Array<any>)
    // these are dev-express default export
    switch (format) {
      case 'JPEG':
      case 'PNG':
      case 'SVG':
        p.instance.exportTo(this.fileName, format)
        break
      case 'PDF':
        // use jsPDF for grid, and exportTo function for chart.
        if (comp instanceof DxDataGridComponent) this.exportToPdf(ds)
        else p.instance.exportTo(this.fileName, format)
        break
      case 'EXCEL':
        c.onExporting
          .pipe(takeUntil(this.toDestroy$))
          .subscribe(this.gridExport)
        c.onExporting.emit(c.instance)
        break
      case 'CSV': {
        // ordered cols
        const columns = [this.widget.item.title, 'COUNT']

        // column name for user access list in csv file
        if (this.widget.item.widgetType === WidgetTypes.USER_ACCESS_LIST) {
          columns[0] = 'User'
          columns[1] = 'Group'
        }

        // values are k/v
        const filteredRows = ds.map((r) => ({
          0: r.Key,
          1: r.Value
        }))
        new CsvBuilder(this.fileName).createCsvAndDownload(
          columns,
          filteredRows
        )
      }
    }
    // handle for other export types.
  }
}
