import {
  ChangeDetectorRef,
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit,
  ViewChild
} from '@angular/core'
import { select, Store } from '@ngrx/store'
import { Select, Store as XsStore } from '@ngxs/store'
import { SpreadsheetComponent } from '@syncfusion/ej2-angular-spreadsheet'
import { ToastrService } from 'ngx-toastr'
import { combineLatest, Observable, of, Subject } from 'rxjs'
import {
  catchError,
  debounceTime,
  filter,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs/operators'
import { UserRights } from '../../../helpers/user-rights'
import { StartupStateSelector } from '../../../stores/selectors'
import { User } from '../../auth/models/user.model'
import { getUserDetails } from '../../auth/store/selectors/access.selectors'
import { ProjectInfo, RightModel } from '../../config/models'
import { ConfigService } from '../../config/services/config.service'
import {
  getControlSettings,
  getProjectInfo
} from '../../config/store/selectors'
import { IndexDBService } from '../../index-db-module/index-db.service'
import {
  DocumentExcelResponse,
  ViewerSettings
} from '../../review/models/document.model'
import { MultiWindowExcelService } from '../../review/services/multi-window-excel.service'
import { MultiWindowSelectionService } from '../../review/services/multi-window-selection.service'
import * as fromDocumentActions from '../../review/store/actions/documents.actions'
import { DocumentState } from '../../review/store/reducers/documents.reducer'
import { getViewerSettings } from '../../review/store/selectors/documents.selectors'
import {
  FetchExcelWorkbook,
  FetchRedactedExcel,
  FetchRedactedTime,
  ReviewSetStateSelector,
  SaveRedactedExcel
} from '../../review/xs-store'

@Component({
  selector: 'app-spread-document',
  templateUrl: './spread-document.component.html',
  styleUrls: ['./spread-document.component.scss']
})
export class SpreadDocumentComponent implements OnInit, OnDestroy {
  /** get #spreadsheet child element from viewer
   * initializes with ejs-spreadsheet instance
   */
  @ViewChild('spreadsheet', { static: false })
  public spreadsheetObj: SpreadsheetComponent

  /** current selected cell range */
  public selectedCell: any

  /**
   * Subject to dispose the subscriptions after component gets destroyed.
   */
  private unsubscribed$ = new Subject<void>()

  /** project info jsonObject */
  projectInfo: ProjectInfo

  /** user detail authentication jsonObject */
  userDetails: User

  /** stores the permission to download boolean */
  isUserHavePermissionToDownloadFile = true

  /** stores the permission to download boolean */

  /**
   * user right interface
   */
  userGroupRights: RightModel

  /**
   * Observable to subscribe spinner for server action.
   */
  showExcelSpinner$: Observable<boolean>

  /** stores the permission to view text*/
  allowViewerTextCopy = false

  /** stores current loaded document id*/
  @Input() currentDocumentId: number

  /** stores current viewer settings*/
  viewerSettings: ViewerSettings

  /** chunck document model object*/
  currentChunkDocument: any

  /**
   * Subject to dispatch to stop the pending excel file request.
   */
  cancelLoad$ = new Subject<void>()

  /** current active index*/
  activeSheet: number

  /**check if the annotations are loaded*/
  loadRedacted = false

  /**check the loaded time*/
  checkTime: number

  /**check the saved time*/
  savedTime: number

  /**on button click boolean*/
  clickSave = false

  /**store original workbook of the current document*/
  spreadsheetOriginal

  /**annotation object of the current document*/
  annotaitonObject: { Root: any }

  /**current active cell*/
  activeSheetCellRedactObj = {}

  /**current active highlight cell*/
  activeSheetCellHighlightObj = {}

  /**array stores the set of redacted cell indexl*/
  redactedIndex = []

  /**array stores the set of highlight cell indexl*/
  highlightIndex = []

  /**Boolean to show excel viewer*/
  showExcelViewer: boolean

  /**boolean to show spinner while laoding*/
  saveLoader: boolean

  /**config services model*/
  config = ConfigService

  /**
   * Select the right to 'ALLOW_TO_APPLY_NATIVE_REDACTION' from the store.
   */
  @Select(
    StartupStateSelector.hasGroupRight(
      UserRights.ALLOW_TO_APPLY_NATIVE_REDACTION
    )
  )
  allowToApplyNativeRedaction$: Observable<boolean>

  /** disable apply native button if no right */
  disableApplyNativeButton: boolean

  /**
   * Cleanup placeholder for the observers when the component get destroyed.
   */
  private readonly toDestroy$ = new Subject<void>()

  redactionMode: boolean

  highlightButtonStatus: boolean

  @Input() isInView: boolean

  isFileSizeLimitExceeded: boolean

  excelErrorMessage: string

  isLoadExcelError: boolean

  constructor(
    private multiWindowSelectionService: MultiWindowSelectionService,
    private multiWindowExcelService: MultiWindowExcelService,
    private store: Store<DocumentState>,
    private xsStore: XsStore,
    private toast: ToastrService,
    private indexDb: IndexDBService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.#selectViewerSetting()
    this.#selectExcelLoadError()
    this.store
      .pipe(select(getProjectInfo), takeUntil(this.unsubscribed$))
      .subscribe((projectInfo: ProjectInfo) => {
        this.projectInfo = projectInfo
      })

    this.store
      .pipe(select(getUserDetails), takeUntil(this.unsubscribed$))
      .subscribe((userDetails: User) => {
        this.userDetails = userDetails
      })

    this.excelOnInit()
    this.checkPermission()
  }

  /** check the premission */
  private checkPermission() {
    combineLatest([this.allowToApplyNativeRedaction$])
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe(([addApplyNativeRight]) => {
        if (!addApplyNativeRight) {
          this.disableApplyNativeButton = false
        } else {
          this.disableApplyNativeButton = true
        }
      })
  }

  /**
   * Method called on ngOninit() that contatin all the subject for excel viewer init
   * @constructor
   */
  private excelOnInit() {
    // this.multiWindowExcelService.excelWorkbook
    //   .pipe(takeUntil(this.unsubscribed$))
    //   .subscribe((data) => {
    //     if (data) {
    //       this.setRedaction(data.RedactedFile)
    //       this.onWorkbookOpen(data.WorkBook)
    //       this.xsStore.dispatch(new ResetExcelSheetLoadErrorResponse())
    //     }
    //   })

    this.multiWindowExcelService.getredactedTime
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((data) => {
        this.savedTime = new Date(data).getTime()
        if (this.clickSave) {
          this.saveToServer()
          this.clickSave = false
        }
      })

    this.showExcelSpinner$ =
      this.multiWindowExcelService.showExcelWorkbookSpinner.pipe(
        takeUntil(this.unsubscribed$)
      )

    this.multiWindowExcelService.getLoader.subscribe((data) => {
      this.saveLoader = false
    })
  }

  ngAfterViewInit(): void {
    this.loadExcel('fetch')
  }

  /** This is a private method called `calculateSizeAndLoadViewer` which takes in a tuple
   * `setting` as its argument, containing a `viewer` and `control` object.
   * The method returns `void`, meaning it doesn't return any value.
   */
  #calculateSizeAndLoadViewer(setting: [ViewerSettings, any]): void {
    this.multiWindowExcelService.resetExcelWorkbook()
    this.isLoadExcelError = false

    // Destructuring the `viewer` and `control` object from the `setting` tuple.
    const [viewer, control] = setting

    // Extracting the `fileSize` property from the `viewer` object.
    const { fileSize } = viewer

    // Extracting the `HTML_CONVERSION_MAX_FILE_SIZE_FROM_VIEWER` property from the `control` object.
    const { HTML_CONVERSION_MAX_FILE_SIZE_FROM_VIEWER } = control

    // Comparing the `fileSize` with `HTML_CONVERSION_MAX_FILE_SIZE_FROM_VIEWER in bytes unit` and setting the
    // `isFileSizeLimitExceeded` property of the object to `true` if the file size is greater than the
    // maximum limit allowed for conversion.
    this.isFileSizeLimitExceeded =
      fileSize > HTML_CONVERSION_MAX_FILE_SIZE_FROM_VIEWER * 1024

    this.excelErrorMessage =
      'This file may take a long time to render due to its size.'
  }

  #selectViewerSetting(): void {
    combineLatest([
      this.store.select(getViewerSettings),
      this.store.select(getControlSettings)
    ])
      .pipe(
        filter((setting) => !!setting[0]),
        takeUntil(this.toDestroy$)
      )
      .subscribe((setting) => this.#calculateSizeAndLoadViewer(setting))
  }

  /**
   * Listens error response when an Excel is not being loaded.
   * The catch error method is used which the error could be any kind.
   */
  #selectExcelLoadError(): void {
    this.xsStore
      .select(ReviewSetStateSelector.SliceOf('excelSheetLoadErrorResponse'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((error) => {
        this.isLoadExcelError = !!error
        // The message is conditional because we have multiple places to mutate this
        if (error) {
          this.excelErrorMessage =
            'An unexpected error occurred while trying to open the file.'
        }
      })
  }

  /**
   * called when workbook is fetched from the store
   * @constructor
   * * @param {} workBook - current document excel workbook
   */
  onWorkbookOpen(workBook) {
    this.spreadsheetOriginal = workBook
    this.showExcelViewer = true
    this.saveLoader = false
  }

  public loadExcel(requestType: string) {
    if (this.isFileSizeLimitExceeded) {
      return
    }

    if (!this.currentDocumentId) {
      return
    }

    this.cdr.detectChanges()
    const chunkDocumentInfo = {
      fileId: null,
      projectId: null,
      userId: null
    }
    chunkDocumentInfo.fileId = this.currentDocumentId
    chunkDocumentInfo.projectId = this.projectInfo.projectId
    chunkDocumentInfo.userId = this.userDetails.userId
    this.currentChunkDocument = chunkDocumentInfo

    this.xsStore
      .dispatch(new FetchRedactedExcel(this.currentChunkDocument))
      .pipe(
        takeUntil(this.unsubscribed$),
        switchMap(() => {
          return this.xsStore
            .select(ReviewSetStateSelector.SliceOf('redactedObject'))
            .pipe(
              debounceTime(100),
              tap((redactionData) => {
                this.annotaitonObject = redactionData
              }),
              takeUntil(this.unsubscribed$)
            )
        }),
        catchError((error) => {
          this.toast.error('Redaction not found')
          this.saveLoader = false
          return of(null)
        }),
        switchMap(() => {
          return this.indexDb
            .getById(
              this.projectInfo.projectId + '-' + this.currentDocumentId,
              'NativeRedaction'
            )
            .pipe(
              switchMap((data) => {
                if (data) {
                  const documentexcelWorkbook = {
                    WorkBook: data.workBook,
                    RedactedFile: this.annotaitonObject
                  }
                  return of(documentexcelWorkbook)
                } else {
                  const request = {
                    excelWorkBookRequest: {
                      requestModel: chunkDocumentInfo,
                      requestType: requestType
                    }
                  }
                  return this.xsStore
                    .dispatch(
                      new FetchExcelWorkbook(
                        request.excelWorkBookRequest.requestModel,
                        request.excelWorkBookRequest.requestType
                      )
                    )
                    .pipe(
                      switchMap(() => {
                        return this.xsStore
                          .select(
                            ReviewSetStateSelector.SliceOf(
                              'documentexcelWorkbook'
                            )
                          )
                          .pipe(
                            debounceTime(100),
                            takeUntil(this.unsubscribed$)
                          )
                      }),
                      catchError((error) => {
                        this.multiWindowExcelService.setShowGrapeCitySpinner =
                          false
                        if (
                          error.error.data.Message.toLowerCase().includes(
                            'workbook is protected and password'
                          )
                        ) {
                          this.toast.error('Spreadsheet is protected')
                        } else if (
                          error.error.data.Message.toLowerCase().includes(
                            ' incorrect password\r\n   at'
                          )
                        ) {
                          this.toast.error('Incorrect Password!!')
                        } else if (
                          error.error.data.Message.toLowerCase().includes(
                            'file is not supported'
                          ) ||
                          error.error.data.Message.toLowerCase().includes(
                            ' cannot recognize current file type'
                          )
                        ) {
                          this.toast.error('Selected Document Is Not Supported')
                        } else if (
                          error.error.data.Message.toLowerCase().includes(
                            'because it is being used by another process'
                          )
                        ) {
                          this.toast.error('Document is being used by other')
                        } else {
                          this.toast.error(
                            'Excel Document Content Cannot Be Requested '
                          )
                        }
                        return of(null)
                      })
                    )
                }
              })
            )
        }),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((documentexcelWorkbook: DocumentExcelResponse) => {
        this.showExcelViewer = true
        try {
          this.spreadsheetOriginal = documentexcelWorkbook.WorkBook
          const json = JSON.parse(documentexcelWorkbook.WorkBook)
          if (json.Workbook) {
            json.Workbook.activeSheetIndex = 0
          }
          setTimeout(() => {
            this.spreadsheetObj.openFromJson({ file: json })
            this.onOpenComplete(null)
          }, 100)
        } catch (error: any) {
          this.cdr.markForCheck()
          //this.errorMessage = 'Error occurred when loading the document'
          console.error('Error', error)
        }
      })
  }

  /**
   *emits whenever item from context menu is clicked
   * @constructor
   * * @param {} event - context menu item click event instance
   */
  contextMenuClick(event) {
    if (
      event === 'Redact' ||
      (event.event && event.event.target.innerText === 'Redact')
    ) {
      this.actionSelection('redaction')
      this.highlightButtonStatus = true
    } else {
      const index = this.redactedIndex.findIndex(
        (item) => item === this.selectedCell
      )
      const id =
        this.selectedCell.split(':')[0] + ':' + this.selectedCell.split(':')[0]
      let singleCellIndex = 0
      if (index == -1) {
        singleCellIndex = this.redactedIndex.findIndex((item) => item === id)
      }
      if (singleCellIndex === -1) {
        this.wholeCellUnRedact('highlight')
        return
      } else {
        this.wholeCellUnRedact('redaction')
      }
    }
  }

  /**
   *apply highlight to cell which has both redaction and highligt on redaction remove
   * @constructor
   */
  applyHighlightOnRedactionRemove(index): void {
    const includes = this.highlightIndex.includes(index)
    includes &&
      this.spreadsheetObj.cellFormat({ backgroundColor: 'yellow' }, index)
  }

  /**
   *called to highlight cell.
   * @constructor
   */
  highlightAction() {
    this.spreadsheetObj.activeSheetIndex !== this.activeSheet &&
      this.spreadsheetObj.cellFormat(
        { backgroundColor: 'yellow' },
        this.selectedCell
      )
    this.actionSelection('highlight')
  }

  /**
   *called to redact cell.
   * @constructor
   * * @param {string} type - annotations type i.e. highlight or redaction
   */
  private actionSelection(type: string) {
    const {
      el,
      selectedRangeSplit,
      firstSelectedArray,
      secondSelectedSplits,
      cellRange
    } = this.cellIndexSplit()
    selectedRangeSplit[0] !== selectedRangeSplit[1]
      ? cellRange[0] !== cellRange[2]
        ? this.cellRangeRedaction(cellRange, type)
        : this.wholeCell(
            firstSelectedArray[1],
            secondSelectedSplits[0],
            secondSelectedSplits[1],
            type,
            el && el.value.includes('='),
            false
          )
      : this.cellAssign(type)
  }

  /**
   *called to redact cell.
   * @constructor
   * * @param {string} type - annotations add or remove type
   */
  cellAssign(type: string): void {
    const key = this.spreadsheetObj.activeSheetIndex.toString()
    this.annotationStatusBtn(this.selectedCell)
    this.cellObjAssign(type, this.selectedCell, key)
  }

  /**
   *called to redact whole cell
   * @constructor
   * * @param {string} first - first init number
   * * @param {string} character - cell range character
   * * @param {string} last - last range number
   * * @param {string} type - type of annotation i.e. highlight or remove
   * * @param {boolean} isDepended - boolean for cell dependend or not
   * * @param {boolean} isRemove - add or remove annotation boolean
   */
  private wholeCell(
    first: string,
    character: string,
    last: string,
    type: string,
    isDependend: boolean,
    isRemove?: boolean
  ) {
    const key = this.spreadsheetObj.activeSheetIndex.toString()
    const firstInt = parseInt(first)
    const lastInt = parseInt(last)
    const startKey = firstInt < lastInt ? firstInt : lastInt
    const endKey = startKey === firstInt ? lastInt : firstInt
    for (let i = startKey; i <= endKey; i++) {
      const ranges = character + i + ':' + character + i
      if (isRemove === true) {
        const highlightSelectedIndex = this.highlightIndex.indexOf(
          this.selectedCell
        )
        const redactSelectedIndex = this.redactedIndex.indexOf(
          this.selectedCell
        )
        if (i === startKey) {
          type === 'highlight'
            ? highlightSelectedIndex !== -1 &&
              this.removeHighLight(this.selectedCell)
            : redactSelectedIndex !== -1 &&
              this.removeRedaction(this.selectedCell)
        }
        type === 'highlight'
          ? this.removeHighLight(ranges)
          : this.removeRedaction(ranges)
      } else {
        /**For dependent cells */
        if (i === startKey && isDependend) {
          this.cellObjAssign(type, this.selectedCell, key)
        }
        this.annotationStatusBtn(ranges)
        this.cellObjAssign(type, ranges, key)
      }
    }
  }

  /**
   *called to redact  cell Range eg:A1:D9
   * @constructor
   * * @param {} cellRange - cell range for redaction
   * * @param {string} type - type of annotation i.e. highlight or remove
   * * @param {boolean} removeType - remove type
   */
  cellRangeRedaction(cellRange, type: string, removeType?: boolean): void {
    let currentChar =
      cellRange[0].charCodeAt(0) < cellRange[2].charCodeAt(0)
        ? cellRange[0]
        : cellRange[2]
    const lastChar = currentChar === cellRange[0] ? cellRange[2] : cellRange[0]
    const first =
      parseInt(cellRange[1].split(':')[0]) < parseInt(cellRange[3])
        ? cellRange[1].split(':')[0]
        : cellRange[3]
    const last =
      parseInt(first) === parseInt(cellRange[1].split(':')[0])
        ? cellRange[3]
        : cellRange[1].split(':')[0]
    const key = this.spreadsheetObj.activeSheetIndex.toString()
    /** spread sheet cells range can be to A1:AB2
     * firt if condition checks whether the cell row range to to A-Z
     * if the first condition is true than while loop will be initiated untill the cell range is z100:z100
     */
    if (lastChar.length === 1) {
      while (currentChar !== String.fromCharCode(lastChar.charCodeAt(0) + 1)) {
        let i = 0
        for (i = parseInt(first); i <= parseInt(last); i++) {
          const ranges = currentChar + i + ':' + currentChar + i
          if (removeType === true) {
            type === 'highlight'
              ? this.removeHighLight(ranges)
              : this.removeRedaction(ranges)
          } else {
            this.annotationStatusBtn(ranges)
            this.cellObjAssign(type, ranges, key)
          }
        }
        currentChar = String.fromCharCode(currentChar.charCodeAt(0) + 1)
      }
    } else {
      /**
       * if the cell range is from A1-CD100 than else will be executed
       *as ever cell ranges till Z i.e Z1:Z100,BZ1:BZ100
       * this will loop will get executes until the end range is reached
       */
      while (currentChar !== String.fromCharCode('Z'.charCodeAt(0) + 1)) {
        for (let i = parseInt(first); i <= parseInt(last); i++) {
          const ranges = currentChar + i + ':' + currentChar + i
          if (removeType === true) {
            type === 'highlight'
              ? this.removeHighLight(ranges)
              : this.removeRedaction(ranges)
          } else {
            this.annotationStatusBtn(ranges)
            this.cellObjAssign(type, ranges, key)
          }
        }
        if (currentChar === 'Z') {
          const items = lastChar.split('')
          let begin = 'A'
          while (begin !== String.fromCharCode(items[0].charCodeAt(0) + 1)) {
            let end = 'A'
            while (
              begin == items[0]
                ? end !== String.fromCharCode(items[1].charCodeAt(0) + 1)
                : end !== String.fromCharCode('Z'.charCodeAt(0) + 1)
            ) {
              for (let i = parseInt(first); i <= parseInt(last); i++) {
                const ranges = begin + end + i + ':' + begin + end + i
                if (removeType === true) {
                  type === 'highlight'
                    ? this.removeHighLight(ranges)
                    : this.removeRedaction(ranges)
                } else {
                  this.annotationStatusBtn(ranges)
                  this.cellObjAssign(type, ranges, key)
                }
              }
              end = String.fromCharCode(end.charCodeAt(0) + 1)
            }
            begin = String.fromCharCode(begin.charCodeAt(0) + 1)
          }
        }
        currentChar = String.fromCharCode(currentChar.charCodeAt(0) + 1)
      }
    }
  }

  /**
   *this events emits on the selection of the cell from ejs-spreadsheet componenet
   * @constructor
   * * @param {} event - selection event
   */
  selectCell(event): void {
    this.selectedCell = event.range
    this.annotationStatusBtn(event.range)
  }

  /**
   *method to check the annotation status of the current cell
   * @constructor
   * * @param {} selectedCell - selected cell range
   */
  annotationStatusBtn(selectedCell: string): void {
    const splitCell = selectedCell.split(':')
    const redactIndex = this.redactedIndex.includes(
      splitCell[0] !== splitCell[1]
        ? splitCell[0] + ':' + splitCell[0]
        : selectedCell
    )
    this.highlightButtonStatus = redactIndex
  }

  /**
   *this method is called upon to load the redacted json
   * @constructor
   * * @param {boolean} forIndexDb - boolean for the request is from while fetching from index db or not
   */
  openRedacted(fromButton?: boolean): void {
    this.saveLoader = true
    this.xsStore
      .dispatch(new FetchRedactedExcel(this.currentChunkDocument))
      .pipe(
        switchMap(() => {
          return this.xsStore
            .select(ReviewSetStateSelector.SliceOf('redactedObject'))
            .pipe(debounceTime(100), takeUntil(this.unsubscribed$))
        }),
        catchError((error) => {
          this.toast.error('Redaction not found')
          this.saveLoader = false
          return of(null)
        }),
        take(1)
      )
      .subscribe((data) => {
        this.showExcelViewer &&
          (this.activeSheet = this.spreadsheetObj.activeSheetIndex)
        this.saveLoader = false
        if (fromButton) {
          this.setRedaction(data, fromButton)
        } else {
          const documentexcelWorkbook = {
            WorkBook: this.spreadsheetOriginal,
            RedactedFile: data
          }
          this.multiWindowExcelService.setExcelWorkbook = documentexcelWorkbook
        }
      })
  }

  /**
   *this method is called upon to set the redaction on the current spreadsheet
   * @constructor
   * * @param {object} data - object that containtatin annotation obj
   * * @param {boolean} fromButton - status boolean for set redaction from button or not
   */
  setRedaction(data, fromButton?: boolean): void {
    this.annotaitonObject = data
    if (data) {
      this.checkTime = new Date().getTime()
      this.loadRedacted = true
      this.activeSheetCellRedactObj = JSON.parse(
        JSON.stringify(this.annotaitonObject.Root.sheetIndex)
      )
      this.activeSheetCellHighlightObj = JSON.parse(
        JSON.stringify(this.annotaitonObject.Root.highlightIndex)
      )
      fromButton && (this.openAnnotation(), this.onPanelChange())
    } else {
      this.loadRedacted = false
    }
  }

  /**
   *annotaiton object is fetched from the server and the JSOn is passed to this method which applies annotation to the opening excel
   * @constructor
   * * @param {string} cellIndex - cell index for annotation
   * * @param {string} event - stype of annotation
   */
  activeSheetAnnotation(cellIndex, type) {
    if (type === 'highlight') {
      for (let x = 0; x < cellIndex.length; x++) {
        this.spreadsheetObj.cellFormat(
          { backgroundColor: 'yellow' },
          cellIndex[x]
        )
      }
      return
    } else {
      for (let x = 0; x < cellIndex.length; x++) {
        this.spreadsheetObj.cellFormat(
          { backgroundColor: 'black' },
          cellIndex[x]
        )
        this.spreadsheetObj.cellFormat({ color: 'black' }, cellIndex[x])
      }
      return
    }
  }

  /**
   *this method is called upon the removing of the highlight annotation on cell
   * @constructor
   * * @param {string} range - cell index for removing highlight
   */
  removeHighLight(range?: string): void {
    if (!this.highlightIndex.includes(range)) {
      return
    }
    this.spreadsheetObj.cellFormat({ backgroundColor: 'white' }, range)
    const index = this.highlightIndex.findIndex((item) => item === range)
    this.highlightIndex.splice(index, 1)
    if (
      this.activeSheetCellHighlightObj[
        this.spreadsheetObj.activeSheetIndex
      ].includes(range)
    ) {
      typeof this.activeSheetCellHighlightObj[
        this.spreadsheetObj.activeSheetIndex
      ] === 'string'
        ? (this.activeSheetCellHighlightObj[
            this.spreadsheetObj.activeSheetIndex
          ] = [])
        : this.activeSheetCellHighlightObj[
            this.spreadsheetObj.activeSheetIndex
          ].splice(index, 1)
    }
    this.highlightIndex = this.filterArray(this.highlightIndex)
    this.annotationStatusBtn(range)
  }

  /**
   *this method is called upon the removing of the redaction on cell
   * @constructor
   * * @param {string} range - cell index for removing redaction
   */
  removeRedaction(range: string): void {
    if (!this.redactedIndex.includes(range)) {
      return
    }
    const index = this.redactedIndex.findIndex((item) => item === range)
    this.redactedIndex.splice(index, 1)
    if (
      this.activeSheetCellRedactObj[
        this.spreadsheetObj.activeSheetIndex
      ].includes(range)
    ) {
      typeof this.activeSheetCellRedactObj[
        this.spreadsheetObj.activeSheetIndex
      ] === 'string'
        ? (this.activeSheetCellRedactObj[this.spreadsheetObj.activeSheetIndex] =
            [])
        : this.activeSheetCellRedactObj[
            this.spreadsheetObj.activeSheetIndex
          ].splice(index, 1)
    }
    this.redactedIndex = this.filterArray(this.redactedIndex)
    this.spreadsheetObj.cellFormat({ backgroundColor: 'white' }, range)
    this.applyHighlightOnRedactionRemove(range)
    this.annotationStatusBtn(range)
  }

  /**
   *this method is called upon the removing empty element from the array
   * @constructor
   * * @param {} testArray - array to remove empty element
   */
  private filterArray(testArray) {
    let index = -1
    const arrayLength = testArray ? testArray.length : 0
    let resIndex = -1
    const result = []

    while (++index < arrayLength) {
      const value = testArray[index]

      if (value) {
        result[++resIndex] = value
      }
    }

    return result
  }

  /**
   *this method is called upon to get all the splitting of the current selected cell ranges
   * @constructor
   */
  private cellIndexSplit() {
    const el: any = document.getElementsByClassName('e-formula-bar')[0]
    const selectedRangeSplit =
      el.value.includes('=') && el.value.includes(':')
        ? el.value.split('(')[1].split(')')[0].split(':')
        : this.selectedCell.split(':')
    const firstSelectedArray = selectedRangeSplit[0].match(/[a-z]+|[^a-z]+/gi)
    const secondSelectedSplits = selectedRangeSplit[1].match(/[a-z]+|[^a-z]+/gi)
    const cellRange = this.selectedCell.match(/[a-z]+|[^a-z]+/gi)
    return {
      el,
      selectedRangeSplit,
      firstSelectedArray,
      secondSelectedSplits,
      cellRange
    }
  }

  /**
   *this method is called upon to unmutate the redaction arrays i.e. hightlightArray and redactionArray
   * @constructor
   * * @param {string} type - type of annotation
   */
  private indexAssign(type: string): void {
    if (type == 'highlight') {
      this.highlightIndex =
        typeof this.highlightIndex === 'string'
          ? [...[this.highlightIndex]]
          : Object.assign([], this.highlightIndex)
    } else {
      this.redactedIndex =
        typeof this.redactedIndex === 'string'
          ? [...[this.redactedIndex]]
          : Object.assign([], this.redactedIndex)
    }
  }

  /**
   *this method is called upon to selected cell range to array based on type of annotation
   * @constructor
   * * @param {string} type - type of annotation
   * * @param {string} range - range of the cell to be redacted
   * * @param {number} key - active sheet
   */
  private cellObjAssign(type, ranges, key) {
    if (this.highlightButtonStatus) {
      return
    }
    if (type === 'highlight') {
      this.highlightIndex.indexOf(ranges) === -1 &&
        (this.highlightIndex.push(ranges),
        this.spreadsheetObj.cellFormat({ backgroundColor: 'yellow' }, ranges))
      if (!this.activeSheetCellHighlightObj) {
        this.activeSheetCellHighlightObj = {}
      }
      this.activeSheetCellHighlightObj[key] = this.highlightIndex
    } else {
      this.redactedIndex.indexOf(ranges) === -1 &&
        (this.redactedIndex.push(ranges),
        this.spreadsheetObj.cellFormat(
          { backgroundColor: 'black', color: 'black' },
          ranges
        ))
      if (!this.activeSheetCellRedactObj) {
        this.activeSheetCellRedactObj = {}
      }
      this.activeSheetCellRedactObj[key] = this.redactedIndex
    }
  }

  /**
   *this method is called upon to save the annotation to the server
   * @constructor
   */
  save() {
    this.clickSave = true
    this.fetchRedactedTime()
  }

  /**
   *this method is called upon from save method to save it to server
   * @constructor
   */
  private saveToServer() {
    if (this.checkTime < this.savedTime) {
      this.toast.error(
        'The annotation has been saved by another user after you have loaded annotations. So, your changes are not saved. Please reload annotations.'
      )
    } else {
      for (const key in this.activeSheetCellRedactObj) {
        this.activeSheetCellRedactObj[key].length === 0 &&
          delete this.activeSheetCellRedactObj[key]
      }
      for (const key in this.activeSheetCellHighlightObj) {
        this.activeSheetCellHighlightObj[key].length === 0 &&
          delete this.activeSheetCellHighlightObj[key]
      }
      this.annotaitonObject = {
        Root: {
          sheetIndex: { ...this.activeSheetCellRedactObj },
          highlightIndex: { ...this.activeSheetCellHighlightObj }
        }
      }
      this.annotaitonObject.Root
      this.saveLoader = true
      const saveSetting = {
        sheetIndex: { ...this.activeSheetCellRedactObj },
        highlightIndex: { ...this.activeSheetCellHighlightObj }
      }
      const requestModel = {
        projectId: this.projectInfo.projectId,
        fileId: this.currentDocumentId,
        userId: this.userDetails.userId
      }
      this.xsStore
        .dispatch(
          new SaveRedactedExcel(
            saveSetting,
            requestModel,
            Object.keys(saveSetting.sheetIndex).length !== 0 ||
              Object.keys(saveSetting.highlightIndex).length !== 0
          )
        )
        .pipe(
          takeUntil(this.unsubscribed$),
          switchMap(() => {
            return this.xsStore
              .select(ReviewSetStateSelector.SliceOf('excelSaveResponse'))
              .pipe(debounceTime(100), takeUntil(this.unsubscribed$))
          }),
          catchError((error) => {
            this.saveLoader = false
            this.toast.error('Cannot Save The Excel')
            this.multiWindowExcelService.setShowGrapeCitySpinner = false
            return of(null)
          })
        )
        .subscribe((data) => {
          if (typeof data === 'string') {
            this.toast.success('Redaction saved successfully')
            this.saveLoader = false
            this.loadRedacted = true
            const payload = {
              fileId: this.currentDocumentId,
              projectId: this.projectInfo.projectId
            }
            this.store.dispatch(
              fromDocumentActions.fetchDocumentMetadata({ payload })
            )
            this.multiWindowExcelService.saveStatus = true
            this.checkTime = new Date().getTime()
          }
        })
    }
  }

  /**
   *this method is called upon to retrive redaction time
   * @constructor
   */
  fetchRedactedTime(): void {
    this.xsStore
      .dispatch(
        new FetchRedactedTime(
          this.projectInfo.projectId,
          this.currentDocumentId,
          this.userDetails.userId
        )
      )
      .pipe(
        takeUntil(this.unsubscribed$),
        switchMap(() => {
          return this.xsStore
            .select(ReviewSetStateSelector.SliceOf('redactedTime'))
            .pipe(debounceTime(100), takeUntil(this.unsubscribed$))
        }),
        catchError((error) => {
          this.multiWindowExcelService.setShowGrapeCitySpinner = false
          return of(null)
        })
      )
      .subscribe((data) => {
        data && (this.multiWindowExcelService.setredactedTime = data)
        !data && this.saveToServer()
      })
  }

  /**
   *this method is called upon to load original un muted excel
   * @constructor
   */
  openOriginalExcel(from?: string): void {
    this.activeSheet = this.spreadsheetObj.activeSheetIndex
    this.spreadsheetObj.openFromJson({ file: this.spreadsheetOriginal })
    setTimeout(() => {
      this.hideSelectAllButton()
      this.selectedCell = 'A1:A1'
      this.spreadsheetObj.selectRange('A1:A1')
      this.spreadsheetObj.activeSheetIndex = this.activeSheet
      this.redactionMode = from === 'reset'
      this.loadRedacted = false
    }, 0)
  }

  /**
   *this method is called upon to redact active sheet
   * @constructor
   */
  activeSheetEventRedaction(): void {
    this.activeSheet = this.spreadsheetObj.activeSheetIndex
    if (this.activeSheetCellRedactObj) {
      this.redactedIndex =
        this.activeSheet in this.activeSheetCellRedactObj
          ? this.activeSheetCellRedactObj[
              this.spreadsheetObj.activeSheetIndex.toString()
            ]
          : (this.redactedIndex = [])
    } else {
      this.redactedIndex = []
    }
  }

  /**
   *this method is called upon to highlight active sheet
   * @constructor
   */
  activeSheetEventHighlight(): void {
    this.activeSheet = this.spreadsheetObj.activeSheetIndex
    if (this.activeSheetCellHighlightObj) {
      this.highlightIndex =
        this.activeSheet in this.activeSheetCellHighlightObj
          ? this.activeSheetCellHighlightObj[
              this.spreadsheetObj.activeSheetIndex.toString()
            ]
          : (this.highlightIndex = [])
    } else {
      this.highlightIndex = []
    }
  }

  /**
   *this method is called upon to reset all the annotation
   * @constructor
   */
  reset(): void {
    this.activeSheet = this.spreadsheetObj.activeSheetIndex
    this.resetRedaction()
    this.resetHighlight()
    this.spreadsheetObj.openFromJson({ file: this.spreadsheetOriginal })
    this.loadRedacted = false
    this.spreadsheetObj.activeSheetIndex = this.activeSheet
  }

  /**
   *this method is called upon to reset all redaction
   * @constructor
   */
  resetRedaction(): void {
    for (const key in this.activeSheetCellRedactObj) {
      this.spreadsheetObj.activeSheetIndex = parseInt(key)
      if (typeof this.activeSheetCellRedactObj[key] === 'string') {
        this.spreadsheetObj.cellFormat(
          { backgroundColor: 'white', color: 'black' },
          this.activeSheetCellRedactObj[key]
        )
      } else {
        for (let i = 0; i < this.activeSheetCellRedactObj[key].length; i++) {
          this.spreadsheetObj.cellFormat(
            { backgroundColor: 'white', color: 'black' },
            this.activeSheetCellRedactObj[key][i]
          )
        }
      }
    }
    this.activeSheetCellRedactObj = {}
    this.redactedIndex = []
  }

  /**
   *this method is called upon to reset all highlight
   * @constructor
   */
  private resetHighlight(): void {
    for (const key in this.activeSheetCellHighlightObj) {
      this.spreadsheetObj.activeSheetIndex = parseInt(key)
      if (typeof this.activeSheetCellHighlightObj[key] === 'string') {
        this.spreadsheetObj.cellFormat(
          { backgroundColor: 'white', color: 'black' },
          this.activeSheetCellHighlightObj[key]
        )
      } else {
        for (let i = 0; i < this.activeSheetCellHighlightObj[key].length; i++) {
          this.spreadsheetObj.cellFormat(
            { backgroundColor: 'white', color: 'black' },
            this.activeSheetCellHighlightObj[key][i]
          )
        }
      }
    }
    this.activeSheetCellHighlightObj = {}
    this.highlightIndex = []
  }

  /**
   *this method is called upon to whole cell un redaction
   * @constructor
   * * @param {string} type - string that stores type of annotation
   */
  private wholeCellUnRedact(type) {
    const {
      el,
      selectedRangeSplit,
      firstSelectedArray,
      secondSelectedSplits,
      cellRange
    } = this.cellIndexSplit()
    if (
      selectedRangeSplit[0] !== selectedRangeSplit[1] &&
      cellRange[0] === cellRange[2]
    ) {
      this.wholeCell(
        firstSelectedArray[1],
        secondSelectedSplits[0],
        secondSelectedSplits[1],
        type,
        el && el.value.includes('='),
        true
      )
    } else if (
      selectedRangeSplit[0] !== selectedRangeSplit[1] &&
      cellRange[0] !== cellRange[2]
    ) {
      this.cellRangeRedaction(cellRange, type, true)
    } else {
      type === 'highlight'
        ? this.removeHighLight(this.selectedCell)
        : this.removeRedaction(this.selectedCell)
    }
  }

  /**
   *this event is emitted for the pannel change event of the spreadsheet
   * @constructor
   */
  onPanelChange(): void {
    try {
      const mainPannel: any = document.getElementsByClassName('e-main-panel')
      const headerPanel: any = document.getElementsByClassName('e-header-panel')
      this.hideFromViewer(mainPannel)
      this.hideFromViewer(headerPanel)
      this.hideSelectAllButton()
    } catch (e) {
      this.spreadsheetObj.openFromJson({ file: this.spreadsheetOriginal })
    } finally {
      Object.assign(
        [],
        document.getElementsByClassName('e-toolbar-item e-template')
      ).forEach((el) => {
        el.addEventListener('click', (event) => {
          setTimeout(() => {
            this.selectedCell = null
            this.openAnnotation()
            this.hideSelectAllButton()
          }, 0)
        })
      })
    }
  }

  /**
   *this event is emitted for hidding select all button from spreadsheet
   * @constructor
   */
  hideSelectAllButton() {
    const selectAllButton = document.getElementById(
      'spreadsheet_select_all'
    ) as HTMLElement
    selectAllButton.style.display = 'none'
  }

  onOpenComplete(event) {
    setTimeout(() => {
      this.openAnnotation()
      this.registerSheetChangedEvent()
    }, 100)
  }

  private registerSheetChangedEvent(): void {
    Object.assign(
      [],
      document.getElementsByClassName('e-toolbar-item e-template')
    ).forEach((el) => {
      el.addEventListener('click', (event) => {
        setTimeout(() => {
          this.selectedCell = null
          this.openAnnotation()
        }, 0)
      })
    })
  }

  /**
   * Apply annotation to the current opening document
   * @constructor
   */
  openAnnotation(): void {
    if (!this.annotaitonObject) return
    this.activeSheetCellRedactObj = { ...this.annotaitonObject.Root.sheetIndex }
    this.activeSheetCellHighlightObj = {
      ...this.annotaitonObject.Root.highlightIndex
    }

    this.activeSheetEventRedaction()
    this.activeSheetEventHighlight()
    if (this.annotaitonObject) {
      this.annotaitonObject.Root.highlightIndex &&
        (this.indexAssign('highlight'),
        this.activeSheetAnnotation(this.highlightIndex, 'highlight'))

      this.annotaitonObject.Root.sheetIndex &&
        (this.indexAssign('redact'),
        this.activeSheetAnnotation(this.redactedIndex, 'redact'))
      this.loadRedacted = true
    }
  }

  /**
   * Hide unwanted elements from the spreadsheet viewer
   * @constructor
   *  * * @param {} pannel - pannel viewer Id
   */
  hideFromViewer(pannel: any): void {
    setTimeout(() => {
      pannel.length > 1 && pannel[1].classList.add('d-none')
    }, 0)
  }

  /**
   * Action called on redaction mode icon click
   * @constructor
   */
  redactionModeMethod(): void {
    this.activeSheet = this.spreadsheetObj.activeSheetIndex
    this.redactionMode = !this.redactionMode
    if (!this.loadRedacted) {
      this.setRedaction(this.annotaitonObject, true)
    }
    this.redactionMode && (this.loadRedacted = true)
  }

  ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
