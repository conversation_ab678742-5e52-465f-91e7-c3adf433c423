<div class="modal-header">
  <h5 class="modal-title pull-left">{{ title }}</h5>
  <button
    type="button"
    class="close pull-right"
    aria-label="Close"
    (click)="bsModalRef.hide()"
  >
    <span aria-hidden="true" class="fa fa-times"></span>
  </button>
</div>
<div class="modal-body">
  <div class="filetype-list">
    <table class="table-file">
      <tr>
        <th class="text-bold">File Type</th>
        <th class="text-bold">Extension</th>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>7z Archive File</td>
        <td>.7z</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>.ZIP File</td>
        <td>.zip</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>.RAR File</td>
        <td>.rar</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>UNIX Tar</td>
        <td>.tar</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Lotus Notes Database R6.x</td>
        <td>.NS2</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Microsoft Cabinet File</td>
        <td>.cab</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>LZH Compress</td>
        <td>.lzh</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Self-Extracting LZH</td>
        <td>.lzh</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>UNIX GZip</td>
        <td>.gz</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>mbox(RFC-822 mailbox)</td>
        <td>.mbox</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>MS Office Binder</td>
        <td>.OBD</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Outlook Express File Type</td>
        <td>.dbx</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Mail Archive DXL</td>
        <td>.dxl</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Microsoft Office 365 OST file</td>
        <td>.ost</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Microsoft Outlook PST/OST 2003</td>
        <td>.pst</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Microsoft Outlook PST/OST 97/2000/XP</td>
        <td>.pst</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Microsoft Outlook file for Mac</td>
        <td>.olm</td>
      </tr>

      <tr *ngIf="!isTranscript">
        <td>Lotus Notes Database File</td>
        <td>.nsf</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>UNIX Compress</td>
        <td>.gz</td>
      </tr>
      <tr *ngIf="!isTranscript">
        <td>Forensic Image</td>
        <td>{{ forensicImageFileExtensions }}</td>
      </tr>
      <tr *ngIf="isTranscript">
        <td>Transcript Files</td>
        <td>.ptf, .pcf</td>
      </tr>
    </table>
  </div>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-grey" (click)="bsModalRef.hide()">
    <i class="fa fa-times"></i>
    Close
  </button>
</div>
