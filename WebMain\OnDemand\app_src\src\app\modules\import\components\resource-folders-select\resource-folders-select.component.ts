import {
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit,
  ViewContainerRef
} from '@angular/core'
import { FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms'
import { ConfigState } from '@config/store/reducers'
import { getThemeClient } from '@config/store/selectors'
import { select, Store } from '@ngrx/store'
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal'
import { combineLatest, Subject } from 'rxjs'
import { filter, map, takeUntil } from 'rxjs/operators'
import {
  ImageLoadFileSummary,
  ResourceSummary
} from '../../models/import-summary'
import { RemoveLoadFileToVenioFieldMapping } from '../../store/actions'
import { ImportState } from '../../store/reducers'
import {
  getConfigImageLoadFilePath,
  getConfigLoadFileProcessOption
} from '../../store/selectors/import-config.selectors'
import {
  getSummaryImageLoadFile,
  getSummaryLoadFileFields,
  getSummaryLoadFileFieldValues
} from '../../store/selectors/import-summary.selectors'
import { FileSelectorComponent } from '../file-selector/file-selector.component'

@Component({
  selector: 'app-resource-folders-select',
  templateUrl: './resource-folders-select.component.html',
  styleUrls: ['./resource-folders-select.component.scss']
})
export class ResourceFoldersSelectComponent implements OnInit, OnDestroy {
  client: string

  folderSelectRef: BsModalRef

  @Input()
  label: string

  @Input()
  isOverlay: boolean

  idSuffix: string

  @Input()
  summaryResourceSelector: any

  @Input()
  configResourceProcessOptionSelector: any

  @Input()
  configureResourceMappingFieldSelector: any

  @Input()
  configResourceFolderPathSelector: any

  @Input()
  configResourceFolderPathUpdateAction: any

  @Input()
  configResourceProcessOptionUpdateAction: any

  @Input()
  configResourceMappingFieldUpdateAction: any

  @Input()
  set formSubmitAttempt(formSubmitAttempt: boolean) {
    this._formSubmitAttempt = formSubmitAttempt
  }

  get formSubmitAttempt(): boolean {
    return this._formSubmitAttempt
  }

  @Input()
  configResourceExtractFulltextOptionSelector: any

  @Input()
  configResourceAutoComputeFileSizeOptionSelector: any

  @Input()
  configResourceAutoComputeFileExtensionOptionSelector: any

  @Input()
  configResourceAutoComputeFileTypeOptionSelector: any

  @Input()
  configResourceExtractFulltextOptionUpdateAction: any

  @Input()
  configHasFullTextInLoadFileSelector: any

  @Input()
  configHasFullTextInLoadFileUpdateAction: any

  @Input()
  configExtractedTextFieldSelector: any

  @Input()
  configExtractedTextFieldUpdateAction: any

  @Input()
  configIsProjectImageTypePDF: boolean

  @Input()
  configResourceAutoComputeFileSizeUpdateAction: any

  @Input()
  configResourceAutoComputeFileExtensionUpdateAction: any

  @Input()
  configResourceAutoComputeFileTypeUpdateAction: any

  summaryLoadFileFieldValues: string[]

  mappingFieldSample: string

  summaryResource: ResourceSummary

  summaryLoadFileFields: string[]

  configResourceProcessOption: boolean

  configureResourceMappingField: string

  configResourceFolderPath: string

  folderSelectFormGroup: FormGroup

  _formSubmitAttempt: boolean

  imageLoadFilePath: string

  processLoadFile: boolean

  extractFulltextFromNative: boolean

  hasFullTextInLoadFile = false

  selectedExtractedTextFields: string[]

  loadFileImageType: string

  overlayFileSizeBasedOnNative: boolean

  overlayFileExtBasedOnNative: boolean

  overlayFileTypeBasedOnNative: boolean

  public unsubscribe$ = new Subject<void>()

  constructor(
    private store: Store<ImportState>,
    private modalService: BsModalService,
    private configStore: Store<ConfigState>,
    private viewContainerRef: ViewContainerRef,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit() {
    this.configStore
      .pipe(select(getThemeClient), takeUntil(this.unsubscribe$))
      .subscribe((client: string) => {
        this.client = client
      })

    this.idSuffix = this.label.replace(/\s+/g, '-').toLowerCase()

    this.folderSelectFormGroup = this.formBuilder.group(
      {
        folderName: [null, Validators.required],
        loadFileField: [null],
        extractedTextFields: [null]
      },
      {
        validator: this.validateIfChecked
      }
    )

    this.store
      .pipe(
        select(getSummaryLoadFileFields),
        filter((res) => !!res),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((fields: string[]) => {
        this.summaryLoadFileFields = fields
        this.setMappedField()
      })

    this.store
      .pipe(select(getSummaryLoadFileFieldValues), takeUntil(this.unsubscribe$))
      .subscribe((values: string[]) => {
        this.summaryLoadFileFieldValues = values
        this.mappingFieldSample = this.getASample(
          this.configureResourceMappingField
        )
        this.setSampleTextForExtractedTextFields()
      })

    combineLatest([
      this.store.pipe(select(this.configResourceProcessOptionSelector)),
      this.store.pipe(select(getConfigImageLoadFilePath)),
      this.store.pipe(select(this.configResourceExtractFulltextOptionSelector)),
      this.store.pipe(select(getSummaryImageLoadFile)),
      this.store.pipe(
        select(this.configResourceAutoComputeFileSizeOptionSelector)
      ),
      this.store.pipe(
        select(this.configResourceAutoComputeFileExtensionOptionSelector)
      ),
      this.store.pipe(
        select(this.configResourceAutoComputeFileTypeOptionSelector)
      )
    ])
      .pipe(
        filter(
          ([
            processConfig,
            imageFilePath,
            extractFulltextFromNative,
            importSummary,
            autoComputeFileSize,
            autoComputeFileExtension,
            autoComputeFileType
          ]) =>
            processConfig !== undefined &&
            processConfig !== null &&
            extractFulltextFromNative !== undefined &&
            extractFulltextFromNative !== null &&
            !!importSummary
        ),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(
        ([
          processConfig,
          imageFilePath,
          extractFulltextFromNative,
          importSummary,
          autoComputeFileSize,
          autoComputeFileExtension,
          autoComputeFileType
        ]: [
          boolean,
          string,
          boolean,
          ImageLoadFileSummary,
          boolean,
          boolean,
          boolean
        ]) => {
          //for process image section
          if (this.label?.toLocaleLowerCase() == 'image') {
            this.configResourceProcessOption =
              processConfig && imageFilePath?.trim().length > 0
            //if the project image type is TIFF and the load file image type is PDF then disable Process Image option
            if (
              importSummary?.imageType === 'PDF' &&
              !this.configIsProjectImageTypePDF
            ) {
              this.configResourceProcessOption = false
              this.setProcessOption(false)
            }
          }
          // for fulltext and native section
          else {
            this.configResourceProcessOption = processConfig
            this.extractFulltextFromNative = extractFulltextFromNative
            this.overlayFileExtBasedOnNative = autoComputeFileExtension
            this.overlayFileSizeBasedOnNative = autoComputeFileSize
            this.overlayFileTypeBasedOnNative = autoComputeFileType
          }
        }
      )

    this.store
      .pipe(
        select(this.configResourceFolderPathSelector),
        takeUntil(this.unsubscribe$),
        map((pathConfig: string) => pathConfig),
        filter((pathConfig) => !!pathConfig)
      )
      .subscribe((pathConfig: string) => {
        this.configResourceFolderPath = pathConfig
        this.populateFolderName(this.configResourceFolderPath)
      })

    this.store
      .pipe(
        select(this.summaryResourceSelector),
        takeUntil(this.unsubscribe$),
        map((summary: ResourceSummary) => summary)
      )
      .subscribe((summary: ResourceSummary) => {
        this.summaryResource = summary
      })

    this.store
      .pipe(
        select(getConfigImageLoadFilePath),
        takeUntil(this.unsubscribe$),
        map((loadFilePath: string) => loadFilePath)
      )
      .subscribe((loadFilePath: string) => {
        this.imageLoadFilePath = loadFilePath
      })

    this.store
      .pipe(
        select(getConfigLoadFileProcessOption),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((importLoadFile: boolean) => {
        this.processLoadFile = importLoadFile
      })

    if (this.configHasFullTextInLoadFileSelector) {
      this.store
        .pipe(
          select(this.configHasFullTextInLoadFileSelector),
          takeUntil(this.unsubscribe$)
        )
        .subscribe((hasFullTextInLoadFile: boolean) => {
          if (this.label?.toLocaleLowerCase() == 'full-text')
            this.handleHasFullTextInLoadFileValueChange(!!hasFullTextInLoadFile)
          else {
            this.handleHasFullTextInLoadFileValueChange(false)
          }
        })
    }

    if (this.configExtractedTextFieldSelector) {
      this.store
        .pipe(
          select(this.configExtractedTextFieldSelector),
          takeUntil(this.unsubscribe$)
        )
        .subscribe((extractedTextFields: string[]) => {
          this.folderSelectFormGroup.patchValue({
            extractedTextFields: extractedTextFields
          })
          this.selectedExtractedTextFields = extractedTextFields
        })
    }

    this.fetchImportImageType()
  }

  private fetchImportImageType(): void {
    this.store
      .pipe(
        select(getSummaryImageLoadFile),
        filter((res) => !!res),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((summary: ImageLoadFileSummary) => {
        this.loadFileImageType = summary?.imageType ?? 'TIFF'
      })
  }

  private setMappedField() {
    this.store
      .pipe(
        select(this.configureResourceMappingFieldSelector),
        takeUntil(this.unsubscribe$),
        map((mappingField: string) => mappingField),
        filter((mappingField) => !!mappingField)
      )
      .subscribe((mappingField: string) => {
        this.configureResourceMappingField = mappingField
        this.populateMappingField(mappingField)
        this.mappingFieldSample = this.getASample(mappingField)
      })
  }

  /**
   * Returns boolean value indicating if the image type and project image type match or not.
   * Returns false if the image type in the load file is PDF and project image type is TIFF.
   */
  public get isImageInLoadFileInvalid(): boolean {
    return this.loadFileImageType === 'PDF' && !this.configIsProjectImageTypePDF
  }

  /**
   * Returns error message to display in the UI if the image type in the load file is PDF and project image type is TIFF
   * @returns {string} error message if the image type in the load file is PDF and project image type is TIFF
   */
  public getImageLoadFileError(): string {
    if (this.loadFileImageType === 'PDF' && !this.configIsProjectImageTypePDF) {
      return 'PDF images are not supported in this project.'
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()
  }

  get f() {
    return this.folderSelectFormGroup.controls
  }

  setProcessOption(option: boolean) {
    this.store.dispatch(
      new this.configResourceProcessOptionUpdateAction(option)
    )
  }

  setMappingField(mappingField: string) {
    /**
     * Remove the previously mapped field
     */
    if (this.configureResourceMappingField) {
      this.store.dispatch(
        new RemoveLoadFileToVenioFieldMapping(
          this.configureResourceMappingField,
          null
        )
      )
    }

    this.store.dispatch(
      new this.configResourceMappingFieldUpdateAction(mappingField)
    )
  }

  setFolderPath(folderPath: string) {
    this.store.dispatch(
      new this.configResourceFolderPathUpdateAction(folderPath)
    )
  }

  populateFolderName(name: string) {
    this.folderSelectFormGroup.patchValue({
      folderName: name
    })
  }

  populateMappingField(name: string) {
    this.folderSelectFormGroup.patchValue({
      loadFileField: this.summaryLoadFileFields?.indexOf(name) > -1 ? name : ''
    })
  }

  openFolderSelectionModal() {
    this.folderSelectRef = this.modalService.show(
      FileSelectorComponent,
      Object.assign({}, { class: 'modal-md', ignoreBackdropClick: true })
    )
    this.folderSelectRef.content.callback = this.onResourceFolderSelect
  }

  onProcessChange = (event) => {
    this.setProcessOption(event.target.checked)
  }

  onResourceFolderSelect = (name: string) => {
    this.setFolderPath(name)
  }

  onFieldChange(event) {
    this.setMappingField(event)
  }

  getASample(fieldName) {
    if (!fieldName) {
      return
    }
    if (!this.summaryLoadFileFieldValues) {
      return "Couldn't load any sample"
    }
    for (const row of this.summaryLoadFileFieldValues) {
      const value = row[fieldName]
      if (value && value.length > 0) {
        return value
      }
    }
  }

  /**
   * Returns a sample text for full text extraction from the selected fields and limits it to 200 characters
   * @param {string[]} fields list of selected fields for full text extraction
   * @returns {string} sample text for full text
   */
  private getSampleForFullTextInLoadFile(fields: string[]): string {
    if (!fields) {
      return ''
    }
    for (const row of this.summaryLoadFileFieldValues) {
      let sampleText = ''
      for (const field of fields) {
        const value = row[field]
        if (value && value.length > 0) {
          sampleText += value
        }
        if (sampleText.length > 200) {
          sampleText = sampleText.slice(0, 200) + '...' //truncate to 200 characters and add ellipsis
          break
        }
      }
      if (sampleText.length > 0) return sampleText
    }
    return ''
  }

  validateIfChecked: ValidatorFn = (fg: FormGroup) => {
    const fieldName = fg.get('loadFileField').value
    if (!this.configResourceProcessOption || this.hasFullTextInLoadFile) {
      return null
    }
    if (this.configResourceProcessOption === true && !fieldName) {
      return {
        requiredIfChecked: true
      }
    }
    return null
  }

  onExtractFulltextChecked(option: boolean): void {
    this.store.dispatch(
      new this.configResourceExtractFulltextOptionUpdateAction(option)
    )
  }

  onAutoComputeFileSizeChecked(option: boolean): void {
    this.store.dispatch(
      new this.configResourceAutoComputeFileSizeUpdateAction(option)
    )
  }

  onAutoComputeFileExtensionChecked(option: boolean): void {
    this.store.dispatch(
      new this.configResourceAutoComputeFileExtensionUpdateAction(option)
    )
  }

  onAutoComputeFileTypeChecked(option: boolean): void {
    this.store.dispatch(
      new this.configResourceAutoComputeFileTypeUpdateAction(option)
    )
  }

  public onHasFullTextInLoadFileChecked(event: boolean): void {
    if (this.configHasFullTextInLoadFileUpdateAction) {
      this.store.dispatch(
        new this.configHasFullTextInLoadFileUpdateAction(event)
      )
    }
  }

  private handleHasFullTextInLoadFileValueChange(checked: boolean): void {
    this.hasFullTextInLoadFile = checked
    const folderNameControl = this.folderSelectFormGroup.get('folderName')
    const extractedTextFieldsControl = this.folderSelectFormGroup.get(
      'extractedTextFields'
    )
    if (checked) {
      folderNameControl.clearValidators()
      folderNameControl.disable()

      extractedTextFieldsControl.setValidators(Validators.required)
      extractedTextFieldsControl.enable()
    } else {
      folderNameControl.setValidators(Validators.required)
      folderNameControl.enable()

      extractedTextFieldsControl.clearValidators()
      extractedTextFieldsControl.disable()
    }
    folderNameControl.updateValueAndValidity()
  }

  public onExtractedTextFieldChange(event: string[]): void {
    this.selectedExtractedTextFields = event
    this.setSampleTextForExtractedTextFields()
    this.store.dispatch(new this.configExtractedTextFieldUpdateAction(event))
  }

  private setSampleTextForExtractedTextFields(): void {
    this.mappingFieldSample = ''
    // if (this.selectedExtractedTextFields?.length > 0) {
    //   this.mappingFieldSample = this.getSampleForFullTextInLoadFile(
    //     this.selectedExtractedTextFields
    //   )
    // } else this.mappingFieldSample = ''
  }
}
