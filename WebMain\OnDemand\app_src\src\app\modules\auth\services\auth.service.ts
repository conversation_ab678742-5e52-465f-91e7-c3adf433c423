import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { ConfigService } from '@config/services/config.service'
import { Store } from '@ngrx/store'
import { Store as xsstore } from '@ngxs/store'
import { publicKey } from '@root/helpers/public-key'
import { ResetCachedProjectRightListAction } from '@root/modules/launchpad/store'
import {
  IframeMessengerService,
  MessageType
} from '@root/modules/micro-apps/config/iframe-messenger.service'
import { AppIdentitiesTypes } from '@root/modules/micro-apps/models/app-identities-types'
import { ServerSessionService } from '@root/services/server-session.service'
import { NavService } from '@shared/services/nav.service'
import { StringUtils } from '@shared/utils/string-utils'
import { SetSelectedProjectIdAction } from '@stores/actions'
import { JSEncrypt } from 'jsencrypt'
import moment from 'moment'
import { EMPTY, Observable } from 'rxjs'
import { map } from 'rxjs/operators'
import { Login } from '../models/login.model'
import { ResetPasswordModel } from '../models/reset-password.model'
import { ResponseModel } from '../models/response.model'
import { AuthState } from '../store/reducers'

declare let encryptStr: any

declare let decryptStr: any

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  key = '92fdce43453434c36f8e3fbcb2b2e4fb'

  constructor(
    private httpClient: HttpClient,
    private configService: ConfigService,
    private serverSessionService: ServerSessionService,
    private route: ActivatedRoute,
    private store: Store<AuthState>,
    private navService: NavService,
    private xsstore: xsstore,
    private iframeMessengerService: IframeMessengerService
  ) {}

  getAccessToken() {
    return localStorage.getItem('access_token')?.replace(/"/g, '')
  }

  setAccessToken(token: string) {
    localStorage.setItem('access_token', token)
    // Whenever there is a change in the token, share the token with the child apps
    this.#shareBaseRequirementForChildApps()
  }

  getRefreshToken() {
    return localStorage.getItem('refresh_token')?.replace(/"/g, '')
  }

  setRefreshToken(token: string) {
    localStorage.setItem('refresh_token', token)
    // Whenever there is a change in refresh token, share the token with the child apps
    this.#shareBaseRequirementForChildApps()
  }

  removeAccessToken() {
    localStorage.removeItem('access_token')
  }

  removeRefreshToken() {
    localStorage.removeItem('refresh_token')
  }

  public decryptStr(plainText: string, key: string): string {
    return decryptStr(plainText, key)
  }

  /**
   * Attempts to verify if the user is authenticated by checking user details with the provided token.
   * If the token is expired (401 response), it will attempt a token refresh.
   * If refresh succeeds, it retries fetching user details.
   * If both direct fetch and refresh fail, return null.
   *
   * @remarks
   * Steps:
   * 1. Encrypt current URL (limited to 200 chars) using RSA public key.
   * 2. Retrieve stored access token.
   * 3. Make a GET request to /user/details with the Bearer token and Signature header.
   * 4. If unauthorized (401), attempt to refresh the token using the /token endpoint with URL-encoded form data.
   * 5. If refresh succeeds, store the new token and retry fetching user details once.
   * 6. Return user details if successful, otherwise null.
   *
   * @returns {Promise<ResponseModel | null>} User details if authenticated, null otherwise.
   */
  public async isUserAuthenticated(): Promise<ResponseModel | null> {
    const encryptor = new JSEncrypt()
    encryptor.setPublicKey(publicKey)
    const url = location.href.substring(0, 200)
    const encryptedData = encryptor.encrypt(url)
    const token = this.getAccessToken()
    const apiUrl = this.configService.getApiUrl() + '/user/details'

    const fetchUserDetails = async (
      accessToken: string
    ): Promise<{ user: ResponseModel | null; status: number }> => {
      try {
        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${accessToken}`.replace(/"/g, ''),
            Signature: encryptedData
          } as HeadersInit
        })

        if (!response.ok) {
          return { user: null, status: response.status }
        }

        const userDetails = await response.json()
        return { user: userDetails, status: 200 }
      } catch {
        return { user: null, status: 0 }
      }
    }

    // Initial attempt
    const { user: userDetails, status } = await fetchUserDetails(token)

    // If no user and status is 401, attempt refresh
    if (!userDetails && status === 401) {
      const refreshUrl = this.configService.getApiUrl() + '/token'
      const refreshToken = this.getRefreshToken()
      try {
        const refreshResponse = await fetch(refreshUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: `grant_type=refresh_token&refresh_token=${refreshToken}`
        })

        if (!refreshResponse.ok) {
          return null
        }

        const refreshData = await refreshResponse.json()
        const newAccessToken = refreshData.access_token
        const newRefreshToken = refreshData.refresh_token
        if (!newAccessToken) {
          return null
        }

        this.setAccessToken(newAccessToken)
        this.setRefreshToken(newRefreshToken)

        // Retry with new token
        const retryFetch = await fetchUserDetails(newAccessToken)
        return retryFetch.user
      } catch {
        return null
      }
    }

    return userDetails
  }

  /**
   * This main app is handling the token refresh for auth so the same tokens should be shared across its children apps.
   *
   * @remarks
   * The authenticated user's tokens are shared with the child apps to maintain the session.
   * When it is development mode, the tokens requires to share across multiple origins.
   *
   * In case of deployment mode, if the micro-app isn't hosted on the same domain,
   * the tokens should be shared, but if the micro-app is hosted on the same domain,
   * the tokens used by the micro-app should be the same as the main app.
   * @see IframeMessengerService
   * @returns {void}
   */
  #shareBaseRequirementForChildApps(): void {
    this.iframeMessengerService.sendMessage({
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: [
        {
          type: MessageType.TOKEN_UPDATE,
          content: {
            refreshToken: String(localStorage.getItem('refresh_token')),
            accessToken: String(localStorage.getItem('access_token'))
          }
        }
      ]
    })
  }

  refreshToken() {
    const refreshToken = this.getRefreshToken()

    if (refreshToken === null) {
      this.logout()
      return EMPTY
    }

    this.serverSessionService.maintainSession()

    return this.httpClient
      .post<any>(
        this.configService.getApiUrl() + '/token',
        `grant_type=refresh_token&refresh_token=${refreshToken}`,
        {
          headers: new HttpHeaders().set(
            'Content-Type',
            'application/x-www-form-urlencoded'
          )
        }
      )
      .pipe(
        map((data) => {
          if (data.access_token && data.refresh_token && data.expires_in) {
            this.setAccessToken(data.access_token)
            this.setRefreshToken(data.refresh_token)
          }

          return <any>data
        })
      )
  }

  logout(expired = false): void {
    let route = location.hash
    route = StringUtils.trim(route, '#')
    const isLoginPage = route === '/' || route.includes('/login')

    if (isLoginPage || expired) {
      this.removeAccessToken()
      this.removeRefreshToken()
      this.navService.navigateToLogin(isLoginPage ? '' : route, expired)
      this.store.dispatch(new ResetCachedProjectRightListAction())
      //selectedProjectId to be reset
      this.xsstore.dispatch(new SetSelectedProjectIdAction(null))
      this.configService.clearSession().subscribe()
    }
  }

  // -------- Services

  fetchToken(login: Login): Observable<any> {
    const endpoint = this.configService.getApiUrl() + '/token'

    // Encrypt the password
    const encryptedPassword = encryptStr(login.password, this.key)

    // Generate request body
    const body = new URLSearchParams()
    body.set('grant_type', 'password')
    body.set('username', login.userName)
    body.set('password', encryptedPassword)
    body.set('sessionId', this.key)

    const options = {
      headers: new HttpHeaders().set(
        'Content-Type',
        'application/x-www-form-urlencoded'
      )
    }

    return this.httpClient.post<ResponseModel>(
      endpoint,
      body.toString(),
      options
    )
  }

  fetchUserDetail(pwd: string): Observable<any> {
    const options = {
      headers: new HttpHeaders().set('Content-Type', 'application/json')
    }

    const endpoint =
      this.configService.getApiUrl() + '/login/AuthenticateCredential'

    return this.httpClient.post<string>(
      endpoint,
      {
        pwd: encryptStr(pwd, this.key),
        key: this.key,
        token: '',
        projectId: -1,
        currentUrl: window.location.href
      },
      options
    )
  }

  fetchExtUserDetail(token: string, projectId: number): Observable<any> {
    const options = {
      headers: new HttpHeaders().set('Content-Type', 'application/json')
    }

    const endpoint =
      this.configService.getApiUrl() + '/login/AuthenticateCredential'

    return this.httpClient.post<string>(
      endpoint,
      {
        pwd: '',
        key: this.key,
        token: token,
        projectId: projectId,
        currentUrl: window.location.href
      },
      options
    )
  }

  fetchExtUser$(userId = -1) {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/user/detailsExternal',
      {
        params: new HttpParams().set('userId', String(userId))
      }
    )
  }

  sendExtUserAuthNotification(
    UserId: number,
    resend: boolean
  ): Observable<any> {
    const options = {
      headers: new HttpHeaders().set('Content-Type', 'application/json')
    }

    // Get Id from earlier call to login controller
    const endpoint =
      this.configService.getApiUrl() +
      '/ExternalUserAuthentication?UserId=' +
      UserId.toString() +
      '&resend=' +
      resend
    return this.httpClient.post(endpoint, null, options)
  }

  verifyExtUserAuthCode(
    UserId: number,
    verificationCode: string,
    userToken: string,
    tokenType: string
  ): Observable<any> {
    const endpoint =
      this.configService.getApiUrl() + '/ExternalUserAuthentication'
    const param = {
      UserId: UserId,
      verificationCode: verificationCode,
      userToken: userToken,
      tokenType: tokenType
    }
    return this.httpClient.post(endpoint, param, {})
  }

  fetchUser$() {
    return this.httpClient.get(this.configService.getApiUrl() + '/user/details')
  }

  sendNotification(UserId: number, resend: boolean) {
    const param = { Email: true, SMS: false, PhoneCall: false }

    const options = {
      headers: new HttpHeaders().set('Content-Type', 'application/json')
    }

    // Get Id from earlier call to login controller
    const endpoint =
      this.configService.getApiUrl() +
      '/TwoFactorAuthentication?resend=' +
      resend
    return this.httpClient.post<boolean>(endpoint, param, options)
  }

  verifyAuthenticationCode(
    verificationCode: string,
    rememberUser: boolean
  ): Observable<any> {
    const endpoint = this.configService.getApiUrl() + '/TwoFactorAuthentication'
    const param = {
      verificationCode: verificationCode,
      rememberUser: rememberUser,
      ipAddress: '::1'
    }
    return this.httpClient.post(endpoint, param, {})
  }

  sendResetPasswordLink(userName: string): Observable<any> {
    const endpoint =
      this.configService.getApiUrl() +
      '/login/SendResetPasswordLink?userName=' +
      userName

    return this.httpClient.post<string>(endpoint, null, {})
  }

  resetPassword(password: string, token: string): Observable<any> {
    const options = {
      headers: new HttpHeaders().set('Content-Type', 'application/json')
    }
    const endpoint =
      this.configService.getApiUrl() + '/login/ResetPassword?token=' + token
    return this.httpClient.post(endpoint, { newPassword: password }, {})
  }

  changePassword(changePasswordModel: ResetPasswordModel, token: string) {
    const docUrl = this.configService.getApiUrl() + '/login/ResetPassword'
    if (token) {
      return this.httpClient.post(docUrl, changePasswordModel, {
        params: new HttpParams().set('token', token)
      })
    } else {
      return this.httpClient.post(docUrl, changePasswordModel, {})
    }
  }

  setServerSession(session: any): Observable<any> {
    const sessionObj = {}
    for (const key of Object.keys(session)) {
      if (key !== 'Script') {
        sessionObj[key] = session[key]
      }
    }

    return this.httpClient.post(
      this.configService.getWebBaseUrl() + '/OnDemand/MaintainSession.aspx',
      sessionObj,
      {
        responseType: 'text'
      }
    )
  }

  setClientSideSession(payload: string) {
    const SetSession = new Function('value', payload)
    SetSession()
  }

  fetchClientInfo$() {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/user/clientInfo'
    )
  }

  fetchMaintenanceInfo$() {
    return this.httpClient.get(this.configService.getApiUrl() + '/maintenance')
  }

  sendHoldUserAuthNotification(
    custId: number,
    resend: boolean
  ): Observable<any> {
    const options = {
      headers: new HttpHeaders().set('Content-Type', 'application/json')
    }

    // Get Id from earlier call to login controller
    const endpoint =
      this.configService.getApiUrl() +
      '/custodian/login/' +
      custId.toString() +
      '?resend=' +
      resend
    return this.httpClient.post(endpoint, null, options)
  }

  verifyHoldUserAuthCode(
    CustodianId: number,
    verificationCode: string,
    userToken: string,
    tokenType: string
  ): Observable<any> {
    const endpoint = this.configService.getApiUrl() + '/custodian/login/verify'
    const param = {
      CustodianId: CustodianId,
      verificationCode: verificationCode,
      userToken: userToken,
      tokenType: tokenType
    }
    return this.httpClient.post(endpoint, param, {})
  }

  checkActiveSession(enabled, sessionMins, userSessionId) {
    const loginTokenId = localStorage.getItem('loginTokenId')
    const activeLastSessionChecked = localStorage.getItem(
      'activeLastSessionChecked'
    )
    if (enabled) {
      const currentDateTime = moment(new Date(), 'YYYY-MM-DD h:mm:ss')
      const duration = moment.duration(
        currentDateTime.diff(activeLastSessionChecked)
      )
      const sub = duration.asMinutes()
      const isInvalidSession =
        sub > sessionMins && userSessionId && loginTokenId != userSessionId
      if (isInvalidSession) {
        this.logout(true)
      }
    }
  }

  emailUpdate(userId: number, emailAddress: string) {
    const url =
      this.configService.getApiUrl() +
      `/email-address/${userId}?emailAddress=${emailAddress}`
    return this.httpClient.put(url, '')
  }

  fetchLoginResponse<T>(sessionId: string): Observable<T> {
    const apiUrl = `${this.configService.getApiUrl()}/idp-settings/idp-login-response`
    return this.httpClient.get<T>(
      `${apiUrl}?sessionId=${encodeURIComponent(sessionId)}`
    )
  }

  fetchLogoutRedirect<T>(providerName: string): Observable<T> {
    const apiUrl = `${this.configService.getApiUrl()}/idp-settings/idp-logout-redirect`
    return this.httpClient.get<T>(
      `${apiUrl}?providerName=${encodeURIComponent(providerName)}`
    )
  }
}
