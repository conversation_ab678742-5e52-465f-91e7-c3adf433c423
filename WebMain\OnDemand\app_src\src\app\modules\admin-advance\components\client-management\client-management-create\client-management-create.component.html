<ng-container>
  <ng-container *ngTemplateOutlet="displayTemplate"> </ng-container>
</ng-container>
<ng-template #mainWrapper>
  <app-route-breadcrumb></app-route-breadcrumb>
  <form [formGroup]="clientForm">
    <div class="bg-white p-0 mb-2 client-info">
      <div class="heading">
        <div class="section-title">
          {{ currentMode === 'Edit' ? 'Update' : 'Create' }} Client Information
        </div>
      </div>
      <div *ngIf="formErrorMessage" class="alert alert-danger">
        {{ formErrorMessage }}
        <button type="button" (click)="formErrorMessage = null" class="close">
          <span aria-hidden="true" class="fa fa-times"></span>
        </button>
      </div>
      <div class="padding-15">
        <div class="row form-group">
          <div class="col-md-2">
            Client Name<span class="text-danger">*</span>
          </div>
          <div class="col-md-10">
            <input
              type="text"
              class="form-control"
              formControlName="name"
              [ngClass]="{ 'is-invalid': displayMessage?.name }"
            />
            <span class="invalid-feedback" *ngIf="displayMessage?.name">
              <!--all dynamic message of the name control rules are displayed here. such as pattern, maxlenght, required etc.,-->
              {{ displayMessage?.name }}
            </span>
          </div>
        </div>
        <div class="row form-group">
          <div class="col-md-2">Address</div>
          <div class="col-md-10">
            <input type="text" class="form-control" formControlName="address" />
          </div>
        </div>
        <div class="row form-group">
          <div class="col-md-2">Contact Person Name</div>
          <div class="col-md-10">
            <input
              type="text"
              class="form-control"
              formControlName="personName"
            />
          </div>
        </div>
        <div class="row form-group">
          <div class="col-md-2">Mobile No.</div>
          <div class="col-md-4">
            <input
              class="form-control"
              formControlName="mobileNumber"
              [ngClass]="{ 'is-invalid': displayMessage?.mobileNumber }"
              (keypress)="disabledAplhabetKeyPress($event)"
            />
            <span *ngIf="displayMessage?.mobileNumber" class="invalid-feedback">
              {{ displayMessage?.mobileNumber }}
            </span>
          </div>
          <div class="col-md-2">Phone No.</div>
          <div class="col-md-4">
            <input
              class="form-control"
              formControlName="phoneNumber"
              [ngClass]="{ 'is-invalid': displayMessage?.phoneNumber }"
              (keypress)="disabledAplhabetKeyPress($event)"
            />
            <span *ngIf="displayMessage?.phoneNumber" class="invalid-feedback">
              {{ displayMessage?.phoneNumber }}
            </span>
          </div>
        </div>
        <div class="row form-group">
          <div class="col-md-2">Email<span class="text-danger">*</span></div>
          <div class="col-md-4">
            <input
              type="email"
              class="form-control"
              formControlName="email"
              [ngClass]="{ 'is-invalid': displayMessage?.email }"
            />
            <span *ngIf="displayMessage?.email" class="invalid-feedback">
              {{ displayMessage?.email }}
            </span>
          </div>
          <div class="col-md-2">Fax</div>
          <div class="col-md-4">
            <input
              type="number"
              class="form-control"
              formControlName="fax"
              (keypress)="disabledAplhabetKeyPress($event)"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-md-2">Account No.</div>
          <div class="col-md-4">
            <input
              type="text"
              class="form-control"
              formControlName="accountNumber"
            />
          </div>
          <div class="col-md-2">Memo</div>
          <div class="col-md-4">
            <textarea
              class="form-control"
              formControlName="memo"
              rows="3"
            ></textarea>
          </div>
        </div>
      </div>
      <div class="padding-15 pt-0">
        <div class="row client-wrap">
          <div class="col-md-6 venio-client">
            <div class="modal-body h-100">
              <p>Assign File Server Location</p>
              <div>
                <div class="row form-group">
                  <div class="col-md-4">Upload Location</div>
                  <div class="col-md-8">
                    <mat-select
                      class="form-control"
                      formControlName="uploadLocation"
                      name="uploadLocation"
                    >
                      <mat-option
                        class="wrap-text"
                        *ngFor="let uploadLocation of fileServerList"
                        [value]="uploadLocation.FSID"
                      >
                        {{ uploadLocation.FSDisplayName }}
                      </mat-option>
                    </mat-select>
                    <small>{{ uploadSharedLocationInfo }}</small>
                  </div>
                </div>
                <div class="row form-group">
                  <div class="col-md-4">Case Location</div>
                  <div class="col-md-8">
                    <mat-select
                      class="form-control"
                      formControlName="projectLocation"
                    >
                      <mat-option
                        class="wrap-text"
                        *ngFor="let projectLocation of fileServerList"
                        [value]="projectLocation.FSID"
                      >
                        {{ projectLocation.FSDisplayName }}
                      </mat-option>
                    </mat-select>
                    <small>{{ projectSharedLocationInfo }}</small>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4">Production/Download Location</div>
                  <div class="col-md-8">
                    <mat-select
                      class="form-control"
                      formControlName="exportLocation"
                    >
                      <mat-option
                        class="wrap-text"
                        *ngFor="let exportLocation of fileServerList"
                        [value]="exportLocation.FSID"
                      >
                        {{ exportLocation.FSDisplayName }}
                      </mat-option>
                    </mat-select>
                    <small>{{ exportSharedLocationInfo }}</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="modal-body h-100">
              <p>Assign Template</p>
              <mat-select
                [(value)]="selectionType"
                (selectionChange)="onSelectionTypeChange($event.value)"
                class="form-control mb-3"
              >
                <mat-option class="wrap-text" value="-1"
                  >Default Service</mat-option
                >
                <!--<mat-option value="single">PDF Service</mat-option> -->
                <mat-option
                  *ngFor="let list of serviceTypeList?.serviceTypes"
                  [value]="list.serviceTypeName"
                  >{{ list.serviceTypeDisplayName }}</mat-option
                >
              </mat-select>

              <ng-container [ngSwitch]="selectionType">
                <mat-tab-group class="assign-template-tab">
                  <ng-container *ngSwitchCase="'-1'">
                    <mat-tab label="Case">
                      <dx-data-grid
                        [dataSource]="projectTemplate"
                        keyExpr="TemplateId"
                        [showBorders]="true"
                        [showRowLines]="true"
                        #projectTemplateGrid
                        [selectedRowKeys]="selectedProjectTemplateRows"
                        (onSelectionChanged)="
                          onGridSelectionChanged('projectTemplateGrid', $event)
                        "
                      >
                        <dxo-selection
                          mode="multiple"
                          showCheckBoxesMode="always"
                        ></dxo-selection>
                        <dxi-column
                          dataField="TemplateName"
                          caption="Template Name"
                        ></dxi-column>
                      </dx-data-grid>
                    </mat-tab>
                    <mat-tab label="Production">
                      <dx-data-grid
                        [dataSource]="exportTemplate"
                        keyExpr="TemplateId"
                        [showBorders]="true"
                        [showRowLines]="true"
                        #exportTemplateGrid
                        [selectedRowKeys]="selectedExportTemplateRows"
                        (onSelectionChanged)="
                          onGridSelectionChanged('exportTemplateGrid', $event)
                        "
                      >
                        <dxo-selection
                          mode="multiple"
                          showCheckBoxesMode="always"
                        ></dxo-selection>
                        <dxi-column
                          dataField="TemplateName"
                          caption="Template Name"
                        ></dxi-column>
                      </dx-data-grid>
                    </mat-tab>
                  </ng-container>
                  <ng-container *ngFor="let config of gridMappingArray">
                    <ng-container *ngSwitchCase="config.serviceType">
                      <mat-tab label="Case">
                        <dx-data-grid
                          [dataSource]="projectTemplate"
                          keyExpr="TemplateId"
                          [showBorders]="true"
                          [showRowLines]="true"
                          [selectedRowKeys]="
                            this[config.serviceProjectTemplateInfo]
                          "
                          #{{config.projectTemplateGrid}}
                        >
                          <dxi-column
                            caption
                            dataField="TemplateId"
                            [width]="70"
                            cellTemplate="selectCellTemplate"
                          >
                            <div
                              class="client-radio-button-wrapper"
                              *dxTemplate="let data of 'selectCellTemplate'"
                            >
                              <dx-radio-group
                                [value]="this[config.selectedProjectTemplateId]"
                                [items]="[{ value: data.data.TemplateId }]"
                                valueExpr="value"
                                (onValueChanged)="
                                  onRadioButtonSelect(
                                    $event.value,
                                    config.projectTemplateGrid,
                                    config.serviceType
                                  )
                                "
                                class="client-radio-group"
                              ></dx-radio-group>
                            </div>
                          </dxi-column>
                          <dxi-column
                            dataField="TemplateName"
                            caption="Template Name"
                          ></dxi-column>
                        </dx-data-grid>
                      </mat-tab>
                      <mat-tab label="Production">
                        <dx-data-grid
                          [dataSource]="exportTemplate"
                          keyExpr="TemplateId"
                          [showBorders]="true"
                          [showRowLines]="true"
                          [selectedRowKeys]="
                            this[config.serviceExportTemplateInfo]
                          "
                          #{{config.exportTemplateGrid}}
                        >
                          <dxi-column
                            caption
                            dataField="TemplateId"
                            [width]="70"
                            cellTemplate="selectCellTemplate"
                          >
                            <div
                              class="client-radio-button-wrapper"
                              *dxTemplate="let data of 'selectCellTemplate'"
                            >
                              <dx-radio-group
                                [value]="this[config.selectedExportTemplateId]"
                                [items]="[{ value: data.data.TemplateId }]"
                                valueExpr="value"
                                (onValueChanged)="
                                  onRadioButtonSelect(
                                    $event.value,
                                    config.exportTemplateGrid,
                                    config.serviceType
                                  )
                                "
                                class="client-radio-group"
                              ></dx-radio-group>
                            </div>
                          </dxi-column>
                          <dxi-column
                            dataField="TemplateName"
                            caption="Template Name"
                          ></dxi-column>
                        </dx-data-grid>
                      </mat-tab>
                    </ng-container>
                  </ng-container>

                  <mat-tab label="Production Field(s)">
                    <dx-data-grid
                      #exportFieldTemplateGrid
                      [dataSource]="exportFieldTemplate"
                      keyExpr="TemplateId"
                      [showBorders]="true"
                      [showRowLines]="true"
                      [selectedRowKeys]="selectedExportFieldTemplateRows"
                      (onSelectionChanged)="
                        onGridSelectionChanged(
                          'exportFieldTemplateGrid',
                          $event
                        )
                      "
                    >
                      <dxo-selection
                        mode="multiple"
                        showCheckBoxesMode="always"
                      ></dxo-selection>
                      <dxi-column
                        dataField="TemplateName"
                        caption="Template Name"
                      ></dxi-column>
                    </dx-data-grid>
                  </mat-tab>
                </mat-tab-group>
              </ng-container>
            </div>
          </div>
        </div>
        <div class="text-right mt-4">
          <button
            class="btn btn-primary"
            [disabled]="isSubmitting"
            (click)="saveChanges()"
            class="btn btn-{{ config.themeClient }}-{{
              isSubmitting ? ' disabled border' : 'primary'
            }} mr-2 "
          >
            <mat-spinner
              *ngIf="isSubmitting"
              [strokeWidth]="2"
              [diameter]="20"
              mode="indeterminate"
              class="mr-1"
            >
            </mat-spinner>
            {{ currentMode == 'Create' ? 'Create' : 'Update' }}
          </button>
        </div>
      </div>
    </div>
  </form>
</ng-template>

<ng-template #editConfirm>
  <form [formGroup]="confirmationForm">
    <div
      class=""
      cdkDrag
      cdkDragBoundary=".cdk-global-overlay-wrapper"
      cdkDragRootElement=".cdk-overlay-pane"
    >
      <div class="modal-header d-flex col-12" cdkDragHandle>
        <h4 class="modal-title pull-left">Confirm!</h4>
        <button
          type="button"
          class="close pull-right close-confirm"
          [mat-dialog-close]="false"
        >
          <span aria-hidden="true" class="fa fa-times"></span>
        </button>
      </div>
      <div class="modal-body h5 text-center mb-0">
        The case location will be updated for all new cases with the selected
        client.
        <br />
        <mat-checkbox
          class="mt-3"
          formControlName="checkEditUpdateExistingProjectLocation"
          >Update case location for new media of existing case(s)</mat-checkbox
        >
      </div>
      <div class="modal-footer text-right">
        <button
          type="button"
          class="btn btn-primary float-right close-confirm"
          [mat-dialog-close]="true"
        >
          Ok
        </button>
        <button
          [mat-dialog-close]="false"
          type="button"
          class="btn btn-grey close-confirm"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</ng-template>
