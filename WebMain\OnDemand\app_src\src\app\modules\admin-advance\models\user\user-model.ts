import { ADUserDetails } from '@admin-advance/store/ad-login-settings/ad-login-settings-state.model'

export interface UserQueryParams {
  userID: number
}
export interface UserModel {
  UserName?: string
  Address?: string
  UserID?: number
  Email?: string
  Mobile?: string
  Fax?: string
  UserLockValidUpto?: string
  UserLockType?: string
  LoggedInUserId: number
  ClientId: number
  EmailAddress: string
  FullName: string
  GlobalRoleId: number
  GlobalRoleName?: string
  UserGlobalRoleId: number
  Phone: string
  isUserDeactivated?: boolean
  IsUserLocked?: boolean
  UserCaseAssignmentModel: UserCaseAssignmentModel
  UserLayoutId?: number
  FailedLoginAttempts?: number
  isADUser?: boolean
  ForceUserToChangePassword?: boolean
  isIdpUser?: boolean
  IsUserDeactivated?: boolean
  userDetails?: ADUserDetails
  generatePassword?: boolean
  DisablePasswordReset?: boolean
  DeactivationModel?: UserDeactivationModel
}

export interface UserCaseAssignmentModel {
  CaseGroupAssignedInfoList?: CaseGroupAssignedInfo[]
  UserId?: number
}

export interface UserDeactivationModel {
  UserId?: number
  DeactivationReason?: string
}

export interface CaseGroupAssignedInfo {
  Assigned: boolean
  AvailableGroups: any
  CaseName: string
  ClientMatterNumber: number
  GroupId: number
  ProjectId: number
  ProjectName: string
  Groups: GroupModel[]
}

export interface GroupModel {
  GroupId: number
  GroupName: string
  GlobalRoleId: number
}

export interface UserRightModel {
  RightDescription: string
  RightId: number
  RightName: string
}

export interface UserRoleModel {
  description?: string
  globalRoleId?: number
  globalRoleName?: string
  groupId?: number
  mappedGroup?: string
}

export interface UserRoleListResponse {
  data: UserRoleModel[]
  message: string
  status: string
}

export interface UserListModel {
  adUserGUID: number
  adUserSID: number
  address: string
  clientId: number
  email: string
  failedLoginAttempts: number
  fax: string
  forceUserToChangePassword: boolean
  fullName: string
  globalRoleId: number
  globalRoleName: string
  hasDesktopAccess: boolean
  hasOnDemandAccess: boolean
  hasReviewAccess: boolean
  hasTouchAccess: boolean
  hasWebECAAccess: boolean
  isADUser: boolean
  isUserAdmin: boolean
  isUserApproved: boolean
  isUserDeactivated: boolean
  isUserLocked: boolean
  mobile: string
  phone: string
  showNotification: boolean
  userCaseAssignmentModel: any
  userID: number
  userLockType: string
  userLockValidUpto: string
  userName: string
  userRole: string
  disablePasswordReset: boolean
}

export interface UserInformation {
  userID: string
  fullName: string
  userName: string
  address: string
  email: string
  phone: string
  mobile: string
  fax: string
  globalRoleId: number
  isUserLocked: boolean
  isUserDeactivated: boolean
  failedLoginAttempts: number
  userLockValidUpto: Date
  userLockType: string
  forceUserToChangePassword: boolean
  hasDesktopAccess: boolean
  hasWebECAAccess: boolean
  hasReviewAccess: boolean
  hasTouchAccess: boolean
  isADUser: boolean
  isIdPUser: boolean
  adUserSID: string
  adUserGUID: string
  hasOnDemandAccess: boolean
  clientId: string
  showNotification: boolean
  isUserApproved: boolean
  createdByName: string
  createdDate: Date
  userRole: string
  globalRoleName: string
  isUserAdmin: boolean
  userCaseAssignmentModel: any
  userlayoutId: number
  disablePasswordReset: boolean
  deactivationReason: string
}

export interface ReviewUserModel {
  userId: number
  fullName: string
}
