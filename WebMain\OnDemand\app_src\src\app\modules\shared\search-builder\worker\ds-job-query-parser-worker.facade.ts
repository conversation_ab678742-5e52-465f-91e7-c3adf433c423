import { Injectable } from '@angular/core'
import { fromWorkerPool } from '@shared/observable-webworker'
import { DsJobValueTypes } from '@shared/search-builder/query-builder-settings.model'
import { FilterPayloadModel } from '@shared/search-builder/query-builder-static.model'
import { getParserWorkerInstances } from '@shared/search-builder/worker/get-parser-worker-instances'
import { forkJoin, Observable, of } from 'rxjs'
import { map } from 'rxjs/operators'

@Injectable()
export class DsJobQueryParserWorkerFacade {
  /**
   * Handles payload processing within the worker context
   * @param {Worker} worker - Worker instance to process the payload
   * @param {FilterPayloadModel<DsJobValueTypes[]>} payload - The payload to be processed
   * @returns {Observable<string>} - An Observable of the processed query string
   */
  #handlePayloadProcessing(
    worker: Worker,
    payload: FilterPayloadModel<DsJobValueTypes[]>
  ): Observable<string> {
    return fromWorkerPool<FilterPayloadModel<DsJobValueTypes[]>, string>(
      () => worker,
      of(payload)
    )
  }

  /**
   * Assembles the query parts into a combined query string
   * @param {string[]} queryParts - Individual query parts returned from workers
   * @param {FilterPayloadModel<DsJobValueTypes[]>} payload - Payload containing the operator
   * @returns {string} - The combined queries string
   */
  #assembleQueries(
    queryParts: string[],
    payload: FilterPayloadModel<DsJobValueTypes[]>
  ): string {
    const isNot = payload.operators.main === 'NOT'
    return queryParts
      .filter(Boolean)
      .map((query) => `${isNot ? `(${query.trim()})` : query}`)
      .join(' AND ')
  }

  /**
   * Joins common query part with the other queries
   * @param {string} commonQuery - Common query part
   * @param {string} combinedQueries - Rest of the combined queries
   * @returns {string} - Joined queries with common query part
   */
  #joinCommonQueryWithOthers(
    commonQuery: string,
    combinedQueries: string
  ): string {
    return `${commonQuery.trim()}${
      combinedQueries && commonQuery ? ' AND ' : ''
    }${combinedQueries}`
  }

  /**
   * Finalizes the query by concatenating with history, removing newline characters and multiple spaces
   * @param {FilterPayloadModel<DsJobValueTypes[]>} payload - The payload containing the history
   * @param {string} combinedQueries - Joined queries
   * @returns {string} - Finalized query string
   */
  #finalizeQueryString(
    payload: FilterPayloadModel<DsJobValueTypes[]>,
    combinedQueries: string
  ): string {
    const isNot = payload.operators.main === 'NOT'
    const operatorStr = isNot
      ? ` AND ${payload.operators.main}`
      : payload.operators.main
    const rawFinalized = `${operatorStr} ${combinedQueries}`.replace(
      /(?:\r\n|\r|\n)/g,
      ''
    )

    // Return finalized query string after replacing any newline characters and multiple space with a single space
    return rawFinalized.replace(/(\r?\n|\r|\s+)/g, ' ')
  }

  /**
   * Generates a final query string for DsJob based on the job type options.
   * It utilizes worker instances to process the payload, assembles the query parts,
   * and finalizes the query string.
   * The order of the workers matters here, and the 'common' worker is always the first one.
   * The payload is the same for all jobs, but data and types are different,
   * which are taken care of within worker instances.
   * @param {FilterPayloadModel<DsJobValueTypes[]>} payload - List of DsJobValueTypes objects
   * @returns {Observable<string>} - An Observable of the final processed query string
   * @see getParserWorkerInstances
   */
  public generateFinalQueryString(
    payload: FilterPayloadModel<DsJobValueTypes[]>
  ): Observable<string> {
    const workerInstances = getParserWorkerInstances().map((workerInstance) =>
      this.#handlePayloadProcessing(workerInstance, payload)
    )
    return forkJoin(workerInstances).pipe(
      map((queryParts) => {
        // Since the array of worker result, the 1st is the common for all, so we have handled it separately.
        const commonQuery = queryParts[0]

        // The other part of worker result excluding `common` being handled separately.
        const otherQueries = queryParts.slice(1)

        const assembledQueries = this.#assembleQueries(otherQueries, payload)
        const queriesWithCommon = this.#joinCommonQueryWithOthers(
          commonQuery,
          assembledQueries
        )
        return this.#finalizeQueryString(payload, queriesWithCommon)
      })
    )
  }
}
