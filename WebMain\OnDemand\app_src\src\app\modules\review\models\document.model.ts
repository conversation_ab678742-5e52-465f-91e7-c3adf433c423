import { SamplingModel } from '@admin-advance/models'
import {
  CommaSeparatedNumberConverter,
  StringArrayNumberArrayConverter,
  StringNumberConverter
} from '@shared/utils/json-converters'
import {
  Any,
  <PERSON>sonConverter,
  JsonCustomConvert,
  JsonObject,
  JsonProperty
} from 'json2typescript'
import { ProjectTag, TagSettings } from './review-params.model'
import { SlipsheetModel } from './review.model'
import { SearchResultRequestData } from './search.model'

// TODO This is suppose to be in a separate file.
// But moving to a different file creates circular dependencies issue.
// Need to figure something out.
@JsonConverter
export class ColonSeparatedStringToUserModelConverter
  implements JsonCustomConvert<DocumentShareUserModel[]>
{
  serialize(userModels: DocumentShareUserModel[]): any {
    if (!userModels) {
      return null
    }
    const models: string[] = []
    for (const userModel of userModels) {
      const model =
        userModel.userId +
        ':' +
        userModel.email +
        ':' +
        userModel.userRole +
        ':' +
        userModel.fullName
      models.push(model)
    }
    return models
  }

  deserialize(models: string[]): DocumentShareUserModel[] {
    if (!models) {
      return null
    }
    const userModels: DocumentShareUserModel[] = []
    for (const model of models) {
      const userModel = new DocumentShareUserModel()
      const values = model.split(':')
      userModel.userId = Number(values[0])
      userModel.email = values[1]
      userModel.userRole = values[2]
      userModel.fullName = values[3]
      userModels.push(userModel)
    }
    return userModels
  }
}

@JsonObject('DocumentTagReview')
export class DocumentTag {
  @JsonProperty('TagId', Number, true) tagId: number = null

  @JsonProperty('Comments', Number, true) comments?: string = null

  @JsonProperty('tagState', Boolean, true) tagState: boolean = null

  @JsonProperty('reviewTagTriState', Boolean, false)
  reviewTagTriState?: boolean = null
}

@JsonObject('DocumentTagRequestModelReview')
export class DocumentTagRequestModel {
  @JsonProperty('FileIds', StringArrayNumberArrayConverter, true)
  fileIds: number[] = null

  @JsonProperty('IsNotReviewedOnly', Boolean, true)
  isNotReviewedOnly: boolean = null

  @JsonProperty('MarkAsReviewed', Boolean, true) markAsReviewed: boolean = null

  @JsonProperty('ProjectId', StringNumberConverter, true)
  projectId: number = null

  @JsonProperty('SearchTempTableName', String, true)
  searchTempTableName: string = null

  @JsonProperty('TagSettings', TagSettings, true)
  tagSettings: TagSettings = null

  @JsonProperty('Tags', [ProjectTag], true) tags: ProjectTag[] = null

  @JsonProperty('UnSelectedFileIds', StringArrayNumberArrayConverter, true)
  unSelectedFileIds: number[] = null

  @JsonProperty('isBatchSelected', Boolean, true)
  isBatchSelected: boolean = null
}

export class DocumentMetadata {
  key: string

  value: string
}

export class ParentChild {
  fileId: number

  fileName: string

  metadata: DocumentMetadata[]

  children: ParentChild[]
}

export class DuplicateDoc {
  fileId: number = null

  orderId: number = null

  metadata: DocumentMetadata[]
}

@JsonObject('DocumentNoteReview')
export class DocumentNote {
  @JsonProperty('Accessibility', String, true) accessibility: string = null

  @JsonProperty('Comment', String, true) comment: string = null

  @JsonProperty('CommentId', Number, true) commentId: number = null

  @JsonProperty('CommentedBy', String, true) commentedBy: string = null

  @JsonProperty('GroupId', Number, true) groupId: number = null

  @JsonProperty('InReplyToCommentId', Number, true)
  inReplyToCommentId: number = null

  @JsonProperty('TimeStamp', String, true) timeStamp: string = null

  @JsonProperty('UserId', Number, true) userId: number = null

  @JsonProperty('children', [DocumentNote], true)
  children: DocumentNote[] = null
}

@JsonObject('DocumentNotesResponseModelReview')
export class DocumentNotesResponseModel {
  @JsonProperty('DocumentNotes', [DocumentNote], true)
  documentNotes: DocumentNote[] = null

  @JsonProperty('EvaluatedCommentId', [Number], true)
  evaluatedCommentIds: number[] = null
}

@JsonObject('EditDocumentNoteRequestModelReview')
export class EditDocumentNoteRequestModel {
  @JsonProperty('Accessibility', String, true) accessibility: string = null

  @JsonProperty('Comment', String, true) comment: string = null

  @JsonProperty('CommentId', Number, true) commentId: number = null

  @JsonProperty('CommentedBy', String, true) commentedBy: string = null

  @JsonProperty('Datetime', String, true) datetime: string = null
}

@JsonObject('SaveTagRequestModelReview')
export class SaveTagRequestModel {
  @JsonProperty('FileIds', StringArrayNumberArrayConverter, true)
  fileIds: number[] = null

  @JsonProperty('IsNotReviewedOnly', Boolean, true)
  isNotReviewedOnly: boolean = null

  @JsonProperty('MarkAsReviewed', Boolean, true) markAsReviewed: boolean = null

  @JsonProperty('ProjectId', StringNumberConverter, true)
  projectId: number = null

  @JsonProperty('SearchTempTableName', String, true)
  searchTempTableName: string = null

  @JsonProperty('TagSettings', TagSettings, true)
  tagSettings: TagSettings = null

  @JsonProperty('Tags', [ProjectTag], true) tags: ProjectTag[] = null

  @JsonProperty('UnSelectedFileIds', StringArrayNumberArrayConverter, true)
  unSelectedFileIds: number[] = null

  @JsonProperty('isBatchSelected', Boolean, true)
  isBatchSelected: boolean = null

  @JsonProperty('MediaList', [Number], true)
  medialist: number[] = null

  @JsonProperty('ReviewSetBatchId', StringNumberConverter, true)
  reviewSetBatchId = -1

  @JsonProperty('DocShareToken', String, true)
  docShareToken: string = null

  @JsonProperty('TagEventComment', String, true)
  tagEventComment: string = null

  @JsonProperty('samplingInfo', Object, true)
  samplingInfo: SamplingModel = null

  @JsonProperty('isRandom', Boolean, true) isRandom: boolean = null

  @JsonProperty('ModuleName', String, true)
  moduleName: string = null

  @JsonProperty('IsEmailThreadTagging', Boolean, false)
  isEmailThreadTagging = false
}

@JsonObject('SaveTagResponseModelReview')
export class SaveTagResponseModel {
  @JsonProperty('AppliedTagRule', TagSettings, true)
  appliedTagRule: TagSettings = null

  @JsonProperty('IncludeEmailThread', Boolean, true)
  includeEmailThread: boolean = null

  @JsonProperty('PropagatePCSet', Boolean, true) propagatePCSet: boolean = null

  @JsonProperty('dupTagOption', Number, true) dupTagOption: number = null

  @JsonProperty('nddTagOption', Number, true) nddTagOption: number = null

  @JsonProperty('DuplicateDocCount', Number, true)
  duplicateDocCount: number = null

  @JsonProperty('EmailThreadCount', Number, true)
  emailThreadCount: number = null

  @JsonProperty('IsTagOperation', Boolean, true) isTagOperation: boolean = null

  @JsonProperty('IsTagOperationSuccessful', Boolean, true)
  isTagOperationSuccessful: boolean = null

  @JsonProperty('NddFileCount', Number, true) nddFileCount: number = null

  @JsonProperty('ParentFileCount', Number, true) parentFileCount: number = null

  @JsonProperty('SelectedDocCount', Number, true)
  selectedDocCount: number = null

  @JsonProperty('TotalCount', Number, true) totalCount: number = null

  @JsonProperty('UntaggedCount', Number, true) untaggedCount: number = null

  @JsonProperty('batchid', Number, true) batchId: number = null

  @JsonProperty('tagName', String, true) tagName: string = null

  @JsonProperty('tagId', Number, true) tagId: number = null

  @JsonProperty('IsExclusive', Boolean, true) isExclusive: boolean = null

  @JsonProperty('TagGroupName', String, true) tagGroupName: string = null
}

export class FileTaggedRequestModel {
  isFileTagged = false

  reviewSetId = -1

  currentPageFileIds: number[] = null
}

export class FileTaggedResponseModel {
  reviewedFileIds: number[] = null
}

/**
 * Tag Saved Model use for Tag copy in review
 */
export class TagSavedModel {
  fileId: number

  fileName: string

  tagSavedChange: TagSavedChangeModel[]
}

/**
 * Tag Save Change use for TagSavedModel
 */
export class TagSavedChangeModel {
  tagId: number

  tagName: string

  isTagOperation: boolean

  isExclusive?: boolean

  tagGroupName?: string
}

@JsonObject('ViewerSettingsReview')
export class ViewerSettings {
  @JsonProperty('AllowTiff', Boolean, true) allowTiff: boolean = null

  @JsonProperty('AllowTiffSettings', Boolean, true)
  allowTiffSettings: boolean = null

  @JsonProperty('ApplyTiffPageLimit', Boolean, true)
  applyTiffPageLimit: boolean = null

  @JsonProperty('CanEditPageLimit', Boolean, true)
  canEditPageLimit: boolean = null

  @JsonProperty('CanRedact', Boolean, true) canRedact: boolean = null

  @JsonProperty('ControlDB', String, true) controlDB: string = null

  @JsonProperty('DisplayMaxFileSize', Number, true)
  displayMaxFileSize: number = null

  @JsonProperty('ErrorMessage', String, true) errorMessage: string = null

  @JsonProperty('Extension', String, true) extension: string = null

  @JsonProperty('FileId', Number, true) fileId: number = null

  @JsonProperty('FileSize', Number, true) fileSize: number = null

  @JsonProperty('FulltextFileSize', Number, true)
  fulltextFileSize: number = null

  @JsonProperty('GeneratedOCRStatus', Number, true)
  generatedOCRStatus: number = null

  @JsonProperty('HideTabs', String, true) hideTabs: string = null

  @JsonProperty('HtmlFileSize', Number, true) htmlFileSize: number = null

  @JsonProperty('IsEdoc', Boolean, true) isEdoc: boolean = null

  @JsonProperty('MaxPageLimit', Number, true) maxPageLimit: number = null

  @JsonProperty('Module', String, true) module: string = null

  @JsonProperty('NativeType', Number, true) nativeType: number = null

  @JsonProperty('PasswordBank', String, true) passwordBank: string = null

  @JsonProperty('PrevFileId', Number, true) prevFileId: number = null

  @JsonProperty('PreviledgeTerms', String, true) privilegeTerms: string = null

  @JsonProperty('ProjectId', Number, true) projectId: number = null

  @JsonProperty('RadioGroupFulltextString', String, true)
  radioGroupFulltextString: string = null

  @JsonProperty('RedactedOCRStatus', Number, true)
  redactedOCRStatus: number = null

  @JsonProperty('SearchTerms', String, true) searchTerms: string = null

  @JsonProperty('SendToTiff', Boolean, true) sendToTiff: boolean = null

  @JsonProperty('SessionId', String, true) sessionId: string = null

  @JsonProperty('TimezoneOffset', Number, true) timezoneOffset: number = null

  @JsonProperty('UserId', Number, true) userId: number = null

  @JsonProperty('IsRedacted', Boolean, true) isRedacted: boolean = null

  @JsonProperty('ParentFileType', String, true) parentFileType: string = null

  @JsonProperty('ShowGeneratedTiffOCR', Boolean, true)
  showGeneratedTiffOCR: boolean = null

  @JsonProperty('ShowRedactedTiffOCR', Boolean, true)
  showRedactedTiffOCR: boolean = null

  @JsonProperty('FulltextPriority', Number, true) fulltextPriority: number =
    null
}

export class DocumentEmailThread {
  conversationId: string

  fileId: number

  metadata: DocumentMetadata[]

  children: DocumentEmailThread[]
}

@JsonObject('DocumentShareUserModelReview')
export class DocumentShareUserModel {
  @JsonProperty('Email', String, true) email: string = null

  @JsonProperty('FullName', String, true) fullName: string = null

  @JsonProperty('UserId', Number, true) userId: number = null

  @JsonProperty('UserRole', String, true) userRole: string = null
}

@JsonObject('DocumentShareModelReview')
export class DocumentShareModel {
  @JsonProperty('AllowToAddDocumentNotes', Boolean, true)
  allowToAddDocumentNotes: boolean = null

  @JsonProperty('DocumentSelectionType', StringNumberConverter, true)
  documentSelectionType: number = null

  @JsonProperty('ProjectId', StringNumberConverter, true)
  projectId: number = null

  @JsonProperty('SearchGlobalTempTableName', String, true)
  searchGlobalTempTableName: string = null

  @JsonProperty('ShareExpiredIn', StringNumberConverter, true)
  shareExpiredIn: number = null

  @JsonProperty('SharePermission', String, true) sharePermission: string = null

  @JsonProperty('SharedBy', StringNumberConverter, true) sharedBy: number = null

  @JsonProperty('SharedDocumentIds', CommaSeparatedNumberConverter, true)
  sharedDocumentIds: number[] = null

  @JsonProperty('SharedExtUserInfo', [String], true)
  sharedExtUserInfo: string[] = null

  @JsonProperty('ShareName', String, true) shareName: string = null

  @JsonProperty('SharedInstructionMsg', String, true)
  sharedInstructionMsg: string = null

  @JsonProperty(
    'SharedUserInfo',
    ColonSeparatedStringToUserModelConverter,
    true
  )
  sharedUserInfo: DocumentShareUserModel[] = null

  @JsonProperty('AllowToTagUntag', Boolean, true)
  allowToTagUntag: boolean = null

  @JsonProperty('AllowToApplyRedaction', Boolean, true)
  allowToApplyRedaction: boolean = null

  @JsonProperty('RemainingDaysUntilExpiry', StringNumberConverter, true)
  remainingDaysUntilExpiry: number = null

  @JsonProperty('ExternalUsers', [DocumentShareUserModel], true)
  externalUsers: DocumentShareUserModel[] = null

  @JsonProperty('InternalUsers', [DocumentShareUserModel], true)
  internalUsers: DocumentShareUserModel[] = null

  @JsonProperty('SharedExpiryDate', String, true)
  sharedExpiryDate: string = null

  @JsonProperty('IsWrite', Boolean, true)
  isWrite: boolean = null

  @JsonProperty('SharedOn', String, true)
  sharedOn: string = null

  @JsonProperty('DocumentShareID', StringNumberConverter, true)
  documentShareID: number = null

  @JsonProperty('CarbonCopySender', Boolean, true)
  carbonCopySender: boolean = null
}

@JsonObject('DocumentShareUserResponseModelReview')
export class DocumentShareUserResponseModel {
  @JsonProperty('ShareExtUserInfoList', [DocumentShareUserModel], true)
  shareExtUserInfoList: DocumentShareUserModel[] = null

  @JsonProperty('ShareUserInfoList', [DocumentShareUserModel], true)
  shareUserInfoList: DocumentShareUserModel[] = null
}
export class ChunkDocumentModel {
  fileId: number = null

  userId: number = null

  previousFileId: number = null

  isCacheAvailable: boolean = null

  currentPartIndex: number = null

  chunkPartsCount: number = null

  includeHeader?: boolean = null

  projectId: number = null

  maxDisplaySize?: number = null

  searchHighlightList?: any = null

  type?: number = null

  isExternalUser?: boolean = null

  fieldRequestData?: SearchResultRequestData = null

  conversationId?: string = null

  highlightTermGroupIds?: number[] = []

  requestPartSize?: number

  imageSetId?: number

  fetchAllParts?: boolean

  highlightSearchIds?: number[]

  isRetry?: boolean
}

@JsonObject('ChunkDocumentPartModelReview')
export class ChunkDocumentPartModel {
  @JsonProperty('PartId', StringNumberConverter, true) PartId: number = null

  @JsonProperty('FileId', StringNumberConverter, true) FileId: number = null

  @JsonProperty('PartIndex', StringNumberConverter, true)
  PartIndex: number = null

  @JsonProperty('Content', String, true) Content: string = null

  @JsonProperty('Size', StringNumberConverter, true) Size: number = null

  @JsonProperty('TotalParts', StringNumberConverter, true)
  TotalParts: number = null

  @JsonProperty('IsForwardedText', Boolean, true)
  IsForwardedText: boolean = null

  @JsonProperty('ChunkPartsLoadedCount', StringNumberConverter, true)
  ChunkPartsLoadedCount: number = null

  @JsonProperty('SnippetText', String, true) SnippetText: string = null
}

//@JsonObject('DocumentCodingModel')
export class DocumentCodingModel {
  //@JsonProperty('CustomFieldInfoId', StringNumberConverter, true)
  customFieldInfoId: number = null

  // @JsonProperty('FieldName', String, true)
  fieldName: string = null

  // @JsonProperty('Description', String, true)
  description: string = null

  // @JsonProperty('DisplayName', String, true)
  displayName: string = null

  // @JsonProperty('UIInputType', String, true)
  uiInputType: string = null

  // @JsonProperty('CreatedOn', String, true)
  createdOn: string = null

  // @JsonProperty('CreatedBy', StringNumberConverter, true)
  createdBy: number = null

  // @JsonProperty('AllowMultipleCodingValues', Boolean, true)
  allowMultipleCodingValues: boolean = null

  // @JsonProperty('DelimiterForCodingValues', String, true)
  delimiterForCodingValues: string = null

  // @JsonProperty('AllowPredefinedCodingValuesOnly', Boolean, true)
  allowPredefinedCodingValuesOnly: boolean = null

  // @JsonProperty('DetailDataCount', StringNumberConverter, true)
  detailDataCount: number = null

  // @JsonProperty('Scale', StringNumberConverter, true)
  scale: number = null

  // @JsonProperty('Length', StringNumberConverter, true)
  length: number = null

  // @JsonProperty('MultiValuedCodingOptions', Number, true)
  multiValueCodingOptions: number = null

  // @JsonProperty('FieldCodingValues', [String], true)
  fieldCodingValues: string[] = null

  // @JsonProperty('UpdatedFieldValue', String, true)
  updatedFieldValue: string = null

  // @JsonProperty('CurrentFieldValue', String, true)
  currentFieldValue: string = null

  // @JsonProperty('DateFormat', String, true)
  dateFormat: string = null
}

//@JsonObject('CodingValues')
export class CodingSummary {
  //@JsonProperty('FieldName', String, true)
  fieldName: string = null

  //@JsonProperty('FieldDisplayName', String, true)
  fieldDisplayName: string = null

  //@JsonProperty('OldValue', String, true)
  oldValue: string = null

  // @JsonProperty('NewValue', String, true)
  newValue: string = null
}

//@JsonObject('CodingState')
export class CodingState {
  // @JsonProperty('GlobalTempTable', String, true)
  globalTempTable: string = null

  // @JsonProperty('CodingParameterValue', String, true)
  codingParameterValue: string = null

  // @JsonProperty('SearchId', StringNumberConverter, true)
  searchId: number = null

  // @JsonProperty('UserId', StringNumberConverter, true)
  userId: number = null

  // @JsonProperty('CodingInfo', String, true)
  codingInfo: string = null

  // @JsonProperty('IsBulkCoding', Boolean, true)
  isBulkCoding: boolean = null
}

/**
 * Cosing Field Saved Model use for Coding Field copy in review
 */
export class CodingFieldSavedModel {
  fileId: number

  fileName: string

  codingFieldSavedChange: CodingSummary[]
}

//@JsonObject('CodingStatus')
export class CodingSaveModel {
  //@JsonProperty('fieldCodingModel', [DocumentCodingModel], true)
  fieldCodingModel: DocumentCodingModel[] = null

  //@JsonProperty('State', CodingState, true)
  state: CodingState = null
}

// social media message Model
@JsonObject('DocumentMessageModel')
export class DocumentMessageModel {
  @JsonProperty('FileId', StringNumberConverter, true) fileId: number = null

  @JsonProperty('UserId', StringNumberConverter, true) userId: number = null

  @JsonProperty('PreviousFileId', StringNumberConverter, true)
  previousFileId: number = null

  @JsonProperty('ProjectId', StringNumberConverter, true)
  projectId: number = null

  @JsonProperty('Type', StringNumberConverter, true) type: number = null

  @JsonProperty('IsExternalUser', Boolean, true) isExternalUser: boolean = null

  @JsonProperty('FileName', String, true) fileName: string = null
}

@JsonObject('AttachmentLinkObject')
export class AttachmentLinkObject {
  @JsonProperty('Buffer', String, true) buffer: string = null

  @JsonProperty('MimeType', String, true) mimeType: string = null

  @JsonProperty('FileName', String, true) fileName: string = null
}

@JsonObject('DocumentExcelResponse')
export class DocumentExcelResponse {
  @JsonProperty('FileId', String, true) FileId: string = null

  @JsonProperty('WorkBook', Any, true) WorkBook: any = null

  @JsonProperty('RedactedFile', Any, true) RedactedFile: any = null
}

export interface SimilarDocumentFetchModel {
  similarityScore?: number
  tempTables?: SimilarDocumentTempTables
  searchTempTable: string
  isSearchScope: boolean
}
export interface SimilarDocumentRequestModel extends SimilarDocumentFetchModel {
  fileId: number
  searchId: number
  mediaList: number[]
  folderList: number[]
}

export interface SimilarDocumentTempTables {
  similarHitTableName: string
  similarResultTableName: string
}

export interface SimilarDocumentResponseModel {
  totalHitCount: number
  tempTables?: SimilarDocumentTempTables
  searchTerms: string[]
}

export class SimilarDocumentModel {
  fileId: number = null

  metadata: DocumentMetadata[]
}
export enum SimilaritySearchScopeType {
  ALL_DOCUMENTS,
  SELECTED_MEDIA
}

export interface ReviewPanelDocumentModel {
  seqNo: number
}

export class DocumentOptions {
  SelectedFileIds: number[]

  UnSelectedFileIds: number[]

  IsBatchSelected: boolean

  SearchTempTable: string

  SessionId: string

  SearchId?: number
}

export class DeleteDocumentOptions extends DocumentOptions {
  DeleteMode: DeleteMode

  DeleteChildRecords: boolean

  DeleteMediaifAllChildAreDeleted: boolean
}

export enum DeleteMode {
  DELETE_ALL,
  DELETE_NATIVE,
  DELETE_IMAGE,
  DELETE_HTML_RTF
}

export enum SelectionMode {
  ALL,
  SELECTED
}

export class DeleteDocumentSummary {
  DeleteOption: DeleteMode

  Title: string

  Count: number
}

export enum SelectionEnum {
  All,
  Selected
}

/**
 * Default object for review query param.
 * Can be extended with more property as needed.
 */
export interface ReviewSetQueryParams {
  projectId: number
  reviewSetId: number
}

export interface UpdatedFileTags {
  fileId: number
  tags: string
}

export interface UpdatedFileDocNotes {
  fileId: number
  documentNotes: string
}

export interface UpdatedFileTagColors {
  fileId: number
  tagNameColors: string
}

export enum DocNavigation {
  First,
  Prev,
  Next,
  Last
}

export class MoveDocumentOptions extends DocumentOptions {
  parentFileid: number
}

export class MoveDocumentStatusModel {
  documentMovedCount: number

  totalDocumentCount: number

  isCompleted: boolean

  statusMessage: string
}

export interface DocumentViewerLogModel {
  userId?: number
  fileId?: number
  viewedDate: Date
  viewedFrom: string
  externalUserId?: number
  viewerType?: string
}
export interface GetDocumentViewerLogModel extends DocumentViewerLogModel {
  viewer: string
  fileName: string
}

export class DownloadTextModel {
  FulltextType: number

  ImagesetId: number
}

export interface SlipsheetSummaryModel {
  totalSlipSheetDocumentCount: number
  previousTIFFCount: number
}

export interface CreateSlipsheetModel {
  documentOptions: DocumentOptions
  slipsheetPayload: SlipsheetModel
}

export interface ValidationError {
  key: string
  message: string
}
