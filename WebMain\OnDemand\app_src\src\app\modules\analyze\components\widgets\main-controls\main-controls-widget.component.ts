import { TAG_CREATE_INJECTION_TOKEN } from '@admin-advance/components/tag/create/tag-create.component'
import {
  DocumentRestrictionModel,
  ICustodian,
  RestrictionMode
} from '@admin-advance/models'
import {
  DocumentRestrictionStateSelector,
  FetchUserDocumentRestrictionAction
} from '@admin-advance/store'
import { Overlay } from '@angular/cdk/overlay'
import { TemplatePortal } from '@angular/cdk/portal'
import { HttpErrorResponse } from '@angular/common/http'
import {
  AfterViewInit,
  Component,
  EventEmitter,
  HostBinding,
  HostListener,
  Inject,
  Injector,
  NgModuleFactory,
  OnDestroy,
  OnInit,
  TemplateRef,
  Type,
  ViewChild,
  ViewContainerRef
} from '@angular/core'
import { FormControl, FormGroup, Validators } from '@angular/forms'
import { MatDialog, MatDialogRef } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { ProjectInfoModel } from '@config/models'
import { ConfigService } from '@config/services/config.service'
import { getControlSetting, getThemeClient } from '@config/store/selectors'
import { select, Store as RxStore } from '@ngrx/store'
import { Select, Store } from '@ngxs/store'
import { SearchState } from '@review/store/reducers/search.reducer'
import { UserRights } from '@root/helpers/user-rights'
import { GetSearchDuplicateOption } from '@root/modules/application-nav/store/actions'
import { searchDuplicateOption } from '@root/modules/application-nav/store/selectors/application-nav.selectors'
import { SearchHelpModule } from '@root/modules/review/components/search-help/search-help.module'
import { animateHeight, fadeInUpDown, fadeInX, inOut } from '@shared/animation'
import {
  SaveTagForProductionRequestModel,
  SearchExpressionCount,
  SearchQueryModule
} from '@shared/models'
import { FolderActionType } from '@shared/models/folder.model'
import {
  FolderInjectorModel,
  SearchDupOption
} from '@shared/models/search.model'
import * as CONSTANT from '@shared/search-builder/child/search-duplicate-option/search-duplicate-option.const'
import {
  SetSearchDupOption,
  TriggerResetAdvancedSearchUiAction
} from '@shared/search-builder/sotre/query-builder.actions'
import { QueryBuilderSelector } from '@shared/search-builder/sotre/query-builder.selector'
import {
  saveTagsForProduction,
  setSearchQuery,
  unsetSearchQuery
} from '@shared/store/actions'
import * as fromSharedDocumentActions from '@shared/store/actions/document.actions'
import { getSearchQuery } from '@shared/store/selectors/document.selectors'
import { DebounceTimer, Uuidv4Generator } from '@shared/utils'
import {
  FetchDisplayCustodianMedia,
  FetchExportName,
  FetchProjectList,
  FetchUploadName,
  InitUploadHistoryQueryAction,
  UploadStateResetAction
} from '@stores/actions'
import { ExportModel, SearchQueryOption, UploadModel } from '@stores/models'
import {
  CaseSelectors,
  StartupStateSelector,
  UploadStateSelector
} from '@stores/selectors'
import DxPopup from 'devextreme/ui/popup'
import { findLast, isEqual } from 'lodash'
import * as moment from 'moment'
import { BsModalService } from 'ngx-bootstrap/modal'
import { ToastrService } from 'ngx-toastr'
import { combineLatest, fromEvent, Observable, of, Subject } from 'rxjs'
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs/operators'
import { SetCanFetchNewCase } from '../../../../launchpad/store/actions'
import { unIndexMedia } from '../../../../launchpad/store/selectors/case.selectors'
// this component should be placed on `shared` directory.
import { SearchHelpComponent } from '../../../../review/components/search-help/search-help.component'
import {
  ReviewSetStateSelector,
  SetIncludeFamilySearch,
  SetSearchResultIncludeFamilySearch
} from '../../../../review/xs-store'
import {
  ActionConfigModel,
  AnalyzeActionTypes,
  SaveSearchModel,
  SearchInputs,
  SearchParams,
  WidgetTypes
} from '../../../models'
import { WidgetCommonExtension } from '../../../models/widget-common.extension'
import { SaveSearch } from '../../../store/actions/analyze.actions'
import {
  AddOrUpdateLayoutAction,
  AnalyzeStateResetAction,
  AnalyzeStateSelectors,
  FetchCustodianMediaAction,
  FetchLayoutAction,
  FetchWidgetMultiSearchStatusResultAction,
  LoadDefaultLayout,
  LoadWidgetFromLayoutAction,
  SearchAction,
  SetSelectedLayout,
  SyncRenderedWidgetAction,
  UpdateBreadcrumbAction,
  UpdateDefaultLayout,
  UpdateSearchParams
} from '../../../store/ngxs'
import { TagDocumentsComponent } from '../../tag-documents/tag-documents.component'

/**
 * Local states type.
 */
type ControlStats = {
  /**
   * Toggle when search is in progress
   */
  isSearching?: boolean
  /**
   * Toggles when reset search in progress
   */
  isResetSearching?: boolean
  /**
   * Toggles between initial search or with user provided expresion.
   */
  isInitialSearch?: boolean

  isLinkLoading?: Map<string, boolean>
}

@Component({
  selector: 'app-main-controls',
  templateUrl: './main-controls-widget.component.html',
  styleUrls: ['./main-controls-widget.component.scss'],
  animations: [fadeInX, animateHeight, fadeInUpDown, inOut]
})
export class MainControlsWidgetComponent
  extends WidgetCommonExtension
  implements OnInit, AfterViewInit, OnDestroy
{
  /**
   * Subject to complete the subscriptions
   */
  private unsubscribed$ = new Subject<void>()

  @HostBinding('class') c = 'd-block position-relative'

  // confirms page refresh
  @HostListener('window:beforeunload')
  readonly beforeUnload = (): boolean => {
    const ctrl = this.inputFrom.get('expression')
    if (ctrl.dirty && ctrl.touched && !ctrl.pristine)
      return confirm('Changes will restore.')
  }

  @HostBinding('style') s = 'min-height: 1px'

  /**
   * Observes currently invoked action with boolean value.
   * @see AnalyzeActionTypes
   */
  activateActionType: Map<AnalyzeActionTypes, boolean> = new Map()

  /**
   * Whether the textarea expands to multiple rows.
   */
  areaExpanded: boolean

  /**
   * Total found document counts. Also applies to filtered count.
   * @see AnalyzeStateSelectors
   */
  @Select(AnalyzeStateSelectors.sliceOf('documentCount'))
  readonly docCount: Observable<number>

  /**
   * Form object to bind user inputs.
   */
  readonly inputFrom = new FormGroup({
    expression: new FormControl('', Validators.required),
    includeFamily: new FormControl(false)
  })

  /**
   * Access global setup, dynamic theming for UI etc.,
   * @see ConfigService
   */
  readonly config = ConfigService

  /**
   * Toggles various local states when performing tasks, such as show spinner, disable/enable controls etc.,
   * @see ControlStats
   */
  readonly localStats: ControlStats = {
    isLinkLoading: new Map<string, boolean>()
  }

  /**
   * Document count of search result
   */
  documentCount: number

  /** true when the control setting to enable second review set view */
  enableReview2: boolean

  /**
   * Checks Whether the search input has length by removing leading,trailing spaces  expression text.
   */
  get isEmptyInput(): boolean {
    return !(this.inputFrom.get('expression').value?.trim().length > 0)
  }

  /**
   * List f lazy component to load based on control clicks.
   * Items are mapped during call of event handler.
   * @see AnalyzeActionTypes
   */
  readonly lazyComponents: Map<
    AnalyzeActionTypes,
    {
      component: Promise<Type<unknown>>
      injector?: Injector
      factory?: NgModuleFactory<any>
    }
  > = new Map()

  /**
   * A template ref to create tag by loading TAG component lazily.
   */
  @ViewChild('lazyCrateTagTplRef')
  private readonly crateTagTplRef: TemplateRef<unknown>

  /**
   * A template ref to create tag by loading TAG component lazily.
   */
  @ViewChild('custodianMediaRef')
  private readonly custodianMediaTplRef: TemplateRef<unknown>

  @ViewChild('folderScopeRef')
  private readonly folderScopeTplRef: TemplateRef<unknown>

  /**
   * A template ref to load add/update component lazily.
   */
  @ViewChild('lazyAddUpdateWidget')
  private readonly addUpdateWidgetTplRef: TemplateRef<unknown>

  /**
   * A template ref to display a text info to select node from widgets to perform search.
   */
  @ViewChild('confirmSelection')
  private readonly confirmNodeSelectionTplRef: TemplateRef<unknown>

  /**
   * A template ref to display layout screen
   */
  @ViewChild('layoutScreen')
  private readonly layoutTplRef: TemplateRef<unknown>

  /**
   * State of breadcrumb items.
   * @see SearchInputs
   * @see AnalyzeStateSelectors
   */
  @Select(AnalyzeStateSelectors.sliceOf('breadcrumbItems'))
  readonly breadcrumbItems: Observable<SearchInputs[]>

  /**
   * Not recommended to use in View as it has `get` modifier.
   * Only use as lazy call.
   * @see AnalyzeStateSelectors
   * @see SearchInputs
   */
  private get preservedQueries(): Array<{ [key: string]: SearchInputs }> {
    return this.store.selectSnapshot(
      AnalyzeStateSelectors.sliceOf('selectedNodeQueries')
    )
  }

  /**
   * Active project info for this component.
   * The object is resolved before analyze page data.
   * @see AnalyzeStates
   * @see AnalyzeStatesExtension
   * @see ProjectInfoModel
   */

  projectInfo: ProjectInfoModel

  /**
   * Whether the r-vod is enabled.
   * @see ConfigService
   */
  get isRVod(): boolean {
    return !this.configService.isVodEnabled
  }

  // discovery report groups
  @ViewChild('discoveryReport')
  private readonly dcReportTpl: TemplateRef<any>

  discoveryReportType: 'Discovery' | 'Export' = 'Discovery'

  discoveryReportUploadNames: UploadModel[] = []

  discoveryReportExports: ExportModel[] = []

  discoveryReportUploadNameCtrl = new FormControl()

  discoveryReportExportCtrl = new FormControl()

  popup: DxPopup

  // media group
  //  selected media list
  mediaIds: number[] = []

  /**
   * Folder list for search scope for the user having document restricted by folder
   */
  folderList: number[] = []

  // check condition to display project scope (custodian/media) when the page is loading
  isPageLoadDisplayMedia: boolean

  /**
   * Gets a last item from the breadcrumb state.
   * @see AnalyzeStateSelectors
   * @see SearchInputs
   */
  private get lastQueryIfExists(): SearchInputs {
    return findLast(
      this.store.selectSnapshot(
        AnalyzeStateSelectors.sliceOf('breadcrumbItems')
      )
    )
  }

  /**
   * Gets list of breadcrumbs
   * @see SearchInputs
   */
  allBreadcrumb: SearchInputs[] = []

  /**
   * Gets search params
   * @see AnalyzeStateSelectors
   */
  private get searchParam(): SearchParams {
    return this.store.selectSnapshot(
      AnalyzeStateSelectors.sliceOf('searchParams')
    )
  }

  /**
   * List of  saved layout
   */
  layouts: any[] = []

  /**
   * Selected analyze layout
   */
  selectedLayout = 'Default'

  /**
   * Layout handler form.
   */
  readonly layoutForm: FormGroup = new FormGroup({
    option: new FormControl(true),
    name: new FormControl(),
    layout: new FormControl()
  })

  /**
   * Layout button conditional text.
   * @see AnalyzeActionTypes
   */
  get layoutButtonText(): string {
    const op = this.layoutForm.get('option').value
    return this.activeActionOf('LAYOUT_LOAD')
      ? 'Load'
      : this.activeActionOf('LAYOUT_SAVE') && op
      ? 'Save'
      : !op && this.activeActionOf('LAYOUT_SAVE')
      ? 'Update'
      : ''
  }

  /**
   * Get the list of layout for loading or updating
   */
  get layoutList() {
    return this.activeActionOf('LAYOUT_LOAD')
      ? this.layouts
      : this.layouts.filter((l) => l.name !== 'Default')
  }

  /**
   * User right to perform search
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_SEARCH))
  allowToPerformSearch$: Observable<boolean>

  /**
   * User right to tag the documents
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TAGGING_UNTAGGING)
  )
  allowToTag$: Observable<boolean>

  /**
   * User right to produce the documents
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_EXPORT))
  allowToProduce$: Observable<boolean>

  /**
   * User right to create new tag
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ADD_NEW_TAG))
  allowToCreateTag$: Observable<boolean>

  /**
   * Handles whether the initial search from other source or from self.
   * When other sends a query to this page will toggle value to truthy.
   * @see handleSyncSearchFromOthers
   * @see initInitialActions
   */
  private isSearchQueryFromOtherSource: boolean

  /**
   * Toggle builder UI box when `advance query builder` button clicked.
   */
  showAdvanceFilterUi: boolean

  /**
   * handles text input event to perform UI toggling task or  element manipulation.
   */
  readonly forQueryBuilder = new EventEmitter()

  /**
   * Selected project id. Value for this variable is set from query parameter.
   * This variable is used for loading the custodian/media popup.
   * We had to use this variable because, 'projectInfo' object is set in store when SearchAction is dispatched
   * But the custodian/media popup is loaded before 'projectInfo' is set.
   */
  private projectId: number

  /**
   * RVOD service settingId passed from the link in the ready for review notification
   */
  private settingId = -1

  /**
   * Flag to identify if VODR is enabled
   */
  private isVodrEnabled = false

  errorRes: HttpErrorResponse

  /**
   * Whether user should be able to write on search input when query builder UI is shown
   */
  toggleInputWritable: boolean

  /**
   * Document restriction mode refference
   */
  restrictionMode = RestrictionMode

  /**
   * Document restriction mode assigned to the user group
   */
  userDocRestrictonMode = RestrictionMode.None

  @ViewChild('searchHelpTemplate')
  private searchHelpTemplate: TemplateRef<any>

  searchHelpComponent: Promise<Type<SearchHelpComponent>>

  searchHelpModule: NgModuleFactory<SearchHelpModule>

  searchHelpInjector: Injector

  /**
   * Restricted folder ids to user group
   */
  restrictedFolderIds: number[] = []

  readonly searchUiRight = UserRights.ALLOW_SEARCH

  /**
   * For a certain period, we will redirect between the old UI and new UI.
   * We store the base path for the new UI to redirect when the new UI feature is enabled.
   * Both apps are hosted on the same origin in production,
   * which means that if one app obtains a JWT token and stores it in storage, the other app can easily access it.
   * However, the key used must be the same for both apps.
   * @private
   */
  private newUiBaseurl = ''

  openDuplicateOption = false

  searchDuplicateOption: SearchDupOption

  public client: string

  constructor(
    private configService: ConfigService,
    public store: Store,
    private rxStoreSS: RxStore<SearchState>,
    private bsm: BsModalService,
    private dialog: MatDialog,
    private injector: Injector,
    private overlay: Overlay,
    private vcr: ViewContainerRef,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    @Inject('WIDGET_CONTENT')
    public widget: ActionConfigModel,
    private router: Router,
    // once the other module like REVIEW, PROD migrates to ngxs, should refactor.
    private rxStore: RxStore
  ) {
    super(store, widget)
    this.isVodrEnabled = !this.configService.isVodEnabled

    //Set the projectId from query parameter
    this.projectId = this.route.snapshot.queryParams['projectId']

    this.settingId = this.route.snapshot.queryParams['settingId']

    if (this.isVodrEnabled && this.settingId > 0) this.initCustodianMediaQuery()
  }

  ngOnInit(): void {
    this.#selectMediaStaus()
    this.GetSearchDuplicationOption()
    this.#selectVodVersionAndSetNewUiRedirectionPath()
    /**
     * always should be the top of the hooks.
     * @see initInitialActions
     */
    this.handleSyncSearchFromOthers()
  }

  /**
   * Sets query string to the expression input
   * @param q incoming query input
   */
  readonly setQuery = (q: string): void => {
    // Only update the search term if the UI is shown
    // This prevents the reset action from overwriting the search term
    // that was just used for searching
    if (this.showAdvanceFilterUi) {
      this.inputFrom.get('expression').setValue(q)
    }
  }

  ngAfterViewInit(): void {
    /**
     * should call after  `handleSyncSearchFromOthers` calls.
     * @see handleSyncSearchFromOthers
     */

    this.initInitialActions()
    this.handleSearchResponseChanged()
    this.initSlices()
    this.handleBuilderEvent()
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()

    // state cleanup, ensure mem leak
    // be careful when cleaning up the state. May incorrect data produce.
    this.store.dispatch(
      new AnalyzeStateResetAction([
        'breadcrumbItems',
        'removedWidget',
        'selectedNodeQueries',
        'searchTermStatusData',
        'documentCount',
        'searchTermStatusIds',
        'widgetDynamicFields'
      ])
    )
  }

  /**
   * Inits state events for selecting slices and performing tasks based on emitted values.
   */
  private readonly initSlices = (): void => {
    this.rxStore
      .pipe(
        select(getControlSetting('ENABLE_REVIEW_2')),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isEnable: boolean) => {
        this.enableReview2 = isEnable
      })
    this.store
      .select(AnalyzeStateSelectors.sliceOf('projectInfo'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (p) => {
          this.projectInfo = p
        }
      })

    this.store
      .select(AnalyzeStateSelectors.sliceOf('documentCount'))
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe({ next: (p) => (this.documentCount = p) })

    this.store
      .select(AnalyzeStateSelectors.sliceOf('breadcrumbItems'))
      .pipe(
        filter((b) => b?.length > 0),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (b) => {
          this.allBreadcrumb = b.map((c, index) => ({
            ...c,
            uuid: new Uuidv4Generator().uuid,
            currentIndex: index
          }))

          // if we've more than 1 search, we need to enable the reset button
          this.localStats.isInitialSearch = !this.allBreadcrumb.some(
            (s) => !s.isInitialSearch
          )
        }
      })

    // check user setting display custodian media when load page or not
    this.store
      .dispatch(new FetchDisplayCustodianMedia())
      .pipe(
        switchMap(() =>
          this.store.select(CaseSelectors.displayCustodianMedias)
        ),
        // don't just rush to display. wait N sec
        debounceTime(2500),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: async (showMedia) => {
          try {
            const documentRestriction: DocumentRestrictionModel =
              await this.getDocumentRestrictionInfo()
            if (documentRestriction?.isDocRestricted) {
              this.userDocRestrictonMode = documentRestriction.restrictionMode
              if (
                documentRestriction.restrictionMode === RestrictionMode.Folder
              )
                this.restrictedFolderIds =
                  documentRestriction.restrictedFolderIds
            }
          } catch (error) {
            this.toastr.error(`Error showing search scope setting`)
            showMedia = false
          }

          this.isPageLoadDisplayMedia = showMedia
          if (showMedia) {
            if (this.userDocRestrictonMode === RestrictionMode.Folder)
              this.setFolderScope()
            else this.handleCustodianMedia('CUSTODIAN_MEDIA')
          }
        }
      })

    this.store
      .select(AnalyzeStateSelectors.sliceOf('selectedLayout'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((layoutName) => {
        this.selectedLayout = layoutName
      })

    this.store
      .select(AnalyzeStateSelectors.sliceOf('responseException'))
      .pipe(
        filter((e) => !!e?.error),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (ex) => {
          this.localStats.isSearching = false
          this.activateActionType.clear()
          this.localStats.isResetSearching = false
          this.errorRes = { ...ex }
          // once we got the message, we need to clean it up.
          this.store.dispatch(
            new AnalyzeStateResetAction(['responseException'])
          )
        }
      })

    this.rxStoreSS
      .pipe(select(getThemeClient), takeUntil(this.toDestroy$))
      .subscribe((client: string) => {
        this.client = client
      })

    this.mediaIds = this.store.selectSnapshot(
      StartupStateSelector.SliceOf('selectedMediaScope')
    )

    this.folderList = this.store.selectSnapshot(
      StartupStateSelector.SliceOf('selectedFolderScope')
    )
  }

  private readonly GetSearchDuplicationOption = () => {
    // First, get the server value from the application-nav store
    this.rxStoreSS
      .pipe(
        select(searchDuplicateOption),
        filter((res) => res !== null && res !== undefined),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        const dupOption = CONSTANT.SearchOption.find((item) => item.id === res)
        // Update the NGXS store with the server value
        this.store.dispatch(new SetSearchDupOption(dupOption?.id))
      })

    // Then, subscribe to the NGXS store to get updates (including the one we just dispatched)
    this.store
      .select(QueryBuilderSelector.sliceOf('searchDuplicateOption'))
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        this.searchDuplicateOption = res
      })
  }

  #selectMediaStaus(): void {
    this.rxStore
      .pipe(
        select(unIndexMedia),
        filter((result) => Boolean(result)),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((result) => {
        const media = result.data
        if (media?.[0]) this.popup.show()
        else this.rxStore.dispatch(new SetCanFetchNewCase(true))
      })
  }

  popupInitialized(ev) {
    this.popup = ev.component
  }

  closePopup(): void {
    this.popup.hide()
    this.rxStore.dispatch(new SetCanFetchNewCase(false))
  }

  /**
   * Fetch the custodian/media associated to RVOD service setting Id and
   * build the custodian/media query to load the data in the analyze page
   */
  initCustodianMediaQuery(): void {
    this.store
      .dispatch(new FetchCustodianMediaAction(this.projectId, this.settingId))
      .pipe(
        switchMap(() =>
          this.store.select(AnalyzeStateSelectors.sliceOf('custodianMedia'))
        ),
        distinctUntilChanged(),
        debounceTime(500),
        takeUntil(this.toDestroy$)
      )
      .subscribe((custMedia: ICustodian[]) => {
        const custMedQuery = custMedia
          .map((cust) =>
            cust.custodainMedias
              .map((med) => {
                return `(CUSTODIAN_NAME="${cust.custodianName}" AND MEDIA_NAME="${med.mediaName}")`
              })
              .join(' OR ')
          )
          .join(' OR ')

        const finalArgs: SearchQueryOption = {
          query: custMedQuery,
          includePc: null
        }

        this.store.dispatch(new InitUploadHistoryQueryAction(finalArgs))
      })
  }

  /**
   * Listens shared query from other modules like review, upload history etc.,
   * @see UploadState
   */
  private readonly handleSyncSearchFromOthers = (): void => {
    const source1 = this.rxStore.pipe(select(getSearchQuery))

    /**
     * Prepares query data of search history if found.
     * When an user selects media from upload history section and sends to search,
     * we then receive that query and use to search here.
     * @see UploadStateSelector
     */
    const source2 = this.store.select(
      UploadStateSelector.SliceOf('initHistoryQuery')
    )
    of(null)
      .pipe(
        switchMap(() => combineLatest([source1, source2])),
        //.pipe(
        // we have to source to listen, one from document and one from upload history
        map(([doc, his]) => {
          // either source query from upload history or from review page.
          const query =
            !!his && !Array.isArray(his.query)
              ? his.query
              : !!doc && doc.sourceModule !== SearchQueryModule.Analyze
              ? doc.searchInputs?.length > 0
                ? findLast(doc.searchInputs)
                : doc.searchQuery
              : null
          // always false to analyze page as client has reported for doc count inconsistency
          this.inputFrom.get('includeFamily').reset(false)
          return {
            // either an object or a query string
            query,
            breadcrumb: doc?.searchInputs,
            searchParameters: doc?.searchParameters,
            //mediaList: his?.mediaList
            mediaList:
              !!doc && doc?.mediaList?.length ? doc.mediaList : his?.mediaList,
            searchDuplicateOption: doc?.searchDuplicateOption,
            includeFamily: doc?.includeFamilySearch
          }
        }),
        filter((q) => !!q?.query),
        // use this when cancellation is called.
        takeUntil(this.toDestroy$),
        // use this to clean up once the source is arrived.
        take(1)
      )
      .subscribe((q) => {
        /**
         * collection of breadcrumbs are sent from other pages,
         * we need to update the breadcrumb at first and then dispatch a search with
         * last item. see on `map` operator. Only applicable if it was sent from `Review` page
         * @see AnalyzeStates
         * @see AnalyzeStatesExtension
         */
        if (q?.breadcrumb) {
          this.store.dispatch(new UpdateBreadcrumbAction(q.breadcrumb))
        }
        //Need to update the search parameter like temptablemodel in the store to reuse the same temp tables created from the review.(when send to analyze is performed from review.)
        //We use the baseGUID from temptable model during search. If baseGUID from review is not used, breadcrumb navigation will provide the incorrect result.

        //Sudhir,July 16 2021, removed the code to updateSearchParams because, we are performing the search again. so no need to set the search parameters
        //if (q?.searchParameters)
        //this.store.dispatch(new UpdateSearchParams(q.searchParameters))

        // it mean initial action from here.
        this.isSearchQueryFromOtherSource = true
        // prevent Angular expression change error
        this.localStats.isInitialSearch = !q
        const hasBreadcrumb = q?.breadcrumb?.some((b) => b)
        const hasMid = q.mediaList?.some((s) => s)
        /**
         * either of manual object or of last item from breadcrumb collection.
         * the query append is happens on the state because we've set the breadcrumb at first so
         * it'll populate the final query by joining `expression` data in the state.
         * @see AnalyzeStates
         * @see AnalyzeStatesExtension
         */
        const payload =
          typeof q?.query === 'string'
            ? {
                isInitialSearch: !q.query,
                isResetBaseGuid: hasMid,
                expression: q.query,
                isFromOtherSource: hasBreadcrumb,
                mediaList: hasMid
                  ? q.mediaList
                  : this.mediaIds?.map((id) => `${id}`),
                searchDuplicateOption: q.searchDuplicateOption,
                includeFamily: q.includeFamily
              }
            : {
                ...q.query,
                // this prevents to add another item on incoming automated breadcrumb.
                // we don't have to add another breadcrumb when the search was triggered from other source who has
                // list of breadcrumbs already.
                mediaList: hasMid
                  ? q.mediaList
                  : this.mediaIds?.map((id) => `${id}`),
                isFromOtherSource: hasBreadcrumb,
                isFilterSearch: q.query?.isFilterSearch,
                searchDuplicateOption: this.searchDuplicateOption
              }
        this.store.dispatch(new SearchAction(payload))

        /**
         * If we have value sent from upload history page,
         * we need to clear the state once it done.
         * @see UploadStateSelector
         * once we use this, set it to null
         */
        this.store.dispatch(new UploadStateResetAction('initHistoryQuery'))

        /**
         * If we have value sent from document /review,
         * after use of the value, should clean up the store.
         * @see DocumentsEffects
         */
        this.rxStore.dispatch(unsetSearchQuery())
      })
  }

  /**
   * Initializes default actions when this component loads for the first time
   * or after `ngAfterViewInit` created.
   * @see handleSyncSearchFromOthers
   */
  private async initInitialActions() {
    this.store.dispatch(new UpdateSearchParams(null))
    // we do not need to maintain the include-family from here so it should be reset to false as
    // it is being used on review page with loadfile but not here
    this.store.dispatch(new SetIncludeFamilySearch(false))

    // avoid angular change expression issue
    setTimeout(() => (this.localStats.isInitialSearch = true), 100)

    // if there other search is in progress, do not process another initial search.
    if (this.isSearchQueryFromOtherSource) return

    // Wait for the search duplication option to be loaded from the server
    // before performing the initial search
    this.getSearchDuplicateData()
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        // Now that we have the server value, perform the initial search
        this.store.dispatch(
          new SearchAction({
            isInitialSearch: true,
            mediaList: this.mediaIds?.map((id) => `${id}`),
            searchDuplicateOption: this.searchDuplicateOption
          })
        )
      })

    //patch action to fetch and load default layout for the current user
    this.store.dispatch(new LoadDefaultLayout())

    const searchResultIncludeFamilySearch = this.store.selectSnapshot(
      ReviewSetStateSelector.SliceOf('includeFamilySearch')
    )
    this.store.dispatch(
      new SetSearchResultIncludeFamilySearch(searchResultIncludeFamilySearch)
    )
  }

  getSearchDuplicateData(): Observable<any> {
    this.rxStoreSS.dispatch(new GetSearchDuplicateOption(this.projectId))
    return this.rxStoreSS.pipe(
      select(searchDuplicateOption),
      filter((res) => res !== null && res !== undefined),
      take(1),
      tap((res) => {
        const dupOption = CONSTANT.SearchOption.find((item) => item.id == res)
        this.store.dispatch(new SetSearchDupOption(dupOption?.id))
      }),
      takeUntil(this.toDestroy$)
    )
  }

  /**
   * Fetch document restriction details for the user
   */
  private getDocumentRestrictionInfo(): Promise<DocumentRestrictionModel> {
    return new Promise((resolve, reject) => {
      this.store
        .dispatch(new FetchUserDocumentRestrictionAction(this.projectId))
        .pipe(
          switchMap(() => {
            return this.store.select(
              DocumentRestrictionStateSelector.SliceOf(
                'documentRestrictionInfo'
              )
            )
          }),
          debounceTime(200),
          catchError(async (error) => reject(error)),
          takeUntil(this.toDestroy$)
        )
        .subscribe((res: DocumentRestrictionModel) => {
          resolve(res)
        })
    })
  }

  /**
   * Restore the initial search.
   * @param type Activated action type.
   */
  private readonly initInputSearch = (type: AnalyzeActionTypes) => {
    this.activateActionType.clear()
    this.activateActionType.set(type, true)

    // cleanup the error state if available
    this.errorRes = undefined

    // if there a value in the input and the UI state is still in show state,
    // it needs to be hide and do the searching.
    this.showAdvanceFilterUi = false

    const fd: SearchInputs = this.inputFrom.value

    if (this.isEmptyInput || (this.isEmptyInput && this.showAdvanceFilterUi)) {
      // when a user performs an empty search, the default `fileid>0` should be performed.
      this.localStats.isResetSearching = false
      this.localStats.isInitialSearch = false
      this.localStats.isSearching = true
      this.handleResetSearch()
      return
    }

    const mIds = this.mediaIds?.map((id) => `${id}`)

    const breadCrumbs: SearchInputs[] = this.store.selectSnapshot(
      AnalyzeStateSelectors.sliceOf('breadcrumbItems')
    )

    //check if the current expression is multiple search term expression
    const isCurrentSearchMultiTermSearch: boolean =
      this.isMultipleTermExpression(fd.expression)
    //check if the previous breadcrumbs has the multiple search term expression
    const doesMultiSearchTermAlreadyExists: boolean =
      breadCrumbs.filter((x) => this.isMultipleTermExpression(x.expression))
        ?.length > 0

    //if current expression is multiple search term expr and previous searches also have the multiple search term
    //then need to reset the breadcrumb and need to behave the current search as initial one.
    if (isCurrentSearchMultiTermSearch && doesMultiSearchTermAlreadyExists) {
      this.store.dispatch(new AnalyzeStateResetAction('breadcrumbItems'))
      this.store.dispatch(
        new FetchWidgetMultiSearchStatusResultAction(this.mediaIds)
      )
    }

    this.store.dispatch(
      new SearchAction({
        ...fd,
        isInitialSearch: false,
        isForwardFilter: type === 'SEARCH',
        /**Set 'isResetBaseGuid' to true this will set the baseGUID to empty.
         *  When baseGUID is empty,  filter operation will be skipped in the backend.*/
        isResetBaseGuid:
          isCurrentSearchMultiTermSearch && doesMultiSearchTermAlreadyExists,
        /**
         * if we have selected media IDs, pass theme here. `undefined`
         * values are handled in store. Check `useRequestParams` in `AnalyzeStatesExtension`
         * @see AnalyzeStatesExtension
         */
        mediaList: mIds.some((i) => i) ? mIds : undefined,
        folderList: this.folderList,
        isFilterSearch: true,
        searchDuplicateOption: this.searchDuplicateOption
      })
    )
    // toggle local states.
    this.localStats.isResetSearching = false
    this.localStats.isInitialSearch = false
    this.localStats.isSearching = true
    this.toggleMultiStatusWidget(true)
  }

  /**
   * returns boolean if the given expression is multiple search term expression.
   * @param expression string value to regex against.
   */
  private isMultipleTermExpression = (expression: string) =>
    expression?.indexOf('\n') > 0

  /**
   * Gets currently activated action value as whether the action is active.
   * @param type action type.
   * @returns boolean
   */
  readonly activeActionOf = (type: AnalyzeActionTypes): boolean =>
    this.activateActionType.get(type)

  /**
   * Calls when click  happens in the UI with an argument of type `AnalyzeActionTypes`
   * @param type An argument input of type `AnalyzeActionTypes`
   * @param input When an input item supplied from the UI event, we use that as a search param.
   */
  readonly actionClicked = (
    type: AnalyzeActionTypes,
    input?: SearchInputs
  ): void => {
    // clear existing  local state
    this.errorRes = null
    this.#notifyAdvancedSearchUiReset()

    switch (type) {
      case 'SEARCH_RESET':
        this.handleResetSearch()
        break
      case 'SEARCH_BREADCRUMB_LINK':
        this.localStats.isLinkLoading.set(input.uuid, true)
        // when a link in breadcrumb was clicked, we then send payload with `isBackwardFilter` to true.
        this.store.dispatch(
          new SearchAction({
            ...input,
            isForwardFilter: undefined,
            isBreadcrumbClicked: true,
            searchDuplicateOption: this.searchDuplicateOption
          })
        )
        // Remove the multi search term widget. This will be generated later if search term includes multiple terms.
        this.toggleMultiStatusWidget(true)

        break
      case 'SEARCH':
        this.initInputSearch(type)
        break
      case 'SEARCH_SHOW_GUIDANCE':
        this.handleGuidanceDisplay()
        break
      case 'BULK_TAG':
      case 'BULK_UNTAG':
        this.handleBulkTagsUnTags(type === 'BULK_TAG')
        break
      case 'CREATE_TAG':
        this.handleCreateTags('CREATE_TAG')
        break
      case 'WIDGET_ADD':
        this.handleAddWidget('WIDGET_ADD')
        break
      case 'INCLUSIVE_FILTER':
      case 'EXCLUSIVE_FILTER':
        if (this.isItemSelected()) {
          const isNot = type === 'EXCLUSIVE_FILTER' ? ' NOT ' : ''
          const query = this.preservedQueries.reduce((accum, el, index) => {
            return (accum +=
              (index > 0 ? ' AND ' : '') + el[Object.keys(el)[0]].expression)
          }, '')

          this.activateActionType.set(type, true)
          this.localStats.isSearching = true
          this.store.dispatch(
            new SearchAction({
              displayText: this.preservedQueries
                .map((el) => {
                  const key = Object.keys(el)[0]
                  return `${key} is: ${isNot}${el[key].displayText}`
                })
                .join(' '),
              expression: `${isNot}${query}`,
              isForwardFilter: true,
              isFilterSearch: true,
              searchDuplicateOption: this.searchDuplicateOption
            })
          )
        }
        break

      case 'DISCOVERY_REPORT':
      case 'DISCOVERY_REPORT_EXPORT':
        this.handleDiscoveryReport(
          type === 'DISCOVERY_REPORT' ? 'Discovery' : 'Export'
        )
        break
      case 'CUSTODIAN_MEDIA':
        this.handleCustodianMedia(type)
        break
      case 'FOLDER_SCOPE':
        this.setFolderScope()
        break
      case 'LAYOUT_LOAD':
      case 'LAYOUT_SAVE':
        this.handleLayoutChanges(type)
        break
      case 'SEND_TO_REVIEW':
        this.sendToReview()
        break
      case 'SEND_TO_PRODUCTION':
        this.sendToProduction()
        break
    }
  }

  /**
   * Displays search expression guidance.
   */
  private readonly handleGuidanceDisplay = () => {
    const modalRef = this.dialog.open(this.searchHelpTemplate)
    this.searchHelpInjector = Injector.create({
      providers: [
        {
          provide: 'searchHelpData',
          useValue: {
            modalRef: modalRef
          }
        }
      ],
      parent: this.injector
    })

    this.searchHelpComponent = import(
      '../../../../review/components/search-help/search-help.component'
    ).then(({ SearchHelpComponent }) => SearchHelpComponent)
  }

  /**
   * Handles state changed after the search happens.
   */
  private readonly handleSearchResponseChanged = () => {
    this.store
      .select(AnalyzeStateSelectors.sliceOf('searchParams'))
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe({
        next: (s) => {
          // must call before restoring the input
          this.initMultiSearchTermStatusWidget()

          // default states when search happens
          this.localStats.isSearching = false
          this.localStats.isResetSearching = false
          this.localStats.isLinkLoading.clear()

          // Don't reset the input form after a search is performed
          // This preserves the search syntax in the input
          // Only reset other form controls if needed
          // this.inputFrom.reset()

          this.activateActionType.clear()
        }
      })
  }

  #selectVodVersionAndSetNewUiRedirectionPath(): void {
    this.rxStore
      .pipe(
        select(getControlSetting('VOD_VERSION')),
        takeUntil(this.toDestroy$)
      )
      .subscribe((VOD_VERSION: number) => {
        /**
         * This method of redirecting users from the old UI to the new one is only temporary.
         * The redirect will be removed once the updated UI is fully functional.
         */
        const isNewUiEnabled = Number(VOD_VERSION) === 3

        if (isNewUiEnabled) {
          this.newUiBaseurl = `/review-next`
        } else {
          this.newUiBaseurl = ''
        }
      })
  }

  /**
   * Temporary workaround fix sing ngrx lib.
   * The analyze page uses NGXS to handle states while the review page uses NGRX.
   * Once the state management is updated in REVIEW page too, consider this to refactor.
   */
  private readonly sendToReview = () => {
    const q = this.allBreadcrumb
      .filter((s) => !s.isInitialSearch)
      .reduce((accum, el, xIndex) => {
        //If breadcrumb queries already has multiple search term, then break those search terms and append the current expression with And operator
        if (this.isMultipleTermExpression(accum)) {
          const multiTermQueries: string[] = accum.split('\n')
          let finalQuery = ''
          multiTermQueries.forEach((query) => {
            finalQuery +=
              '((' + query + ')' + ' AND ' + '(' + el.expression + '))' + '\n'
          })
          return (accum = finalQuery.trim())
        }
        //If current search expression is multiple search term expression, then break those search terms and append the breadcrumb query expressions with And operator
        else if (this.isMultipleTermExpression(el.expression)) {
          const multiTermQueries: string[] = el.expression.split('\n')
          let finalQuery = ''
          multiTermQueries.forEach((query) => {
            if (accum.length > 0)
              finalQuery +=
                '((' + accum + ')' + ' AND ' + '(' + query + '))' + '\n'
            else finalQuery += query + '\n'
          })
          return (accum = finalQuery.trim())
        } else {
          return (accum += `${xIndex > 0 ? ' AND ' : ''}(${el.expression})`)
        }
      }, '')

    this.rxStore.dispatch(
      setSearchQuery({
        payload: {
          searchQueryModel: {
            searchQuery: !q ? 'FileId>0' : q,
            sourceModule: SearchQueryModule.Analyze,
            searchInputs: this.allBreadcrumb,
            searchParameters: {
              ...this.searchParam,
              searchResultIntialParameters: {
                ...this.searchParam.searchResultIntialParameters,
                totalHitCount: this.documentCount
              }
            },
            // Include the current searchDuplicateOption value to ensure user selections are preserved
            searchDuplicateOption: this.searchDuplicateOption
          }
        }
      })
    )

    this.router.navigate(
      [
        `${
          this.enableReview2
            ? '/review2'
            : this.newUiBaseurl
            ? '/review-next'
            : '/review'
        }`
      ],
      {
        queryParams: { projectId: this.projectInfo.ProjectId }
      }
    )
  }

  /**
   * Temporary workaround fix sing ngrx lib.
   * The analyze page uses NGXS to handle states while the review page uses NGRX.
   * Once the state management is updated in PRODUCTION page too, consider this to refactor.
   */
  private readonly sendToProduction = () => {
    if (
      !this.configService.isVodEnabled &&
      this.projectInfo.IsFilteringServiceCase
    ) {
      let searchExpressionList: SearchExpressionCount[] = []
      searchExpressionList = this.allBreadcrumb
        .filter((x) => !x.isInitialSearch)
        .map((b) => ({
          searchExpression: b.expression,
          totalHitCount: b.documentCounts
        }))

      this.store.dispatch(
        fromSharedDocumentActions.setSearchExpressionListTemp({
          payload: { searchExpressionList }
        })
      )

      // This is the Ricoh case
      const productionTagModel = new SaveTagForProductionRequestModel()
      productionTagModel.moduleName = 'ANALYSE'
      productionTagModel.productionTagName =
        'Production_' + moment().format('YYYYMMDD_HHmmss')
      productionTagModel.projectId = +this.projectId
      productionTagModel.userId = +localStorage.getItem('UserId')
      productionTagModel.searchTempTableName =
        this.searchParam.tempTables.searchResultTempTable
      this.rxStore.dispatch(
        saveTagsForProduction({
          payload: { saveTagRequestModel: productionTagModel }
        })
      )
    } else {
      this.store.dispatch(
        fromSharedDocumentActions.setSearchQuery({
          payload: {
            searchQueryModel: {
              searchQuery: this.lastQueryIfExists.expression,
              sourceModule: SearchQueryModule.Analyze
            }
          }
        })
      )

      const searchModel = new SaveSearchModel()
      searchModel.searchGuid = this.searchParam.tempTables.searchGuid ?? '0'
      searchModel.searchName =
        'ProductionSave_' + moment().format('YYYYMMDD_HHmmss')
      searchModel.userType = localStorage.getItem('IsExternalUserEnabled')
        ? 'INTERNAL'
        : 'EXTERNAL'

      this.rxStore.dispatch(
        SaveSearch({ projectId: this.projectId, saveSearchModel: searchModel })
      )
    }
  }

  /**
   * Handles layout tasks.
   * @param type invoked action type to perform task.
   */
  private readonly handleLayoutChanges = (type: AnalyzeActionTypes) => {
    // should always be default.
    this.layoutForm.reset({ option: true })
    this.activateActionType.set(type, true)
    const ref = this.dialog.open(this.layoutTplRef, {
      width: '400px',
      // height: '200px',
      autoFocus: false,
      closeOnNavigation: true
    })

    ref
      .afterOpened()
      .pipe(
        tap(() => this.store.dispatch(new FetchLayoutAction())),
        switchMap(() =>
          this.store.select(AnalyzeStateSelectors.sliceOf('layouts'))
        ),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (l) => {
          this.layouts = l
          this.layoutForm.patchValue({
            layout:
              this.activeActionOf('LAYOUT_SAVE') &&
              this.selectedLayout === 'Default'
                ? null
                : this.selectedLayout
          })
        }
      })

    ref
      .afterClosed()
      .pipe(
        // only process the task if the user clicks on non cancel button
        filter((isChanges) => {
          // only if the user clicks on the non cancel button.
          if (!isChanges) this.activateActionType.delete(type)
          return isChanges
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: () => {
          const val = this.layoutForm.value as {
            option: boolean
            layout: string
            name: string
          }

          const widgets = this.layouts
            .filter((l) => l.name === val.layout)
            .map((w) => w.widgets)[0]

          //const payload = val.layout && !val.option ? val.layout.name : val.name
          const payload = val.layout && !val.option ? val.layout : val.name
          if (this.activeActionOf('LAYOUT_LOAD')) {
            this.store.dispatch([
              new LoadWidgetFromLayoutAction(widgets),
              new SetSelectedLayout(val.layout),
              new UpdateDefaultLayout(val.layout)
            ])
            this.toastr.success(`Layout ${val.layout} is loading..`)
          } else if (this.activeActionOf('LAYOUT_SAVE') && val.option) {
            if (!val?.name) {
              this.toastr.error('Layout name must be provided.')
            } else {
              this.store.dispatch(new AddOrUpdateLayoutAction(payload))
              this.toastr.success(`Layout ${val.name} has been saved`)
            }
          } else if (!val.option && this.activeActionOf('LAYOUT_SAVE')) {
            this.store.dispatch(new AddOrUpdateLayoutAction(payload, true))
            this.toastr.success(`Layout ${val.layout} has been updated`)
          }
          this.activateActionType.delete(type)
        }
      })
  }

  /**
   * Handles tag/untag click event. Loads a separated component iin a modal.
   * @param isTag Whether the request t create `tag`
   */
  private readonly handleBulkTagsUnTags = (isTag: boolean): void => {
    const initialState = {
      tagAction: isTag ? 'Tag' : 'Untag'
    }
    this.bsm.show(
      TagDocumentsComponent,
      Object.assign(
        { initialState },
        { class: 'modal-mid-size', ignoreBackdropClick: true, keyboard: false }
      )
    )
  }

  /**
   * Loads tag component lazily on user demand to create tags.
   * @param name A type of action  (act as identification token)
   */
  private readonly handleCreateTags = (name: AnalyzeActionTypes): void => {
    // dialog config
    const ref = this.dialog.open(this.crateTagTplRef, {
      width: '800px',
      minHeight: '45vh',
      maxHeight: '95vh',
      autoFocus: false,
      closeOnNavigation: true,
      disableClose: true
    })

    // load and map lazy component with identity
    this.lazyComponents.set(name, {
      component: import(
        '@admin-advance/components/tag/create/tag-create.component'
      ).then(({ TagCreateComponent }) => TagCreateComponent),
      // dialog ref has been passed to the component via injector.
      injector: Injector.create({
        providers: [
          {
            provide: TAG_CREATE_INJECTION_TOKEN,
            useValue: {
              // this will set when user clicks
              dialogRef: ref
            }
          }
        ],
        parent: this.injector
      })
    })

    ref
      .afterOpened()
      .pipe(debounceTime(500), takeUntil(this.toDestroy$))
      .subscribe({
        next: () => {
          // After loading this component, the  project list in used thus,
          // send request to load them.
          this.store.dispatch([new FetchProjectList()])
        }
      })
  }

  /**
   * Loads tag component lazily on user demand to create tags.
   * @param name A type of action  (act as identification token)
   */
  private readonly handleCustodianMedia = async (name: AnalyzeActionTypes) => {
    await import('@shared/components/custodian-media/custodian-media.module')
    // dialog config
    const ref = this.dialog.open(this.custodianMediaTplRef, {
      width: '1000px',
      minHeight: '55vh',
      maxHeight: '95vh',
      autoFocus: false,
      closeOnNavigation: true,
      disableClose: true
    })
    // load and map lazy component with identity
    this.lazyComponents.set(name, {
      component: import(
        '@shared/components/custodian-media/custodian-media.component'
      ).then(({ CustodianMediaComponent }) => CustodianMediaComponent),
      // dialog ref has been passed to the component via injector.
      injector: Injector.create({
        providers: [
          {
            provide: 'data',
            useValue: {
              projectId: this.projectId,
              userId: +localStorage.getItem('UserId'),
              client: this.config.themeClient,
              mediaList: this.mediaIds,
              dialogRef: ref
            }
          }
        ],
        parent: this.injector
      })
    })

    ref
      .afterClosed()
      .pipe(
        filter(
          (value) =>
            (this.isPageLoadDisplayMedia && !!value) ||
            (!!value && !isEqual(this.mediaIds, value))
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (mediaIds) => {
          const lastQuery = this.lastQueryIfExists
          this.mediaIds = mediaIds
          this.isPageLoadDisplayMedia = false
          // if we have updated media IDs from the end user, restore the search.
          this.store.dispatch(
            new SearchAction({
              expression: lastQuery?.expression ?? 'FileId > 0',
              isInitialSearch: !lastQuery,
              mediaList: Array.isArray(mediaIds)
                ? mediaIds?.map((id) => `${id}`)
                : null,
              searchDuplicateOption: this.searchDuplicateOption
            })
          )
        }
      })
  }

  private async setFolderScope() {
    // dialog config
    const ref = this.dialog.open(this.folderScopeTplRef, {
      width: '1000px',
      minHeight: '55vh',
      maxHeight: '95vh',
      autoFocus: false,
      closeOnNavigation: true,
      disableClose: true
    })
    // load and map lazy component with identity
    await import(
      '@shared/components/folder/folder-selector/folder-selector.module'
    )

    this.lazyComponents.set('FOLDER_SCOPE', {
      component: import(
        '@shared/components/folder/folder-selector/folder-selector.component'
      ).then(({ FolderSelectorComponent }) => FolderSelectorComponent),
      // dialog ref has been passed to the component via injector.
      injector: Injector.create({
        providers: [
          {
            provide: 'FolderData',
            useValue: {
              projectId: this.projectId,
              actionType: FolderActionType.Set_Folder_Scope,
              dialogRef: ref,
              folderIds:
                this.folderList?.length > 0
                  ? this.folderList
                  : this.restrictedFolderIds
            } as FolderInjectorModel
          }
        ],
        parent: this.injector
      })
    })

    ref
      .afterClosed()
      .pipe(
        filter(
          (value) =>
            (this.isPageLoadDisplayMedia && !!value) ||
            (!!value && !isEqual(this.folderList, value))
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (folderIds: number[]) => {
          const lastQuery = this.lastQueryIfExists
          this.folderList = folderIds.filter((id) => id !== -1)
          this.isPageLoadDisplayMedia = false
          // if we have updated media IDs from the end user, restore the search.
          this.store.dispatch(
            new SearchAction({
              expression: lastQuery?.expression ?? 'FileId > 0',
              isInitialSearch: !lastQuery,
              folderList: this.folderList
            })
          )
        }
      })
  }

  /**
   * Loads tag component lazily on user demand to create tags.
   * @param name A type of action  (act as identification token)
   */
  private readonly handleAddWidget = (name: AnalyzeActionTypes): void => {
    // dialog config
    const ref = this.dialog.open(this.addUpdateWidgetTplRef, {
      width: '1200px',
      minHeight: '45vh',
      maxHeight: '95vh',
      autoFocus: false,
      closeOnNavigation: true,
      disableClose: true
    })

    this.widget.item.activeAction = 'WIDGET_ADD'

    // load and map lazy component with identity
    this.lazyComponents.set(name, {
      component: import('../widgets.component').then(
        ({ WidgetsComponent }) => WidgetsComponent
      ),
      // dialog ref has been passed to the component via injector.
      injector: Injector.create({
        providers: [
          {
            provide: 'WIDGET_CONTENT',
            useValue: this.widget
          },
          {
            provide: MatDialogRef,
            useValue: ref
          }
        ],
        parent: this.injector
      })
    })

    ref
      .afterOpened()
      .pipe(debounceTime(500), takeUntil(this.toDestroy$))
      .subscribe({
        next: () => {
          // After loading this component, the  project list in used thus,
          // send request to load them.
          const gridster = this.widget.gridsterItemComponent.first.gridster
          gridster.options.scrollToNewItems = true
          gridster.optionsChanged()
          gridster.updateGrid()
        }
      })
  }

  /**
   * Toggles (add/remove) multi search status widget.
   * @param isRemove Whether to add or remove the widget.
   */
  private readonly toggleMultiStatusWidget = (isRemove: boolean) =>
    this.store.dispatch(
      new SyncRenderedWidgetAction(isRemove ? 'WIDGET_REMOVE' : 'WIDGET_ADD', {
        uuid: new Uuidv4Generator().uuid,
        widgetType: WidgetTypes.MULTI_SEARCH_STATUS,
        title: 'MULTI SEARCH TERM STATUS',
        cols: 24,
        rows: 3,
        y: 0,
        x: 0,
        dragEnabled: false,
        resizeEnabled: false
      })
    )

  /**
   * Handles restore search event.
   * Local states and other default states are restored or requested to restore from here.
   */
  private readonly handleResetSearch = (): void => {
    this.errorRes = undefined
    this.allBreadcrumb = []
    this.store.dispatch(new AnalyzeStateResetAction('breadcrumbItems'))
    // perform initial search only at once
    if (!this.localStats.isInitialSearch) {
      this.localStats.isInitialSearch = true
      this.store.dispatch(
        new SearchAction({
          isInitialSearch: true,
          mediaList: this.mediaIds?.map((id) => `${id}`),
          folderList: this.folderList,
          searchDuplicateOption: this.searchDuplicateOption
        })
      )
    }

    // toggle local states.
    this.localStats.isResetSearching = true
    this.localStats.isInitialSearch = true
    this.toggleMultiStatusWidget(true)
  }

  /**
   * Checks if there's multi-line in the expression text and initialize multi-search term status grid.
   */
  private readonly initMultiSearchTermStatusWidget = (): void => {
    // do nothing if it was an initial search
    if (this.localStats.isInitialSearch) return

    // Se if we have multiple line in the search input
    const exp = this.inputFrom.get('expression').value as string

    const breadCrumbs: SearchInputs[] = this.store.selectSnapshot(
      AnalyzeStateSelectors.sliceOf('breadcrumbItems')
    )
    const isCurrentSearchMultiTermSearch: boolean =
      this.isMultipleTermExpression(exp)
    //check if the previous breadcrumbs has the multiple search term expression
    const doesMultiSearchTermAlreadyExists: boolean =
      breadCrumbs.filter((x) => this.isMultipleTermExpression(x.expression))
        ?.length > 0

    // whether the text contains multi lines.
    const hasMultiLine =
      isCurrentSearchMultiTermSearch || doesMultiSearchTermAlreadyExists
    if (!hasMultiLine) return
    // init if contains multi line.
    this.toggleMultiStatusWidget(false)
  }

  /**
   * Checks if any widget has selected items to filter, pops up an alert to remind that
   * an item must be selected to perform a filter.
   */
  private isItemSelected() {
    // checks for selected items so we can perform search.
    const hasBarItems = this.preservedQueries.some((s) => s)

    if (!hasBarItems) {
      // we use cdk overlay module by angular to prompt reminder alert
      const portal = new TemplatePortal(
        this.confirmNodeSelectionTplRef,
        this.vcr
      )
      const overlayRef = this.overlay.create({
        positionStrategy: this.overlay
          .position()
          .global()
          .centerHorizontally()
          .centerVertically(),
        hasBackdrop: true,
        maxWidth: '490px'
      })
      // remove from DOM when user clicks to the backdrop
      overlayRef
        .backdropClick()
        .pipe(takeUntil(this.toDestroy$))
        .subscribe({ next: () => [overlayRef.detach(), overlayRef.dispose()] })
      overlayRef.attach(portal)

      // since  the block contains button element, we listen their event to close/dispose this.
      fromEvent(
        overlayRef.overlayElement.querySelectorAll('.close-confirm'),
        'click'
      )
        .pipe(takeUntil(this.toDestroy$))
        .subscribe({ next: () => [overlayRef.detach(), overlayRef.dispose()] })
      return hasBarItems
    }

    return hasBarItems
  }

  /**
   * Handles discovery report task.
   * @param reportType Either `discovery` or `export`
   */
  private readonly handleDiscoveryReport = (
    reportType: 'Discovery' | 'Export' = 'Discovery'
  ): void => {
    this.discoveryReportUploadNameCtrl.reset()
    this.discoveryReportExportCtrl.reset()
    this.discoveryReportType = reportType
    this.store
      .dispatch(new FetchUploadName(this.projectInfo.ProjectId))
      .pipe(
        switchMap(() =>
          this.store.select(CaseSelectors.discoveryReportUploadName)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (result) => (this.discoveryReportUploadNames = result)
      })
    this.store
      .dispatch(new FetchExportName(this.projectInfo.ProjectId))
      .pipe(
        switchMap(() =>
          this.store.select(CaseSelectors.discoveryReportExportName)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (result) => (this.discoveryReportExports = result)
      })
    this.dialog.open(this.dcReportTpl, {
      closeOnNavigation: true,
      autoFocus: false,
      width: '400px',
      panelClass: 'discovery-report-dialog'
    })
  }

  /**
   * Launches new window to generate and display report.
   * @param reportType either `discovery` or `export`
   */
  readonly generateReport = (reportType: string): void => {
    if (!reportType) return
    if (
      reportType === 'Discovery' &&
      !this.discoveryReportUploadNameCtrl.value
    ) {
      this.toastr.error(`Please select a upload name.`)
      return
    } else if (
      reportType === 'Export' &&
      !this.discoveryReportExportCtrl.value
    ) {
      this.toastr.error(`Please select a export name.`)
      return
    }
    let urlWithParam = ''
    if (reportType === 'Discovery') {
      urlWithParam = `${this.configService.getWebBaseUrl()}/onDemand/VoDR/showReport.aspx?ProjectId=${
        this.projectId
      }&ReportType=DISCOVERY_REPORT&ServiceSubmitInstance=${
        this.discoveryReportUploadNameCtrl.value
      }`
    } else if (reportType === 'Export') {
      const exportId = this.discoveryReportExportCtrl.value
      const exportDetails = this.discoveryReportExports.filter(
        (c) => c.exportId === exportId
      )[0]
      urlWithParam = `${this.configService.getWebBaseUrl()}/onDemand/VoDR/showReport.aspx?ProjectId=${
        this.projectId
      }&ReportType=PROCESS_EXPORT_REPORT&ExportId=${exportId}&ServiceSubmitInstance=${
        exportDetails.exportName
      }`
    }

    window.open(
      urlWithParam,
      '_blank',
      'location=1,status=1,scrollbars=1, resizable=1, directories=1, toolbar=1, titlebar=1, height=500, width=650'
    )
  }

  /**
   * Handles input click event to toggle advance filter UI
   */
  private readonly handleBuilderEvent = () => {
    this.forQueryBuilder
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe({
        next: () => {
          this.inputFrom.get('expression').reset()
          this.showAdvanceFilterUi = true
        }
      })
  }

  #notifyAdvancedSearchUiReset(): void {
    if (!this.showAdvanceFilterUi) return

    this.store.dispatch(new TriggerResetAdvancedSearchUiAction(true))
  }

  onClearSearchSyntax(): void {
    this.inputFrom.reset()
    this.#notifyAdvancedSearchUiReset()
  }

  @DebounceTimer(100)
  onSearchInputKeydown(event: Event): void {
    const input = event.target as HTMLTextAreaElement
    const value = (input.value || '').trim()
    if (value) return

    this.#notifyAdvancedSearchUiReset()
  }
}
