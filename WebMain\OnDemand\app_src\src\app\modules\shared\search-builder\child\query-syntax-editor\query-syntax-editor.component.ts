import { OverlayModule } from '@angular/cdk/overlay'
import { CommonModule } from '@angular/common'
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  NgModule,
  OnDestroy,
  OnInit,
  ViewChild
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { MatTooltipModule } from '@angular/material/tooltip'
import { Store } from '@ngxs/store'
import {
  StoreCompleteQuerySyntaxAction,
  TriggerResetAdvancedSearchUiAction
} from '@shared/search-builder/sotre/query-builder.actions'
import { QueryBuilderSelector } from '@shared/search-builder/sotre/query-builder.selector'
import { Subject } from 'rxjs'
import { filter, takeUntil } from 'rxjs/operators'

@Component({
  selector: 'app-query-syntax-editor',
  templateUrl: './query-syntax-editor.component.html',
  styleUrls: ['./query-syntax-editor.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class QuerySyntaxEditorComponent implements OnInit, OnDestroy {
  readonly toDestroy$ = new Subject<void>()

  combinedQueryText = ''

  @ViewChild('queryTextarea') queryTextarea: ElementRef

  constructor(
    private changeDetectorRef: ChangeDetectorRef,
    private store: Store
  ) {}

  public ngOnInit(): void {
    this.#selectSearchQueryItems()
    this.#selectQuerySyntaxReset()
  }

  /**
   * Adjust the height of the textarea based on its content
   * @returns {void}
   */
  private adjustTextareaHeight(): void {
    if (this.queryTextarea && this.queryTextarea.nativeElement) {
      const textarea = this.queryTextarea.nativeElement

      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto'

      // Set the height based on scrollHeight (content height)
      // Get line height to calculate equivalent of 4 rows
      const computedStyle = window.getComputedStyle(textarea)
      const lineHeight = parseInt(computedStyle.lineHeight, 10) || 20
      const maxHeight = lineHeight * 4 + 12

      // Set the height based on content, but cap at maxHeight
      const newHeight = Math.min(textarea.scrollHeight, maxHeight)
      textarea.style.height = `${newHeight}px`

      this.changeDetectorRef.markForCheck()
    }
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  onCombinedQueryChange(event: FocusEvent): void {
    const newValue = event.target['value']
    this.combinedQueryText = newValue
    const newTrimmedQuery = newValue.trim()
    this.adjustTextareaHeight()
    this.changeDetectorRef.markForCheck()

    this.store.dispatch(new StoreCompleteQuerySyntaxAction(newTrimmedQuery))
  }

  #selectSearchQueryItems(): void {
    this.store
      .select(QueryBuilderSelector.sliceOf('completeQuerySyntax'))
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((content) => {
        this.combinedQueryText = content
        this.adjustTextareaHeight()
        this.changeDetectorRef.markForCheck()
      })
  }

  #selectQuerySyntaxReset(): void {
    this.store
      .select(QueryBuilderSelector.sliceOf('isResetAdvancedSearchUi'))
      .pipe(
        filter((isReset) => isReset),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.combinedQueryText = ''
        // Reset the flag to false as soon as
        // we receive value as true to perform cleanup.
        // we wait for next cleanup by setting it as false.
        this.store.dispatch(new TriggerResetAdvancedSearchUiAction(false))
        this.changeDetectorRef.markForCheck()
      })
  }
}

@NgModule({
  imports: [CommonModule, FormsModule, OverlayModule, MatTooltipModule],
  declarations: [QuerySyntaxEditorComponent]
})
export class QuerySyntaxEditorLazyModule {}
