import {
  AfterViewInit,
  ChangeDetector<PERSON>ef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { Client } from '@auth/models/user.model'
import { getClientInfo } from '@auth/store/selectors/access.selectors'
import { ConfigService } from '@config/services/config.service'
import { ConfigState } from '@config/store/reducers'
import { getControlSetting } from '@config/store/selectors'
import { Action, select, Store as RXStore } from '@ngrx/store'
import { Store } from '@ngxs/store'
import { DxDataGridComponent } from 'devextreme-angular'
import _, { cloneDeep } from 'lodash'
import * as plupload from 'node_modules/plupload'
import { fromEvent, Subject, timer } from 'rxjs'
import { debounceTime, filter, takeUntil, tap } from 'rxjs/operators'
import { AddedFilesList, MediaSourceType } from 'src/app/modules/upload/models'
import { StartUpload } from 'src/app/modules/upload/store/actions'
import { startUploadResponse } from 'src/app/modules/upload/store/selectors/upload.selectors'
import { NotificationService } from 'src/app/services/notification.service'
import {
  ImportRepairModel,
  ProcessingFailedFileModel,
  RepairMethod,
  UserIdFile
} from '../../models/file-repair.model'
import {
  ClearFileRepairResponseAction,
  ClearImportRepairResponseAction,
  GetImportFailedFilesAction,
  GetProcessingFailedFiles,
  RepairImportFailedFilesAction,
  RepairProcessingFailedFiles,
  ValidateImportTokenAction,
  ValidateToken
} from '../../store/file-repair.actions'
import { FileRepairStateSelector } from '../../store/file-repair.selectors'

@Component({
  selector: 'app-file-repair-page',
  templateUrl: './file-repair-page.component.html',
  styleUrls: ['./file-repair-page.component.scss']
})
export class FileRepairPageComponent
  implements OnDestroy, AfterViewInit, OnInit
{
  /**
   * Project Id having the processing failed files and require file repair/replace
   */
  projectId: number

  /**
   * RVOD settingId for service submit
   */
  settingId: number

  /**
   * Import id
   */
  importId: number

  /**
   * Flag to indicate repair is for import
   */
  isImportRepair = false

  selectedRepairItemKeys: number[] = []

  /**
   * Token in the email link
   */
  token: string

  /**
   * Client of the logged in user
   */
  client: Client

  @ViewChild(DxDataGridComponent)
  private readonly dxGridFile: DxDataGridComponent

  /**
   * Enable content placeholder to indicate preparing to load data.
   */
  isWorking = true

  /**
   * Flag to indicate the repair request is being submitted
   */
  submitting = false

  /**
   * Flag to indicate the token is invalid or expired
   */
  invalidToken = false

  queryParamsSubscription: any

  /**
   * List of processing failed files
   */
  processingFailedFiles: ProcessingFailedFileModel[] = []

  /**
   * List of import failed files
   */
  importFailedFiles: ImportRepairModel[] = []

  /**
   * File repair methods
   */
  repairMethods = [
    {
      Value: RepairMethod.REPROCESS_WITH_REPAIRED_FILE,
      DisplayText: 'Reprocess with repaired file'
    },
    {
      Value: RepairMethod.REPROCESS_WITH_PASSWORD,
      DisplayText: 'Reprocess with new password'
    },
    {
      Value: RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD,
      DisplayText: 'Reprocess with new user id and password'
    },
    {
      Value: RepairMethod.DONOT_REPAIR,
      DisplayText: 'Do not repair'
    }
  ]

  /**
   * File repair methods for import
   */
  importRepairMethods = [
    {
      Value: RepairMethod.REPROCESS_WITH_REPAIRED_FILE,
      DisplayText: 'Re-import with repaired file'
    },
    {
      Value: RepairMethod.REPROCESS_WITH_PASSWORD,
      DisplayText: 'Re-import with new password'
    },
    {
      Value: RepairMethod.DONOT_REPAIR,
      DisplayText: 'Do not repair'
    }
  ]

  /**
   * Error message during submit
   */
  errorMessage: string

  /**
   * Static service of app config.
   */
  config = ConfigService

  /**
   * Currently selected row file Id
   */
  static editingFileId = 0

  /**
   * Use to show the alert messages in the page
   */
  @ViewChild('alertTemplate')
  private readonly alertDialog: TemplateRef<HTMLDialogElement>

  /**
   * Title for the alert dialog
   */
  alertTitle = 'VenioOne OnDemand'

  /**
   * Message/content for the alert dialog
   */
  alertMessage = ''

  /**
   * Cleanup placeholder for the oberserver when the component is destroyed
   */
  private readonly unSubscribe$ = new Subject<void>()

  //#region upload related variables
  currentFileUploadProgress: number

  currentlyUploadingId = ''

  /**
   * PLUploader instance
   */
  mUploader: any

  /**
   * Forensic image file formats
   */
  forensicImageFileFormat = 'ISO|E\\d+|L\\d+'

  /**
   * Stores the uploaded files
   */
  filesUploaded = []

  /**
   * Files added for upload
   */
  addedFilesList: AddedFilesList[] = []

  /**
   * User Ids added for reprocessing
   */
  userIdFiles: UserIdFile[] = []

  //#endregion upload related variables

  constructor(
    private store: Store,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private toastr: NotificationService,
    private configService: ConfigService,
    private configStore: RXStore<ConfigState>,
    private rxStore: RXStore<Action>,
    private router: Router,
    private dialog: MatDialog
  ) {
    this.getRepairOptions = this.getRepairOptions.bind(this)
  }

  ngOnInit(): void {
    this.queryParamsSubscription = this.route.queryParams
      .pipe(takeUntil(this.unSubscribe$))
      .subscribe((params) => {
        this.projectId = params['projectId']
        this.settingId = params['settingId']
        this.importId = params['importId']

        if (
          this.importId !== undefined &&
          this.importId !== null &&
          this.importId > 0
        ) {
          this.isImportRepair = true
        }

        this.token = params['token']

        if (this.isImportRepair) {
          this.store.dispatch(
            new ValidateImportTokenAction(this.projectId, this.token)
          )
        } else
          this.store.dispatch(new ValidateToken(this.projectId, this.token))
      })
  }

  ngAfterViewInit(): void {
    timer(700)
      .pipe(
        tap(() => {
          this.cdr.markForCheck()
          this.isWorking = false
        }),
        debounceTime(100),
        takeUntil(this.unSubscribe$)
      )
      .subscribe({
        next: () => {
          this.mUploader = null
          this.currentlyUploadingId = null
          this.currentFileUploadProgress = null

          setTimeout(() => {
            this.mUploader = this.uploaderObject()
            this.mUploader.init()

            if (this.isImportRepair) this.initImportSlices()
            else this.initSlices()
          }, 1000)
        }
      })
  }

  /**
   * Init data slice listener
   */
  initSlices(): void {
    /**
     * Select flag to Check if the token is valid and not expired
     */
    this.store
      .select(FileRepairStateSelector.SliceOf('tokenExpired'))
      .pipe(takeUntil(this.unSubscribe$))
      .subscribe((tokenExpired) => {
        this.invalidToken = tokenExpired

        /**
         * Dispatch the action to fetch the processing files only if the token is valid and not expiried
         */
        if (!this.invalidToken) {
          this.store.dispatch(
            new GetProcessingFailedFiles(this.projectId, this.settingId)
          )
        } else {
          this.alertMessage =
            'The link is expired or invalid. This link cannot be used to repair the files.'

          const confirmRef = this.dialog.open(this.alertDialog, {
            closeOnNavigation: false,
            autoFocus: false,
            disableClose: true
          })

          setTimeout(() => {
            const actions = document.querySelectorAll('.close-confirm')
            fromEvent(actions, 'click')
              .pipe(takeUntil(this.unSubscribe$))
              .subscribe({
                next: (e) => {
                  confirmRef.close()
                  this.router.navigateByUrl('/launchpad/caselaunchpad')
                }
              })
          }, 500)
        }
      })

    this.store.dispatch(new ClearFileRepairResponseAction())

    /**
     *Select/get the processing failed files
     */
    this.store
      .select(FileRepairStateSelector.SliceOf('processingFailedFiles'))
      .pipe(
        filter((res) => !!res && res.length > 0),
        takeUntil(this.unSubscribe$)
      )
      .subscribe((files) => {
        this.processingFailedFiles = files.map((el) => {
          return { ...el }
        })
      })

    /**
     * Handling for file repair success
     */
    this.store
      .select(FileRepairStateSelector.SliceOf('repairSuccessResponse'))
      .pipe(
        filter((res) => !!res && !this.invalidToken),
        takeUntil(this.unSubscribe$)
      )
      .subscribe((res) => {
        this.toastr.showSuccess(
          'Your file replacement request has been successfully submitted.',
          true
        )
        this.submitting = false

        this.router.navigate(['launchpad/caselaunchpad'], {
          queryParamsHandling: 'preserve'
        })
      })

    /**
     * Handling for file repair error
     */
    this.store
      .select(FileRepairStateSelector.SliceOf('repairErrorResponse'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.unSubscribe$)
      )
      .subscribe((res) => {
        this.toastr.showError(`Failed the repair the files.\n${res.message}`)
        this.submitting = false
      })

    /**
     * Get the MIP supported forensic image file type from control setting
     */
    this.configStore
      .pipe(
        select(
          getControlSetting('MIP_SUPPORTED_EXTENSION'),
          takeUntil(this.unSubscribe$)
        )
      )
      .subscribe((mipSupportedFileFormats: string) => {
        const mipFileFormatsArray = mipSupportedFileFormats?.split(',')
        mipFileFormatsArray.forEach((extension: string) => {
          this.forensicImageFileFormat += `|${extension.replace(
            /\d+$/,
            '\\d+'
          )}`
        })
      })

    /**
     * Get the client
     */
    this.configStore
      .pipe(select(getClientInfo), takeUntil(this.unSubscribe$))
      .subscribe((client: Client) => {
        this.client = client
      })

    /**
     * Start the file upload once the file info is inserted into database
     */
    this.rxStore
      .pipe(
        select(startUploadResponse),
        filter((res) => !!res),
        takeUntil(this.unSubscribe$)
      )
      .subscribe((res) => {
        if (res[0].hasError) {
          this.toastr.showError(
            'An error has occurred. Error details : ' + res[1]
          )
        } else {
          if (this.mUploader) {
            this.start()
          }
        }
      })
  }

  /**
   * Init data slice listener
   */
  initImportSlices(): void {
    /**
     * Select flag to Check if the token is valid and not expired
     */
    this.store
      .select(FileRepairStateSelector.SliceOf('importTokenExpired'))
      .pipe(takeUntil(this.unSubscribe$))
      .subscribe((tokenExpired) => {
        this.invalidToken = tokenExpired

        /**
         * Dispatch the action to fetch the processing files only if the token is valid and not expiried
         */
        if (!this.invalidToken) {
          this.store.dispatch(new GetImportFailedFilesAction(this.projectId))
        } else {
          this.alertMessage =
            'The link is expired or invalid. This link cannot be used to repair the files.'

          const confirmRef = this.dialog.open(this.alertDialog, {
            closeOnNavigation: false,
            autoFocus: false,
            disableClose: true
          })

          setTimeout(() => {
            const actions = document.querySelectorAll('.close-confirm')
            fromEvent(actions, 'click')
              .pipe(takeUntil(this.unSubscribe$))
              .subscribe({
                next: (e) => {
                  confirmRef.close()
                  this.router.navigateByUrl('/launchpad/caselaunchpad')
                }
              })
          }, 500)
        }
      })

    this.store.dispatch(new ClearImportRepairResponseAction())

    /**
     *Select/get the import failed files
     */
    this.store
      .select(FileRepairStateSelector.SliceOf('importFailedFiles'))
      .pipe(
        filter((res) => !!res && res.length > 0),
        takeUntil(this.unSubscribe$)
      )
      .subscribe((files: ImportRepairModel[]) => {
        const tempImportFailedFiles = cloneDeep(files)

        const tempProcessingFailedFiles: ProcessingFailedFileModel[] = []

        tempImportFailedFiles.forEach((file: ImportRepairModel) => {
          const fileInfo: ProcessingFailedFileModel = {
            fileId: file.importId,
            fileName: file.fileName,
            errorType: file.errorType,
            errorDescription: file.errorDescription,
            repairMethod: file.repairMethod
          }
          tempProcessingFailedFiles.push(fileInfo)
        })

        this.processingFailedFiles = tempProcessingFailedFiles.map((el) => {
          return { ...el }
        })

        if (this.importId > 0) {
          this.selectedRepairItemKeys.push(this.importId)
        }
      })

    /**
     * Handling for import file repair success
     */
    this.store
      .select(FileRepairStateSelector.SliceOf('importRepairSuccessResponse'))
      .pipe(
        filter((res) => !!res && !this.invalidToken),
        takeUntil(this.unSubscribe$)
      )
      .subscribe((res) => {
        this.toastr.showSuccess(
          'Your file replacement request has been successfully submitted.',
          true
        )
        this.submitting = false

        this.router.navigate(['launchpad/caselaunchpad'], {
          queryParamsHandling: 'preserve'
        })
      })

    /**
     * Handling for import file repair error
     */
    this.store
      .select(FileRepairStateSelector.SliceOf('importRepairErrorResponse'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.unSubscribe$)
      )
      .subscribe((res) => {
        this.toastr.showError(`Failed the repair the files.\n${res.message}`)
        this.submitting = false
      })

    /**
     * Get the client
     */
    this.configStore
      .pipe(select(getClientInfo), takeUntil(this.unSubscribe$))
      .subscribe((client: Client) => {
        this.client = client
      })

    /**
     * Start the file upload once the file info is inserted into database
     */
    this.rxStore
      .pipe(
        select(startUploadResponse),
        filter((res) => !!res),
        takeUntil(this.unSubscribe$)
      )
      .subscribe((res) => {
        if (res[0].hasError) {
          this.toastr.showError(
            'An error has occurred. Error details : ' + res[1]
          )
        } else {
          if (this.mUploader) {
            this.start()
          }
        }
      })
  }

  ngOnDestroy(): void {
    this.unSubscribe$.next()
    this.unSubscribe$.complete()

    this.queryParamsSubscription.unsubscribe()
  }

  /**
   * Get the repair method options based on the errorType of the file
   */
  getRepairOptions(options) {
    if (!this.isImportRepair) {
      return {
        store: this.repairMethods,
        filter: options.data
          ? options.data.errorType === 'PASSWORD_PROTECTED_DECRYPTION_SUPPORTED'
            ? ['Value', '<>', RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD]
            : options.data.errorType === 'NSF'
            ? ['Value', '<>', RepairMethod.REPROCESS_WITH_PASSWORD]
            : [
                ['Value', '<>', RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD],
                ['Value', '<>', RepairMethod.REPROCESS_WITH_PASSWORD]
              ]
          : null
      }
    } else {
      return {
        store: this.importRepairMethods
      }
    }
  }

  /**
   * Chck if repair method with file replacement is selected for a file
   * @param data - grid data
   */
  allowRepairWithPassword(data: any): boolean {
    return data.data.repairMethod == RepairMethod.REPROCESS_WITH_PASSWORD
  }

  /**
   * Check if repair method with password is selected for a file
   * @param data - grid data
   */
  allowRepairWithFile(data: any): boolean {
    return data.data.repairMethod == RepairMethod.REPROCESS_WITH_REPAIRED_FILE
  }

  allowRepairWithUserIdFile(data: any): boolean {
    return (
      data.data.repairMethod == RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD
    )
  }

  checkSelection(e: any): boolean {
    return this.selectedRepairItemKeys?.includes(e?.data.fileId)
  }

  /**
   * Check if file can be browsed for file replacement
   * @param e - event
   */
  allowBrowseFile(e): boolean {
    return (
      e.row.data.repairMethod === RepairMethod.REPROCESS_WITH_REPAIRED_FILE ||
      e.row.data.repairMethod === RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD
    )
  }

  /**
   * Get placeholder text according to the repair method selected
   * @param data grid data
   * @returns placeholder text
   */
  getPlaceholderTextForRepairAction(data: any): string {
    if (data.data.repairMethod === RepairMethod.REPROCESS_WITH_PASSWORD) {
      return 'Password'
    } else if (
      data.data.repairMethod === RepairMethod.REPROCESS_WITH_REPAIRED_FILE
    ) {
      return 'File Path'
    } else if (
      data.data.repairMethod === RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD
    ) {
      return 'User Id Path'
    }
  }

  /**
   * set password/userid file/repair file value
   * @param data grid data
   * @returns set value
   */
  getValueForRepairAction(data: any): any {
    if (data.data.repairMethod === RepairMethod.REPROCESS_WITH_PASSWORD) {
      return data.data.newPassword
    } else if (
      data.data.repairMethod === RepairMethod.REPROCESS_WITH_REPAIRED_FILE
    ) {
      return data.data.newFilePath
    } else if (
      data.data.repairMethod === RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD
    ) {
      return data.data.newUserIdFile
    }
  }

  /**
   * Event for value changed in password and file path grid cell
   * @param evt
   * @param data
   */
  onValueChangedForRepairAction(evt: any, data: any): void {
    //data.setValue(evt.value)
    if (data.data.repairMethod === RepairMethod.REPROCESS_WITH_PASSWORD) {
      data.data.newPassword = evt.value
    } else if (
      data.data.repairMethod === RepairMethod.REPROCESS_WITH_REPAIRED_FILE
    ) {
      data.data.newFilePath = evt.value
    } else if (
      data.data.repairMethod === RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD
    ) {
      data.data.newUserIdFile = evt.value
    }
  }

  /**
   * Event for value changed in password and file path grid cell
   * @param evt
   * @param data
   */
  onValueChangedForNsfPassword(evt: any, data: any): void {
    //data.setValue(evt.value)
    if (
      data.data.repairMethod === RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD
    ) {
      data.data.newPassword = evt.value
    }
  }

  /**
   * handle input event
   * @param e event
   * @param data grid data
   */
  handleFileInput(e, data: any): void {
    const fileId = data.data.fileId
    const uploadedIdfile: File = e.target['files'][0]

    data.data.newUserIdFile = uploadedIdfile.name

    if (this.userIdFiles.findIndex((x) => x.fileId == fileId) != -1) {
      this.userIdFiles.find((x) => x.fileId == fileId).userIdFile =
        uploadedIdfile
    } else {
      this.userIdFiles.push({ fileId, userIdFile: uploadedIdfile })
    }
  }

  /**
   * Repair button event
   */
  repair(): void {
    if (!this.validateRepairActions()) {
      this.toastr.showError(this.errorMessage)
      return
    }

    this.submitting = true

    if (this.getFilesForReplacement().length > 0) {
      this.rxStore.dispatch(
        new StartUpload(
          this.projectId,
          this.addedFilesList,
          0,
          false,
          false,
          -1,
          false,
          '',
          []
        )
      )
    } else {
      this.submitRepairRequest()
    }
  }

  submitRepairRequest(): void {
    const files: ProcessingFailedFileModel[] = this.dxGridFile.instance
      .getDataSource()
      .items()
      .filter((x) => this.selectedRepairItemKeys.includes(x.fileId))

    if (!this.isImportRepair) {
      this.store.dispatch(
        new RepairProcessingFailedFiles(
          this.projectId,
          this.settingId,
          this.token,
          files,
          this.userIdFiles
        )
      )
    } else {
      const repairFiles: ImportRepairModel[] = []

      files.forEach((file) => {
        const fileInfo: ImportRepairModel = {
          importId: file.fileId,
          fileName: file.fileName,
          errorType: file.errorType,
          errorDescription: file.errorDescription,
          repairMethod: file.repairMethod,
          newFilePath: file.newFilePath,
          uploadId: 0,
          uploadGuId: file.newFileUploadId,
          newPassword: file.newPassword,
          newPasswordBankId: file.newPasswordBankId
        }
        repairFiles.push(fileInfo)
      })

      this.store.dispatch(
        new RepairImportFailedFilesAction(
          this.projectId,
          this.token,
          repairFiles
        )
      )
    }
  }

  /**
   * Validate if repair options/inputs are correctly provided for all files in grid
   */
  validateRepairActions(): boolean {
    this.errorMessage = null
    const repairData = this.dxGridFile.instance
      .getDataSource()
      .items()
      .filter((x) => this.selectedRepairItemKeys.includes(x.fileId))

    const filesWithoutRepairPassword = repairData.filter(
      (file: ProcessingFailedFileModel) =>
        file.repairMethod === RepairMethod.REPROCESS_WITH_PASSWORD &&
        (!file.newPassword || file.newPassword?.trim().length === 0)
    )
    if (filesWithoutRepairPassword.length > 0) {
      if (!this.isImportRepair) {
        this.errorMessage =
          "Please provide password for the file(s) with 'Reprocess with new password' as the repair method."
      } else {
        this.errorMessage =
          "Please provide password for the file(s) with 'Re-import with new password' as the repair method."
      }

      return false
    }

    const filesWithoutRepairFile = repairData.filter(
      (file: ProcessingFailedFileModel) =>
        file.repairMethod === RepairMethod.REPROCESS_WITH_REPAIRED_FILE &&
        (!file.newFilePath || file.newFilePath?.trim().length === 0)
    )

    if (filesWithoutRepairFile.length > 0) {
      if (!this.isImportRepair) {
        this.errorMessage =
          "Please input a new file for the file(s) with 'Reprocess with repaired file' as the repair method."
      } else {
        this.errorMessage =
          "Please input a new file for the file(s) with 'Re-import with repaired file' as the repair method."
      }

      return false
    }

    const filesWithoutUserIdFile = repairData.filter(
      (file: ProcessingFailedFileModel) =>
        file.repairMethod === RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD &&
        (!file.newUserIdFile ||
          file.newUserIdFile?.trim().length === 0 ||
          /.id$/i.test(file.newUserIdFile) == false)
    )

    if (filesWithoutUserIdFile.length > 0) {
      this.errorMessage =
        "Please input a user id file for the file(s) with 'Reprocess with new user id and password' as the repair method."

      return false
    }

    return true
  }

  /**
   * Click event for browse button to select/browse the file for the replacement
   */
  onBrowse(data): void {
    if (
      data.data.repairMethod == RepairMethod.REPROCESS_WITH_USER_ID_PASSWORD
    ) {
      document.getElementById('idFileData' + data.data.fileId).click()
    } else {
      FileRepairPageComponent.editingFileId = data.data.fileId
      document.getElementById('pickfiles').click()
    }
  }

  /**
   * Get the files for upload and replacement
   */
  getFilesForReplacement(): ProcessingFailedFileModel[] {
    return this.processingFailedFiles.filter(
      (file) =>
        file.repairMethod === RepairMethod.REPROCESS_WITH_REPAIRED_FILE &&
        this.selectedRepairItemKeys.includes(file.fileId)
    )
  }

  /**
   * Check whether to show the upload status or not
   */
  showUploadStatus(): boolean {
    return this.submitting && this.getFilesForReplacement().length > 0
  }

  /**
   * Check if the file with the specified id is uploaded or not
   */
  isFileUPloaded(id: string): boolean {
    if (this.filesUploaded.length > 0) {
      const files = this.filesUploaded.filter((file) => {
        return file.newFileUploadId === id
      })

      if (files !== null && files.length > 0) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  }

  /**
   * Get the extension of the file
   * @param filename name of the file
   */
  getFileExtension(filename: string): string {
    return /[.]/.exec(filename) ? /[^.]+$/.exec(filename)[0] : undefined
  }

  /**
   * Convert the size in bytes to KB, MB, GB
   * @param bytes size in bytes
   * @param decimals decimal places
   */
  formatBytes(bytes: number, decimals: number): string {
    if (bytes === 0) {
      return '0 Byte'
    }
    const k = 1024
    const dm = decimals + 1 || 3
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  }

  //#region upload related functions
  uploaderObject() {
    const self = this

    const uploader = new plupload.Uploader({
      runtimes: 'html5,flash,silverlight,html4',
      browse_button: 'pickfiles', // you can pass an id...
      url:
        this.configService.getVodBaseUrl() +
        '/VodWebService.asmx/ChunkUploadFile',
      flash_swf_url: '../js/Moxie.swf',
      silverlight_xap_url: '../js/Moxie.xap',
      max_file_size: 0,
      chunk_size: `2mb`,
      max_retries: 3,
      max_retries_time: 5,
      unique_names: false,
      multiple_queues: true,
      // Rename files by clicking on their titles
      rename: true,
      // Sort files
      sortable: true,
      dragdrop: false,

      // Views to activate
      views: {
        list: true,
        thumbs: true, // Show thumbs
        active: 'thumbs'
      },
      init: {
        PostInit: function () {},
        // upload control event
        FilesAdded: function (up, files) {
          self.uploaderObjectFilesAdded(up, files, self)
        },
        BeforeUpload: function (up, file) {
          self.uploaderObjectBeforeUpload(up, file, self)
        },
        UploadProgress: function (up, file) {
          self.uploaderObjectUploadProgress(up, file, self)
        },
        FileUploaded: function (up, file) {
          self.uploaderObjectFileUploaded(up, file, self)
        },
        UploadComplete: function (up, file) {
          self.uploaderObjectUploadComplete(up, file, self)
        },
        UploadFile: function (up, file) {
          self.uploaderObjectUploadFile(up, file, self)
        },
        Error: function (up, err) {
          if (err.code === -601) {
            self.toastr.showError(
              err.message + '\nSelected file format is not supported.'
            )
          } else if (err.code === -600) {
            self.toastr.showError(
              'Error: Multiple file with different extension is selected' +
                '\nAll selected file format is not supported.'
            )
          } else {
            self.toastr.showError('\nError #' + err.code + ': ' + err.message)
          }
        }
      }
    })

    return uploader
  }

  /**
   * Finds out the invalid file format and report to the end user to select valid file.
   */
  private readonly fileExtensionValidation = () => {
    // supported file formats.
    const uploadSupportedFileFormat = `zip|zipx|rar|7z|gz|lzh|obd|cab|tar|dbx|dxl|mbox|ns2|nsf|ost|pst|olm|ISO|E\\d+|L\\d+|${this.forensicImageFileFormat}`

    const filePattern = new RegExp(`(${uploadSupportedFileFormat})$`, 'i')

    // see if we have the file which is unsupported format
    const invalidFound = this.mUploader.files.find(
      (f) => !filePattern.test(f.name.split('.').pop())
    )
    // if found, remove from `plupload`
    const index = this.mUploader.files.findIndex(
      (a) => a.name === invalidFound?.name
    )
    if (index > -1) {
      this.mUploader.removeFile(this.mUploader.files[index])
    }

    return invalidFound ?? false
  }

  uploaderObjectFilesAdded(up, files, self) {
    // do not add invalid files
    if (this.fileExtensionValidation()) {
      self.toastr.showError('Selected file format is not supported')
      return false
    }

    const tempFileArray: AddedFilesList[] = []

    plupload.each(files, function (file) {
      const fileInfo: AddedFilesList = {
        FileId: file.id,
        FileName: file.name,
        FileSizeFormatted: self.formatBytes(file.size, 2),
        FileSize: file.size,
        CustodianName: '',
        IsStructured: self.isImportRepair > 0 ? true : false,
        mediaSourceType: MediaSourceType.SOURCE_FILE,
        FullName: file.name,
        Extension: self.getFileExtension(file.name),
        Name: file.name,
        IsForensicImageMultipart: 'N',
        MediaName: '',
        Password: ''
      }
      tempFileArray.push(fileInfo)
    })
    if (self.addedFilesList) {
      self.addedFilesList = self.addedFilesList.concat(tempFileArray)
    } else {
      self.addedFilesList = tempFileArray
    }

    const editingFile = self.processingFailedFiles.filter(
      (file: ProcessingFailedFileModel) =>
        file.fileId === FileRepairPageComponent.editingFileId
    )

    if (editingFile?.length > 0) {
      editingFile[0].newFilePath = files[0].name
      editingFile[0].newFileUploadId = files[0].id
    }

    return true
  }

  uploaderObjectBeforeUpload(up, file, self) {
    file.IsForensicImageMultipart = file.IsForensicImageMultipart
      ? file.IsForensicImageMultipart
      : 'false'

    $.ajax({
      type: 'POST',
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
      url:
        self.configService.getVodBaseUrl() +
        '/VodWebService.asmx/InitializeUpload',
      data:
        '{UploadingFileID:"' +
        file.id +
        '" ,uploadToProjectLoc:"' +
        localStorage.getItem('UploadInProjectLocation') +
        '",IsForensicImageMultipart:"' +
        file.IsForensicImageMultipart +
        '"}',
      timeout: 1000 * 600,
      async: false,
      success: function (data) {
        if (data.d !== 'PASS') {
          self.toastr.showError(data.d)
        }
        //up.start()
      },
      error: function (data) {
        self.toastr.showError(
          'Failed to insert uploaded file information into the table'
        )
      }
    })
  }

  uploaderObjectUploadProgress(up, file, self) {
    if (document.getElementById(file.id + '-progressbar')) {
      self.currentlyUploadingId = file.id
      self.currentFileUploadProgress = file.percent
      document.getElementById(file.id + '-progress-digit').textContent =
        file.percent.toString() + '%'
    }
  }

  uploaderObjectFileUploaded(up, file, self) {
    $.ajax({
      type: 'POST',
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
      url:
        self.configService.getVodBaseUrl() +
        '/VodWebService.asmx/LoadUploadedFileToSessionAndTable',
      data:
        '{Filename:"' +
        file.name +
        '" ,filesize:"' +
        file.size +
        '" ,fileid:"' +
        file.id +
        '" ,uploadToProjectLoc:"' +
        'false' +
        '"}',
      timeout: 1000 * 600,
      async: false,
      success: function (data) {
        try {
          const recentUploadedFile = self.processingFailedFiles.filter(
            (repairFile) => repairFile.newFileUploadId === file.id
          )

          self.filesUploaded = self.filesUploaded.concat(recentUploadedFile)
        } catch (e) {
          console.error('error', e)
        }

        if (data.d === null) {
          self.toastr.showError(
            'Failed to insert uploaded file information into the project'
          )
        }
      },
      error: function (data) {
        self.toastr.showError(
          'Failed to insert uploaded file information into the table'
        )
      }
    })

    //upload another file in queue
    this.uploadNext()
  }

  uploaderObjectUploadComplete(up, file, self) {
    $.ajax({
      type: 'POST',
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
      url:
        self.configService.getVodBaseUrl() +
        '/VodWebService.asmx/ClearTimeStamp',
      data: '{UploadingFileID:"' + file.id + '"}',
      timeout: 1000 * 600,
      async: false
    })
    if (self.currentlyUploadingId === file.id) {
      self.currentlyUploadingId = ''
    }

    self.submitRepairRequest()
  }

  /**
   * binded event for PlUpload's UploadFile event to implement retry inverval for chunck retry
   * @param up
   * @param file
   * @param self
   */
  uploaderObjectUploadFile(up, file, self): void {
    let url = up.settings.url
    const chunkSize = up.settings.chunk_size
    let retries = up.settings.max_retries
    const features = up.features
    let offset = 0
    let blob

    const runtimeOptions = {
      runtime_order: up.settings.runtimes,
      required_caps: up.settings.required_features,
      preferred_caps: up.preferred_caps,
      swf_url: up.settings.flash_swf_url,
      xap_url: up.settings.silverlight_xap_url
    }

    if (file.loaded) {
      offset = file.loaded = chunkSize
        ? chunkSize * Math.floor(file.loaded / chunkSize)
        : 0
    }

    function handleError() {
      if (retries-- > 0) {
        const retryInterval =
          (up.settings.max_retries - retries) *
          up.settings.max_retries_time *
          1000

        _.delay(uploadNextChunk, retryInterval)
      } else {
        // reset all progress
        file.loaded = offset

        self.toastr.showError(
          '\nError #' +
            plupload.HTTP_ERROR +
            ': ' +
            plupload.translate('HTTP Error')
        )
      }
    }

    function uploadNextChunk() {
      let chunkBlob, curChunkSize
      const args = {
        name: '',
        chunk: 0,
        chunks: 0,
        offset: 0,
        total: 0
      }

      // make sure that file wasn't cancelled and upload is not stopped in general
      if (file.status !== plupload.UPLOADING || up.state === plupload.STOPPED) {
        return
      }

      // send additional 'name' parameter only if required
      if (up.settings.send_file_name) {
        args['name'] = file.target_name || file.name
      }

      if (chunkSize && features.chunks && blob.size > chunkSize) {
        curChunkSize = Math.min(chunkSize, blob.size - offset)
        chunkBlob = blob.slice(offset, offset + curChunkSize)
      } else {
        curChunkSize = blob.size
        chunkBlob = blob
      }

      // If chunking is enabled add corresponding args, no matter if file is bigger than chunk or smaller
      if (chunkSize && features.chunks) {
        // Setup query string arguments
        if (up.settings.send_chunk_number) {
          args['chunk'] = Math.ceil(offset / chunkSize)
          args['chunks'] = Math.ceil(blob.size / chunkSize)
        } else {
          // keep support for experimental chunk format, just in case
          args['offset'] = offset
          args['total'] = blob.size
        }
      }

      if (up.trigger('BeforeChunkUpload', file, args, chunkBlob, offset)) {
        uploadChunk(args, chunkBlob, curChunkSize)
      }
    }

    function uploadChunk(args, chunkBlob, curChunkSize) {
      let formData
      const xhr = new plupload.moxie.xhr.XMLHttpRequest()

      // Do we have upload progress support
      if (xhr.upload) {
        xhr.upload.onprogress = function (e) {
          file.loaded = Math.min(file.size, offset + e.loaded)
          up.trigger('UploadProgress', file)
        }
      }

      xhr.onload = function () {
        // check if upload made itself through
        if (xhr.status < 200 || xhr.status >= 400) {
          handleError()
          return
        }

        // reset the counter
        retries = up.settings.max_retries

        // Handle chunk response
        if (curChunkSize < blob.size) {
          chunkBlob.destroy()

          offset += curChunkSize
          file.loaded = Math.min(offset, blob.size)

          up.trigger('ChunkUploaded', file, {
            offset: file.loaded,
            total: blob.size,
            response: xhr.responseText,
            status: xhr.status,
            responseHeaders: xhr.getAllResponseHeaders()
          })

          // stock Android browser doesn't fire upload progress events, but in chunking mode we can fake them
          if (plupload.ua.browser === 'Android Browser') {
            up.trigger('UploadProgress', file)
          }
        } else {
          file.loaded = file.size
        }

        // Free memory
        chunkBlob = formData = null

        // Check if file is uploaded
        if (!offset || offset >= blob.size) {
          // If file was modified, destory the copy
          if (file.size != file.origSize) {
            blob.destroy()
            blob = null
          }
          up.trigger('UploadProgress', file)

          file.status = plupload.DONE
          file.completeTimestamp = +new Date()
          self.uploaderObjectFileUploaded(up, file, self)
        } else {
          // Still chunks left
          // run detached, otherwise event handlers interfere
          _.delay(uploadNextChunk, 1)
        }
      }

      xhr.onerror = function () {
        handleError()
      }

      xhr.onloadend = function () {
        xhr.destroy()
      }

      // Build multipart request
      if (up.settings.multipart && features.multipart) {
        xhr.open(up.settings.http_method, url, true)

        // Set custom headers
        plupload.each(up.settings.headers, function (value, name) {
          xhr.setRequestHeader(name, value)
        })

        formData = new plupload.moxie.xhr.FormData()

        // Add multipart params
        plupload.each(
          plupload.extend(args, up.settings.multipart_params),
          function (value, name) {
            formData.append(name, value)
          }
        )

        // Add file and send it
        formData.append(up.settings.file_data_name, chunkBlob)
        xhr.send(formData, runtimeOptions)
      } else {
        // if no multipart, send as binary stream
        url = plupload.buildUrl(
          up.settings.url,
          plupload.extend(args, up.settings.multipart_params)
        )

        xhr.open(up.settings.http_method, url, true)

        // Set custom headers
        plupload.each(up.settings.headers, function (value, name) {
          xhr.setRequestHeader(name, value)
        })

        // do not set Content-Type, if it was defined previously (see #1203)
        if (!xhr.hasRequestHeader('Content-Type')) {
          xhr.setRequestHeader('Content-Type', 'application/octet-stream') // Binary stream header
        }

        xhr.send(chunkBlob, runtimeOptions)
      }
    }

    blob = file.getSource()

    // Start uploading chunks
    uploadNextChunk()
  }

  /**
   * Start uploading the files
   */
  start(): void {
    if (this.mUploader.state != plupload.STARTED) {
      this.mUploader.state = plupload.STARTED
      this.mUploader.trigger('StateChanged')

      this.uploadNext()
    }
  }

  uploadNext(): void {
    let file,
      count = 0,
      i = 0

    if (this.mUploader.state == plupload.STARTED) {
      // Find first QUEUED file
      for (i = 0; i < this.mUploader.files.length; i++) {
        if (!file && this.mUploader.files[i].status == plupload.QUEUED) {
          file = this.mUploader.files[i]
          if (this.mUploader.trigger('BeforeUpload', file)) {
            file.status = plupload.UPLOADING
            //this.mUploader.trigger('UploadFile', file)
            this.uploaderObjectUploadFile(this.mUploader, file, this)
          }
        } else {
          count++
        }
      }

      // All files are DONE or FAILED
      if (count == this.mUploader.files.length) {
        if (this.mUploader.state !== plupload.STOPPED) {
          this.mUploader.state = plupload.STOPPED
          this.mUploader.trigger('StateChanged')
        }
        this.mUploader.trigger('UploadComplete', this.mUploader.files)
      }
    }
  }
  //#endregion upload related functions

  onRepairItemSelectionChanged(e): void {
    this.selectedRepairItemKeys = e.selectedRowKeys
    if (e.currentDeselectedRowKeys) {
      e.currentDeselectedRowKeys.forEach((fieldKey) => {
        const fieldInfo = this.processingFailedFiles.find(
          (x) => x.fileId === fieldKey
        )
        if (fieldInfo) {
          if (+fieldKey === +this.importId) {
            e.component.selectRows([fieldKey])
          }
        }
      })
    }
  }

  onEditorPreparing(e): void {
    if (e.parentType === 'dataRow') {
      if (this.isImportRepair) {
        e.editorOptions.disabled = +e.row.data.fileId === +this.importId
        if (e.dataField === 'repairMethod') {
          e.editorOptions.disabled = false
        }
      }
      this.cdr.markForCheck()
    }
  }
}
