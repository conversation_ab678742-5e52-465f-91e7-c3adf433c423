<div>
  <div class="case-container">
    <form [formGroup]="caseForm" (ngSubmit)="onCreateCase()">
      <fieldset [disabled]="showSpinner">
        <div class="bg-white">
          <div>
            <!-- <h6 class="text-uppercase block-title-text">Case Details</h6> -->
            <h6 class="text-uppercase block-title-text">
              Case {{ titleText }} Details
            </h6>
            <div class="row col-sm-spacing">
              <div class="col-md-4" [hidden]="isTemplateRelated">
                <label for="">
                  Case Name <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="caseName"
                  placeholder="Type Case Name"
                />
              </div>
              <div class="col-md-4" [hidden]="isEdit || isTemplateRelated">
                <label for="" [hidden]="isEdit || isTemplateRelated">
                  Case Template
                </label>
                <ng-select
                  id="saved-search-selection"
                  formControlName="projectTemplateId"
                  [items]="projectTemplates"
                  [loading]="isProjectTemplateLoading"
                  [ngStyle]="{ width: '100%' }"
                  [multiple]="false"
                  [clearable]="false"
                  [searchable]="true"
                  [closeOnSelect]="true"
                  [bindValue]="'Id'"
                  placeholder="Select Project Template"
                >
                  <ng-template ng-label-tmp let-item="item" let-clear="clear">
                    <span class="ng-value-label">{{ item.Name }}</span>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item">
                    <span>{{ item.Name }}</span>
                  </ng-template>
                </ng-select>
              </div>

              <div
                class="col-md-2 view-link"
                [hidden]="isEdit || isTemplateRelated"
              >
                <span
                  class="block-title-text"
                  title="View Template Details"
                  (click)="openExportFieldDetailsPreviewModal()"
                >
                  <i class="fa fa-question-circle r-red fa-2x"></i>
                </span>
              </div>
              <!--TEmplate DEtails-->

              <div class="col-md-4" [hidden]="!isTemplateRelated">
                <label for="">
                  Template Name <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="caseName"
                  placeholder="Type Template Name"
                />
              </div>
              <div class="col-md-4" [hidden]="!isTemplateRelated">
                <label for="">Template Note</label>
                <textarea
                  class="form-control"
                  rows="3"
                  required
                  formControlName="templateNote"
                ></textarea>
              </div>

              <div class="col-md-4" [hidden]="isTemplateRelated">
                <label for="">Client Matter Number</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="clientMatterNumber"
                  placeholder="Type Client Matter Number"
                />
              </div>
              <div class="col-md-4">
                <label for="">Time Zone</label>

                <select
                  id="timeZone"
                  class="form-control"
                  formControlName="timeZone"
                >
                  <option
                    *ngFor="let timezone of timeZoneList; index as i"
                    [value]="timezone.TzTimeZone"
                  >
                    {{ timezone.DisplayName }}
                  </option>
                </select>
              </div>

              <div class="col-md-4">
                <label for="search-duplicate">Search Duplicate Option</label>
                <mat-select
                  id="search-duplicate"
                  class="form-control"
                  formControlName="searchDupOption"
                >
                  <mat-option
                    class="wrap-text"
                    matTooltipPosition="above"
                    matTooltipClass="mx-1 my-1 px-1 bg-white text-dark"
                    [matTooltipShowDelay]="400"
                    [matTooltip]="opt.option"
                    *ngFor="let opt of searchOptions; index as i"
                    [value]="opt.id"
                  >
                    {{ opt.option }}
                  </mat-option>
                </mat-select>
              </div>
              <div class="col-md-4" [hidden]="isTemplateRelated">
                <label for="internal-domain">Internal Domain</label>
                <dx-tag-box
                  [items]="internalDomains"
                  [value]="selectedInternalDomains"
                  displayExpr="name"
                  valueExpr="id"
                  placeholder="Type Internal Domains"
                  [showSelectionControls]="true"
                  formControlName="internalDomain"
                  (onMultiTagPreparing)="onMultiTagPreparing($event)"
                  (onCustomItemCreating)="onCustomTagCreating($event)"
                  [maxDisplayedTags]="3"
                  [acceptCustomValue]="true"
                ></dx-tag-box>
              </div>
              <div class="col-8" *ngIf="enableEdaiEcaFeature">
                <app-edai-eca
                  [parentForm]="caseForm"
                  [isEditMode]="isEdit"
                ></app-edai-eca>
              </div>
            </div>
          </div>

          <!-- <mat-expansion-panel #adv
                                 [hideToggle]="true"
                                 [class]="'w-100 shadow-none bg-transparent more-options'"
            > -->

          <div class="more-option">
            <h6 class="block-title-text text-uppercase">More Options</h6>

            <mat-accordion
              displayMode="flat"
              [multi]="true"
              #matAccordion="matAccordion"
            >
              <mat-expansion-panel
                [disabled]="isProjectTemplateLoading"
                class="panel-items-right mat-elevation-z0 position-relative"
              >
                <mat-expansion-panel-header>
                  <mat-panel-title class="align-items-center w-25">
                    <div class="title-dot"></div>
                    <span>
                      <span class="font-weight-bold">File Filters</span></span
                    >
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <div class="padding-15">
                  <div class="row">
                    <!--Filter System Type start-->
                    <div class="col-md-4 border-right">
                      <div
                        *ngIf="isTOALicensed && !isTemplateRelated"
                        class="mb-4"
                      >
                        <div class="mb-2 block-title-text">
                          Native File Options
                          <!-- Sushil <i class="fa fa-question-circle"></i> -->
                        </div>
                        <div class="mb-4">
                          <div class="custom-control custom-radio mr-sm-2">
                            <input
                              id="r1"
                              class="custom-control-input"
                              type="radio"
                              [value]="true"
                              (change)="changeNativeFileOption($event)"
                              formControlName="nativeFileHandling"
                              [attr.disabled]="disableExtractNativeOption()"
                            />
                            <label
                              class="custom-control-label"
                              [ngClass]="{
                                'custom-control-label-disabled':
                                  disableExtractNativeOption()
                              }"
                              for="r1"
                            >
                              Extract Native
                            </label>
                          </div>
                          <div class="custom-control custom-radio mr-sm-2">
                            <input
                              id="r2"
                              class="custom-control-input"
                              type="radio"
                              [value]="false"
                              (change)="changeNativeFileOption($event)"
                              formControlName="nativeFileHandling"
                              [attr.disabled]="disableECAOption()"
                            />
                            <label
                              class="custom-control-label"
                              [ngClass]="{
                                'custom-control-label-disabled':
                                  disableECAOption()
                              }"
                              for="r2"
                              title="Task synchronizer needs to be installed for TOA(Text Only Analysis) feature to function properly."
                            >
                              TOA(Text Only Analysis)
                              <i class="fa fa-info-circle"></i>
                            </label>
                          </div>
                        </div>
                      </div>

                      <div class="block-title-text">Filter System Type</div>
                      <div class="custom-control custom-checkbox mr-sm-2">
                        <div class="mb-2">
                          <input
                            formControlName="deNistingFlag"
                            class="custom-control-input"
                            type="checkbox"
                            id="DeNistingFlag"
                          />
                          <label
                            class="custom-control-label"
                            for="DeNistingFlag"
                            >Exclude NIST List(DE-NIST)
                          </label>
                        </div>

                        <div>
                          <input
                            formControlName="fileTypeFlag"
                            class="custom-control-input"
                            type="checkbox"
                            id="FileTypeFlag"
                          />
                          <label
                            class="custom-control-label"
                            for="FileTypeFlag"
                            title="Following file types are filtered as system file type
                                1.EXECUTABLE
                                2.COM
                                3.WIN_EXPLORERCMD
                                4.JAVACLASS
                                5.MAC.DS_Store and AppleDouble Files
                                6.Thumbs.db"
                            >Exclude File Types(Venio identifies and filters
                            system files using file header)
                            <i class="fa fa-info-circle"></i
                          ></label>
                        </div>
                      </div>
                    </div>

                    <!--Filter System Type END-->
                    <div class="col-md-8">
                      <!--Filter Duplicate start-->
                      <div class="mb-2 block-title-text">
                        Filter Duplicate Files
                      </div>
                      <div class="row">
                        <div class="col-md-6">
                          <div class="mb-2">
                            Choose Hash Algorithm for deduplication
                          </div>
                          <div class="">
                            <div class="custom-control custom-radio mr-sm-2">
                              <input
                                id="sha1"
                                class="custom-control-input"
                                type="radio"
                                [value]="1"
                                name="filterDuplicateFile"
                                formControlName="filterDuplicateFile"
                              />
                              <label class="custom-control-label" for="sha1">
                                SHA1
                              </label>
                            </div>
                            <div class="custom-control custom-radio mr-sm-2">
                              <input
                                id="md5"
                                class="custom-control-input"
                                type="radio"
                                [value]="0"
                                name="filterDuplicateFile"
                                formControlName="filterDuplicateFile"
                              />
                              <label class="custom-control-label" for="md5">
                                MD5
                              </label>
                            </div>
                          </div>
                          <div class="">
                            <div class="custom-control custom-checkbox mr-sm-2">
                              <input
                                class="custom-control-input"
                                type="checkbox"
                                id="store"
                                [value]="0"
                                formControlName="hasSecondary"
                              />
                              <label class="custom-control-label" for="store">
                                Compute and store {{ SecondaryHash }} hash value
                                as secondary hash value
                              </label>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <p class="text-primary">Advanced Hash Setting</p>
                          <div class="mb-2">List of Hash Fields</div>
                          <div>
                            <dx-data-grid
                              #hashFieldGrid
                              [dataSource]="hashOptions"
                              keyExpr="HashId"
                              [showBorders]="true"
                              [showRowLines]="false"
                              [disabled]="isEdit && !this.isTemplateEditModule"
                              [height]="190"
                            >
                              <dxo-selection
                                mode="multiple"
                                showCheckBoxesMode="always"
                              ></dxo-selection>
                              <dxi-column
                                dataField="HashName"
                                caption="Hash Fields"
                                [allowHeaderFiltering]="false"
                                [allowSearch]="false"
                                [allowFiltering]="false"
                                [allowSorting]="false"
                                [allowEditing]="false"
                              ></dxi-column>
                            </dx-data-grid>
                          </div>
                          <!-- <mat-accordion class="case-adv-option">
                                    <mat-expansion-panel hideToggle>
                                      <mat-expansion-panel-header>
                                        <mat-panel-title>
                                          <i class="fa fa-caret-down mr-3"></i>

                                        </mat-panel-title>
                                      </mat-expansion-panel-header>

                                    </mat-expansion-panel>
                                  </mat-accordion> -->
                        </div>
                      </div>
                      <!--Filter Duplicate end-->
                    </div>
                  </div>

                  <div>
                    <app-file-extension-filter
                      [parent]="caseForm"
                      [isEdit]="isEdit"
                    ></app-file-extension-filter>
                  </div>

                  <app-settings-container
                    class="col-12"
                    [patchFormValue]="formData"
                    [componentNamesToLoad]="['FILE_TYPE_SETTING']"
                    (formValueChanged)="changedFormValue($event)"
                  ></app-settings-container>

                  <div>
                    <app-date-filter
                      [parent]="caseForm"
                      [isEdit]="isEdit"
                      (dateFilterItemEvent)="getDateFilterList($event)"
                    ></app-date-filter>
                  </div>
                  <div>
                    <app-embedded-item-filter
                      [parent]="caseForm"
                      [isEdit]="isEdit"
                    ></app-embedded-item-filter>
                  </div>
                </div>
              </mat-expansion-panel>
              <mat-expansion-panel
                [disabled]="isProjectTemplateLoading"
                class="panel-items-right mat-elevation-z0 position-relative"
              >
                <mat-expansion-panel-header>
                  <mat-panel-title class="align-items-center w-25">
                    <div class="title-dot"></div>
                    <span>
                      <span class="font-weight-bold">Image settings</span></span
                    >
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <div class="padding-15">
                  <div>
                    <div class="block-title-text">Image Conversion Options</div>
                    <div class="row mb-4">
                      <div class="col-md-8">
                        <div class="checkbox-container">
                          <label class="checkbox-wrap">
                            <input
                              type="checkbox"
                              formControlName="enableImage"
                            />Enable Image
                            <span class="check-mark"></span>
                          </label>
                        </div>
                        <div class="pl-25 d-flex mb-2" *ngIf="enablePDFImaging">
                          <label for="" class="mr-3">Image Type</label>
                          <div class="custom-control custom-radio mr-sm-2">
                            <input
                              id="tiff"
                              class="custom-control-input"
                              type="radio"
                              [value]="true"
                              name="imageTypeTiff"
                              formControlName="imageTypeTiff"
                            />
                            <label class="custom-control-label" for="tiff">
                              Tiff
                            </label>
                          </div>
                          <div class="custom-control custom-radio mr-sm-2">
                            <input
                              id="pdf"
                              class="custom-control-input"
                              type="radio"
                              [value]="false"
                              name="imageTypeTiff"
                              formControlName="imageTypeTiff"
                            />
                            <label class="custom-control-label" for="pdf">
                              PDF
                            </label>
                          </div>
                        </div>
                        <div id="grid">
                          <dx-data-grid
                            #fileTypeGrid
                            id="gridContainerImageSetting"
                            [dataSource]="filetypePageLimitList"
                            keyExpr="FileTypeGroupID"
                            [showRowLines]="false"
                            [showBorders]="true"
                            [disabled]="disableImageOptions"
                            style="height: 40vh"
                          >
                            <dxo-paging [enabled]="false"></dxo-paging>
                            <dxi-column
                              dataField="FileTypeGroup"
                              caption="File Type Class"
                            ></dxi-column>
                            <dxi-column
                              caption="Page Limit"
                              dataField="PageLimit"
                              cellTemplate="pageLimitTemplate"
                            ></dxi-column>
                            <div *dxTemplate="let item of 'pageLimitTemplate'">
                              <!-- <mat-select [ngModelOptions]="{standalone: true}"  [(ngModel)]="item.data.PageLimit"  placeholder="Max Pages"  (selectionChange)="GetSelectedPageLimit($event,item,$event.value)">
                                        <mat-option *ngFor="let pagelimit of pageLimitdb" [value]="pagelimit">{{ pagelimit }}</mat-option>
                                        <mat-option [value]="-1">Max Pages</mat-option>
                                        <mat-option [value]="0">All Pages</mat-option>
                                        <mat-option [value]="10">10</mat-option>
                                        <mat-option [value]="100">100</mat-option>
                                        <mat-option [value]="200">200</mat-option>
                                        <mat-option [value]="300">300</mat-option>
                                        <mat-option [value]="400">400</mat-option>
                                        <mat-option [value]="500">500</mat-option>
                                        <mat-option [value]="1000">1000</mat-option>
                                        <mat-option [value]="2000">2000</mat-option>
                                      </mat-select> -->

                              <input
                                [ngModelOptions]="{ standalone: true }"
                                [(ngModel)]="item.data.PageLimit"
                                type="text"
                                [id]="
                                  'imagePageLimit' + item.data.FileTypeGroupID
                                "
                                matInput
                                [matAutocomplete]="autoPageLimit"
                                class="form-control"
                                (keypress)="validateDefaultMaxPage($event)"
                                (ngModelChange)="
                                  GetSelectedPageLimit(
                                    $event,
                                    item,
                                    $event.value
                                  )
                                "
                              />
                              <mat-autocomplete
                                autoActiveFirstOption
                                #autoPageLimit="matAutocomplete"
                              >
                                <mat-option
                                  *ngFor="
                                    let o of ['Max Pages', 'All Pages'].concat(
                                      pageLimitdb
                                    );
                                    index as index
                                  "
                                  [value]="o"
                                >
                                  {{ o }}
                                </mat-option>
                              </mat-autocomplete>
                            </div>
                            <dxi-column
                              caption="Color Conversion"
                              dataField="TiffColorOption"
                              cellTemplate="colorConversion"
                            ></dxi-column>
                            <div *dxTemplate="let c1 of 'colorConversion'">
                              <mat-select
                                [ngModelOptions]="{ standalone: true }"
                                [(ngModel)]="c1.data.TiffColorOption"
                                placeholder="Default"
                                [id]="
                                  'colorConversion' + c1.data.FileTypeGroupID
                                "
                                (ngModelChange)="SetPageLimitDefaultValue(c1)"
                              >
                                <mat-option
                                  [value]="0"
                                  *ngIf="
                                    c1.data.FileTypeGroupID !== 9 &&
                                    c1.data.FileTypeGroupID !== 29
                                  "
                                  >Default</mat-option
                                ><!--Donot show option if (xml,text) 9 is xml,29 is text-->
                                <mat-option [value]="1"
                                  >Black and White</mat-option
                                >
                                <mat-option
                                  [value]="2"
                                  *ngIf="
                                    c1.data.FileTypeGroupID !== 9 &&
                                    c1.data.FileTypeGroupID !== 29
                                  "
                                  >Color</mat-option
                                ><!--Donot show option if (xml,text) 9 is xml,29 is text-->
                                <mat-option [value]="3">Grayscale</mat-option>
                                <mat-option
                                  [value]="4"
                                  *ngIf="
                                    c1.data.FileTypeGroupID !== 9 &&
                                    c1.data.FileTypeGroupID !== 29
                                  "
                                  >Color for color</mat-option
                                ><!--Donot show option if (xml,text) 9 is xml,29 is text-->
                              </mat-select>
                            </div>
                            <dxi-column
                              caption="Time Out Period (in min)"
                              dataField="TiffTimeOutPeriod"
                              cellTemplate="timeoutPeriod"
                            >
                            </dxi-column>
                            <div *dxTemplate="let item of 'timeoutPeriod'">
                              <input
                                type="number"
                                class="form-control"
                                min="1"
                                max="999"
                                [id]="'timeout' + item.data.FileTypeGroupID"
                                [ngModelOptions]="{ standalone: true }"
                                [placeholder]="placeholderTextTimeout"
                                [(ngModel)]="item.data.TiffTimeOutPeriod"
                                (keypress)="
                                  validateTimeout(
                                    $event,
                                    item.data.FileTypeGroupID,
                                    item.data.TiffTimeOutPeriod
                                  )
                                "
                                (ngModelChange)="
                                  checkMaxMinTimeout(
                                    $event,
                                    item.data.FileTypeGroupID,
                                    item.data.TiffTimeOutPeriod,
                                    item
                                  )
                                "
                              />
                            </div>
                          </dx-data-grid>
                          <div class="d-flex grid-footer">
                            <div class="max-pages mr-3">
                              <label for="">Max Pages</label>
                              <!-- <mat-select class="form-control" formControlName="defaultMaxPage"> -->
                              <!-- <mat-select class="form-control" >
                                      <mat-option [value]="0">All Pages</mat-option>
                                      <mat-option [value]="1">1</mat-option>
                                      <mat-option [value]="10">10</mat-option>
                                      <mat-option [value]="100">100</mat-option>
                                      <mat-option [value]="500">500</mat-option>
                                      <mat-option [value]="1000">1000</mat-option>
                                    </mat-select> -->
                              <input
                                type="text"
                                id="defaultMaxPage"
                                matInput
                                [matAutocomplete]="auto"
                                class="form-control"
                                formControlName="defaultMaxPage"
                                (keypress)="validateDefaultMaxPage($event)"
                              />
                              <mat-autocomplete
                                autoActiveFirstOption
                                #auto="matAutocomplete"
                              >
                                <mat-option
                                  *ngFor="
                                    let o of ['All Pages'].concat(pageOptions);
                                    index as index
                                  "
                                  [value]="o"
                                >
                                  {{ o }}
                                </mat-option>
                              </mat-autocomplete>
                            </div>
                            <div class="max-pages mr-3 color-conversion">
                              <label for="cbodefaultTiffColorOption"
                                >Default Color Conversion</label
                              >
                              <select
                                id="cbodefaultTiffColorOption"
                                class="form-control"
                                formControlName="defaultTiffColorOption"
                                placeholder="Black and White"
                              >
                                <option [value]="1">Black and White</option>
                                <option [value]="2">Color</option>
                                <option [value]="3">Grayscale</option>
                                <option [value]="4">Color for color</option>
                              </select>
                            </div>
                            <div class="max-pages">
                              <label for="">Default Timeout (in min)</label>
                              <input
                                id="defaultTimeOutForImage"
                                type="number"
                                class="form-control"
                                min="1"
                                max="999"
                                formControlName="defaultTimeOut"
                                (keypress)="validateDefaultTimeout($event)"
                                (blur)="onBlurCheckMinMaxValue()"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <p class="text-primary">Advanced Image Option</p>
                        <div class="mb-2">Apply System Bates Number Option</div>
                        <div class="custom-control custom-checkbox mr-sm-2">
                          <input
                            class="custom-control-input"
                            type="checkbox"
                            formControlName="generateBates"
                            id="generateBates"
                          />
                          <label
                            class="custom-control-label"
                            for="generateBates"
                            >Generate bates number for generated images</label
                          >
                        </div>
                        <div class="pl-25 generate-wrap">
                          <div class="row">
                            <label for="" class="col-md-2">Prefix</label>
                            <div class="col-md-6 d-flex">
                              <mat-select
                                class="form-control col-md-2 mr-2"
                                formControlName="prefixList"
                              >
                                <mat-option value="TEXT">TEXT</mat-option>
                                <mat-option value="FIELD">FIELD</mat-option>
                              </mat-select>
                              <input
                                type="text"
                                class="form-control mr-2"
                                formControlName="prefixText"
                                [hidden]="isTextHidden"
                              />
                              <mat-select
                                placeholder="DOCUMENT_UNIQUE_IDENTIFIER"
                                class="form-control col-md-3"
                                formControlName="prefixField"
                                [hidden]="isFieldHidden"
                              >
                                <!-- <mat-option *ngFor="let customField of customFielddb" [value]="customField" [selected]="customField==='DOCUMENT_UNIQUE_IDENTIFIER'">{{ customField }}</mat-option> -->
                                <mat-option
                                  value="DOCUMENT_UNIQUE_IDENTIFIER"
                                  selected="true"
                                  >DOCUMENT_UNIQUE_IDENTIFIER</mat-option
                                >
                                <mat-option value="INTERNAL_FILE_ID"
                                  >INTERNAL_FILE_ID</mat-option
                                >
                                <mat-option value="ORIGINAL_FILE_NAME"
                                  >ORIGINAL_FILE_NAME</mat-option
                                >
                                <mat-option value="SYSTEM_BATES"
                                  >SYSTEM_BATES</mat-option
                                >
                              </mat-select>
                            </div>
                          </div>
                          <div class="row">
                            <label for="Start number" class="col-md-2"
                              >Start No.</label
                            >
                            <div class="col-md-6">
                              <input
                                type="number"
                                class="form-control"
                                formControlName="startNumber"
                                min="1"
                                max="2147483647"
                                (keypress)="validateStartNumber($event)"
                              />
                            </div>
                          </div>
                          <div class="row">
                            <label for="" class="col-md-2">Padding</label>
                            <div class="col-md-6">
                              <input
                                type="number"
                                class="form-control"
                                formControlName="padding"
                                min="1"
                                max="99"
                                (keypress)="validatePadding($event)"
                              />
                            </div>
                          </div>
                          <div class="custom-control custom-radio mr-sm-2">
                            <input
                              id="without-branch"
                              class="custom-control-input"
                              type="radio"
                              [value]="false"
                              name="brandingBates"
                              formControlName="brandingBates"
                            />
                            <label
                              class="custom-control-label"
                              for="without-branch"
                            >
                              Generates Image without branding bates number
                            </label>
                          </div>
                          <div class="custom-control custom-radio mr-sm-2">
                            <input
                              id="with-branch"
                              class="custom-control-input"
                              type="radio"
                              [value]="true"
                              name="brandingBates"
                              formControlName="brandingBates"
                            />
                            <label
                              class="custom-control-label"
                              for="with-branch"
                            >
                              Generates Image with branding bates number
                            </label>
                          </div>
                        </div>
                        <div class="custom-control custom-checkbox mr-sm-2">
                          <input
                            class="custom-control-input"
                            type="checkbox"
                            formControlName="autoGenerateImgForIngestedFile"
                            id="chkBoxAutoGen"
                          />
                          <label
                            class="custom-control-label"
                            for="chkBoxAutoGen"
                            >Auto generate images for ingested files</label
                          >
                        </div>
                        <div class="custom-control custom-checkbox mr-sm-2">
                          <input
                            class="custom-control-input"
                            type="checkbox"
                            formControlName="notifyAfterImgGenComplete"
                            id="chkBoxAutoGenImg"
                          />
                          <label
                            class="custom-control-label"
                            for="chkBoxAutoGenImg"
                            title="If this option is checked, processing will be marked as completed only when tiffing jobs are completed."
                          >
                            Notify me after image generation is complete
                            <i class="fa fa-info-circle"></i>
                          </label>
                        </div>
                      </div>
                    </div>

                    <!-- <mat-accordion class="case-adv-option" id="matAcc" [hidden]="!isNativeFileOptionOnlyIndex && disableImageOptions">
                            <mat-expansion-panel hideToggle>
                              <mat-expansion-panel-header>
                                <mat-panel-title>
                                  <i class="fa fa-caret-down mr-3"></i>

                                </mat-panel-title>
                              </mat-expansion-panel-header>


                            </mat-expansion-panel>
                          </mat-accordion> -->
                    <div class="row">
                      <app-settings-container
                        class="col-md-6"
                        [patchFormValue]="formData"
                        [componentNamesToLoad]="['IMAGE_FILE_TYPE_SETTING']"
                        (formValueChanged)="changedFormValue($event)"
                      ></app-settings-container>

                      <app-image-file-extension
                        class="col-md-6"
                        [parent]="caseForm"
                        [isEdit]="isEdit"
                        (imageFileExtensionItemEvent)="
                          getImageFileExtensionList($event)
                        "
                      ></app-image-file-extension>
                    </div>
                  </div>
                  <div>
                    <div class="block-title-text">OCR Setting</div>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <!-- ocr setting start -->
                        <div>
                          <div class="checkbox-container">
                            <label class="checkbox-wrap">
                              <input
                                type="checkbox"
                                formControlName="allowOCR"
                                id="allowOCR"
                              />Enable OCR
                              <span class="check-mark"></span>
                            </label>
                          </div>
                          Select file type for OCR
                        </div>
                        <div>
                          <div>
                            <dx-data-grid
                              #ocrFileTypeGrid
                              [dataSource]="ocrFileOptions"
                              style="height: 44vh; width: 100%"
                              keyExpr="ExtNo"
                              [showBorders]="true"
                              [showRowLines]="false"
                              [disabled]="
                                disableOCROPtions &&
                                !isNativeFileOptionOnlyIndex
                              "
                            >
                              <dxo-selection
                                mode="multiple"
                                showCheckBoxesMode="always"
                              ></dxo-selection>
                              <dxi-column
                                dataField="ExtensionName"
                                caption="File Type"
                                [allowHeaderFiltering]="false"
                                [allowSearch]="false"
                                [allowFiltering]="false"
                                [allowSorting]="false"
                                [allowEditing]="false"
                              ></dxi-column>
                              <dxo-scrolling
                                showScrollbar="always"
                                mode="standard"
                              ></dxo-scrolling>
                            </dx-data-grid>
                          </div>
                        </div>
                        <div class="mt-2">
                          <label class="checkbox-wrap">
                            <input
                              type="checkbox"
                              formControlName="autoQueueOCR"
                              id="autoQueueOCR"
                            />Automatically queue documents for OCR
                            <span class="check-mark"></span>
                          </label>
                        </div>
                        <!-- ocr setting end -->
                      </div>
                      <div class="col-md-6">
                        <p class="text-primary">Advanced OCR Option</p>
                        <mat-tab-group class="ocr-setting-adv mb-2">
                          <mat-tab label="PDF">
                            <div class="checkbox-container">
                              <label class="checkbox-wrap">
                                <input
                                  type="checkbox"
                                  id="enableOCRThresholdTextOption"
                                  formControlName="enableOCRThresholdTextOption"
                                />Enable OCR for PDF document with fewer text
                                <span class="check-mark"></span>
                              </label>
                            </div>
                            <div class="pl-25">
                              <mat-radio-group
                                class="radio-btn-wrap"
                                aria-label="Select an option"
                                formControlName="oCRPDFSetting"
                              >
                                <mat-radio-button value="1">
                                  OCR PDF documents with average character per
                                  page less than
                                  <div class="mt-2 input-filed">
                                    <input
                                      type="text"
                                      class="form-control form-control-md"
                                      formControlName="oCRThresholdAverageCharacters"
                                      (keypress)="
                                        validateOCR(
                                          $event,
                                          'oCRThresholdAverageCharacters'
                                        )
                                      "
                                    />
                                    <a
                                      *ngIf="!disableOCROPtions"
                                      (click)="setMaxValue()"
                                      >Use max value</a
                                    >
                                  </div>
                                </mat-radio-button>
                                <mat-radio-button value="2">
                                  OCR PDF documents if at least one page has
                                  character less than
                                  <div class="mt-2 input-filed">
                                    <input
                                      type="text"
                                      class="form-control form-control-md"
                                      formControlName="oCRMinCharInPDfInAtleastOnePage"
                                      (keypress)="
                                        validateOCR(
                                          $event,
                                          'oCRMinCharInPDfInAtleastOnePage'
                                        )
                                      "
                                    />
                                  </div>
                                </mat-radio-button>
                              </mat-radio-group>
                              <p>
                                <small class="text-danger"
                                  >Note: Only PDF page matching set threshold
                                  will be OCR'ed and merged with extracted
                                  text.</small
                                >
                              </p>
                            </div>
                          </mat-tab>
                          <mat-tab label="Language">
                            <div>
                              <!-- OCR language options -->

                              <div class="block-title-text">
                                OCR Language Option
                              </div>
                              <div>
                                <span
                                  >This setting will be only applicable for
                                  Nuance OCR Engine</span
                                >
                              </div>

                              <div
                                class="row mt-2"
                                formArrayName="ocrLanguages"
                              >
                                <!-- let l of ocrControls(); -->
                                <div
                                  class="col-md-4 mb-1"
                                  *ngFor="
                                    let l of ocrControls();
                                    trackBy: trackByFn;
                                    let i = index
                                  "
                                >
                                  <div
                                    class="
                                      custom-control custom-checkbox
                                      mr-sm-2
                                    "
                                  >
                                    <input
                                      [formControlName]="i"
                                      (change)="onCheckboxChange($event, i)"
                                      class="custom-control-input"
                                      type="checkbox"
                                      [id]="i"
                                    />
                                    <label
                                      class="custom-control-label"
                                      [for]="i"
                                    >
                                      {{ ocrLanguages[i]?.LangName }}
                                    </label>
                                  </div>
                                </div>
                                <div class="col-12 mb-3">
                                  <div
                                    class="text-danger"
                                    *ngIf="
                                      !hasSelectedOcrLanguage() &&
                                      !disableOCROPtions
                                    "
                                  >
                                    Select at least one language
                                  </div>
                                </div>
                              </div>
                            </div>
                          </mat-tab>
                          <mat-tab label="Others">
                            <p>
                              Numbers of time to retry when failed
                              <input
                                type="number"
                                class="form-control form-control-md"
                                min="1"
                                max="10"
                                formControlName="maxOcrTryCount"
                                (keypress)="
                                  validateOCR($event, 'maxOcrTryCount')
                                "
                              />
                            </p>
                            <mat-radio-group
                              aria-label="Select an option"
                              class="radio-btn-wrap"
                              formControlName="oCROtherSetting"
                            >
                              <mat-radio-button [value]="false">
                                Discard all ocr'ed text and mark document as ocr
                                failed if one or more pages failed to OCR
                              </mat-radio-button>
                              <mat-radio-button [value]="true">
                                Index ocr'ed text (based on fulltext priority if
                                required) and mark document as having text if
                                one or more pages failed to OCR
                              </mat-radio-button>
                            </mat-radio-group>
                          </mat-tab>
                        </mat-tab-group>
                        <small class="text-danger">
                          Note: This setting will be only applicable for Nuance
                          OCR Engine
                        </small>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <app-settings-container
                      class="col-12"
                      [patchFormValue]="formData"
                      [componentNamesToLoad]="['SLIP_SHEET']"
                      (formValueChanged)="changedFormValue($event)"
                    ></app-settings-container>
                  </div>
                </div>
              </mat-expansion-panel>
              <!-- start directory panel -->
              <mat-expansion-panel
                *ngIf="showADSettings || showIDPSettings"
                [disabled]="isProjectTemplateLoading"
                class="panel-items-right mat-elevation-z0 position-relative"
              >
                <mat-expansion-panel-header>
                  <mat-panel-title class="align-items-center w-25">
                    <div class="title-dot"></div>
                    <span>
                      <span class="font-weight-bold"
                        >Directory Services - Group Mappings</span
                      ></span
                    >
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <div class="padding-15" *ngIf="showADSettings">
                  <div class="block-title-text">
                    Active Directory Group Mapping
                  </div>
                  <div>
                    <app-ad-group-mapping
                      [parent]="caseForm"
                      [isEdit]="isEdit"
                      (directoryItemEvent)="getADGroupMappingList($event)"
                    ></app-ad-group-mapping>
                  </div>
                </div>
                <div class="padding-15" *ngIf="showIDPSettings">
                  <div class="block-title-text">SAML-IDP Group Mapping</div>
                  <div>
                    <app-idp-group-mapping
                      [parent]="caseForm"
                      [isEdit]="isEdit"
                      (directoryItemEvent)="getIDPGroupMappingList($event)"
                    ></app-idp-group-mapping>
                  </div>
                </div>
              </mat-expansion-panel>
              <!-- end directory panel -->
              <!-- start processing panel -->
              <mat-expansion-panel
                [disabled]="isProjectTemplateLoading"
                class="panel-items-right mat-elevation-z0 position-relative"
              >
                <mat-expansion-panel-header>
                  <mat-panel-title class="align-items-center w-25">
                    <div class="title-dot"></div>
                    <span>
                      <span class="font-weight-bold">Processing</span></span
                    >
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <div class="padding-15">
                  <!-- Ingestion engine settings start-->
                  <div class="block-title-text" [hidden]="isTemplateRelated">
                    Ingestion Engine
                  </div>
                  <div class="mb-3" [hidden]="isTemplateRelated">
                    <div class="custom-control custom-checkbox mr-sm-2">
                      <input
                        class="custom-control-input"
                        type="checkbox"
                        formControlName="isLegacyIngestionEngine"
                        id="chkBoxLegacyIngestionEngine"
                      />
                      <label
                        class="custom-control-label"
                        for="chkBoxLegacyIngestionEngine"
                        title="For features like TOA and social media processing, the Legacy ingestion engine should not be selected."
                      >
                        Legacy <i class="fa fa-info-circle"></i>
                      </label>
                    </div>
                  </div>
                  <!-- Ingestion engine settings end -->
                  <app-ingestion-settings
                    [parent]="caseForm"
                    [isEdit]="isEdit"
                    [nativeFileHandling]="nativeFileHandling"
                    [isTemplateEditModule]="isTemplateEditModule"
                    [isTemplateRelated]="isTemplateRelated"
                    [showSocialMedia]="showSocialMedia"
                    (JobListADVItemEvent)="getJobListADV($event)"
                    (IngestionTimeItemEvent)="getTimeOutItem($event)"
                  ></app-ingestion-settings>

                  <div class="row col-lg-spacing">
                    <div class="col-md-6 border-right">
                      <!--Indexing Start-->
                      <div class="block-title-text">Indexing</div>
                      <div class="mb-4">
                        <div
                          class="custom-control custom-checkbox mr-sm-2 mb-4"
                        >
                          <div class="mb-2">
                            <input
                              formControlName="indexFullText"
                              class="custom-control-input"
                              type="checkbox"
                              id="IndexFullText"
                            />
                            <label
                              class="custom-control-label"
                              for="IndexFullText"
                              >Index Fulltext
                            </label>
                          </div>
                          <div class="pl-3 mb-2">
                            <input
                              formControlName="indexEmailHeader"
                              class="custom-control-input"
                              type="checkbox"
                              id="IndexEmailHeader"
                            />
                            <label
                              class="custom-control-label"
                              for="IndexEmailHeader"
                              title="Following email headers will be included in the fulltext index:
                                    EmailFrom
                                    EmailTo
                                    EmailCC
                                    EmailBCC
                                    EmailSubject
                                    EmailAttachments"
                              >Index Email Headers with Fulltext
                              <i class="fa fa-info-circle"></i>
                            </label>
                          </div>
                          <div>
                            <input
                              formControlName="indexMetaData"
                              class="custom-control-input"
                              type="checkbox"
                              id="IndexMetaData"
                            />
                            <label
                              class="custom-control-label"
                              for="IndexMetaData"
                              >Index MetaData
                            </label>
                          </div>
                        </div>

                        <!--start indexing adv setting-->
                        <div class="more-option">
                          <label class="block-title-text"
                            >Customize metadata for indexing</label
                          >
                          <div class="mb-2">
                            Selected metadata are indexed into 'Attributes'
                            field.This field is used when running searches with
                            Attributes or 'Fulltext and Attributes' option in
                            search.
                          </div>
                          <div>
                            <dx-data-grid
                              #indexMetaGrid
                              [dataSource]="indexDataSource"
                              keyExpr="FieldId"
                              [showBorders]="true"
                              [showRowLines]="false"
                              [height]="250"
                            >
                              <dxo-selection
                                mode="multiple"
                                showCheckBoxesMode="always"
                              ></dxo-selection>
                              <dxi-column
                                dataField="DisplayFieldName"
                                caption="Field Name"
                                [allowHeaderFiltering]="false"
                                [allowSearch]="false"
                                [allowFiltering]="false"
                                [allowSorting]="false"
                                [allowEditing]="false"
                              ></dxi-column>
                              <dxo-scrolling
                                showScrollbar="always"
                                mode="standard"
                              ></dxo-scrolling>
                              <dxo-paging [enabled]="false"></dxo-paging>
                            </dx-data-grid>
                          </div>
                        </div>
                        <!--end indexing adv setting-->
                      </div>
                      <!--Indexing end-->
                      <!-- Transcribe settings -->
                      <div class="block-title-text">Transcribe Settings</div>
                      <div>
                        <span> Select Transcribe Engine </span>
                        <div class="p-3">
                          <div class="transcribe-engine-selector">
                            <select
                              id="transcribeSettings"
                              class="form-control col-md-6"
                              formControlName="transcribeSettings"
                              #t
                              (change)="onTranscribeEngineChanged(t.value)"
                              [value]="selectedEngine"
                            >
                              <option
                                *ngFor="let engine of transcribingEngines"
                                [value]="engine"
                              >
                                {{ engine }}
                              </option>
                            </select>

                            <div
                              class="mt-3"
                              *ngIf="
                                supportedFilesArray &&
                                supportedFilesArray.length != 0
                              "
                            >
                              <span>Select file type for transcribing</span>
                              <div
                                class="custom-control custom-checkbox"
                                *ngFor="
                                  let file of supportedFilesArray;
                                  let i = index
                                "
                              >
                                <input
                                  [id]="selectedEngine + file.fileName"
                                  class="custom-control-input"
                                  type="checkbox"
                                  [value]="file"
                                  #u
                                  (change)="
                                    onSelectedFilesChange(
                                      file.fileName,
                                      $event.target['checked']
                                    )
                                  "
                                  [checked]="file.selected"
                                />
                                <label
                                  class="custom-control-label"
                                  [for]="selectedEngine + file.fileName"
                                >
                                  {{ file.fileName }}
                                </label>
                              </div>
                            </div>
                          </div>
                          <div class="pl-5">
                            <div
                              class="missing-access-key"
                              *ngIf="accessKeyMissingMessage !== ''"
                            >
                              {{ accessKeyMissingMessage }}
                            </div>
                          </div>
                        </div>
                        <div class="custom-control custom-checkbox mr-sm-2">
                          <input
                            formControlName="autoQueueTranscribe"
                            class="custom-control-input"
                            type="checkbox"
                            id="queue"
                          />
                          <label class="custom-control-label" for="queue">
                            Automatically queue document for transcribing
                          </label>
                        </div>
                      </div>
                      <!-- Transcribe settings End-->
                    </div>
                    <div class="col-md-6">
                      <!-- Start LI, EA, ET -->
                      <div class="mb-4 w-100">
                        <div class="mb-2 block-title-text">
                          Language Identification, Email Analysis and Email
                          Threading
                        </div>
                        <div class="">
                          <div class="checkbox-container">
                            <label class="checkbox-wrap">
                              <input
                                type="checkbox"
                                formControlName="languageIdentifier"
                                id="languageIdentifier"
                              />Enable Language Identification
                              <span class="check-mark"></span>
                            </label>
                          </div>
                          <div class="pl-3 checkbox-container">
                            <label class="checkbox-wrap">
                              <input
                                type="checkbox"
                                formControlName="doNotComputeLanguageIdentificationForSpreadsheets"
                                id="doNotComputeLanguageIdentificationForSpreadsheets"
                              />Compute Language Identification for Spreadsheet
                              <span class="check-mark"></span>
                            </label>
                          </div>
                          <div class="checkbox-container">
                            <label class="checkbox-wrap">
                              <input
                                type="checkbox"
                                formControlName="allowEmailAnalysis"
                                id="allowEmailAnalysis"
                              />Enable Email Analytic
                              <span class="check-mark"></span>
                            </label>
                          </div>
                          <div class="checkbox-container">
                            <label class="checkbox-wrap">
                              <input
                                type="checkbox"
                                formControlName="allowAutoLaunchEmailThreading"
                                id="allowAutoLaunchEmailThreading"
                              />Compute Email Thread Identification during
                              Ingestion
                              <span class="check-mark"></span>
                            </label>
                          </div>
                          <div class="pl-3">
                            <div class="checkbox-container">
                              <label class="checkbox-wrap">
                                <input
                                  type="checkbox"
                                  formControlName="computeInclusiveEmail"
                                  id="computeInclusiveEmail"
                                />Compute Inclusive email during Ingestion
                                <span class="check-mark"></span>
                              </label>
                            </div>
                            <div class="checkbox-container">
                              <label class="checkbox-wrap">
                                <input
                                  type="checkbox"
                                  formControlName="autoGenerateMissingEmail"
                                  id="autoGenerateMissingEmail"
                                />Identify and generate missing email during
                                Ingestion
                                <span class="check-mark"></span>
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- ENd LI, EA, ET -->

                      <div>
                        <app-file-identification
                          [parent]="caseForm"
                          [isEdit]="isEdit"
                        ></app-file-identification>
                      </div>
                      <div>
                        <app-near-duplicate-setting
                          [parent]="caseForm"
                          [isEdit]="isEdit"
                          (disclaimerItemEvent)="getDisclaimerList($event)"
                        ></app-near-duplicate-setting>

                        <app-stop-words
                          [parent]="caseForm"
                          [isEdit]="isEdit"
                          [isTemplateRelated]="isTemplateRelated"
                          (stopWordsItemEvent)="getStopWords($event)"
                        ></app-stop-words>
                      </div>
                    </div>
                  </div>
                </div>
              </mat-expansion-panel>
              <!-- end processing panel -->
              <!-- start general panel -->
              <mat-expansion-panel
                [disabled]="isProjectTemplateLoading"
                class="panel-items-right mat-elevation-z0 position-relative"
              >
                <mat-expansion-panel-header>
                  <mat-panel-title class="align-items-center w-25">
                    <div class="title-dot"></div>
                    <span> <span class="font-weight-bold">General</span></span>
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <div class="padding-15">
                  <div class="row">
                    <app-settings-container
                      class="col-12"
                      [patchFormValue]="formData"
                      [componentNamesToLoad]="['FULL_TEXT_OPTIONS']"
                      (formValueChanged)="changedFormValue($event)"
                    ></app-settings-container>
                  </div>
                  <div class="row col-lg-spacing">
                    <!-- Start Native File Options -->
                    <div class="col-md-6 border-right">
                      <div class="mb-4 w-100">
                        <div class="block-title-text">Native File Options</div>
                        <div class="mb-4">
                          <div class="mb-2">
                            Select File type to replace native
                          </div>
                          <div class="row mb-2">
                            <label class="col label-wrap">For PST/MSG</label>
                            <div class="col-md-9 radio-wrap">
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="msg"
                                  class="custom-control-input"
                                  type="radio"
                                  value="msg"
                                  name="nativeOptionPST_MSG"
                                  formControlName="nativeOptionPST_MSG"
                                />
                                <label class="custom-control-label" for="msg">
                                  msg
                                </label>
                              </div>
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="html-mht"
                                  class="custom-control-input"
                                  type="radio"
                                  value="mht"
                                  name="nativeOptionPST_MSG"
                                  formControlName="nativeOptionPST_MSG"
                                />
                                <label
                                  class="custom-control-label"
                                  for="html-mht"
                                >
                                  html/mht
                                </label>
                              </div>
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="rtf"
                                  class="custom-control-input"
                                  type="radio"
                                  value="rtf"
                                  name="nativeOptionPST_MSG"
                                  formControlName="nativeOptionPST_MSG"
                                />
                                <label class="custom-control-label" for="rtf">
                                  rtf
                                </label>
                              </div>
                            </div>
                          </div>
                          <div class="row mb-2">
                            <label class="col label-wrap">For MBOX/EML</label>
                            <div class="col-md-9 radio-wrap">
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="eml"
                                  class="custom-control-input"
                                  type="radio"
                                  value="eml"
                                  name="nativeOptionMBOX_EML"
                                  formControlName="nativeOptionMBOX_EML"
                                />
                                <label class="custom-control-label" for="eml">
                                  eml
                                </label>
                              </div>
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="html-mht1"
                                  class="custom-control-input"
                                  type="radio"
                                  value="mht"
                                  name="nativeOptionMBOX_EML"
                                  formControlName="nativeOptionMBOX_EML"
                                />
                                <label
                                  class="custom-control-label"
                                  for="html-mht1"
                                >
                                  html/mht
                                </label>
                              </div>
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="rtf1"
                                  class="custom-control-input"
                                  type="radio"
                                  value="rtf"
                                  name="nativeOptionMBOX_EML"
                                  formControlName="nativeOptionMBOX_EML"
                                />
                                <label class="custom-control-label" for="rtf1">
                                  rtf
                                </label>
                              </div>
                            </div>
                          </div>
                          <div class="row mb-2">
                            <label class="col label-wrap">NFS/DXL</label>
                            <div class="col-md-9 radio-wrap">
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="html-eml"
                                  class="custom-control-input"
                                  type="radio"
                                  value="html"
                                  name="nativeOptionNSF_DXL"
                                  formControlName="nativeOptionNSF_DXL"
                                />
                                <label
                                  class="custom-control-label"
                                  for="html-eml"
                                >
                                  html/eml
                                </label>
                              </div>
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="html-mht2"
                                  class="custom-control-input"
                                  type="radio"
                                  value="mht"
                                  name="nativeOptionNSF_DXL"
                                  formControlName="nativeOptionNSF_DXL"
                                />
                                <label
                                  class="custom-control-label"
                                  for="html-mht2"
                                >
                                  html/mht
                                </label>
                              </div>
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="rtf2"
                                  class="custom-control-input"
                                  type="radio"
                                  value="rtf"
                                  name="nativeOptionNSF_DXL"
                                  formControlName="nativeOptionNSF_DXL"
                                />
                                <label class="custom-control-label" for="rtf2">
                                  rtf
                                </label>
                              </div>
                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="dxl"
                                  class="custom-control-input"
                                  type="radio"
                                  value="dxl"
                                  name="nativeOptionNSF_DXL"
                                  formControlName="nativeOptionNSF_DXL"
                                />
                                <label class="custom-control-label" for="dxl">
                                  dxl
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="">
                          <div class="mb-2">Html/Mht</div>
                          <div class="row mb-2">
                            <label class="col label-wrap"
                              >Create Html/Mht file</label
                            >
                            <div class="col-md-9">
                              <div class="d-flex">
                                <div class="custom-control custom-radio mr-3">
                                  <input
                                    id="create-html-file"
                                    class="custom-control-input"
                                    type="radio"
                                    [value]="false"
                                    name="convertToMHT"
                                    formControlName="convertToMHT"
                                  />
                                  <label
                                    class="custom-control-label"
                                    for="create-html-file"
                                  >
                                    Create HTML files
                                  </label>
                                </div>
                                <label class="checkbox-wrap w-auto">
                                  <input
                                    type="checkbox"
                                    formControlName="preserverMHT"
                                    id="preserverMHT"
                                  />Preserve MHT files
                                  <span class="check-mark"></span>
                                </label>
                              </div>

                              <div class="custom-control custom-radio mr-2">
                                <input
                                  id="create-mht-file"
                                  class="custom-control-input"
                                  type="radio"
                                  [value]="true"
                                  name="convertToMHT"
                                  formControlName="convertToMHT"
                                />
                                <label
                                  class="custom-control-label"
                                  for="create-mht-file"
                                >
                                  Create MHT files
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="">
                          <label class="checkbox-wrap">
                            <input
                              type="checkbox"
                              formControlName="autoQueueNative"
                              id="autoQueueNative"
                            />Automatically Queue documents for generating MHT
                            and/or RTF based on the selection above
                            <span class="check-mark"></span>
                          </label>
                        </div>
                      </div>
                      <!-- Native File Options end -->

                      <div
                        class="block-title-text"
                        [hidden]="isTemplateRelated"
                      >
                        Passwords
                      </div>
                      <div class="mb-4" [hidden]="isTemplateRelated">
                        <span
                          >Please list passwords below, list each password on a
                          separate line.</span
                        >
                        <textarea
                          class="form-control"
                          rows="3"
                          required
                          formControlName="passwords"
                        ></textarea>
                      </div>
                      <!-- Auto queue entity extraction -->
                      <div *ngIf="enableEntityExtraction">
                        <div class="mb-2 block-title-text">
                          Entity Extraction Option
                        </div>
                        <div class="mb-3">
                          <div class="custom-control custom-checkbox mr-sm-2">
                            <input
                              class="custom-control-input"
                              type="checkbox"
                              formControlName="autoQueueForEntityExtraction"
                              id="chkBoxAutoQueueForEntityExtraction"
                            />
                            <label
                              class="custom-control-label"
                              for="chkBoxAutoQueueForEntityExtraction"
                              >Auto queue ingested files for entity
                              extraction</label
                            >
                          </div>
                        </div>
                      </div>

                      <!-- <div
                                class="col-md-12 mb-3"
                                formGroupName="imageConversionOption"
                              >
                                <div class="row">
                                  <div class="col-md-6">
                                    <div class="custom-control custom-checkbox mr-sm-2">
                                      <input
                                        class="custom-control-input"
                                        type="checkbox"
                                        formControlName="autoGenerateImgForIngestedFile"
                                        id="chkBoxAutoGen"
                                      />
                                      <label
                                        class="custom-control-label"
                                        for="chkBoxAutoGen"
                                      >Auto generate images for ingested files</label
                                      >
                                    </div>
                                    <div class="custom-control custom-checkbox mr-sm-2">
                                      <input
                                        class="custom-control-input"
                                        type="checkbox"
                                        formControlName="notifyAfterImgGenComplete"
                                        id="chkBoxAutoGenImg"
                                      />
                                      <label
                                        class="custom-control-label"
                                        for="chkBoxAutoGenImg"
                                        title="If this option is checked, processing will be marked as completed only when tiffing jobs are completed."
                                      >
                                        Notify me after image generation is complete
                                        <i class="fa fa-info-circle"></i>
                                      </label>
                                    </div>
                                  </div>
                                  <div class="col-md-6">
                                    <div class="custom-control mr-sm-2">
                                      <label
                                        class="control-label"
                                        for="cbodefaultTiffColorOption"
                                      >
                                        Default color conversion option</label
                                      >
                                      <select
                                        id="cbodefaultTiffColorOption"
                                        class="form-control"
                                        formControlName="defaultTiffColorOption"
                                      >
                                        <option value="1"
                                        >Black and White
                                        </option
                                        >
                                        <option value="2">Color</option>
                                        <option value="3">Grayscale</option>
                                        <option value="4"
                                        >Color for color
                                        </option
                                        >
                                      </select>
                                    </div>
                                  </div>
                                </div>
                              </div> -->
                      <!-- <app-file-identification></app-file-identification> -->
                    </div>
                    <div class="col-md-6">
                      <div class="mb-2 block-title-text">
                        Discovery Exception Handling
                        <!-- <i class="fa fa-question-circle"></i> -->
                      </div>
                      <div class="mb-4">
                        <div class="custom-control custom-radio mr-sm-2">
                          <input
                            id="r3"
                            class="custom-control-input"
                            type="radio"
                            [value]="true"
                            formControlName="discoveryExceptionHandling"
                          />
                          <label class="custom-control-label" for="r3">
                            Notify me and allow file repair
                          </label>
                        </div>
                        <div class="custom-control custom-radio mr-sm-2">
                          <input
                            id="r4"
                            class="custom-control-input"
                            type="radio"
                            [value]="false"
                            formControlName="discoveryExceptionHandling"
                          />
                          <label class="custom-control-label" for="r4">
                            Do not notify me, complete the project and report
                          </label>
                        </div>
                      </div>
                      <!-- Post processing settings -->
                      <!-- <div class="block-title-text">
                              Post processing setting
                            </div>
                            <div class="mb-4">
                              <div class="custom-control custom-checkbox mr-sm-2">
                                <input
                                  class="custom-control-input"
                                  type="checkbox"
                                  formControlName="autoFolderRelativePathDuringIngestion"
                                  id="chkBoxAutoFolderRelativePathDuringIngestion"
                                />
                                <label
                                  class="custom-control-label"
                                  for="chkBoxAutoFolderRelativePathDuringIngestion"
                                >Auto folder using relative path during ingestion</label
                                >
                              </div>
                            </div> -->

                      <app-conversion-setting
                        [parent]="caseForm"
                        [isEdit]="isEdit"
                      ></app-conversion-setting>
                      <div class="block-title-text">Transcript Options</div>
                      <div class="mb-3">
                        <mat-checkbox formControlName="enableTranscriptViewer"
                          >Enable Transcript
                        </mat-checkbox>
                      </div>
                      <div
                        class="block-title-text"
                        [hidden]="isTemplateRelated"
                      >
                        Search Terms
                      </div>
                      <div class="mb-3" [hidden]="isTemplateRelated">
                        <span
                          >Please list the search terms below, each terms in
                          separate line. The searched document will be tagged
                          with the respective tag.</span
                        >
                        <textarea
                          class="form-control"
                          rows="3"
                          formControlName="searchTerm"
                          required
                        ></textarea>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <app-settings-container
                      class="col-12"
                      [patchFormValue]="formData"
                      [componentNamesToLoad]="['ADVANCE_SETTINGS']"
                      (formValueChanged)="changedFormValue($event)"
                    ></app-settings-container>
                  </div>

                  <!--
                 <app-fulltext-option></app-fulltext-option> -->
                </div>
              </mat-expansion-panel>
              <!-- end general panel -->
            </mat-accordion>
          </div>

          <!-- </mat-expansion-panel> -->
          <!-- <div class="row"> -->
          <!-- <div
                class="col-md-7 block-title-text toggle-advance-options"
                (click)="adv.toggle()"
              >
                <fa-icon
                  class="margin-left:5px"
                  [icon]="['fas', !adv.expanded ? 'caret-down' :'caret-up']"
                  size="lg"
                >
                </fa-icon>
                <span> Advanced Options </span>
              </div> -->
          <div class="d-flex justify-content-end mt-4">
            <div class="text-danger pr-2" *ngIf="isDefaultTemplateEdit">
              Note: Default template is not editable.&nbsp;
            </div>
            <button
              [title]="
                !hasSelectedOcrLanguage() && !disableOCROPtions
                  ? 'Case name with at least one language is required to create'
                  : !hasCaseName()
                  ? 'Please enter the case name'
                  : 'Create a case'
              "
              [disabled]="
                !hasCaseName() ||
                !isEdaiECAFormValid ||
                isProjectTemplateLoading ||
                (!hasSelectedOcrLanguage() && !disableOCROPtions) ||
                showSpinner ||
                isDefaultTemplateEdit ||
                isTagGroupListEmpty
              "
              class="btn btn-{{ client }}-primary d-flex align-items-center"
              type="submit"
            >
              <mat-spinner
                [diameter]="18"
                [strokeWidth]="2"
                color="primary"
                *ngIf="showSpinner"
                class="mr-2"
              >
              </mat-spinner>
              <span>
                {{ isEdit ? 'Updat' : 'Creat' }}{{ showSpinner ? 'ing' : 'e' }}
                {{ buttonName }}</span
              >
            </button>
          </div>
        </div>
        <!-- TODO: we'll use this for more settings.-->
        <!-- <app-settings-container [patchFormValue]="formData"></app-settings-container> -->
      </fieldset>
    </form>
  </div>
</div>
