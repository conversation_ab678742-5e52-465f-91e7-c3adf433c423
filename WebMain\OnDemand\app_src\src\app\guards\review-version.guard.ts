import { Injectable } from '@angular/core'
import {
  ActivatedRouteSnapshot,
  CanActivate,
  NavigationExtras,
  Router,
  RouterStateSnapshot,
  UrlTree
} from '@angular/router'
import { getControlSetting } from '@config/store/selectors'
import { Store } from '@ngrx/store'
import { combineLatest, Observable, of } from 'rxjs'
import {
  catchError,
  debounceTime,
  filter,
  switchMap,
  take
} from 'rxjs/operators'

@Injectable({
  providedIn: 'root'
})
export class ReviewVersionGuard implements CanActivate {
  private readonly DEFAULT_VERSION = 1

  constructor(private router: Router, private store: Store) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> {
    const currentPath = this.extractPathFromUrl(state.url)
    if (!currentPath) {
      return of(true)
    }

    const navigationExtras: NavigationExtras = {
      queryParams: route.queryParams,
      replaceUrl: true
    }

    return combineLatest([
      this.store.select(getControlSetting('ENABLE_REVIEW_2')),
      this.store.select(getControlSetting('VOD_VERSION'))
    ]).pipe(
      debounceTime(500),
      filter(
        ([isFbiReview, vodVersion]) =>
          vodVersion !== undefined && vodVersion !== null
      ),
      switchMap(([isFbiReview, vodVersion]) =>
        Number(isFbiReview) === 1
          ? this.redirectIfNeeded(
              currentPath.split('/')[1],
              'review2',
              navigationExtras
            )
          : this.handleRedirection(
              Number(vodVersion),
              currentPath,
              navigationExtras
            )
      ),
      catchError((error) => {
        console.warn(
          'Error in ReviewVersionGuard, falling back to default review: ',
          error
        )
        return this.handleRedirection(
          this.DEFAULT_VERSION,
          currentPath,
          navigationExtras
        )
      }),
      take(1)
    )
  }

  private extractPathFromUrl(url: string): string {
    const pathEndIndex = url.indexOf('?') !== -1 ? url.indexOf('?') : url.length
    return url.substring(0, pathEndIndex)
  }

  private getCaseInsensitiveQueryParam(
    queryParams: { [key: string]: any },
    paramKey: string
  ): number {
    for (const key in queryParams) {
      if (key.toLowerCase() === paramKey.toLowerCase()) {
        return Number(queryParams[key])
      }
    }
    return 0
  }

  private handleRedirection(
    vodVersion: number,
    currentPath: string,
    navigationExtras: NavigationExtras
  ): Observable<boolean | UrlTree> {
    const basePath = currentPath.split('/')[1]
    const reviewSetId = this.getCaseInsensitiveQueryParam(
      navigationExtras.queryParams,
      'reviewSetId'
    )

    switch (vodVersion) {
      case 1:
        return this.redirectIfNeeded(basePath, 'review', navigationExtras)
      case 3:
        // return reviewSetId
        //   ? this.redirectIfNeeded(basePath, 'review-next', navigationExtras)
        //:
        return this.redirectIfNeeded(basePath, 'review-next', navigationExtras)
      default:
        return this.redirectIfNeeded(basePath, 'review', navigationExtras)
    }
  }

  private redirectIfNeeded(
    basePath: string,
    targetPath: string,
    navigationExtras: NavigationExtras
  ): Observable<boolean | UrlTree> {
    return basePath === targetPath
      ? of(true)
      : of(this.router.createUrlTree(['/' + targetPath], navigationExtras))
  }
}
