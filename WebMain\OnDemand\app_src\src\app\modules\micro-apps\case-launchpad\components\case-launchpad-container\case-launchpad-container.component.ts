import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  OnDestroy,
  Output,
  ViewChild,
  ViewEncapsulation
} from '@angular/core'
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser'
import { ActivatedRoute, Router } from '@angular/router'
import { IframeManagerService } from '@root/modules/micro-apps/config/iframe-manager.service'
import {
  IframeMessengerService,
  MessageType
} from '@root/modules/micro-apps/config/iframe-messenger.service'
import { AppIdentitiesTypes } from '@root/modules/micro-apps/models/app-identities-types'
import { LaunchpadNextEvent } from '@root/modules/micro-apps/models/launchpad-next.model'
import { Subject } from 'rxjs'
import { debounceTime, filter, takeUntil, tap } from 'rxjs/operators'
import { environment } from '../../../../../../environments/environment'

@Component({
  selector: 'app-launchpad-next-container',
  templateUrl: './case-launchpad-container.component.html',
  styleUrls: ['./case-launchpad-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class CaseLaunchpadContainerComponent
  implements AfterViewInit, OnDestroy
{
  readonly #toDestroy$ = new Subject<void>()

  isMicroAppLoading = true

  private iframeCustomPath = '#/launchpad'

  public readonly appIdentitiesTypes = AppIdentitiesTypes

  @ViewChild('launchpadNextIframe', { static: true })
  private readonly launchpadNextFrame: ElementRef<HTMLIFrameElement>

  @Output()
  public readonly launchpadNextEvent = new EventEmitter<LaunchpadNextEvent>()

  #oldStyles: Partial<CSSStyleDeclaration> = {}

  get #iframe(): HTMLIFrameElement {
    return this.launchpadNextFrame.nativeElement
  }

  /**
   * Constructs the iframe URL for the micro app, accommodating both development and production environments.
   * This method uses the current window's hash fragment and merges any existing query parameters with additional parameters provided.
   *
   * @param {Record<string, any>} additionalParams - Additional query parameters to append to the URL.
   * @returns {string} The constructed iframe URL.
   */
  private getUrl(additionalParams: Record<string, any> = {}): string {
    // The micro app port for development is 4300.
    // If you change the port of the target micro app, please update it here.
    const localDevelopmentPort = ':4300'

    // Determine the path segment or port based on the environment.
    const pathSegmentOrPort = !environment.production
      ? localDevelopmentPort
      : ''

    // Base URL for the micro app.
    // In production, it becomes something like /VenioWeb/OnDemand/venio-next
    const microAppBaseUrl =
      `${environment.microAppDeployUrl}${pathSegmentOrPort}`.replace(
        /([^:]\/)\/+/g,
        '$1'
      )

    // Get the current window location's hash fragment,
    // which includes the path and query parameters after the '#' symbol.
    const currentHash = window.location.hash

    // Separate the hash fragment into path and query string.
    const [hashPath, hashQueryString] = currentHash.split('?')

    // Parse the existing query parameters from the hash.
    const existingParams = new URLSearchParams(hashQueryString || '')

    // Merge additional parameters, overwriting existing ones if necessary.
    for (const [key, value] of Object.entries(additionalParams)) {
      if (value !== undefined && value !== null) {
        existingParams.set(key, String(value))
      }
    }

    // Build the new query string.
    const newQueryString = existingParams.toString()

    // Reconstruct the final hash fragment.
    const newHash = newQueryString
      ? `${this.iframeCustomPath || hashPath}?${newQueryString}`
      : this.iframeCustomPath || hashPath

    // Combine to form the final URL for the iframe.
    return `${microAppBaseUrl}/${newHash}`
  }

  venioNextUrl: SafeResourceUrl

  selectedCommandEvent: any

  constructor(
    private sanitizer: DomSanitizer,
    private changeDetectorRef: ChangeDetectorRef,
    private iframeMessengerService: IframeMessengerService,
    private iframeManagerService: IframeManagerService,
    public router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    this.#setIframeSrc()
  }

  ngAfterViewInit(): void {
    this.#notifyIframeToCloseAllPopoutWindows()
    this.iframeManagerService.findAndStoreIframeInstances()
    this.#selectLaunchpadNextDocumentReadyEvent()
    this.#fixParentContentDivStylesForThisAppOnly()

    this.#toggleLoadingFlagOnIframeLoad()
    this.#selectLaunchpadNextEvent()
    this.#selectChildUiLayoutReady()

    this.#handleReprocessNotification()

    this.#handleProductionStatusNotification()
  }

  ngOnDestroy(): void {
    this.#fixParentContentDivStylesForThisAppOnly(true)
    this.#toDestroy$.next()
    this.#toDestroy$.complete()
  }

  #setIframeSrc(): void {
    const uniqueParam = `reloadTime=${new Date().getTime()}`

    // Extract query parameters from the current activatedRoute
    const queryParams = this.activatedRoute.snapshot.queryParams

    let url = this.getUrl(queryParams)
    url = url + (url.includes('?') ? '&' : '?') + uniqueParam

    /**
     * Sanitize url properly & securely
     * @see https://stackoverflow.com/a/69211616/4444844
     */
    this.venioNextUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url)
  }

  #setLoadedStyles(element: HTMLElement): void {
    this.#oldStyles.background = element.style.background
    this.#oldStyles.paddingTop = element.style.paddingTop
    this.#oldStyles.paddingLeft = element.style.paddingLeft
    this.#oldStyles.paddingRight = element.style.paddingRight
    this.#oldStyles.paddingBottom = element.style.paddingBottom
    this.#oldStyles.margin = element.style.margin
  }

  /**
   * Because the left-side navigation is hidden during the review-next feature,
   * the container div continues to occupy space.
   * Therefore, we must address this in accordance with our conditions.
   * Once we reach this point, we adjust the space to occupy the full width.
   * When this component is subsequently destroyed, the class is removed to restore the previous state."
   * @see CaseLaunchpadContainerComponent.ngOnDestroy
   */
  #fixParentContentDivStylesForThisAppOnly(isRemove = false): void {
    const contentDiv = document.querySelector('.page-content') as HTMLDivElement
    if (!isRemove && contentDiv) {
      this.#setLoadedStyles(contentDiv)
    }

    if (contentDiv) {
      contentDiv.style.background = isRemove
        ? this.#oldStyles.background
        : '#FFF'
      contentDiv.style.margin = isRemove ? this.#oldStyles.margin : '0'
      contentDiv.style.paddingTop = isRemove
        ? this.#oldStyles.paddingTop
        : '50px'
      contentDiv.style.paddingLeft = isRemove
        ? this.#oldStyles.paddingLeft
        : '0'
      contentDiv.style.paddingRight = isRemove
        ? this.#oldStyles.paddingRight
        : '0'
      contentDiv.style.paddingBottom = isRemove
        ? this.#oldStyles.paddingBottom
        : '0'
      contentDiv.classList.toggle('case-launchpad-next-active')
    }
  }

  /**
   * This main app is handling the token refresh for auth, so the same tokens should be shared across its children apps.
   * @see IframeMessengerService
   */
  #shareBaseRequirementForChildApps(): void {
    this.iframeMessengerService.sendMessage({
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: [
        {
          type: MessageType.TOKEN_UPDATE,
          content: {
            timestamp: new Date().toUTCString(),
            refreshToken: String(localStorage.getItem('refresh_token')),
            accessToken: String(localStorage.getItem('access_token'))
          }
        }
      ]
    })
  }

  /**
   * Once the #iframe is loaded, we need to toggle the loader
   */
  #toggleLoadingFlagOnIframeLoad(): void {
    // Make it invisible until the document is ready and the child app is ready to receive the data.
    this.#iframe.style.opacity = '0'
    // Allow iframe to perform post-message communication with the parent window.
    this.#iframe.onload = () => {
      this.changeDetectorRef.markForCheck()
      this.isMicroAppLoading = false
    }
  }

  #selectChildUiLayoutReady(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.LAYOUT_CHANGE &&
            e.payload['content']?.['layoutReady']
        ),

        tap(() => {
          // A workaround to hide the toolbar and left bar
          // Notifying multiple times to trigger state change in the child app.
          ;[0, 1, 2, 3, 4, 5].forEach((n) => {
            setTimeout(() => {
              this.#iframe.style.opacity = `${n * 0.2}`
              this.iframeMessengerService.sendMessage({
                iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
                eventTriggeredFor: 'FRAME_WINDOW',
                type: 'MICRO_APP_DATA_CHANGE',
                payload: {
                  content: {
                    toolbar: 'hide',
                    leftBar: 'hide',
                    // Tell NGRX state to reflect the new changes.
                    timestamp: Date.now() + Math.random() * n
                  },
                  type: MessageType.UI_STATE_CHANGE
                }
              })
            }, n * 100)
          })
        }),
        debounceTime(200),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(() => {
        // Once the iframe is ready, and the information is shared, we can now show the iframe.
        this.#iframe.style.opacity = '1'
      })
  }

  /**
   * Once the child notifies that the document is ready state, we can then pass the additional info to
   * child apps that it can proceed with.
   * Such as configs, tokens, initial states are passed initially when app is loaded for the first time.
   */
  #selectLaunchpadNextDocumentReadyEvent(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.DOCUMENT_READY
        ),
        tap(() => (this.#iframe.style.opacity = '0')),
        debounceTime(100),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(() => {
        this.#shareBaseRequirementForChildApps()
      })
  }

  #selectLaunchpadNextEvent(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (e) =>
            e.eventTriggeredFor === 'PARENT_WINDOW' &&
            e.payload['type'] === MessageType.ROUTE_CHANGE &&
            (e.payload['content']?.['isCaseActionClick'] ||
              e.payload['content']?.['isReviewSetActionClick'])
        ),
        takeUntil(this.#toDestroy$)
      )
      .subscribe((e) => {
        this.launchpadNextEvent.emit({
          actionType: e.payload['content']['actionType'],
          content:
            e.payload['content']['selectedCase'] ||
            e.payload['content']['selectedReviewSet']
        })
      })
  }

  /**
   * Notifies the iframe to destroy all popout windows.
   * This method sends a message to the iframe to close all popout windows that will
   * be handled in the iframe window by listening to the message event.
   */
  #notifyIframeToCloseAllPopoutWindows(): void {
    window.addEventListener('beforeunload', () => {
      this.#iframe.style.opacity = '0'
      this.#iframe.contentWindow.postMessage(
        {
          type: 'MICRO_APP_DATA_CHANGE',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          payload: {
            type: MessageType.ROUTE_CHANGE,
            content: {
              closeAllPopoutWindows: true
            }
          }
        },
        environment.allowedOrigin
      )
    })
  }

  #handleReprocessNotification(): void {
    this.activatedRoute.queryParams
      .pipe(
        filter((param) => param['popup'] === 'reprocessing'),
        debounceTime(100),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(() => {
        const isOpenFromEmail =
          this.activatedRoute.snapshot.queryParams['isFromEmail']
        const settingId = this.activatedRoute.snapshot.queryParams['settingId']
        const token = this.activatedRoute.snapshot.queryParams['token']
        const projectId = this.activatedRoute.snapshot.queryParams['projectId']

        this.iframeMessengerService.sendMessage({
          eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
          eventTriggeredFor: 'FRAME_WINDOW',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          payload: {
            type: MessageType.ROUTE_CHANGE,
            content: {
              popup: 'reprocessing',
              loadReprocessing: true,
              projectId: projectId,
              isOpenFromEmail: isOpenFromEmail,
              settingId: settingId,
              token: token
            }
          }
        })
      })

    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (m: any) =>
            m.eventTriggeredBy === AppIdentitiesTypes.VENIO_NEXT &&
            m.eventTriggeredFor === 'PARENT_WINDOW' &&
            m.payload['type'] === MessageType.ROUTE_CHANGE &&
            m.payload['content']?.['popup'] === 'reprocessing' &&
            Boolean(m.payload['content']?.['removeParameters'])
        ),
        debounceTime(100),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(() => {
        this.router.navigate([], {
          queryParams: {},
          queryParamsHandling: '',
          replaceUrl: true
        })
      })
  }

  #handleProductionStatusNotification(): void {
    this.activatedRoute.queryParams
      .pipe(
        filter((param) => param['popup'] === 'production-status'),
        debounceTime(100),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(() => {
        const isOpenFromEmail =
          this.activatedRoute.snapshot.queryParams['isFromEmail']
        const projectId = this.activatedRoute.snapshot.queryParams['projectId']
        this.iframeMessengerService.sendMessage({
          eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
          eventTriggeredFor: 'FRAME_WINDOW',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          payload: {
            type: MessageType.ROUTE_CHANGE,
            content: {
              popup: 'production-status',
              projectId: projectId,
              isOpenFromEmail: isOpenFromEmail
            }
          }
        })
      })

    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (m: any) =>
            m.eventTriggeredBy === AppIdentitiesTypes.VENIO_NEXT &&
            m.eventTriggeredFor === 'PARENT_WINDOW' &&
            m.payload['type'] === MessageType.ROUTE_CHANGE &&
            m.payload['content']?.['popup'] === 'production-status' &&
            Boolean(m.payload['content']?.['removeParameters'])
        ),
        debounceTime(100),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(() => {
        this.router.navigate([], {
          queryParams: {},
          queryParamsHandling: '',
          replaceUrl: true
        })
      })
  }
}
