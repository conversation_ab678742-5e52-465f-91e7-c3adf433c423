// Import required packages
const execa = require('execa')
const browserSync = require('browser-sync').create()
const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')
const ansi_up = require('ansi_up')
const ansiUp = new ansi_up.default()

// Example usages:
// node .\bs-config.js --proxy "http://localhost:2066/ondemand/appplus/" --files "your-drive\\VenioWebMain\\WebMain\\OnDemand\\AppPlus/dist/**/*.*"  --bsOptions '{\"port\": 4000}'

// Initialize variables
let buildInProgress = false

// Parse command-line arguments
const argv = yargs(hideBin(process.argv))
  .option('proxy', {
    alias: 'p',
    type: 'string',
    description: 'Set the proxy for BrowserSync'
  })
  .option('files', {
    alias: 'f',
    type: 'string',
    description: 'Set the files to watch for BrowserSync'
  })
  .option('bsOptions', {
    alias: 'b',
    type: 'string',
    description: `Additional BrowserSync options in JSON format e.g. --bsOptions  '{\\"port\\": 1234, \\"open\\": false}'`
  })
  .demandOption(
    ['proxy', 'files'],
    'Please provide both proxy and files arguments'
  )
  .help().argv

// Parse additional BrowserSync options
let bsOptions = {}
if (argv.bsOptions) {
  try {
    bsOptions = JSON.parse(argv.bsOptions)
  } catch (e) {
    console.error(
      `Invalid JSON format for BrowserSync options. format example: --bsOptions  '{\\"port\\": 1234, \\"open\\": false}'`
    )
    process.exit(1)
  }
}

// Initialize BrowserSync with merged options
browserSync.init({
  proxy: argv.proxy,
  files: argv.files,
  ...bsOptions,
  timeout: 1000,
  ignoreURLs: ['browser-sync-client'],
  middleware: function (req, res, next) {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate')
    next()
  },
  proxyReq: [
    function (proxyReq) {
      proxyReq.setHeader('Pragma', 'no-cache')
    }
  ]
})

// Function to handle build events
const handleData = (data) => {
  const output = data.toString()

  // Check if this is a build progress message
  if (output.match('Generating browser application bundles')) {
    // Use carriage return to overwrite the previous line instead of creating a new line
    process.stdout.write('\r' + output)
    buildInProgress = true
    browserSync.notify(
      "<span style='color: #f3b137; margin: -15px;  box-shadow: 0 0 20px 0 #3A3A3A; display: inherit;padding: 15px; font-weight: 700; font-size: 12px;'>Angular build in progress. Reloading upon completion...</span>",
      10 * 60 * 1000
    )
  } else if (output.match('Error:')) {
    buildInProgress = true
    browserSync.notify(
      "<span style='color: #ec3232; margin: -15px;  box-shadow: 0 0 20px 0 #3A3A3A;display: inherit;padding: 15px; font-weight: 700; font-size: 12px;white-space: pre-wrap;text-align: left;'>Error during Angular build <br> <code>" +
        ansiUp.ansi_to_html(output) +
        '</code></span>',
      10 * 60 * 1000
    )
    process.stdout.write(data + '\n')
  } else if (output.match('Index html generation complete')) {
    if (buildInProgress) {
      browserSync.notify(
        "<span style='color: #56e656; margin: -15px;  box-shadow: 0 0 20px 0 #3A3A3A;display: inherit;padding: 15px; font-weight: 700; font-size: 12px;'>Angular build complete, Reloading...</span>",
        2000
      )
      buildInProgress = false
      browserSync.reload()
    }
    process.stdout.write(data + '\n')
  } else {
    // For other messages, use normal newline behavior
    process.stdout.write(data + '\n')
  }
}

// Run Angular build with --watch
const angularBuild = execa(
  'ng',
  ['build', '--watch', '--progress', '--output-hashing=all'],
  {
    stdout: 'inherit',
    env: { ...process.env, NODE_OPTIONS: '--max-old-space-size=24000' }
  }
)

// Listen for build events
angularBuild.stderr.on('data', handleData)

// Handle errors
angularBuild.on('error', (error) => {
  console.error('Error during Angular build:', error)
  browserSync.notify(
    "<span style='color: #ec3232; margin: -15px;  box-shadow: 0 0 20px 0 #3A3A3A;display: inherit;padding: 15px; font-weight: 700; font-size: 12px;white-space: pre-wrap;text-align: left;'>Error during Angular build</span>"
  )
})
