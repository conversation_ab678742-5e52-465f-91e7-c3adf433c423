import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ConfigService } from '@config/services/config.service'
import { Store } from '@ngxs/store'
import {
  DeleteDocumentOptions,
  DeleteDocumentSummary,
  DeleteMode
} from '@review/models/document.model'
import {
  CheckIfFilesToBeDeletedInFolder,
  DeleteDocument,
  GetDeleteDocumentStatus,
  GetDeleteDocumentSummary,
  ResetDeleteStatus,
  ReviewSetStateSelector
} from '@review/xs-store'
import { TempTableResponseModel } from '@shared/models/search.model'
import { CsvBuilder } from '@shared/utils/csv-downloader'
import { Guid } from 'guid-typescript'
import { BsModalRef } from 'ngx-bootstrap/modal'
import { ToastrService } from 'ngx-toastr'
import { EMPTY, interval, Subject } from 'rxjs'
import {
  catchError,
  filter,
  startWith,
  switchMap,
  takeUntil,
  takeWhile
} from 'rxjs/operators'

@Component({
  selector: 'app-delete-document',
  templateUrl: './delete-document.component.html',
  styleUrls: ['./delete-document.component.scss']
})
export class DeleteDocumentComponent implements OnInit {
  // Static service of app config.
  config = ConfigService

  projectId: number

  userId: number

  selectedDocuments: number[]

  unSelectedFileIds: number[]

  isBatchSelected: boolean

  tempTables: TempTableResponseModel

  //session id is for creating unique temp table name which will be used for progress bar
  sessionId: string

  selectedDocumentsCount: number

  childFileToBeDeleted: number

  filesToBeDeleted: number

  documentSummary: DeleteDocumentSummary[]

  selectedOption: DeleteMode = DeleteMode.DELETE_ALL

  deleteChildRecord = false

  deleteMediaifAllChildAreDeleted = false

  loading = false

  isDeleting = false

  isDeletionCompleted = false

  disableOptions = false

  deleteProgress = '0'

  totalFiles = 0

  deletedCount = 0

  errorOccurred = false

  ErrorMessages: any

  isErrorReportDownloaded = false

  private readonly toDestroy$ = new Subject<void>()

  isFilesToBeDeletedinFolder = false

  public onClose: Subject<boolean>

  constructor(
    private store: Store,
    public bsModalRef: BsModalRef,
    private toast: ToastrService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.onClose = new Subject()
    this.sessionId = this.getRandomSessionId()
    this.getInitData()
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()

    this.onClose.complete()
  }

  getDeleteOption(): DeleteDocumentOptions {
    const deleteOption = new DeleteDocumentOptions()
    deleteOption.DeleteChildRecords = this.deleteChildRecord
    deleteOption.DeleteMediaifAllChildAreDeleted =
      this.deleteMediaifAllChildAreDeleted
    deleteOption.DeleteMode = this.selectedOption
    deleteOption.SelectedFileIds = this.selectedDocuments
    deleteOption.UnSelectedFileIds = this.unSelectedFileIds
    deleteOption.IsBatchSelected = this.isBatchSelected
    deleteOption.SearchTempTable = this.tempTables.searchResultTempTable
    deleteOption.SessionId = this.sessionId
    deleteOption.SearchId = this.tempTables.searchId
    return deleteOption
  }

  getRandomSessionId(): string {
    return Guid.create().toString().replace(/-/g, '_')
  }

  /**
   * initialize data
   */
  getInitData(): void {
    const deleteOption = this.getDeleteOption()
    deleteOption.SessionId = this.getRandomSessionId()
    this.loading = true

    this.store
      .dispatch(new GetDeleteDocumentSummary(this.projectId, deleteOption))
      .pipe(
        takeUntil(this.toDestroy$),
        switchMap(() =>
          this.store.selectOnce(ReviewSetStateSelector.SliceOf('deleteSummary'))
        ),
        catchError((res) => {
          this.loading = false
          return EMPTY
        })
      )
      .subscribe((summary) => {
        this.documentSummary = summary
        this.updateDeleteSummary()
        this.loading = false
      })

    const _deleteOption = this.getDeleteOption()
    _deleteOption.SessionId = this.getRandomSessionId()

    this.store
      .dispatch(
        new CheckIfFilesToBeDeletedInFolder(this.projectId, _deleteOption)
      )
      .pipe(
        takeUntil(this.toDestroy$),
        switchMap(() =>
          this.store.selectOnce(
            ReviewSetStateSelector.SliceOf('isFilesToBeDeletedInFolder')
          )
        )
      )
      .subscribe((res) => {
        this.isFilesToBeDeletedinFolder = res
      })
  }

  updateChildCount(event: { target: { checked: boolean } }): void {
    if (this.isSelected(DeleteMode[DeleteMode.DELETE_ALL])) {
      this.deleteChildRecord = event.target.checked
      this.updateDeleteSummary()
    }
  }

  updateDeleteMediaOption(event): void {
    if (this.isSelected(DeleteMode[DeleteMode.DELETE_ALL])) {
      this.deleteMediaifAllChildAreDeleted = event.target.checked
    }
  }

  /**
   * update file count summary
   */
  updateDeleteSummary(): void {
    if (this.documentSummary != null) {
      this.selectedDocumentsCount = this.documentSummary.filter(
        (x) => x.DeleteOption == DeleteMode.DELETE_ALL
      )[0].Count
      this.childFileToBeDeleted = 0

      this.filesToBeDeleted = this.documentSummary.filter(
        (x) => x.DeleteOption == this.selectedOption
      )[0].Count

      if (this.selectedOption == DeleteMode.DELETE_ALL) {
        this.childFileToBeDeleted = this.documentSummary.filter(
          (x) => x.Title == 'ChildCount'
        )[0].Count
        if (this.deleteChildRecord) {
          const childCountWithOption = this.documentSummary.filter(
            (x) => x.Title == 'ChildCountWithOption'
          )[0].Count
          this.childFileToBeDeleted += childCountWithOption
        }
      }
    }
  }

  setDeleteOption(selection: string): void {
    this.selectedOption = DeleteMode[selection]
    this.updateDeleteSummary()
  }

  isSelected(name: string): boolean {
    return DeleteMode[this.selectedOption] === name
  }

  /**
   * update delete progress
   * @param deleteStatus delete state
   */
  updateDeleteProgress(deleteStatus): void {
    if (deleteStatus.deletedDocuments >= deleteStatus.totalDocuments) {
      this.isDeleting = false
      this.isDeletionCompleted = true
      this.deleteProgress = '100'
      this.totalFiles = deleteStatus.totalDocuments
      this.deletedCount = deleteStatus.deletedDocuments
      this.enableUI(false)

      if (deleteStatus.errorOccurred) {
        this.errorOccurred = true
        this.ErrorMessages = deleteStatus.errorMessages
        this.toast.error(
          'Error occurred during deletion. Download error report for details.'
        )
      } else {
        this.toast.success('Deletion Completed.')
      }
    }

    if (deleteStatus.totalDocuments != 0) {
      this.deleteProgress = (
        (deleteStatus.deletedDocuments * 100) /
        deleteStatus.totalDocuments
      ).toString()
      this.totalFiles = deleteStatus.totalDocuments
      this.deletedCount = deleteStatus.deletedDocuments
    }
  }

  /**
   * Performs delete operation
   */
  deleteDocument(): void {
    this.isDeleting = true
    this.enableUI(true)
    const deleteOption = this.getDeleteOption()
    deleteOption.SessionId = this.getRandomSessionId()
    this.sessionId = deleteOption.SessionId

    this.store.dispatch(new ResetDeleteStatus())

    this.store.dispatch(new DeleteDocument(this.projectId, deleteOption))

    interval(2000)
      .pipe(
        startWith(0),
        takeUntil(this.toDestroy$),
        takeWhile((x) => this.isDeleting)
      )
      .subscribe(() => {
        this.store.dispatch(
          new GetDeleteDocumentStatus(this.projectId, this.sessionId)
        )
      })

    this.store
      .select(ReviewSetStateSelector.SliceOf('deleteDocumentStatus'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$),
        takeWhile((x) => this.isDeleting)
      )
      .subscribe((status) => {
        if (status) {
          this.updateDeleteProgress(status)
        }
      })
  }

  enableUI(enable: boolean): void {
    this.disableOptions = enable
  }

  disableDeleteOption(): boolean {
    return (
      this.disableOptions ||
      this.isDeletionCompleted ||
      this.filesToBeDeleted + this.childFileToBeDeleted <= 0
    )
  }

  /**
   * export data for csv
   */
  readonly exportErrorReport = (): void => {
    if (this.ErrorMessages != null) {
      const data = this.ErrorMessages

      const keys = Object.keys(data[0])
      const makeHeader = (str) =>
        str
          .split(/(?=[A-Z])/)
          .map((str) => str.charAt(0).toUpperCase() + str.substring(1))
          .join(' ')
      const columns = keys.map(makeHeader)
      const filteredRows = data.reduce(
        (acc, obj) => [...acc, Object.values(obj).map((y) => y)],
        []
      )
      new CsvBuilder(`Error-Report-${Guid.create()}.csv`).createCsvAndDownload(
        columns,
        filteredRows
      )

      this.isErrorReportDownloaded = true
    }
  }

  closeForm() {
    this.bsModalRef.hide()
    this.onClose.next(this.isDeletionCompleted)
  }
}
