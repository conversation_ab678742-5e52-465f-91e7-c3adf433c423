import {
  DeleteBatchFileAssociationModel,
  DeleteDocumentRequestModel,
  DeleteDocumentReviewSetRequestModel,
  ReviewerPerformanceRequestModel,
  ReviewerSummaryRequestModel
} from '@admin-advance/models'
import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ConfigService } from '@config/services/config.service'
import {
  ResponseModel,
  ReviewSetInfo,
  ReviewSetOperation
} from '@shared/models'
import { NavigationMode } from '@shared/models/source.model'
import { StringUtils } from '@shared/utils/string-utils'
import { JsonConvert } from 'json2typescript'
import { Observable, of } from 'rxjs'
import { UtilityService } from './../../../../services/utility.service'

@Injectable()
export class ReviewSetService {
  readonly jsonConvert = new JsonConvert()

  /**
   * Gets a base url of API.
   */
  private get _url() {
    return this.configService.getApiUrl()
  }

  constructor(
    private configService: ConfigService,
    private http: HttpClient,
    private us: UtilityService
  ) {}

  /**
   * Fetch review-set
   * @param projectId Project Id
   */
  readonly fetchReviewSet = <T>(projectId: number): Observable<T> =>
    this.http.get<T>(`${this._url}/Project/${projectId}/ReviewSet`)

  /**
   * Fetch review-set
   * @param projectId Project Id
   * @param reviewSetId review set Id
   */
  readonly fetchReviewSetById = <T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._url}/Project/${projectId}/reviewset/${reviewSetId}`
    )

  /**
   * Fetch review-set summary
   * @param projectId Project Id
   * @param reviewSetId review set id
   */
  readonly fetchReviewSetSummary = <T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._url}/project/${projectId}/ReviewSet/${reviewSetId}/Summary/Count`
    )

  /**
   * Fetch review-set summary rate
   * @param projectId Project Id
   * @param reviewSetId review set id
   */
  readonly fetchReviewSetSummaryRate = <T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._url}/project/${projectId}/ReviewSet/${reviewSetId}/Summary/Rate`
    )

  /**
   * Fetch review-set progress
   * @param projectId Project Id
   * @param reviewSetId review set id
   */
  readonly fetchReviewSetProgress = <T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> =>
    this.http.post<T>(
      `${this._url}/project/${projectId}/ReviewSet/${reviewSetId}/daily-review-status`,
      null
    )

  /**
   * Fetch review-set reviewer
   * @param projectId Project Id
   * @param reviewSetId review set id
   */
  readonly fetchReviewSetReviewer = <T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> =>
    this.http.post<T>(
      `${this._url}/project/${projectId}/ReviewSet/${reviewSetId}/daily-review-status-reviewer `,
      null
    )

  /**
   * Fetch review-set tag status
   * @param projectId Project Id
   * @param reviewSetId review set id
   */
  readonly fetchReviewSetTagStatus = <T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> =>
    this.http.post<T>(
      `${this._url}/project/${projectId}/ReviewSet/${reviewSetId}/tagged-status `,
      null
    )

  /**
   * Fetch review-set tag rate
   * @param projectId Project Id
   * @param reviewSetId review set id
   */
  readonly fetchReviewSetTagRate = <T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> =>
    this.http.post<T>(
      `${this._url}/project/${projectId}/ReviewSet/${reviewSetId}/tagged-rate-status`,
      null
    )

  /**
   * Get the navigation mode
   * @param projectId Selected project Id
   */
  fetchNavigationMode$(projectId: number) {
    return this.http.get(
      this._url + `/cases/project/${projectId}/navigation-mode`
    )
  }

  /**
   * Fetch the tags of the selected project
   * @param projectId Selected Project Id
   * @param navigationMode Naviagation mode
   */
  fetchSourceTags$(projectId: number, navigationMode: NavigationMode) {
    const navigationModel = this.jsonConvert.serializeObject(
      navigationMode,
      NavigationMode
    )
    return this.http.post(
      this._url + `/production/project/${projectId}/source-tags`,
      navigationModel,
      {}
    )
  }

  /**
   * Fetch the folders of the selected project
   * @param projectId Selected Project Id
   * @param navigationMode Naviagation mode
   */
  fetchSourceFolders$(projectId: number, navigationMode: NavigationMode) {
    const navModel = this.jsonConvert.serializeObject(
      navigationMode,
      NavigationMode
    )
    return this.http.post(
      this._url + `/production/project/${projectId}/source-folders`,
      navModel,
      {}
    )
  }

  /**
   * Fetch the saves searches of the selected project
   * @param projectId Selected Project Id
   * @param navigationMode Naviagation mode
   */
  fetchSavedSearches$(projectId: number, navigationMode: NavigationMode) {
    const navModel = this.jsonConvert.serializeObject(
      navigationMode,
      NavigationMode
    )
    return this.http.post(
      this._url + `/production/project/${projectId}/saved-searches`,
      navModel,
      {}
    )
  }

  /**
   * Fetch the saves searches of the selected project
   * @param projectId Selected Project Id
   * @param navigationMode Naviagation mode
   */
  fetchSourceMedias$(projectId: number) {
    return this.http.get(
      this._url + `/production/project/${projectId}/source-medias`
    )
  }

  /**
   * Fetch the datasource of user and user groups
   * @param projectId Selected project id
   */
  fetchUserAndUserGroups$(projectId: number) {
    return this.http.get(this._url + '/user/project/' + projectId)
  }

  /**
   * Fetch the datasource of user and user groups associated to reviewset
   */
  fetchReviewSetUserAndUserGroups$(projectId: number, reviewSetId: number) {
    return this.http.get(
      this._url + '/user/project/' + projectId + '/reviewset/' + reviewSetId
    )
  }

  /**
   * Fetch review sets of the selected project
   * @param projectId Selected project id
   */
  fetchAllReviewSets$(projectId: number) {
    return this.http.get(this._url + '/project/' + projectId + '/reviewset')
  }

  /**
   * Fetch the review set details of selected project and review set
   * @param projectId Selected project id
   * @param reviewSetId Selected review set id
   */
  fetchReviewSet$(projectId: number, reviewSetId: number) {
    if (!projectId || !reviewSetId) return of(null)
    return this.http.get(
      this._url + '/project/' + projectId + '/reviewset/' + reviewSetId
    )
  }

  /**
   * Fetch the review set job status details of selected project and review set
   * @param projectId Selected project id
   * @param reviewSetId Selected review set id
   */
  fetchReviewSetJobs$(projectId: number, reviewSetId: number) {
    if (!projectId || !reviewSetId) return of(null)
    return this.http.get(
      this._url +
        '/project/' +
        projectId +
        '/reviewsets/' +
        reviewSetId +
        '/job-status'
    )
  }

  /**
   * Fetch the tag info of review tag.
   * @param projectId
   * @param reviewTagId
   */
  fetchReviewTagInfo$(projectId: number, reviewTagId: number) {
    return this.http.get(
      this._url + '/project/' + projectId + '/tag/' + reviewTagId
    )
  }

  /**
   * Delete the review set of selected project and review set
   * @param projectId Selected project id
   * @param reviewSetId Selected review set
   */
  deleteReviewSet$(projectId: number, reviewSetId: number) {
    return this.http.delete(
      this._url +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/purpose/delete reviewset'
    )
  }

  /**
   * Fetch the review set summary of selected project and review set
   * @param projectId Selected project id
   * @param reviewSetId Selected review set id
   */
  fetchReviewSetSummary$(projectId: number, reviewSetId: number) {
    return this.http.get(
      this._url +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/summary/count'
    )
  }

  /**
   * Fetch CAL Progress info
   * @param projectId
   * @param reviewSetId
   */
  fetchCALProgress$(projectId: number, reviewSetId: number) {
    return this.http.get(
      this._url + '/project/' + projectId + '/calreviewset/' + reviewSetId
    )
  }

  /**
   * Fetch the review set batches of selected project and review set
   * @param projectId Project Id to fetch review set batches from.
   * @param reviewSet Selected review set model.
   * @param viewMode selected view mode.
   * @param groupBy selected group option.
   * @param chartFor specic chart name.
   */
  fetchCALReviewStatisticsChartData$(
    projectId: number,
    reviewSetId: number,
    viewMode: string,
    sortBy: string,
    chartFor: string
  ) {
    return this.http.get(
      this._url +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/calstatistics/viewmode/' +
        viewMode +
        '/sortby/' +
        sortBy +
        '/chartFor/' +
        chartFor
    )
  }

  /**
   * Fetch the review set batches of selected project and review set
   * @param projectId Selected project id
   * @param reviewSetId Selected review set id
   */
  fetchReviewSetBatches$(projectId: number, reviewSetId: number) {
    return this.http.get(
      this._url +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/batch'
    )
  }

  /**
   * Fetch the review set details of selected project and review set
   * @param projectId Selected project id
   * @param reviewSetId Selected review set id
   */
  fetchReviewSetBatchInfo$(
    projectId: number,
    reviewSetId: number,
    batchId: number
  ) {
    return this.http.get(
      this._url +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/batch/' +
        batchId
    )
  }

  /**
   * Delete the review set batch of selected project and review set and batch id
   * @param projectId Selected project id
   * @param reviewSetId Selected review set id
   * @param batchId Selected review set batch id
   */
  deleteReviewSetBatches$(
    projectId: number,
    reviewSetId: number,
    batchIds: number[]
  ) {
    const url =
      this._url +
      '/project/' +
      projectId +
      '/reviewset/' +
      reviewSetId +
      '/batch'

    return this.http.request<ResponseModel>('delete', url, {
      body: batchIds
    })
  }

  /**
   * Purget the specified batches and re-batch the unreviewed documents within those batches
   * @param projectId
   * @param reviewSetId
   * @param batchIds
   * @returns
   */
  purgeBatches$(projectId: number, reviewSetId: number, batchIds: number[]) {
    return this.http.post(
      this._url +
        '/project/' +
        projectId +
        '/ReviewSet/' +
        reviewSetId +
        '/batch/purge',
      batchIds
    )
  }

  /**
   * Assign/reassign user to selected batch of the review set
   * @param projectId Selected project id
   * @param reviewSetId Selected review set id
   * @param batchId Selected batch id
   * @param reviewerId
   */
  assignUserInBatch$(
    projectId: number,
    reviewSetId: number,
    batchId: number,
    reviewerId: number
  ) {
    return this.http.put(
      this._url +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/batch/' +
        batchId +
        '/user/' +
        reviewerId,
      {}
    )
  }

  /**
   * Creates or Updates the review set
   * @param projectId Selected project id
   * @param reviewSetInfo Review set options
   * @param operationType Either create or update
   * @param reviewSetId selected review set id
   */
  createUpdateReviewSet$(
    projectId: number,
    reviewSetInfo: ReviewSetInfo,
    operationType: ReviewSetOperation,
    reviewSetId: number
  ) {
    const reviewSetOptions = this.serializeReviewSetInfo(reviewSetInfo)
    if (operationType === ReviewSetOperation.Create)
      return this.http.post(
        this._url + '/project/' + projectId + '/ReviewSet',
        reviewSetOptions
      )
    else
      return this.http.put(
        this._url + '/project/' + projectId + '/ReviewSet/' + reviewSetId,
        reviewSetOptions
      )
  }

  /**
   * Get File conversion count
   * @param projectId Selected project id
   * @param reviewSetInfo Review set options
   * @param operationType Either create or update
   * @param reviewSetId selected review set id
   */
  getFileConversionCount$(
    projectId: number,
    reviewSetInfo: ReviewSetInfo,
    operationType: ReviewSetOperation,
    reviewSetId: number
  ) {
    const reviewSetOptions = this.serializeReviewSetInfo(reviewSetInfo)
    if (operationType === ReviewSetOperation.Create)
      return this.http.post(
        this._url + '/project/' + projectId + '/ReviewSetFileConversionCount',
        reviewSetOptions
      )
    else
      return this.http.get(
        this._url +
          '/project/' +
          projectId +
          '/ReviewSetFileConversionCount/' +
          reviewSetId
      )
  }

  /**
   * Checks if the given review set name already exists
   * @param projectId Selected project id
   * @param reviewSetName Name of the review set to check
   */
  validateReviewSetExists$(projectId: number, reviewSetName: string) {
    return this.http.get(
      this._url +
        '/project/' +
        projectId +
        '/reviewSetName/' +
        reviewSetName +
        '/lookup'
    )
  }

  /**
   * Returns the object after the tagid and folder ids are converted to string from array of numbers
   * @param reviewSetInfo Review set options
   */
  serializeReviewSetInfo(reviewSetInfo: ReviewSetInfo) {
    return {
      ...reviewSetInfo,
      tagID: reviewSetInfo.tagID
        ? StringUtils.trim(reviewSetInfo?.tagID?.join(','), ',')
        : null,
      folderID: reviewSetInfo.folderID
        ? StringUtils.trim(reviewSetInfo?.folderID?.join(','), ',')
        : null
    }
  }

  /**
   * Get the status of sql agent status
   */
  getSqlAgentStatus() {
    return this.http.get(this._url + '/common/sqlagent/status')
  }

  /**
   * Get the list of review layouts
   */
  getReviewLayouts(clientId: number) {
    return this.http.get(this._url + '/client/' + clientId + '/reviewlayout')
  }

  /**
   * Get the layout details
   */
  getLayoutDetails(layoutId: number) {
    return this.http.get(this._url + '/reviewlayout/' + layoutId)
  }

  /**
   * Get the CAL options
   * @param projectId Project id
   * @param reviewSetId Review set id
   */
  getCalOptions(projectId: number, reviewSetId: number) {
    return this.http.get(
      this._url + `/project/${projectId}/reviewset/${reviewSetId}/caloptions`
    )
  }

  /**
   * Get the CAL options
   * @param projectId Project id
   * @param reviewSetId Review set id
   */
  isReviewSetCAlOptions(projectId: number, reviewSetId: number) {
    return this.http.get(
      this._url +
        `/project/${projectId}/reviewset/${reviewSetId}/isReviewSetCAl`
    )
  }

  /**
   * Get the reviewers assigned to the selected reviewsets
   */
  getReviewSetReviewers$(projectId: number, reviewSetIds: number[]) {
    return this.http.post(
      this._url + `/project/${projectId}/reviewset/reviewers`,
      reviewSetIds
    )
  }

  /**
   * Get the summary of review set, its batches and assigned reviewers to the batches.
   */
  getReviewSetBatchSummary$(
    projectId: number,
    requestModel: ReviewerSummaryRequestModel
  ) {
    return this.http.post(
      this._url + `/project/${projectId}/reviewer/summary`,
      requestModel
    )
  }

  /**
   * Get the summary of review set, its batches and assigned reviewers to the batches.
   */
  getReviewerPerformanceData$(
    projectId: number,
    requestModel: ReviewerPerformanceRequestModel
  ) {
    return this.http.post(
      this._url + `/project/${projectId}/reviewer/performance`,
      requestModel
    )
  }

  /**
   * Get the summary of review set, its batches and assigned reviewers to the batches.
   */
  getReviewerAccuracyData$(
    projectId: number,
    requestModel: ReviewerSummaryRequestModel
  ) {
    return this.http.post(
      this._url + `/project/${projectId}/reviewer/accuracy`,
      requestModel
    )
  }

  /**
   * Fetch user selected layout
   * @param userId user Id
   */
  readonly fetchUserLayoutId = <T>(userId: number): Observable<T> =>
    this.http.get<T>(`${this._url}/user/userlayout/${userId}/GetUserLayoutId`)

  readonly getDeleteDocumentList = <T>(
    req: DeleteDocumentRequestModel
  ): Observable<T> => {
    const payload: DeleteDocumentReviewSetRequestModel = {
      searchResultTempTable: req.searchResultTempTable
    }
    return this.http.post<T>(
      `${this._url}/project/${req.projectId}/reviewsets/${req.reviewSetId}/document-view`,
      payload
    )
  }

  /**
   * Delete batch file association
   * @param userId user Id
   */
  readonly deleteBatchFileAssociationById = <T>(
    projectId: number,
    reviewSetId: number,
    payload: DeleteBatchFileAssociationModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._url}/project/${projectId}/reviewsets/${reviewSetId}/delete-batch-file`,
      payload
    )

  /**
   * Fetch review-set batch details
   * @param projectId Project Id
   * @param reviewSetId review set id
   * @param batchId Batch Id
   */
  readonly getReviewSetBatchDetailById = <T>(
    projectId: number,
    reviewSetId: number,
    batchId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._url}/project/${projectId}/reviewset/${reviewSetId}/batch/${batchId}/details`
    )

  /**
   * Fetch review-set batch details
   * @param projectId Project Id
   * @param reviewSetId review set id
   */
  readonly getReviewSetBatchTaggedDocumentTags = <T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._url}/project/${projectId}/reviewset/${reviewSetId}/tagged-document`
    )
}
