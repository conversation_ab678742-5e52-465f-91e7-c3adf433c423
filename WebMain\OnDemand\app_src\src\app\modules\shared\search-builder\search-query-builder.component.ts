import {
  Component,
  EventEmitter,
  HostListener,
  Injector,
  Input,
  NgZone,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  SimpleChanges,
  Type,
  ViewChildren
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { MatExpansionPanel } from '@angular/material/expansion'
import { ActivatedRoute } from '@angular/router'
import { Store } from '@ngxs/store'
import { animateHeight, fadeInOut } from '@shared/animation'
import { FetchCustodianMedia } from '@stores/actions/case.actions'
import { cloneDeep } from 'lodash'
import { Subject } from 'rxjs'
import {
  debounceTime,
  filter,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs/operators'
import { SearchBuilderService } from '../services/advance-search.service'
import { ConfirmCloseComponent } from './child/confirm-search/confirm-close.component'
import { UiSettingsContainerComponent } from './child/ui-settings/ui-settings-container.component'
import {
  DEFAULT_FILTER_UI,
  FilterItem,
  FilterTypes,
  InjectorItem,
  SyntaxTerm
} from './query-builder-static.model'
import {
  FetchSearchUiSetting,
  SetMasterFieldsAction,
  SetMasterFilterUisAction,
  TriggerResetAdvancedSearchUiAction
} from './sotre/query-builder.actions'
import { QueryBuilderSelector } from './sotre/query-builder.selector'
import {
  useCachedControlValues,
  useSearchResponseData,
  useSyncData
} from './sqb-local-state.extension'

type Item = {
  [key: string]: {
    comp: Promise<Type<any>>
    injector: any
  }
}
@Component({
  selector: 'app-advance-search-query-builder',
  templateUrl: './search-query-builder.component.html',
  styleUrls: ['./search-query-builder.component.scss'],
  providers: [SearchBuilderService],
  animations: [fadeInOut, animateHeight]
})
export class SearchQueryBuilderComponent
  implements OnInit, OnDestroy, OnChanges
{
  /** Used to clean up rxjs streams within the component's subscriptions.
      Any rxjs streams must be chained with this token in the pipe as a last
      operator to ensuring the memory leak when component gets destroyed.
   */
  private readonly toDestroy$ = new Subject<void>()

  isAllExpanded: boolean

  @ViewChildren(MatExpansionPanel) panels: QueryList<MatExpansionPanel>

  /**
   * Emits an output event to notify to the parent which toggles show/hide
   * builder UI. Not entire component itself.
   */
  @Output()
  readonly cancelAdvanceSearch = new EventEmitter<boolean>(true)

  /**
   * Emits an output event to notify  with generated query as an argument.
   */
  @Output()
  readonly queryValue = new EventEmitter<string>(true)

  /**
   * Emits an output event to notify  whether should  enable the search input editable mode
   */
  @Output()
  readonly isEnableInputWrite = new EventEmitter<boolean>(true)

  /**
   * Emits an output event to notify whether the query is valid to perform search
   */
  @Output()
  readonly isInvalidQuery = new EventEmitter<boolean>(true)

  /**
   * Whether the query builder UI should show.
   */
  @Input() shown: boolean

  /**
   * Currently generated query out of controls.
   */
  private query: string

  /**
   * Observes generated query item from the different type of f filters.
   */
  filterValues: Array<FilterItem> = []

  /**
   * Default selected tab.
   */
  activeFilterType: FilterTypes = FilterTypes.FIELD

  filterTypes = FilterTypes

  fileSelected: boolean

  _isQueryModeActive: boolean

  /**
   * k/v pair lazy components.
   */
  lazyComponents: Item = {}

  /**
   * handler to notify that the tab has changed.
   */
  activeTabEvent = new EventEmitter<FilterTypes>(true)

  @Output()
  areAllLazyUiChunksLoaded = new EventEmitter<boolean>(true)

  /**
   * Whether all lazy/chunks are loaded.
   */
  get isLoadedAllChunks(): boolean {
    return this.lazyChunkCounts <= 0
  }

  /**
   * Tabs title
   */
  titles: FilterTypes[] = []

  /**
   * Number of lazy component to load.
   * @see initLazyComp for lazy components
   */
  private lazyChunkCounts = 11

  isWorking = true

  querySyntaxEditorComp: Type<unknown>

  selectedSyntaxFromEditor: SyntaxTerm

  constructor(
    private store: Store,
    private searchBuilderService: SearchBuilderService,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private injector: Injector,
    private ngZone: NgZone
  ) {}

  // confirms page refresh
  @HostListener('window:beforeunload')
  readonly beforeUnload = (): boolean => {
    if (this.query) return confirm('')
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.isWorking = true
    this.isAllExpanded = false
    try {
      // hides the body overflow when the Ui is shown, otherwise revert to it's default.
      this.updateBodyScroll(this.shown)

      // clean up of cached values from node input controls.
      const { clearCachedValues } = useCachedControlValues()
      clearCachedValues()
      this.filterValues = []
      // initialize default tab and lazy component
      if (this.shown && this.isWorking) {
        this.store.dispatch(new FetchSearchUiSetting())
        this.initDataShare()
        if (!this.isLoadedAllChunks) this.initLazyComp()
        this.ngZone.onStable
          .pipe(debounceTime(10), take(1), takeUntil(this.toDestroy$))
          .subscribe(() => {
            this.isWorking = false
            this.fetchSearchFields()
          })
      } else {
        // Reset the flag to false when UI is closed
        this.store.dispatch(new TriggerResetAdvancedSearchUiAction(false))
      }
    } catch (e) {
      console.error(e)
    }
  }

  ngOnInit(): void {
    this.initFilterUIs()
    this.fetchSearchFields()
    this.#selectGeneratedCompleteQuerySyntax()
    this.#initQuerySyntaxEditorLazyComponent()
    this.#selectSelectedSyntaxFromEditor()
  }

  private readonly initFilterUIs = (): void => {
    // the main static items that we have in SPA side.
    const master = DEFAULT_FILTER_UI.slice()

    this.store
      .dispatch([
        // always load master so we can derive it to other places.
        new SetMasterFilterUisAction(cloneDeep(master)),
        new FetchCustodianMedia(+this.route.snapshot.queryParams['projectId'])
      ])
      .pipe(
        switchMap(() =>
          this.store.select(QueryBuilderSelector.sliceOf('masterFilterUi'))
        ),
        filter((u) => !!u),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (items) => {
          this.titles = items.filter((i) => i.isRendered).map((i) => i.uiType)
          this.initLazyComp()
        }
      })
  }

  private readonly initDataShare = (): void => {
    // we have some shared data between lazy component
    // and we avoid to use state LIB  for such small task.
    const {
      setInjectedItem,
      fileSelected,
      isQueryModeActive,
      toggleInputOfQueryMode
    } = useSyncData()
    toggleInputOfQueryMode
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({ next: (is) => this.isEnableInputWrite.emit(is) })
    const inj: InjectorItem = {
      isInvalidQuery: (event) => this.isInvalidQuery.emit(event),
      queryData: (event) => {},
      shown: this.shown,
      tabChanged: this.activeTabEvent
    }
    setInjectedItem(inj)
    fileSelected
      .pipe(
        tap((is) => (this.fileSelected = is)),
        debounceTime(400),
        takeUntil(this.toDestroy$)
      )
      .subscribe((is) => {
        this.panels.filter((p) => p.disabled).forEach((p) => p.close())
      })
    isQueryModeActive
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((is) => (this._isQueryModeActive = is))
  }

  readonly toggleExpansion = (): void => {
    const anyDisabled = this.panels.some((p) => p.disabled)
    if (anyDisabled) {
      this.panels.filter((p) => !p.disabled).forEach((panel) => panel.toggle())
    } else {
      this.panels.forEach((p) => (this.isAllExpanded ? p.close() : p.open()))
    }
    this.isAllExpanded = this.panels.some((p) => p.expanded)
  }

  /**
   * Initializes lazy components.
   */
  private readonly initLazyComp = (): void => {
    // set k/v pair lazy components.
    this.titles.forEach((title) => {
      if (
        title === FilterTypes.WILDCARD ||
        title === FilterTypes.FUZZY ||
        title === FilterTypes.SYNONYM
      )
        this.lazyComponents[title] = {
          comp: import('./child/common/common.component').then(
            ({ CommonComponent }) => {
              return new Promise((resolve) => {
                const wait = setTimeout(() => {
                  resolve(CommonComponent)
                  clearTimeout(wait)
                  this.lazyChunkCounts--
                  this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
                })
              })
            }
          ),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }
      if (title === FilterTypes.FIELD)
        this.lazyComponents[title] = {
          comp: import('./child/field/field.component').then(
            ({ FieldComponent }) => {
              return new Promise((resolve) => {
                const wait = setTimeout(() => {
                  resolve(FieldComponent)
                  clearTimeout(wait)
                  this.lazyChunkCounts--
                  this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
                })
              })
            }
          ),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }

      if (title === FilterTypes.TERM)
        this.lazyComponents[title] = {
          comp: import('./child/load-file/load-file.component').then(
            ({ LoadFileComponent }) => {
              return new Promise((resolve) => {
                const wait = setTimeout(() => {
                  resolve(LoadFileComponent)
                  clearTimeout(wait)
                  this.lazyChunkCounts--
                  this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
                })
              })
            }
          ),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }

      if (
        title === FilterTypes.REVIEWSETBATCH ||
        title === FilterTypes.REVIEWSETSTATUS
      )
        this.lazyComponents[title] = {
          comp: import(
            './child/reviewset-batch/reviewset-batch.component'
          ).then(({ ReviewsetBatchComponent }) => {
            return new Promise((resolve) => {
              const wait = setTimeout(() => {
                resolve(ReviewsetBatchComponent)
                clearTimeout(wait)
                this.lazyChunkCounts--
                this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
              })
            })
          }),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }

      if (title === FilterTypes.TAGHISTORY)
        this.lazyComponents[title] = {
          comp: import('./child/tag-history/tag-history.component').then(
            ({ TagHistoryComponent }) => {
              return new Promise((resolve) => {
                const wait = setTimeout(() => {
                  resolve(TagHistoryComponent)
                  clearTimeout(wait)
                  this.lazyChunkCounts--
                  this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
                })
              })
            }
          ),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }

      if (title === FilterTypes.NPS)
        this.lazyComponents[title] = {
          comp: import('./child/nps/nps.component').then(({ NpsComponent }) => {
            return new Promise((resolve) => {
              const wait = setTimeout(() => {
                resolve(NpsComponent)
                clearTimeout(wait)
                this.lazyChunkCounts--
                this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
              })
            })
          }),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }

      if (title === FilterTypes.LOAD_FILE_SEARCH)
        this.lazyComponents[title] = {
          comp: import(
            './child/load-file-search/load-file-search.component'
          ).then(({ LoadFileSearchComponent }) => {
            return new Promise((resolve) => {
              const wait = setTimeout(() => {
                resolve(LoadFileSearchComponent)
                clearTimeout(wait)
                this.lazyChunkCounts--
                this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
              })
            })
          }),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'LOAD_FILE_DATA' }]
          })
        }

      if (title === FilterTypes.FOLDER)
        this.lazyComponents[title] = {
          comp: import('./child/folder/folder-search.component').then(
            ({ FolderSearchComponent }) => {
              return new Promise((resolve) => {
                const wait = setTimeout(() => {
                  resolve(FolderSearchComponent)
                  clearTimeout(wait)
                  this.lazyChunkCounts--
                  this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
                })
              })
            }
          ),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }

      if (title === FilterTypes.DS_SEARCH)
        this.lazyComponents[title] = {
          comp: import('./child/ds-search/ds-search.component').then(
            ({ DsSearchComponent }) => {
              return new Promise((resolve) => {
                const wait = setTimeout(() => {
                  resolve(DsSearchComponent)
                  clearTimeout(wait)
                  this.lazyChunkCounts--
                  this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
                })
              })
            }
          ),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }

      if (title === FilterTypes.TAG)
        this.lazyComponents[title] = {
          comp: import('./child/tags/tags-search.component').then(
            ({ TagsSearchComponent }) => {
              return new Promise((resolve) => {
                const wait = setTimeout(() => {
                  resolve(TagsSearchComponent)
                  clearTimeout(wait)
                  this.lazyChunkCounts--
                  this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
                })
              })
            }
          ),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }
      if (title === FilterTypes.SAVED_SEARCH)
        this.lazyComponents[title] = {
          comp: import('./child/saved-search/saved-search.component').then(
            ({ SavedSearchComponent }) => {
              return new Promise((resolve) => {
                const wait = setTimeout(() => {
                  resolve(SavedSearchComponent)
                  clearTimeout(wait)
                  this.lazyChunkCounts--
                  this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
                })
              })
            }
          ),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }
      if (title === FilterTypes.TAG_CONFLICT)
        this.lazyComponents[title] = {
          comp: import(
            './child/tags-conflict-search/tags-conflict-search.component'
          ).then(({ TagsConflictSearchComponent }) => {
            return new Promise((resolve) => {
              const wait = setTimeout(() => {
                resolve(TagsConflictSearchComponent)
                clearTimeout(wait)
                this.lazyChunkCounts--
                this.areAllLazyUiChunksLoaded.emit(this.lazyChunkCounts <= 0)
              })
            })
          }),
          injector: Injector.create({
            parent: this.injector,
            providers: [{ useValue: title, provide: 'SEARCH_QUERY_DATA' }]
          })
        }
    })
  }

  private fetchSearchFields = (): void => {
    // since this component has local store to handle node values,
    // we don't have to use complicated store just to retrieve search fields.
    this.searchBuilderService
      .fetchSearchFields({
        // userId: +localStorage.getItem('UserId'),
        moduleToRefresh: 'ALL',
        projectId: +this.route.snapshot.queryParams['projectId']
        // isExternalUser: false
      })
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe({
        next: (res: unknown[]) => {
          let fields = res['SearchFields']
          // These two are not filed and should exclude from the field search
          // so do not include these in the field collections.
          fields = fields.filter(
            (f) =>
              f.FieldName.trim().toLowerCase() !== 'folders' &&
              f.FieldName.trim().toLowerCase() !== 'tags'
          )
          this.store.dispatch(new SetMasterFieldsAction(fields))
          const { updateContent } = useSearchResponseData()
          updateContent(res)
        }
      })
  }

  /**
   * When user clicks to  toggle the query builder UI, the body scroll should be hidden. Otherwise should revert to default.
   * @param isHidden whether the builder  UI is hidden
   */
  private readonly updateBodyScroll = (isHidden?: boolean) => {
    this.ngZone.runOutsideAngular(() => {
      document.body.style.overflowY = isHidden ? 'hidden' : 'auto'
    })
  }

  /**
   * When user clicks on the hide builder UI button,  emit true to notify the parent to hide.
   */
  readonly cancel = (): void => {
    const { fileSelected, isQueryModeActive } = useSyncData()
    // clean up of cached values from node input controls.
    const { clearCachedValues } = useCachedControlValues()

    if (!this.query || this.query.trim().replace(/\s+/g, '') === '()') {
      this.cancelAdvanceSearch.emit(true)
      this.queryValue.emit(null)
      fileSelected.emit(null)
      isQueryModeActive.emit(null)
      // Reset the flag to false when UI is closed
      this.store.dispatch(new TriggerResetAdvancedSearchUiAction(false))
      return
    }
    // otherwise display a message to the end user that they needs to add query
    const ref = this.dialog.open(ConfirmCloseComponent, {
      autoFocus: false,
      closeOnNavigation: true,
      data: {
        message: 'Do you want to close the UI or keep building the query?',
        hasQuery: true
      }
    })

    ref
      .beforeClosed()
      .pipe(
        filter((yes) => yes),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: () => {
          this.store.dispatch(new TriggerResetAdvancedSearchUiAction(true))
          this.queryValue.emit('')
          this.cancelAdvanceSearch.emit(true)
          this.isInvalidQuery.emit(false)
          this.filterValues = []
          this.query = null
          fileSelected.emit(null)
          isQueryModeActive.emit(null)
          clearCachedValues()
        }
      })
  }

  readonly afterOpened = (titleIndex: number): void => {
    this.activeFilterType = this.titles[titleIndex]
    this.activeTabEvent.emit(this.titles[titleIndex])
  }

  readonly uiSettings = (): void => {
    this.dialog.open(UiSettingsContainerComponent, {
      autoFocus: false,
      closeOnNavigation: true,
      disableClose: true,
      width: '95%'
    })
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.updateBodyScroll()
    const { clearSyncData } = useSyncData()
    clearSyncData()
    // clean up of cached values from node input controls.
    const { clearCachedValues } = useCachedControlValues()
    clearCachedValues()
  }

  #initQuerySyntaxEditorLazyComponent(): void {
    import(
      '@shared/search-builder/child/query-syntax-editor/query-syntax-editor.component'
    ).then(({ QuerySyntaxEditorComponent }) => {
      this.querySyntaxEditorComp = QuerySyntaxEditorComponent
    })
  }

  #selectSelectedSyntaxFromEditor(): void {
    this.store
      .select(QueryBuilderSelector.sliceOf('selectedSyntaxFromEditor'))
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((selected) => (this.selectedSyntaxFromEditor = selected))
  }

  #selectGeneratedCompleteQuerySyntax(): void {
    this.store
      .select(QueryBuilderSelector.sliceOf('completeQuerySyntax'))
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((query) => {
        this.query = query
        this.queryValue.emit(query)
      })
  }
}
