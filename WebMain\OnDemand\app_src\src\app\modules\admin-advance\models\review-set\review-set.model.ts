import { ReviewSetInfo } from '@shared/models'

export interface ReviewSetModel {
  reviewSetId?: number
  name?: string
  progress?: number | string
  reviewedDocCount?: number
  totalDocCount?: number

  // extra prop
  link?: string
}

export interface ReviewStatusModel {
  userId?: number
  username?: string
  tagId?: number
  tagName?: string
  taggedDocCount?: number
  reviewedDocCount?: number
  reviewedDate?: string
  // extra
  day: string
}

export interface ReviewSetInfoModel {
  templateName?: string
  reviewSetInformation?: ReviewSetInfo
  notes?: string
  templateId?: number
  clientName?: string
  clientId?: number
  projectId?: number
}

export interface DeleteDocumentRequestModel
  extends DeleteDocumentReviewSetRequestModel {
  projectId: number
  reviewSetId: number
}

export interface DeleteDocumentReviewSetRequestModel {
  searchResultTempTable: string
}

export interface DeleteDocumentReviewSetModel {
  batchId: number
  treeParentId: string
  batchName: string
  batchStatus: string
  reviewer: string
  fileId: number
  fileSize: string
  fileType: string
  fileName: string
  treeId: string
  batchFileAssocicationId: number
}

export interface DeleteBatchFileAssociationModel {
  batchFileAssocicationIds: number[]
}

export interface ReviewBatchDetailModel {
  batchId: number
  batchName: string
  reviewer: string
  reviewBatchFileDetails: ReviewBatchFileDetailModel[]
}

export interface ReviewBatchFileDetailModel {
  fileId: number
  fileSize: number
  fileType: string
  fileName: string
}
