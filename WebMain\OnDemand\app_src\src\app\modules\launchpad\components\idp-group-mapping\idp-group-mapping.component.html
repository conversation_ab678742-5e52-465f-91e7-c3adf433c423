<dx-data-grid
  [dataSource]="adGroupMapping"
  [showBorders]="true"
  [showRowLines]="false"
  [height]="250"
>
  <dxi-column dataField="venioadminlevel" caption="Venio Group"></dxi-column>
  <dxi-column
    caption="IDP Group"
    dataField="groupsid"
    cellTemplate="adgroup"
  ></dxi-column>
  <div *dxTemplate="let item of 'adgroup'">
    <mat-select
      [ngModelOptions]="{ standalone: true }"
      [(ngModel)]="item.data.groupsid"
      (selectionChange)="GetSelectedValue($event, item)"
    >
      <mat-option [value]="o.idpgroup" *ngFor="let o of adGroups">
        {{ o.idpgroup }}
      </mat-option>
    </mat-select>
  </div>
</dx-data-grid>
