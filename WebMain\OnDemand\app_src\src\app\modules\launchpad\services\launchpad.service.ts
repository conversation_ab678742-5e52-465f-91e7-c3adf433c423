import { CaseModel } from '@admin-advance/models'
import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ConfigService } from '@config/services/config.service'
import { SettingsInfo } from '@stores/models'
import { JsonConvert } from 'json2typescript'
import { Observable } from 'rxjs'
import { ProjectSetupInfo } from '../models/case-template-settings.model'
import { StateErrorModel } from '../models/state-error.model'

@Injectable({
  providedIn: 'root'
})
export class LaunchpadService {
  jsonConvert: JsonConvert

  constructor(
    private httpClient: HttpClient,
    private configService: ConfigService
  ) {
    this.jsonConvert = new JsonConvert()
  }

  fetchCases$(
    start: number,
    end: number,
    sortBy: string,
    isDesc: boolean,
    searchTerm?: string,
    clientIdString?: string
  ): Observable<any> {
    let param = new HttpParams()
    if (searchTerm || clientIdString) {
      param = param
        .set('searchText', searchTerm)
        .set('sortColumn', sortBy)
        .set('OrderDirection', `${isDesc}`)
        .set('clientIdString', clientIdString)
    } else {
      param = param
        .set('start', String(start))
        .set('end', String(end))
        .set('sortby', sortBy)
        .set('desc', String(isDesc))
    }
    return this.httpClient.get(this.configService.getApiUrl() + '/cases', {
      params: param
    })
  }

  fetchCaseInfoById$(projectId: number): Observable<CaseModel> {
    return this.httpClient.get(
      this.configService.getApiUrl() + `/cases/project/${projectId}/case-info`
    )
  }

  fetchProjectTemplates$(): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/cases/projecttemplate'
    )
  }

  fetchCasesBySearchTerm$(searchString: string): Observable<any> {
    return this.httpClient.get(this.configService.getApiUrl() + '/cases', {
      params: new HttpParams().set('searchText', searchString)
    })
  }

  // TODO Remove CreateCaseReqModel after removing all of its usages
  //Use ProjectsetupInfo model for both VOD or ROD case

  createCase$(payload: ProjectSetupInfo): Observable<any> {
    let request

    if (payload.ProjectName === undefined) {
      request = {
        ...payload,
        SettingsInfo: this.jsonConvert.serialize(
          payload.SettingsInfo,
          SettingsInfo
        )
      }
    } else {
      request = payload
    }

    return this.httpClient.post(
      this.configService.getApiUrl() + '/cases',
      request
    )
  }

  createCaseServiceType$(payload: SettingsInfo): Observable<any> {
    return this.httpClient.post(
      this.configService.getApiUrl() + '/cases/create/service-type',
      payload
    )
  }

  updateCase$(projectId: number, payload: ProjectSetupInfo): Observable<any> {
    return this.httpClient.put(
      this.configService.getApiUrl() + `/cases/project/${projectId}`,
      payload,
      {}
    )
  }

  getTimeZones$(): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/cases/gettimezones',
      {}
    )
  }

  getProjectMediaStatus$(projectId: number): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() +
        `/cases/project/${projectId}/media-status`
    )
  }

  getProjectRightList$(
    projectId: number,
    moduleString: string,
    isForOnDemand: boolean
  ): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() + `/cases/project/${projectId}/right-list`,
      {
        params: new HttpParams()
          .set('ModuleString', moduleString)
          .set('IsForOndemand', String(isForOnDemand))
      }
    )
  }

  getUsersList$(projectId: number): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/UploadInvitation',
      {
        params: new HttpParams().set('projectId', String(projectId))
      }
    )
  }

  getExtUsersList$(): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/UploadInvitation',
      {}
    )
  }

  sendInvitation$(payload: any): Observable<any> {
    return this.httpClient.post(
      this.configService.getApiUrl() + '/UploadInvitation',
      payload,
      {}
    )
  }

  getCustodianList$(projectId): Observable<any> {
    const getDataUrl =
      this.configService.getApiUrl() +
      `/cases/project/${projectId}/custodian-list`
    return this.httpClient.post(getDataUrl, {})
  }

  projectList$() {
    const getDataUrl = this.configService.getApiUrl() + '/cases'
    return this.httpClient.get(getDataUrl)
  }

  searchDuplicateOption$(projectId: number) {
    const getDataUrl =
      this.configService.getApiUrl() +
      `/cases/project/${projectId}/search-option`
    return this.httpClient.get(getDataUrl)
  }

  getImageType$(projectId: number) {
    const getDataUrl =
      this.configService.getApiUrl() +
      `/cases/project/${projectId}/is-image-pdf`
    return this.httpClient.get(getDataUrl)
  }

  setSearchDuplicateOption$(projectId: number, searchDupOption: any) {
    const getDataUrl =
      this.configService.getApiUrl() +
      `/cases/project/${projectId}/set-search-option`
    return this.httpClient.post(getDataUrl, null, {
      params: new HttpParams().set('searchDupOption', searchDupOption)
    })
  }

  projectGroupList$(projectId, isFromTagManagement) {
    const getDataUrl =
      this.configService.getApiUrl() + `/cases/project/${projectId}/group`
    return this.httpClient.get(getDataUrl, {
      params: new HttpParams().set('isFromTagManagement', isFromTagManagement)
    })
  }

  getProjectTemplateSettings$(templateId) {
    const getDataUrl = `${this.configService.getApiUrl()}/cases/template`
    return this.httpClient.get(getDataUrl, {
      params: new HttpParams().set('templateId', templateId)
    })
  }

  getProjectInfoForEdit$(projectId): Observable<any> {
    const getDataUrl =
      this.configService.getApiUrl() + `/cases/project/${projectId}/info`
    return this.httpClient.get(getDataUrl)
  }

  getTranscriptStatus$(projectId: number): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() +
        `/cases/project/${projectId}/transcript-status`
    )
  }

  getSupportedFileTypesForTranscribing$(): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() +
        `/cases/supported-file-types-for-transcribing`
    )
  }

  getTranscribeAccessKeys$() {
    return this.httpClient.get(
      this.configService.getApiUrl() + `/cases/transcribe-access-keys`
    )
  }

  getImageFileExtensions$(): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/cases/getImageFileExtensions',
      {}
    )
  }

  getDefaultProjectTemplate$(): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/cases/projecttemplate/default',
      {}
    )
  }

  createCaseTemplate$(payload: ProjectSetupInfo): Observable<any> {
    let request

    if (payload.ProjectName === undefined) {
      request = {
        ...payload,
        SettingsInfo: this.jsonConvert.serialize(
          payload.SettingsInfo,
          SettingsInfo
        )
      }
    } else {
      request = payload
    }

    return this.httpClient.post(
      this.configService.getApiUrl() + '/cases/projecttemplate',
      request
    )
  }

  updateCaseTemplate$(
    templateId: number,
    payload: ProjectSetupInfo
  ): Observable<any> {
    return this.httpClient.put(
      this.configService.getApiUrl() + `/cases/projecttemplate/${templateId}`,
      payload,
      {}
    )
  }

  hasStateError$(): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/stateerror/haserror',
      {}
    )
  }

  getStateErrorDetails$(): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() + '/stateerror/details',
      {}
    )
  }

  fixStateError$(stateErrorModelList: StateErrorModel[]): Observable<any> {
    return this.httpClient.post(
      this.configService.getApiUrl() + '/stateerror/markfix',
      stateErrorModelList
    )
  }

  fetchUnIndexMedia$(projectId: number): Observable<any> {
    return this.httpClient.get(
      this.configService.getApiUrl() +
        `/media/project/${projectId}/unindexed-media`,
      {}
    )
  }
}
