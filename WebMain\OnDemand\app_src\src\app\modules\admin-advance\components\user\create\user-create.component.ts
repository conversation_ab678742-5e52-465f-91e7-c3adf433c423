import { ImportAdComponent } from '@admin-advance/components/import-ad/import-ad.component'
import { ClientModel } from '@admin-advance/models/client-management/client-management.model'
import { ReviewLayout } from '@admin-advance/models/layout/layout.model'
import {
  AddOrUpdateUser,
  ClearResponse,
  GetUserById,
  SamlIdpServerFacade,
  UserStateSelector
} from '@admin-advance/store'
import { ADUserDetails } from '@admin-advance/store/ad-login-settings/ad-login-settings-state.model'
import { FetchADLoginSettingAction } from '@admin-advance/store/ad-login-settings/ad-login-settings.action'
import { ADLoginSettingsSelectors } from '@admin-advance/store/ad-login-settings/ad-login-settings.selector'
import {
  ClientListAction,
  ClientMgmtStateSelector
} from '@admin-advance/store/client-management'
import { Overlay, OverlayRef } from '@angular/cdk/overlay'
import { TemplatePortal } from '@angular/cdk/portal'
import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  QueryList,
  TemplateRef,
  ViewChild,
  ViewChildren,
  ViewContainerRef
} from '@angular/core'
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormControlName,
  FormGroup,
  Validators
} from '@angular/forms'
import { MatDialog } from '@angular/material/dialog'
import { MatExpansionPanel } from '@angular/material/expansion'
import { ActivatedRoute, Router } from '@angular/router'
import { User } from '@auth/models/user.model'
import { getUserDetails } from '@auth/store/selectors/access.selectors'
import { ConfigService } from '@config/services/config.service'
import { getControlSetting } from '@config/store/selectors'
import { select, Store } from '@ngrx/store'
import { Navigate } from '@ngxs/router-plugin'
import { Store as xsStore } from '@ngxs/store'
import {
  GetGlobalRoles,
  GetRoleWiseRightsDetails,
  GetUserCaseInfo,
  GetUserRoles
} from '@root/modules/application-nav/store/actions'
import {
  globalRoles,
  roleWiseRightDetails,
  userCaseInfo,
  userRoles
} from '@root/modules/application-nav/store/selectors/application-nav.selectors'
import {
  FetchReviewLayoutsForSearch,
  GetUserLayoutId,
  ReviewSetStateSelector
} from '@root/modules/review/xs-store'
import { ResponseModel } from '@shared/models'
import { DxDataGridComponent } from 'devextreme-angular'
import { cloneDeep } from 'lodash'
import { ToastrService } from 'ngx-toastr'
import { fromEvent, of, Subject, timer } from 'rxjs'
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs/operators'
import { UserRights } from '../../../../../helpers/user-rights'
import { StartupStateSelector } from '../../../../../stores/selectors'
import { animateHeight, fadeInX } from '../../../../shared/animation'
import {
  GenericValidator,
  MessageModel
} from '../../../../shared/validators/generic-validators'
import {
  CaseGroupAssignedInfo,
  UserCaseAssignmentModel,
  UserInformation,
  UserModel,
  UserQueryParams,
  UserRightModel,
  UserRoleListResponse,
  UserRoleModel
} from '../../../models'
import { IdPGroupMappingStatus } from '../../../models/saml-idp-server/saml-idp-server.model'
import { LoginSettingsModel } from '../../../store/login-settings/login-settings-state.model'
import { FetchLoginSettingAction } from '../../../store/login-settings/login-settings.action'
import { LoginSettingsSelectors } from '../../../store/login-settings/login-settings.selector'
import { UserRightsComponent } from './user-rights/user-rights.component'
import { UserRoleComponent } from './user-role/user-role.component'

@Component({
  selector: 'app-user',
  templateUrl: './user-create.component.html',
  styleUrls: ['./user-create.component.scss'],
  animations: [fadeInX, animateHeight]
})
export class UserCreateComponent
  implements OnInit, OnDestroy, AfterViewInit, AfterViewChecked
{
  private readonly toDestroy$ = new Subject<void>()

  private genericValidator: GenericValidator

  @ViewChildren(FormControlName, { read: ElementRef })
  private readonly formInputElements: QueryList<ElementRef>

  @ViewChild(DxDataGridComponent)
  private readonly dxGridCase: DxDataGridComponent

  @ViewChild(MatExpansionPanel)
  readonly panel: MatExpansionPanel

  qParams: UserQueryParams

  mode: 'Create' | 'Edit' | 'Clone' = 'Create'

  addUserForm: FormGroup

  displayMessage: MessageModel

  isLoading: boolean

  isWorking = true

  formErrorMessage: string

  selectedCasesCount = 0

  selectedCases: CaseGroupAssignedInfo[] = []

  roleWiseRights: UserRightModel[] = []

  selectedUserRole: UserRoleModel

  client: string

  rightsDialog: OverlayRef

  casesDatasource: CaseGroupAssignedInfo[] = []

  userCaseInformation: UserCaseAssignmentModel

  isEmailAlreadyInUse = false

  userRoleList: UserRoleModel[] = []

  selectedUserRoleName: string

  userData: any

  selectedRows: number[]

  loginSettings: LoginSettingsModel

  passwordValidationMessage = ''

  /**
   * Static service of app config.
   */
  config = ConfigService

  /**
   * Whether the form is submitting.
   */
  isSubmitting: boolean

  /** user associated client id */
  clientId: number

  /**
   * List of review layout that user can select for the review.
   */
  layouts: ReviewLayout[]

  clientData: ClientModel[] = []

  userDetails: User

  updatedCaseDataSource: CaseGroupAssignedInfo[] = []

  // is the layout loaded
  isLayoutLoading = true

  isADUser: boolean

  importUserDetails: ADUserDetails

  isADEnabled: boolean

  isIDPUser: boolean

  isIDPEnabled: boolean

  disableCreateUser = false

  assignedCases: number[] = []

  oldEmail: string

  public generatePw = new FormControl(false)

  /**
   * Template reference to show update email confirmation
   */
  @ViewChild('updateEmailConfirm')
  private readonly updateEmailDialog: TemplateRef<any>

  allGroupsMapped = false

  disableGrid = false

  isMixMode = false

  disableForcePasswordChange = false

  showOptionForDisablePasswordReset = false

  showImportUser = false

  idPGroupMappingStatus: IdPGroupMappingStatus[]

  constructor(
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog,
    private overlay: Overlay,
    private toastr: ToastrService,
    private store: Store,
    private xsstore: xsStore,
    private fb: FormBuilder,
    private vcr: ViewContainerRef,
    private route: ActivatedRoute,
    private toast: ToastrService,
    private router: Router,
    private configService: ConfigService,
    private samlIdpServerFacade: SamlIdpServerFacade
  ) {
    this.qParams = route.snapshot.queryParams as UserQueryParams
    //based on the url, identify the 'create','update', 'clone' mode
    this.mode = this.router.url.includes('/user/clone')
      ? 'Clone'
      : this.router.url.includes('/user/edit')
      ? 'Edit'
      : 'Create'
    this.isADEnabled = this.configService.isADEnabled
    this.isIDPEnabled = this.configService.isIdpEnabled
    this.disableCreateUser =
      this.isADEnabled && this.mode == 'Create' ? true : false
  }

  prepareSelection(cellInfo: any): any[] {
    return cellInfo.data.Groups
  }

  isCreateMode() {
    return this.mode === 'Create'
  }

  isUpdateMode() {
    return this.mode === 'Edit'
  }

  isCloneMode() {
    return this.mode === 'Clone'
  }

  ngOnInit(): void {
    this.initForm()

    this.clientId = +localStorage.getItem('ClientId')

    this.loadValueForEdit()

    this.disableForcePasswordChange = false

    this.showOptionForDisablePasswordReset = false

    this.addUserForm
      .get('ClientName')
      .valueChanges.pipe(
        filter((res: number) => !!res && res > 0),
        takeUntil(this.toDestroy$)
      )
      .subscribe((clientid) => {
        this.clientId = clientid
        this.isLayoutLoading = true
        this.xsstore.dispatch(
          new FetchReviewLayoutsForSearch(
            this.clientId,
            this.qParams?.userID ?? 0
          )
        )
        if (this.mode !== 'Create' && this.assignedCases?.length) {
          this.selectedRows = this.assignedCases
        }

        this.store.dispatch(
          new GetUserCaseInfo(
            localStorage.UserId,
            this.qParams?.userID ?? -1,
            this.clientId
          )
        )
      })
    if (this.isADEnabled) {
      this.fetchADSettings()

      this.store
        .pipe(
          select(getControlSetting('ENABLE_MIXED_MODE')),
          takeUntil(this.toDestroy$)
        )
        .subscribe((isEnable: boolean) => {
          this.isMixMode = isEnable
          const createEnable = this.isADEnabled && this.isMixMode
          if (createEnable) {
            this.disableCreateUser = false
          }
        })
    }

    this.checkIdPGroupMappings()
  }

  private checkIdPGroupMappings(): void {
    this.samlIdpServerFacade.GetIdPGroupMappingSettingsStatus()

    this.samlIdpServerFacade.selectSamlIdPGroupMappingSettingsStatusResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({
        next: (response: ResponseModel) => {
          if (!response || !response.data) return

          this.cdr.markForCheck()
          if (!this.userDetails) return
          const { clientId, globalRoleId } = this.userDetails
          this.idPGroupMappingStatus = response.data
          const clientIdZeroEntry = response.data.find(
            (item) => item.clientId === 0
          )
          const clientIdEntry = response.data.find(
            (item) => item.clientId === clientId
          )

          if (clientId === 1 && globalRoleId === 1) {
            // Super User
            this.showImportUser = response.data.some(
              (item) => item.hasAnySettingDisabled
            )
          } else {
            // Non-Super User
            const entry = clientIdZeroEntry || clientIdEntry

            if (entry) {
              this.showImportUser = entry.hasAnySettingDisabled
            }
          }
        },
        error: (err) => {
          console.error(
            'Error while fetching IdP Group Mapping Settings Status:',
            err
          )
        }
      })
  }

  ngAfterViewChecked(): void {}

  private loadValueForEdit(): void {
    if (this.isUpdateMode() || this.isCloneMode()) {
      this.xsstore.dispatch(new GetUserById(this.qParams?.userID))
      this.addUserForm.markAsTouched()
    }
  }

  ngAfterViewInit(): void {
    timer(700)
      .pipe(debounceTime(500), takeUntil(this.toDestroy$))
      .subscribe({
        next: () => {
          this.cdr.markForCheck()
          this.initDxSelection()
        }
      })

    this.initSlice()

    this.initValidationRules()

    this.validationWatcher()

    /**
     * Listens `userRole` change event and updates the group role in
     * case grid
     */
    this.onRoleChanged()

    this.resetSelectedRowsInCreateMode()
  }

  private resetSelectedRowsInCreateMode() {
    if (this.isCreateMode()) {
      this.selectedRows = []
      this.selectedCases = []
      this.selectedUserRoleName = ''
    }
  }

  private initDxSelection() {
    this.dxGridCase?.selectedRowKeysChange
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe({
        next: (s) => [
          (this.selectedCasesCount = s?.length),
          (this.selectedCases = this.casesDatasource.filter(
            (c) => s.indexOf(c.ProjectId) > -1
          ))
        ]
      })
  }

  private initSlice() {
    this.fetchLoginSettings()
    this.store.dispatch(new GetUserRoles())
    if (this.isCreateMode()) {
      this.store.dispatch(
        new GetUserCaseInfo(localStorage.UserId, -1, this.clientId)
      )
    }

    this.store
      .pipe(
        select(userCaseInfo),
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.userCaseInformation = cloneDeep(res)
        this.selectedCases =
          this.mode === 'Create'
            ? []
            : this.userCaseInformation?.CaseGroupAssignedInfoList?.filter(
                (project) => {
                  return project.Assigned
                }
              )
        this.casesDatasource =
          this.userCaseInformation.CaseGroupAssignedInfoList.map((el) => {
            /**
             * `Groups` being used in case grid group selection.
             */
            el.AvailableGroups.push({ GroupId: -1, GroupName: 'None' })

            const selectedRoleId = this.addUserForm.get('UserRole').value
            let projectGroupId = -1
            if (this.mode === 'Edit') projectGroupId = el.GroupId
            else {
              const group = el.AvailableGroups.find(
                (c) => c.GlobalRoleId === selectedRoleId
              )
              projectGroupId = group ? group.GroupId : -1
            }

            return {
              ...el,
              GroupId: projectGroupId,
              Groups: el.AvailableGroups
            }
          })

        this.updatedCaseDataSource = this.casesDatasource
      })

    this.store
      .pipe(
        select(roleWiseRightDetails),
        distinctUntilChanged(),
        map((res: UserRightModel[]) => res),
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.roleWiseRights = [...res]
      })

    of(this.store.dispatch(new GetGlobalRoles()))
      .pipe(
        switchMap(() => this.store.select(globalRoles)),
        debounceTime(500),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (res: UserRoleListResponse) => {
          this.userRoleList = res.data
            // should filter out in API instead of here
            .filter(
              (r) => !r.globalRoleName.includes('OnDemand Client External User')
            )
            .map((r) => ({
              globalRoleName: r.globalRoleName,
              globalRoleId: r.globalRoleId,
              groupId: r.groupId
            }))
          // workaround end

          // says it's an ID of project Admin which was already here previously.
          const projectAdmin = res.data.find((r) =>
            r.globalRoleName?.includes('Project Admin')
          )
          setTimeout(() => {
            if (this.mode === 'Create') {
              this.addUserForm.get('UserRole').reset(projectAdmin?.globalRoleId)
              this.dxGridCase.instance.clearSelection()
            }
            this.addUserForm.markAsPristine()
            this.addUserForm.markAsUntouched()
            this.addUserForm.markAsPending()
          }, 500)
        }
      })

    this.store
      .pipe(
        select(userRoles),
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.userData = res.data
      })

    this.xsstore
      .select(UserStateSelector.SliceOf('userInformation'))
      .pipe(
        filter((d) => !!d),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (res: UserInformation) => {
          this.cdr.markForCheck()
          this.isUpdateMode() ? (this.oldEmail = res.email) : ''

          if (res.isIdPUser && this.isUpdateMode()) {
            this.updateControlStateAsPerGroupMappingState(+res.clientId)
          }

          const data: UserModel = {
            UserID: this.isUpdateMode() ? +res.userID : 0,
            EmailAddress: this.isUpdateMode() ? res.email : '',
            FullName: this.isUpdateMode()
              ? res.fullName
              : this.isCloneMode()
              ? res.fullName + '-Clone'
              : '',
            GlobalRoleId: res.globalRoleId,
            Phone: this.isUpdateMode()
              ? res.phone
              : this.isCloneMode()
              ? res.phone
              : '',
            UserGlobalRoleId: res.globalRoleId,
            IsUserLocked: res.isUserLocked,
            IsUserDeactivated: res.isUserDeactivated,
            ClientId:
              this.isUpdateMode() || this.isCloneMode()
                ? +res.clientId
                : this.clientId,
            LoggedInUserId: +res.userID,
            UserCaseAssignmentModel: res.userCaseAssignmentModel,
            UserLayoutId: res.userlayoutId,
            ForceUserToChangePassword: res.forceUserToChangePassword,
            DisablePasswordReset: res.disablePasswordReset,
            DeactivationModel: {
              UserId: this.isUpdateMode() ? +res.userID : 0,
              DeactivationReason: this.isUpdateMode()
                ? res.deactivationReason
                : ''
            }
          }

          // show option to disable password reset when editing super user only
          this.showOptionForDisablePasswordReset =
            +res.userID === 1 && res.globalRoleId === 1

          this.selectedUserRoleName = res.globalRoleName
          this.assignedCases =
            res.userCaseAssignmentModel.caseGroupAssignedInfoList
              .filter((c) => c.assigned)
              .map((c) => c.projectId)
          this.selectedRows = this.assignedCases
          this.casesDatasource =
            res.userCaseAssignmentModel.caseGroupAssignedInfoList.map((el) => {
              /**
               * `Groups` being used in case grid group selection.
               */
              return {
                Assigned: el.assigned,
                CaseName: el.caseName,
                ClientMatterNumber: el.clientMatterNumber,
                GroupId: el.groupId,
                ProjectId: el.projectId,
                ProjectName: el.projectName,
                Groups: el.availableGroups.map((c) => {
                  return {
                    GroupId: c.groupId,
                    GroupName: c.groupName,
                    GlobalRoleId: c.globalRoleId
                  }
                })
              }
            })
          this.updatedCaseDataSource = this.casesDatasource
          this.clientId = data.ClientId
          this.patchForm(data)
          if (!res.isIdPUser)
            this.disableForcePasswordChange = !!data.ForceUserToChangePassword

          this.isWorking = false
        }
      })

    this.xsstore
      .select(ReviewSetStateSelector.SliceOf('reviewLayouts'))
      .pipe(
        filter((res) => !!res),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe((reviewLayouts) => {
        this.layouts = reviewLayouts

        //Set the default review layout template in create mode.
        if (this.isCreateMode()) {
          const defaultLayout = this.layouts.find(
            (layout) => layout.name === 'DefaultReviewLayout'
          )
          this.addUserForm
            .get('UserLayout')
            .setValue(defaultLayout?.reviewLayoutId)
        }
        if (this.isCreateMode()) {
          this.isWorking = false
        }
        this.isLayoutLoading = false
      })

    this.getClientList()
  }

  private fetchLoginSettings(): void {
    this.xsstore
      .dispatch(new FetchLoginSettingAction())
      .pipe(
        switchMap(() =>
          this.xsstore.select(LoginSettingsSelectors.SliceOf('settings'))
        ),
        filter((res) => !!res),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((setting) => {
        this.loginSettings = setting
      })
  }

  private updateControlStateAsPerGroupMappingState(clientId: number): void {
    const idpGroupMappingState: IdPGroupMappingStatus | undefined =
      this.idPGroupMappingStatus?.find((item) => item.clientId === clientId)

    if (idpGroupMappingState && clientId > 0) {
      this.addUserForm.patchValue({
        ClientName: clientId
      })
      // Disable the ClientName field
      this.addUserForm.get('ClientName').disable()
    } else {
      this.addUserForm.get('ClientName')?.enable()
    }

    if (idpGroupMappingState?.chkADGroupAdminLevel) {
      this.addUserForm.controls['UserRole'].disable()
      this.addUserForm.patchValue({ UserRole: '' })
    } else {
      this.addUserForm.controls['UserRole'].enable()
    }

    this.disableGrid = !!idpGroupMappingState?.chkADGroupProjectLevel

    if (idpGroupMappingState?.chkADGroupActiveUsers) {
      this.addUserForm.controls['IsUserLocked'].disable()
      this.addUserForm.controls['IsUserDeactivated'].disable()
      this.disableForcePasswordChange = true
      this.addUserForm.controls['DisablePasswordReset'].disable()
    } else {
      this.addUserForm.controls['IsUserLocked'].enable()
      this.addUserForm.controls['IsUserDeactivated'].enable()
      this.disableForcePasswordChange = false
      this.addUserForm.controls['DisablePasswordReset'].enable()
    }
  }

  onRowUpdated(event): void {
    const updatedRow: CaseGroupAssignedInfo = event?.data
    this.updatedCaseDataSource = this.casesDatasource.map((c) => {
      return {
        ...c,
        GroupId:
          c.ProjectId === updatedRow.ProjectId ? updatedRow.GroupId : c.GroupId
      }
    })
  }

  private initValidationRules() {
    this.genericValidator = new GenericValidator({
      // the parent properties are same as the form group property and
      // the child properties are the either of angular validator or custom property
      EmailAddress: {
        required: 'Email Address is a required field.',
        pattern: 'Invalid Email Address.'
      },
      FullName: {
        required: 'Full Name is a required field.',
        pattern: 'Special characters and digits are not allowed',
        minlength: 'Minimum length exceed 2'
      },
      Phone: {
        pattern: 'Phone number is incorrectly formatted',
        minlength: 'Minimum length exceed 6'
      },
      UserRole: {
        required: 'User Role is a required field.'
      },
      ClientName: {
        required: 'Client Name is a required field.'
      },
      Password: {
        passwordComplexity: 'Password complexity does not meet the requirements'
      },
      ConfirmPassword: {
        passwordMismatch: 'Password and Confirm Password do not match.'
      }
    })
  }

  private validationWatcher() {
    this.genericValidator
      .initValidationProcess(this.addUserForm, this.formInputElements)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({
        next: (m) => [(this.displayMessage = m)]
      })
  }

  private patchForm(d) {
    this.addUserForm.patchValue({
      UserRole: d.UserGlobalRoleId,
      UserLayout: d.UserLayoutId,
      ClientName: d.ClientId,
      Reason: d.DeactivationModel?.DeactivationReason,
      ...d
    })
    this.addUserForm.markAsDirty()
    this.addUserForm.markAllAsTouched()
  }

  ngOnDestroy(): void {
    this.selectedRows = []
    this.selectedCases = []
    this.isEmailAlreadyInUse = false
    this.toDefault()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  private initForm(): void {
    this.addUserForm = this.fb.group({
      UserID: 0,
      EmailAddress: [
        '',
        [
          Validators.required,
          Validators.pattern(
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          )
        ]
      ],
      FullName: ['', [Validators.required, Validators.minLength(2)]],
      Phone: ['', [Validators.pattern(/^\d+$/)]],
      UserRole: [null, Validators.required],
      UserLayout: [null, Validators.required],
      IsUserLocked: false,
      IsUserDeactivated: false,
      ClientName: [null, Validators.required],
      Password: [
        null,
        {
          updateOn: 'blur',
          validators: [
            Validators.required,
            this.passwordComplexityValidator.bind(this)
          ]
        }
      ],
      ConfirmPassword: [
        null,
        {
          updateOn: 'blur',
          validators: this.confirmPasswordValidator()
        }
      ],
      ForceUserToChangePassword: false,
      DisablePasswordReset: false,
      Reason: ['']
    })
  }

  private formatValidationMessages(messages: string[]): string {
    if (messages.length === 1) return messages[0]
    if (messages.length === 2) return `${messages[0]} and ${messages[1]}`
    return `${messages.slice(0, -1).join(', ')}, and ${
      messages[messages.length - 1]
    }`
  }

  private passwordComplexityValidator(control: AbstractControl) {
    const password = control.value || ''
    const errors: any = {}
    const errorMessages: string[] = []

    if (!password) {
      this.passwordValidationMessage = ''
      return null
    }

    // Validation rules

    if (this.loginSettings?.checkEditLowerCase && !/[a-z]/.test(password)) {
      errorMessages.push('one lowercase letter')
    }
    if (this.loginSettings?.checkEditUpperCase && !/[A-Z]/.test(password)) {
      errorMessages.push('one uppercase letter')
    }
    if (this.loginSettings?.checkEditNumeric && !/[0-9]/.test(password)) {
      errorMessages.push('one number')
    }
    if (
      this.loginSettings?.checkEditSpecialCharacter &&
      !/[!@#$%^&*(),.?":{}|<>]/.test(password)
    ) {
      errorMessages.push('one special character')
    }
    if (
      this.loginSettings?.checkEditPwdLength &&
      password.length < this.loginSettings.spinEditPwdLength
    ) {
      errorMessages.push(
        `at least ${this.loginSettings.spinEditPwdLength} characters`
      )
    }

    if (errorMessages.length > 0) {
      errors.passwordComplexity = true
      this.passwordValidationMessage = `Password must include ${this.formatValidationMessages(
        errorMessages
      )}.`
    }

    return Object.keys(errors).length ? errors : null
  }

  confirmPasswordValidator() {
    return (control: FormControl) => {
      const pwd = control.parent?.get('Password')?.value ?? ''
      const confirmPwd = control.parent?.get('ConfirmPassword')?.value ?? ''
      if (pwd !== confirmPwd) {
        return { passwordMismatch: true }
      }

      return null
    }
  }

  showRights(template: TemplateRef<any>) {
    const portal = new TemplatePortal(template, this.vcr)
    const overlayRef = this.overlay.create({
      disposeOnNavigation: true,
      positionStrategy: this.overlay
        .position()
        .global()
        .centerHorizontally()
        .centerVertically(),
      hasBackdrop: true,
      width: '450px',
      scrollStrategy: this.overlay.scrollStrategies.block()
    })
    overlayRef.attach(portal)
    this.rightsDialog = overlayRef
  }

  /**
   * Whether the logged-in user has right to lock users
   */
  readonly hasLockRight$ = this.xsstore.select(
    StartupStateSelector.hasGlobalRight(UserRights.LOCK_USER)
  )

  hasValue = () =>
    Object.keys(this.addUserForm.value).filter(
      (k) => !!this.addUserForm.value[k]
    ).length > 0

  saveChanges() {
    this.formErrorMessage = null

    this.addUserForm
      .get('EmailAddress')
      .valueChanges.pipe(debounceTime(400), takeUntil(this.toDestroy$))
      .subscribe({
        next: () => (this.isEmailAlreadyInUse = false)
      })

    //when api throws error it is resetting the grid values so commenting it and adding manual validation for mandatory fields
    // const errorMessage = validateBeforeSubmit(this.addUserForm)
    // if (errorMessage) {
    //   this.formErrorMessage = errorMessage
    //   return false
    // }

    const formValue = this.addUserForm.value
    if (formValue.Phone?.length > 10) {
      this.toastr.error('Invalid Mobile Number.')
      return
    }

    let clientName = formValue.ClientName
    if (!clientName) {
      clientName = this.addUserForm.get('ClientName').value
    }
    let userRole = formValue.UserRole
    if (!userRole) {
      const controlValue = this.addUserForm.get('UserRole').value
      userRole = this.addUserForm.controls['UserRole'].disabled
        ? controlValue || 0
        : controlValue
    }

    // Skip the required validation for UserRole if the control is disabled
    if (
      formValue.EmailAddress.length <= 0 ||
      formValue.FullName.length <= 0 ||
      clientName.length <= 0 ||
      (!this.addUserForm.controls['UserRole'].disabled &&
        userRole.length <= 0) ||
      formValue.UserLayout.length <= 0
    ) {
      this.formErrorMessage =
        'All fields marked with an asterisk (*) are required. Please check the form below.'
      return
    }

    if (formValue.Password && !this.addUserForm.controls.Password.valid) {
      this.formErrorMessage =
        'Password complexity does not meet the requirements'
      return
    }

    if ((formValue.Password ?? '') !== (formValue.ConfirmPassword ?? '')) {
      this.formErrorMessage = 'Password and Confirm Password do not match.'
      return
    }

    if (!this.addUserForm.controls.EmailAddress.valid) {
      this.toastr.error('Invalid Email Address.')
      return
    }

    let data = null
    if (!this.updatedCaseDataSource?.length) {
      this.updatedCaseDataSource = this.casesDatasource
    }
    if (this.selectedUserRoleName !== 'Legal Admin') {
      // const hasSelectedCase = this.selectedCases.some((g) => g.GroupId > 0)
      // if (!hasSelectedCase) {
      //   this.notific.showModal('Please select group for all assigned cases.')
      //   return
      // }
      data = this.updatedCaseDataSource?.map((el) => {
        const isEq = this.dxGridCase.selectedRowKeys.includes(el.ProjectId)
        return {
          ...el,
          Assigned: isEq
        }
      })
    }
    const genPassword = this.isUpdateMode()
      ? this.generatePw.value
      : false ?? false
    const addUserParam: UserModel = {
      ...formValue,
      // UserID: payload.user,
      LoggedInUserId: localStorage.UserId,
      ClientId: this.clientId,
      GlobalRoleId: userRole,
      UserGlobalRoleId: userRole,
      UserLayoutId: formValue.UserLayout,
      PhoneNumber: formValue.Phone,
      Email: formValue.EmailAddress,
      UserCaseAssignmentModel:
        data?.length > 0
          ? { CaseGroupAssignedInfoList: data, UserId: formValue.UserID }
          : {},
      userDetails: this.importUserDetails,
      isADUser: this.isADUser,
      isIdpUser: this.isIDPUser,
      generatePassword: genPassword,
      DeactivationModel: {
        UserId: formValue.UserID,
        DeactivationReason: formValue.Reason
      }
    }
    this.isSubmitting = true
    this.xsstore
      .dispatch(new AddOrUpdateUser(addUserParam))
      .pipe(
        switchMap(() =>
          this.xsstore.select(UserStateSelector.SliceOf('addUpdateResponse'))
        ),
        takeUntil(this.toDestroy$),
        tap(() => (this.isSubmitting = false)),
        filter((res) => !!res)
      )
      .subscribe({
        next: (res: ResponseModel) => {
          this.cdr.markForCheck()
          if (res.status === 'Success') {
            if (+addUserParam.UserID === +addUserParam.LoggedInUserId) {
              this.xsstore.dispatch(new GetUserLayoutId(addUserParam.UserID))
            }
            this.toDefault()
            this.toast.success(res.message)
            this.xsstore.dispatch(new ClearResponse())
            this.xsstore.dispatch(new Navigate(['/admin/system/user/manage']))
          } else if (res.status == undefined) {
            this.toastr.error('An error occurred while creating user.')
          } else {
            this.toastr.error(res.message)
          }
        }
      })
  }

  loadRights() {
    this.dialog.open(UserRightsComponent, {
      width: '450px',
      maxHeight: '80vh',
      autoFocus: false,
      closeOnNavigation: true,
      disableClose: true,
      data: {
        // The selected project ID being supplied to the reason box so we can use there.
        roleWiseRights: this.roleWiseRights
      }
    })
  }

  loadUserRole(): void {
    this.getUserSelectedRole()
    this.dialog.open(UserRoleComponent, {
      minWidth: '450px',
      maxWidth: 'calc(75% - 100px)',
      maxHeight: '80vh',
      autoFocus: false,
      closeOnNavigation: true,
      disableClose: true,
      data: {
        selectedUserRole: this.selectedUserRole
      }
    })
  }

  getUserSelectedRole(): void {
    const selectedRoleId = this.addUserForm.get('UserRole').value
    const selectedUserRole = this.userRoleList.find(
      (x) => x.globalRoleId === selectedRoleId
    )
    this.selectedUserRole = selectedUserRole
  }

  private toDefault() {
    const projectAdmin = this.userRoleList?.find((r) =>
      r.globalRoleName?.includes('Project Admin')
    )?.globalRoleId
    const initialValues = {
      UserID: 0,
      UserRole: projectAdmin,
      ClientName: 0,
      EmailAddress: '',
      FullName: ''
    }
    this.selectedCases = []
    this.selectedRows = []
    this.selectedCasesCount = 0
    this.layouts = []
    this.isLoading = false
    if (this.dxGridCase != null && this.dxGridCase.instance != null) {
      this.dxGridCase.instance.clearSelection()
    }
    this.addUserForm.reset(initialValues)
  }

  /**
   * makes project's group selected by selected role Id
   */
  private onRoleChanged() {
    this.addUserForm
      .get('UserRole')
      .valueChanges.pipe(
        filter((val) => !!val),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (id: number) => {
          const roleGroup = this.userRoleList.find((g) => g.globalRoleId === id)
          if (!roleGroup) return
          this.casesDatasource.forEach((u) => {
            const group = u.Groups.find(
              (c) => c.GlobalRoleId == roleGroup.globalRoleId
            )
            u.GroupId = group ? group.GroupId : -1
          })
          this.updatedCaseDataSource = this.casesDatasource
          setTimeout(() => {
            this.store.dispatch(new GetRoleWiseRightsDetails(id))
          }, 500)
        }
      })
  }

  resetForm(tpl: TemplateRef<HTMLDialogElement>) {
    const dRef = this.dialog.open(tpl, {
      autoFocus: false,
      closeOnNavigation: true,
      width: '380px'
    })
    setTimeout(() => {
      const actions = document.querySelectorAll('.close-confirm')
      fromEvent(actions, 'click')
        .pipe(takeUntil(this.toDestroy$))
        .subscribe({
          next: (e) => {
            const isReset =
              (e.currentTarget as HTMLButtonElement).getAttribute(
                'data-reset'
              ) === 'true'
            if (isReset) {
              const r = this.userRoleList.find(
                (n) => n.globalRoleName?.toLowerCase() === 'project admin'
              )
              this.addUserForm.reset()
              this.dxGridCase?.selectedRowKeysChange.emit([])
              this.addUserForm.get('UserRole').reset(r?.globalRoleId)
              this.dxGridCase.instance.refresh()
            }
            dRef.close()
          }
        })
    }, 1000)
  }

  createLayout() {
    this.router.navigate(['admin/system/layout/create'])
  }

  onUserRoleSelectionChange(e): void {
    this.setSelectedGlobalUserRole(e.value)
  }

  setSelectedGlobalUserRole(selectedGlobalRoleId: number): void {
    const selectedUserRole = this.userRoleList.find(
      (x) => x.globalRoleId === selectedGlobalRoleId
    )

    this.selectedUserRoleName = selectedUserRole.globalRoleName
  }

  private getClientList(): void {
    this.xsstore
      .dispatch(new ClientListAction())
      .pipe(
        takeUntil(this.toDestroy$),
        switchMap(() =>
          this.xsstore.select(
            ClientMgmtStateSelector.SliceOf('clientListModel')
          )
        )
      )
      .subscribe((data) => {
        if (data) {
          this.clientData = data
          if (this.mode === 'Create') {
            this.addUserForm.get('ClientName').patchValue(this.clientId)
          }
        }
      })

    this.store.pipe(select(getUserDetails), take(1)).subscribe((uDetails) => {
      this.userDetails = uDetails
    })
  }

  onImportUser() {
    const dialogRef = this.dialog.open(ImportAdComponent, {
      disableClose: true,
      closeOnNavigation: true,
      width: '70vw'
    })

    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        if (res) {
          this.cdr.markForCheck()
          const { selectedUser, selectedClientId } = res.data
          if (this.isADEnabled) {
            this.isADUser = true
          } else if (this.isIDPEnabled) {
            this.isIDPUser = true
          }
          this.addUserForm.patchValue({
            FullName: selectedUser[0].fullName,
            EmailAddress: selectedUser[0].email
          })

          this.disableCreateUser = false
          this.importUserDetails = selectedUser[0]

          if (this.isIDPUser)
            this.updateControlStateAsPerGroupMappingState(selectedClientId)
        } else {
          this.isADUser = false
          this.isIDPUser = false
        }
      })
  }

  updateUser() {
    if (
      this.addUserForm.get('EmailAddress').value !== this.oldEmail &&
      this.isUpdateMode()
    ) {
      this.updateEmail()
    } else {
      this.saveChanges()
    }
  }

  updateEmail() {
    const dialogRef = this.dialog.open(this.updateEmailDialog, {
      disableClose: true,
      closeOnNavigation: true
    })

    dialogRef
      .afterClosed()
      .pipe(
        filter((yes) => yes),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.saveChanges()
      })
  }

  fetchADSettings() {
    this.xsstore.dispatch(new FetchADLoginSettingAction())

    /**
     * Selects ad settings data from the store.
     */
    this.xsstore
      .select(ADLoginSettingsSelectors.SliceOf('ADSettings'))
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((setting) => {
        if (
          !setting.chkADGroupAdminLevel ||
          !setting.chkADGroupProjectLevel ||
          !setting.chkADGroupApplicationLevel ||
          !setting.chkADGroupActiveUsers
        ) {
          this.allGroupsMapped = false
        } else {
          this.allGroupsMapped = true
        }
        if (setting.chkADGroupAdminLevel) {
          this.addUserForm.controls['UserRole'].disable()
        } else {
          this.addUserForm.controls['UserRole'].enable()
        }
        if (setting.chkADGroupProjectLevel) {
          this.disableGrid = true
        } else {
          this.disableGrid = false
        }
      })
  }
}
