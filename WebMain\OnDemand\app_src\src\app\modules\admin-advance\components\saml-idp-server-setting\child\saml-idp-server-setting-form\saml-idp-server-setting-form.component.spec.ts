import { SamlIdpServerComponentService } from '@admin-advance/components/saml-idp-server-setting/saml-idp-server-component.service'
import { SamlIdpServerSettingTestingModule } from '@admin-advance/components/saml-idp-server-setting/saml-idp-server-setting-testing.module'
import { getFormData } from '@admin-advance/components/saml-idp-server-setting/saml-idp-testing-data'
import { LoadGroupTypes } from '@admin-advance/models/saml-idp-server/saml-idp-server.model'
import { SamlIdpServerSettingsService } from '@admin-advance/services'
import { SamlIdpServerFacade } from '@admin-advance/store'
import { SimpleChanges } from '@angular/core'
import {
  ComponentFixture,
  fakeAsync,
  flush,
  TestBed,
  tick
} from '@angular/core/testing'
import { MatDialogModule } from '@angular/material/dialog'
import { By } from '@angular/platform-browser'
import { UiButtonComponent } from '@shared/components/ui-button/ui-button.component'
import { ResponseModel } from '@shared/models'
import { ToastrService } from 'ngx-toastr'
import { of } from 'rxjs'
import { SamlIdpServerSettingFormComponent } from './saml-idp-server-setting-form.component'

describe('SamlIdpServerSettingFormComponent', () => {
  let component: SamlIdpServerSettingFormComponent
  let fixture: ComponentFixture<SamlIdpServerSettingFormComponent>
  let samlIdpServerFacade: SamlIdpServerFacade
  let samlIdpServerComponentService: SamlIdpServerComponentService
  let toastrService: ToastrService
  let samlIdpServerSettingsService: SamlIdpServerSettingsService

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      teardown: { destroyAfterEach: true },
      declarations: [SamlIdpServerSettingFormComponent],
      imports: [SamlIdpServerSettingTestingModule, MatDialogModule]
    }).compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(SamlIdpServerSettingFormComponent)
    component = fixture.componentInstance
    samlIdpServerFacade = TestBed.inject(SamlIdpServerFacade)
    samlIdpServerComponentService = TestBed.inject(
      SamlIdpServerComponentService
    )
    toastrService = TestBed.inject(ToastrService)
    samlIdpServerSettingsService = TestBed.inject(SamlIdpServerSettingsService)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it(`should create instance of FormGroup for SAML-Idp form when ngOnInit hook is called`, () => {
    // GIVEN saml form instance undefined
    component.samlIdpForm = undefined
    const initSamlFormSpy = jest.spyOn(
      samlIdpServerComponentService,
      'initSamlForm'
    )

    // WHEN saml form component hook is ngOnInit called
    component.ngOnInit()

    // THEN should create an instance of FormGroup
    expect(initSamlFormSpy).toHaveBeenCalledTimes(1)
    expect(component.samlIdpForm).toBeDefined()
  })

  it(`should disable the SAML-Idp form when 'Enable SAML-Idp Server Based Authentication' checkbox is unchecked`, () => {
    // GIVEN Enable SAML-Idp Server Based Authentication checkbox
    const initSamlFormSpy = jest.spyOn(
      samlIdpServerComponentService,
      'initSamlForm'
    )

    // WHEN Enable SAML-Idp Server Based Authentication checkbox is unchecked
    component.ngOnInit()
    component.isParentEnable = false

    // THEN disabled the SAML-Idp FormGroup
    expect(initSamlFormSpy).toHaveBeenCalledTimes(1)
    expect(component.samlIdpForm).toBeDefined()
    expect(component.samlIdpForm.disabled).toBeTruthy()
  })

  it(`should enable the SAML-Idp form when 'Enable SAML-Idp Server Based Authentication' checkbox is checked`, () => {
    // GIVEN Enable SAML-Idp Server Based Authentication checkbox
    const isParentEnable = true
    const initSamlFormSpy = jest.spyOn(
      samlIdpServerComponentService,
      'initSamlForm'
    )
    const isParentEnableSimpleChanges: SimpleChanges = {
      isParentEnable: {
        firstChange: false,
        previousValue: false,
        currentValue: isParentEnable,
        isFirstChange(): boolean {
          return false
        }
      }
    }

    // WHEN user clicks on checkbox to enable Server Based Authentication checkbox
    component.ngOnInit()
    component.isParentEnable = isParentEnable
    component.ngOnChanges(isParentEnableSimpleChanges)

    // THEN enable the SAML-Idp FormGroup
    expect(initSamlFormSpy).toHaveBeenCalledTimes(1)
    expect(component.samlIdpForm).toBeDefined()
    expect(component.samlIdpForm.disabled).toBeFalsy()
  })

  it.each([
    ['Load groups', 'Token', LoadGroupTypes.TOKEN],
    ['Load groups from file', 'file having group name', LoadGroupTypes.FILE],
    ['Load groups', 'user profile', LoadGroupTypes.PROFILE]
  ])(
    `should change load groups button label to '%s' when load group type is selected to '%s'`,
    fakeAsync(
      (
        expectedLabel: string,
        radioLabel: string,
        groupType: LoadGroupTypes
      ) => {
        // GIVEN load saml group button label
        component.loadButtonLabel = ''
        component.ngOnInit()

        // WHEN group type radio button option is changed
        component.ngAfterViewInit()
        component.loadGroupTypeControl.setValue(groupType)

        tick(200)

        // THEN change load group button label accordingly
        expect(component.loadButtonLabel).toBe(expectedLabel)

        fixture.destroy()
        flush()
      }
    )
  )

  it(`should show loader when load groups button is clicked`, () => {
    // GIVEN load saml group button
    component.isLoading = false

    // WHEN load group button is clicked
    component.loadSamlGroups()

    // THEN show SAML data loader
    expect(component.isLoading).toBeTruthy()
  })

  it(`should notify to loader event emitter when load groups button is clicked`, () => {
    // GIVEN load groups button
    const isLoading = true
    component.isLoading = false
    const loadingChangedSpy = jest.spyOn(component.loadingChanged, 'emit')

    // WHEN load groups button is clicked
    component.loadSamlGroups()

    // THEN notify groups is loading
    expect(component.isLoading).toBeTruthy()
    expect(loadingChangedSpy).toHaveBeenCalledTimes(1)
    expect(loadingChangedSpy).toHaveBeenCalledWith(isLoading)
  })

  it(`should disable the load groups button and show spinner when load groups button is clicked`, () => {
    // GIVEN load groups button
    component.isLoading = false

    // WHEN load groups button is clicked
    component.loadSamlGroups()
    fixture.detectChanges()

    const loadGroupComponent = fixture.debugElement.query(
      By.directive(UiButtonComponent)
    )
    const loadGroupButton: UiButtonComponent =
      loadGroupComponent.componentInstance

    // THEN notify groups is loading
    expect(loadGroupButton.disabled).toBeTruthy()
    expect(loadGroupButton.showSpinner).toBeTruthy()
    expect(loadGroupButton.showSpinner).toBeTruthy()
  })

  it(`should dispatch fetch groups action with form data payload when user clicks on load groups button`, () => {
    // GIVEN form data
    const mockFormPayload = getFormData()
    component.ngOnInit()

    // WHEN load groups button clicked
    const checkSamlIdpGroupDataSpy = jest
      .spyOn(samlIdpServerSettingsService, 'checkMissingIdpGroups')
      .mockImplementation(() => of(mockFormPayload))

    component.loadSamlGroups()

    // THEN should check for missing groups and load groups data according to payload
    expect(component.isLoading).toBeTruthy()
    expect(checkSamlIdpGroupDataSpy).toHaveBeenCalledTimes(1)
    expect(checkSamlIdpGroupDataSpy).toHaveBeenCalledWith(mockFormPayload)
  })

  it(`should not patch values of SAML-Idp settings in the form when xml meta file being parsed and patch its value to form`, fakeAsync(() => {
    // GIVEN xml meta file
    const xmlText = 'i am xml text'
    const file = {
      text: () => Promise.resolve(xmlText)
    }
    const event: Event = { target: { files: [file] } } as any

    // WHEN xml meta file being parsed
    component.ngAfterViewInit()
    component.onXmlMetafileChange(event)
    const fetchSamlIdpServerSettingParseXmlMetafileSpy = jest.spyOn(
      samlIdpServerFacade,
      'fetchSamlIdpServerSettingParseXmlMetafile'
    )

    tick(400)
    flush()

    // THEN should patch parsed data instead of saved SAML-Idp setting
    expect(component.isXmlMetafileParsing).toBeTruthy()
    expect(fetchSamlIdpServerSettingParseXmlMetafileSpy).toHaveBeenCalledTimes(
      1
    )
    expect(fetchSamlIdpServerSettingParseXmlMetafileSpy).toHaveBeenCalledWith(
      xmlText
    )
  }))

  it(`should show error message when trying to load SAML-Idp admin groups using invalid token`, () => {
    // GIVEN invalid token
    const token = 'invalid'
    const message = 'token is invalid'
    const errorResponse: ResponseModel = { status: 'error', message }
    samlIdpServerFacade.selectSamlIdpGroupsData$ = of(errorResponse)
    const errorToastSpy = jest.spyOn(toastrService, 'error')

    // WHEN trying to load admin level groups
    component.ngOnInit()
    component.samlIdpForm.get('token').setValue(token)
    component.loadSamlGroups()

    // THEN should show error message
    expect(errorToastSpy).toHaveBeenCalledTimes(1)
    expect(errorToastSpy).toHaveBeenCalledWith(message)
  })
})
