import { Dictionary } from '@ngrx/entity'
import { SettingsInfo } from '@stores/models'
import { JsonObject, JsonProperty } from 'json2typescript'
import { FileTypeFilterModel } from './case.model'
import { SlipSheetContentModel } from './slip-sheet.model'
import { TiffColorOption } from './tiff-color-option.model'

export class CaseTemplateSettings {
  IngestionSetting: IngestionSetting

  AdvancedTiffOption?: AdvancedTiffOption

  TiffSetting?: TiffSetting

  AdvancedSettings?: AdvancedSettings

  AdvanceOcrOptionLanguages?: AdvanceOcrOptionLanguages[]

  TranscribeSetting?: TranscribeSetting
}
export interface TranscribeSetting {
  TranscribingEngine: string

  Automaticallyqueuetranscribe: boolean

  TranscribeFileTypes: TranscribeFiles[]
}

export interface IngestionSetting {
  LocalTimeZone: string
  TzTimeZone: string
  LotusNoteHtmlDateFormat: string
}

/**
 * Advance tiff options
 */
export interface AdvancedTiffOption {
  IgnoreAutoTiff?: boolean
  AutoGenerateTiff?: boolean
}

/**
 * Tiff setting
 */
export interface TiffSetting {
  DefaultTiffColorOption?: TiffColorOption
  IsGenerateColorTiff?: boolean
  AllowTiff?: boolean
}

export interface AdvancedSettings {
  SearchDuplicateOption: number
}
export interface TranscribeSupportedFiles {
  Engine?: string
  SupportedFiles?: string[]
}
export interface TranscribeFiles {
  fileName?: string
  selected?: boolean
}
export interface TranscribeAccessKeys {
  Engine?: string
  AccessKey?: string
}
/**
 * Advance OCR language option model.
 */
export interface AdvanceOcrOptionLanguages {
  /**
   * Name of a language
   */
  LangName?: string

  /**
   * Whether the language is derived (Selected) from a template
   */
  Selected?: boolean
}

@JsonObject('ProjectTemplateModelLaunchpad')
export class ProjectTemplateModel {
  @JsonProperty('Id', Number, true) Id: number = null

  @JsonProperty('Name', String, true) Name: string = null

  @JsonProperty('Note', String, true) Note: string = null

  @JsonProperty('IsDefaultTemplate', Boolean, true) IsDefaultTemplate: boolean =
    null
}

//ALL PROJECT INFORMATION FOR PROJECT CREATION,UPDATE,TEMPLATE DATA LISTING REFERS THIS CLASS ONLY
export class ProjectSetupInfo {
  //start:ingestion setting
  LocalTimeZone: string

  TzTimeZone: string

  LotusNoteHtmlDateFormat: string
  //end:ingestion setting

  //start:AdvancedTiffOption
  IgnoreAutoTiff?: boolean

  ///////AutoGenerateTiff?: boolean
  //end:AdvancedTiffOption

  //start:TiffSetting
  DefaultTiffColorOption?: TiffColorOption

  IsGenerateColorTiff?: boolean

  AllowTiff?: boolean
  //end:TiffSetting

  //start:AdvancedSettings
  SearchDuplicateOption: number
  //end:AdvancedSettings

  //start:AdvanceOcrOptionLanguages[]
  LanguageSettingsForOCR: string[]

  AdvanceOcrOptionLanguages?: AdvanceOcrOptionLanguages[] //this is maintained as it is in old flow
  //end:AdvanceOcrOptionLanguages[]

  //start:TranscribeSetting
  transcribeAllowedList: string[]

  AutoQueueTranscribe: boolean

  ProjectSettings: { [name in ProjectSetting]?: any }
  //end:TranscribeSetting

  //Start:Other Used Setting for getprojectinfo
  ProjectId?: number

  // @JsonProperty('ProjectName', Number, true)
  ProjectName?: string

  IndexOnlyCase?: boolean

  MatterNumber?: string

  //ExceptionHandling?: boolean

  //AutoQueueForEntityExtraction?: boolean

  Passwords?: string

  NotifyAfterImageGeneration?: boolean

  EnableTranscriptViewer: boolean

  SearchTerm?: string

  //AutoFolderRelativePathDuringIngestion?: boolean

  IngestionEngine?: number

  //EnableDiscoveryExceptionHandling?: boolean

  EnableNativeFileHandling?: boolean

  ProjectTemplateId?: number

  AutoGenerateImagesAfterIngestion?: boolean

  SettingsInfo?: SettingsInfo

  //TranscribeEngine?: string

  SearchTermList?: string[]

  ImageConversionOption: {
    TimeZone: string
    PasswordList: Array<string>
    AutoGenerateImagesAfterIngestion: boolean
  }
  //END:Other used setting

  //Filter System Type Setting
  DeNistingFlag?: boolean

  FileTypeFlag?: boolean

  //Indexing Setting
  IndexingFlag?: boolean

  IndexMetaData?: boolean

  IndexEmailHeader?: boolean

  IndexFullText?: boolean

  //Filter Duplicate Setting
  DedupingAlgo: DedupingAlgorithm = DedupingAlgorithm.SHA1

  HashAlgorithmSecondary?: DedupingAlgorithm = null

  SelectedHashField?: any = null

  //LI,EA,ET Setting
  LanguageIdentifier: boolean

  AllowEmailAnalysis: boolean

  AllowAutoLaunchEmailThreading: boolean

  ComputeInclusiveEmail: boolean

  //MissingEmailGenerate: boolean //In ProjectSetting

  //OCR Further Setting
  AutoQueueOCR: boolean

  OcrFileExtension?: OCRExtensions[]

  //Native File Options
  AutoQueueNative?: boolean

  //ConvertToMHT?: boolean //projectsettting

  //PreserverMHT?: boolean //projectsetting

  PreferedFileTypeNative?: NativeFileType[] = []

  NativeOptionPST_MSG: NativeFileType

  NativeOptionMBOX_EML: NativeFileType

  NativeOptionNSF_DXL: NativeFileType

  DoNotComputeLanguageIdentificationForSpreadsheets: boolean

  OcrAllowedList: OCRExtensionType[]

  AllowOCR?: boolean

  IsImageTypePDF?: boolean

  DefaultTiffTimeoutPeriod?: number

  MaxPages?: number

  AdvancedTiffOption: SystemBatesModel

  TiffFileClassPageLimit: TiffFileTypesPageLimit[] = []

  //HTML CONVERSION
  ShowHiddenRowColumnSheets: boolean

  ShowHiddenText: boolean

  ShowTrackChanges: boolean

  ShowSpreadsheetGridLines: boolean

  //Embedded Setting
  EmbeddedFilterSettingsEmail: EmbeddedFilterType =
    EmbeddedFilterType.EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES

  EmbeddedFilterSettingsEdoc: EmbeddedFilterType =
    EmbeddedFilterType.EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES

  EmbeddedFilterSettingsPPT: EmbeddedFilterType =
    EmbeddedFilterType.EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES

  //Meta Indexing
  MetaIndexingFields: MetaIndexingFields[] = []

  //NDD
  AutoQueueNDDSig: boolean

  NDDMinThershold: number

  NDDMinChar: number

  IncludeMetadataForNDD: boolean

  NDDDisclaimerList: string[]

  //Date Filter

  DateResctrictions: DateRestriction[] = []

  //Image File Extensions
  OverrideFileTypeAssignment: boolean

  dicTiffFileExtensionSetting: Dictionary<ImageFileExtension> = {}

  ImageFileExtensions: ImageFileExtension[] = []

  ArchivalFileExtension: FileExtensionArchivalContainer

  //OCR Advanced setting
  OcrForDocumentsWithFewerText: boolean

  OCRThresholdAverageCharacters: number

  OCRMinCharInPDfInAtleastOnePage: number

  IncludePartialOcredFile: boolean

  maxOcrTryCount: number

  FileExtensionFilterInclude: boolean

  ExtensionList: string[]

  fileExtensionAdvanceFilterOpt: FileExtensionAdvanceFilterOpt

  //Ingestion Setting
  minValidDateTime: Date

  NsfDateTimeFormat: string

  SetMeetingDate: boolean

  AutoCopyCrashDocumentFlag: boolean

  DefaultIngestionTimeout: number

  ExtractInternetEmailHeader: boolean

  PopulateCustodianDedup: boolean

  PopulateDuplicateFilePath: boolean

  //advanced ingestion option
  FileCopyOpt: string

  ShowAllFileCopyOptions: boolean

  DuplicateComputingOption: string

  DuplicateExcludeJobsSettings: DuplicateExcludeJobsSettings

  ADGroupMapping: ADGroupMapping[] = []

  ADSettingsXML: string

  FileTypeFilterInclude: boolean

  FileTypeFilters: Array<FileTypeFilterModel>

  defaultCustomFields: MyCustomField[]

  customFields: MyCustomField[]

  listFoldering: FolderModelProjectSetup[]

  IngestionFileTypeTimeOut: FileTypeTimeOut[] = []

  SocialMediaSettings: SocialMediaSettings = new SocialMediaSettings()

  IDPGroupMapping: ADGroupMapping[] = []

  ProjectTemplateName?: string

  ProjectTemplateNote?: string

  SlipSheetSettings: SlipSheetContentModel
}

export enum ProjectSetting {
  TRANSCRIBING_ENGINE = 'TRANSCRIBING_ENGINE',
  AUTO_GENERATE_MISSING_EMAILS = 'AUTO_GENERATE_MISSING_EMAILS',
  CONVERT_TO_MHT = 'CONVERT_TO_MHT',
  PRESERVE_MHT = 'PRESERVE_MHT',
  EXCEPTION_HANDLING = 'EXCEPTION_HANDLING',
  AUTO_QUEUE_FILES_FOR_ENTITY_EXTRACTION = 'AUTO_QUEUE_FILES_FOR_ENTITY_EXTRACTION',
  AUTO_FOLDER_RELATIVE_PATH = 'AUTO_FOLDER_RELATIVE_PATH',
  DO_NOT_EXTRACT_FUTURE_DATETIME = 'DO_NOT_EXTRACT_FUTURE_DATETIME',
  NSF_VIEW_EXTRACTION_OPTION = 'NSF_VIEW_EXTRACTION_OPTION',
  DUPLICATE_COMPUTING_OPTION = 'DUPLICATE_COMPUTING_OPTION',
  ENABLE_TRANSCRIPT = 'ENABLE_TRANSCRIPT',
  INTERNAL_DOMAIN = 'INTERNAL_DOMAIN',
  STOP_WORDS = 'STOP_WORDS',
  ENABLE_EDAI_ECA = 'ENABLE_EDAI_ECA',
  EDAI_ECA_BACKGROUND = 'EDAI_ECA_BACKGROUND',
  EDAI_ECA_RELEVANT_DESCRIPTION = 'EDAI_ECA_RELEVANT_DESCRIPTION',
  EDAI_ECA_NON_RELEVANT_DESCRIPTION = 'EDAI_ECA_NON_RELEVANT_DESCRIPTION'
}

export enum DedupingAlgorithm {
  /// <summary>
  /// MD5
  /// </summary>
  MD5 = 0,
  /// <summary>
  /// SHA1
  /// </summary>
  SHA1 = 1
}

export interface HashField {
  HashId: number
  HashName?: string
}

export interface OCRExtensions {
  ExtensionName?: string

  ExtNo?: number

  /**
   * Whether the language is derived (Selected) from a template
   */
  Selected?: boolean
}

export class OCRExtensionType {
  ExtNo?: number
}

export enum NativeFileTypeList {
  OUTLOOK_MSG = 0,
  MIMEOUTLOOKEML = 1,
  DXL_MAILMESSAGE = 2
}

export class NativeFileType {
  FileType: NativeFileTypeList

  DefaultFileType: string
}

export interface TiffFileTypesPageLimit {
  FileTypeGroupID: number
  PageLimit: number
  TiffColorOption: number
  TiffColorOptionToDisplay: string
  TiffTimeOutPeriod: number
  FileTypeGroup: string
}

export class SystemBatesModel {
  PrefixText: string // = 'IMG'

  PrefixField: string //= 'DOCUMENT_UNIQUE_IDENTIFIER'

  Padding: number // = 8

  StartingNum: number // = 1

  GenerateBates: boolean // = false

  BrandingBates: boolean // = false

  AutogenerateTiff: boolean //= false

  IgnoreAutoTiffJobsForMediaProcessingStatus: boolean // = false

  PrefixType: string // = 'TEXT'
}

export enum EmbeddedFilterType {
  EXCLUDE_NONE = 0,
  EXCLUDE_ALL_EMBEDDED_FILES = 1,
  EXCLUDE_ALL_EMBEDDED_IMAGE_FILES = 2,
  EXCLUDE_ALL_EMBEDDED_UNKNOWN_FILES = 3,
  EXLUCDE_ALL_EMBEDDED_IMAGE_AND_UNKNOWN_FILES = 4
}

export interface MetaIndexingFields {
  DisplayFieldName: string
  FieldId: number
  IsDefaultMetaIndexingField: boolean
  Selected: boolean
}

export interface DisclaimerList {
  DisclaimerText: string
}

export interface StopWord {
  id: number
  name: string
}
// export class FileTypeListImageExtension {
//   fileExtension: string

//   imageOption: string
// }
export interface FileTypeFilterList {
  name: string
}

export enum DocType {
  EMAIL = 'EMAIL',
  EDOC = 'EDOC',
  BOTH = 'BOTH'
}
export enum DateOption {
  GROUP_DATE = 'Group date',
  SENT_DATE = 'Sent date',
  RECEIVED_DATE = 'Received date',
  CREATED_DATE = 'Created date',
  MODIFIED_DATE = 'Modified date'
}

export enum DateOperator {
  BEFORE = 'BEFORE',
  AFTER = 'AFTER',
  EQUALS_TO = 'EQUALS_TO',
  BETWEEN = 'BETWEEN'
}

export interface DateRestriction {
  DateField: DateOption
  DocumentType: DocType
  FromDate: Date
  Operator: DateOperator
  Text?: string
  ToDate: Date
}

export interface DateFilterList {
  DateFilterText: string
}

export interface ImageFileExtensionDic {
  ImagingEngine: string
  ExtensionList: string[]
}

export interface ImageFileExtension {
  Extension: string
  ImagerPlugin: string
}

export interface FileExtensionArchivalContainer {
  TreatAsEdoc: boolean
  Extensions: string[]
}

export interface FileExtensionAdvanceFilterOpt {
  FilterLevel: string
  ApplyContainerFile: boolean
}

// export enum DuplicateComputingOption
// {

//     CUSTODIAN_PRIORITY=0,

//     MEDIA_ADDED=1,

//     MEDIA_PROCESSED=2,

//     DOCUMENT_PROCESSED=3
// }

// export enum FileCopyOption
// {
//     COPY_CREATE_SECONDCOPY_OF_SCANNED_FILES,
//     DONOT_COPY_CREATE_ARCHIVE_SECONDCOPY_OF_SCANNED_FILES,
//     DONOT_COPY_CREATE_SECONDCOPY_OF_SCANNED_FILES

// }

export interface DuplicateExcludeJobsSettings {
  ExcludeJob: boolean
  ExcludeLevel: string
  ExcludedJobs: Dictionary<ExcludeJobsDic>
}

export interface ExcludeJobsDic {
  JobName: string
  SelectedFlag: boolean
}
export class ADGroupMapping {
  venioadminlevel: string

  groupsid: string
}

export interface AllADGroups {
  groupsid: string
  adgroup: string
}

export class MyCustomField {
  AllowCoding: boolean

  AllowEmptyValue: boolean

  AllowMultipleCodingValues: boolean

  AllowPredefinedCodingValuesOnly: boolean

  CodingValues: string

  Connection: any

  CreatedBy: number

  CustomFieldInfoId: number

  DateFormat: string

  DbType: any

  DefaultValue: string

  DefaultValueForDb: string

  Delimeter: string

  Description: string

  DisplayName: string

  DisplaySequenceNumber: number

  DoNotMap: boolean

  FieldName: string

  FieldNameForDb: string

  FieldType: CustomFieldType

  FieldValue: string

  FieldValueForDb: any

  IndexForDefaultCustomField: boolean

  IndexName: string

  IsSearchable: boolean

  Length: number

  Precision: number

  Scale: number
}

export class FolderModelProjectSetup {
  FolderId: number

  FolderName: string

  ParentFolderId: number
}
export interface FileTypeTimeOut {
  FileTypeGroup: string
  ToolsValueId: number
  ToolsDisplayName: string
  ToolsFileTypeID: string
  TimeOut: string
}

export class SplitOptions {
  SplitType?: string

  SplitValue?: number
}

export class SocialMediaSettings {
  SplitOptions?: SplitOptions
}

export interface AllIDPGroups {
  groupsid: string
  idpgroup: string
}

export enum CustomFieldType {
  numeric = 0,
  text = 1,
  unicode_text = 2,
  paragraph = 3,
  unicode_paragraph = 4,
  date = 5,
  boolean = 6,
  datetime = 7
}
