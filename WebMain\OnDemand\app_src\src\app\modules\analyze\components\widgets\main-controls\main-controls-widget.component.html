<div class="analyze-controls">
  <div class="bg-white mb-10">
    <div class="row">
      <div class="col-md-6">
        <div class="row mt-1">
          <div
            *ngIf="
              (!isRVod || (isRVod && projectInfo?.IsFilteringServiceCase)) &&
              (allowToPerformSearch$ | async)
            "
            class="col-md-12"
          >
            <div class="input-group" [formGroup]="inputFrom">
              <div class="input-group-prepend" *ngIf="!showAdvanceFilterUi">
                <button
                  class="btn btn-{{ config.themeClient }}-primary"
                  type="button"
                  [title]="
                    userDocRestrictonMode === restrictionMode.Folder
                      ? 'Change Folder Scope'
                      : 'Change Custodian/Media Scope'
                  "
                  (click)="
                    actionClicked(
                      userDocRestrictonMode === restrictionMode.Folder
                        ? 'FOLDER_SCOPE'
                        : 'CUSTODIAN_MEDIA'
                    )
                  "
                >
                  <i class="fas fa-folder-open"></i>
                  <span class="mediaList-count" *ngIf="mediaIds?.length > 0">{{
                    mediaIds.length
                  }}</span>
                  <span
                    class="mediaList-count"
                    *ngIf="folderList?.length > 0"
                    >{{ folderList?.length }}</span
                  >
                </button>
              </div>
              <textarea
                style="resize: none"
                class="form-control"
                [placeholder]="
                  showAdvanceFilterUi
                    ? 'add and/or change field search value or any other filter criteria to generate a valid query'
                    : 'Type search term here...'
                "
                [attr.rows]="
                  areaExpanded && showAdvanceFilterUi ? 5 : areaExpanded ? 2 : 1
                "
                formControlName="expression"
                (keydown)="onSearchInputKeydown($event)"
              ></textarea>
              <ng-template
                cdkConnectedOverlay
                [cdkConnectedOverlayOrigin]="trigger"
                [cdkConnectedOverlayOpen]="openDuplicateOption"
                (overlayOutsideClick)="openDuplicateOption = false"
              >
                <app-search-duplicate-option></app-search-duplicate-option>
              </ng-template>
              <button
                cdkOverlayOrigin
                #trigger="cdkOverlayOrigin"
                class="btn btn-{{ client }}-primary"
                title="Search duplicate option"
                (click)="openDuplicateOption = true"
              >
                <fa-icon [icon]="['fas', 'clone']"></fa-icon>
                <fa-icon class="pl-1" [icon]="['fas', 'caret-down']"></fa-icon>
              </button>
              <ng-container
                *ngIf="!showAdvanceFilterUi"
                [ngTemplateOutlet]="actionButtons"
              ></ng-container>
            </div>
          </div>
        </div>
        <app-advance-search-query-builder
          [shown]="showAdvanceFilterUi"
          (cancelAdvanceSearch)="showAdvanceFilterUi = false"
          (queryValue)="setQuery($event)"
          (isEnableInputWrite)="toggleInputWritable = $event"
          *appHasUserGroupRights="[searchUiRight]"
          [formGroup]="inputFrom"
        >
          <app-advanced-search-query-builder-right>
            <div
              *ngIf="showAdvanceFilterUi"
              class="input-group text-nowrap h-100"
              style="border-top-left-radius: 0; border-bottom-left-radius: 0"
            >
              <ng-container *ngTemplateOutlet="actionButtons"></ng-container>
            </div>
          </app-advanced-search-query-builder-right>
        </app-advance-search-query-builder>
      </div>
      <div class="col-md-6">
        <div class="btn-toolbar float-right ml-2" role="toolbar">
          <div class="btn-group ml-2" role="group">
            <button
              [title]="
                localStats.isInitialSearch
                  ? 'Currently nothing to reset'
                  : 'Reset'
              "
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              (click)="actionClicked('SEARCH_RESET')"
              [disabled]="
                localStats.isResetSearching || localStats.isInitialSearch
              "
            >
              <i
                *ngIf="!localStats.isResetSearching || activeActionOf('SEARCH')"
                class="fas fa-redo-alt fa-md"
              ></i>
              <mat-spinner
                *ngIf="localStats.isResetSearching && !activeActionOf('SEARCH')"
                diameter="17"
                strokeWidth="2"
              ></mat-spinner>
            </button>
          </div>
          <div class="btn-group ml-2" role="group" *ngIf="isRVod">
            <button
              title="Discovery Report"
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              (click)="actionClicked('DISCOVERY_REPORT')"
            >
              <i class="far fa-lg fa-file-alt" style="font-size: 20px"></i>
              <i class="float-right success-subscript">D</i>
            </button>
            <button
              title="Export Report"
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              (click)="actionClicked('DISCOVERY_REPORT_EXPORT')"
            >
              <i class="far fa-lg fa-file-alt" style="font-size: 20px"></i>
              <span class="float-right success-subscript" size="sm">E</span>
            </button>
          </div>
          <div class="btn-group ml-2" role="group">
            <button
              title="Load Analyze Layout"
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right btn-layout"
              (click)="actionClicked('LAYOUT_LOAD')"
            >
              <i class="icon-vod-layout icon-vod-layout-custom"></i>
              <i
                class="float-right fa-sm fas fa-arrow-down superscript success"
              >
              </i>
            </button>
            <button
              title="Save Analyze Layout"
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right btn-layout"
              (click)="actionClicked('LAYOUT_SAVE')"
            >
              <i class="icon-vod-layout icon-vod-layout-custom"></i>
              <i class="float-right fas fa-save fa-sm superscript success"> </i>
            </button>
          </div>
          <div class="btn-group ml-2" role="group">
            <button
              title="Bulk Tag"
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              (click)="actionClicked('BULK_TAG')"
              *ngIf="allowToTag$ | async"
              [attr.disabled]="documentCount === 0 ? 'disabled' : null"
            >
              <i class="fas fa-lg fa-tags float-left icon-primary"> </i>
              <i class="float-right fas fa-plus fa-sm superscript success"> </i>
            </button>
            <button
              title="Bulk UnTag"
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              (click)="actionClicked('BULK_UNTAG')"
              *ngIf="allowToTag$ | async"
              [attr.disabled]="documentCount === 0 ? 'disabled' : null"
            >
              <i class="float-left fas fa-tags fa-lg icon-primary"> </i>
              <i class="float-right fas fa-minus fa-lg superscript danger"> </i>
            </button>
            <button
              title="Create new Tag"
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              style="display: none"
              (click)="actionClicked('CREATE_TAG')"
              *ngIf="allowToCreateTag$ | async"
              [attr.disabled]="documentCount === 0 ? 'disabled' : null"
            >
              <i class="float-left fas fa-cog fa-lg icon-primary"></i>
            </button>
            <button
              title="Add Widget"
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              (click)="actionClicked('WIDGET_ADD')"
            >
              <i class="float-left fas fa-plus fa-lg icon-primary"></i>
            </button>
          </div>
          <div class="btn-group ml-2" role="group">
            <button
              title="Send To Review"
              *ngIf="
                (!isRVod || (isRVod && projectInfo?.IsFilteringServiceCase)) &&
                (allowToPerformSearch$ | async)
              "
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              (click)="actionClicked('SEND_TO_REVIEW')"
              [attr.disabled]="documentCount === 0 ? 'disabled' : null"
            >
              <i class="float-left fas fa-lg fa-search icon-primary"> </i>
              <i
                class="float-right fas fa-arrow-right fa-sm superscript success"
              >
              </i>
            </button>
            <button
              title="Send To Production"
              *ngIf="
                (!isRVod || (isRVod && projectInfo?.IsFilteringServiceCase)) &&
                (allowToProduce$ | async)
              "
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              (click)="actionClicked('SEND_TO_PRODUCTION')"
              [attr.disabled]="
                documentCount === 0 || !projectInfo?.EnableNativeAutoPrefetch
                  ? 'disabled'
                  : null
              "
            >
              <i class="float-left fas fa-download fa-lg icon-primary"> </i>
              <i
                class="float-right fas fa-arrow-right fa-lg superscript success"
              >
              </i>
            </button>
          </div>
          <div class="btn-group ml-2" role="group">
            <button
              title="Apply Inclusive Filter"
              class="btn btn-outline-{{
                config.themeClient
              }}-primary float-right"
              [ngClass]="
                localStats.isSearching && activeActionOf('INCLUSIVE_FILTER')
                  ? 'btn-is-working'
                  : 'btn-outline-' + config.themeClient + '-primary'
              "
              (click)="actionClicked('INCLUSIVE_FILTER')"
              [disabled]="localStats.isSearching || documentCount === 0"
            >
              <i
                class="float-left fas fa-filter fa-lg icon-primary"
                *ngIf="!activeActionOf('INCLUSIVE_FILTER')"
              >
              </i>
              <i
                class="float-right fas fa-plus fa-lg superscript success"
                *ngIf="!activeActionOf('INCLUSIVE_FILTER')"
              >
              </i>
              <mat-spinner
                *ngIf="
                  localStats.isSearching && activeActionOf('INCLUSIVE_FILTER')
                "
                diameter="17"
                strokeWidth="2"
              ></mat-spinner>
            </button>
            <button
              title="Apply Exclusive Filter"
              class="btn float-right"
              [ngClass]="
                localStats.isSearching && activeActionOf('EXCLUSIVE_FILTER')
                  ? 'btn-is-working'
                  : 'btn-outline-' + config.themeClient + '-primary'
              "
              (click)="actionClicked('EXCLUSIVE_FILTER')"
              [disabled]="
                localStats.isSearching || documentCount === 0
                  ? 'disabled'
                  : null
              "
            >
              <i
                class="float-left fas fa-filter fa-lg icon-primary"
                *ngIf="!activeActionOf('EXCLUSIVE_FILTER')"
              >
              </i>
              <i
                class="float-right fas fa-minus fa-lg superscript danger"
                *ngIf="!activeActionOf('EXCLUSIVE_FILTER')"
              >
              </i>
              <mat-spinner
                *ngIf="
                  localStats.isSearching && activeActionOf('EXCLUSIVE_FILTER')
                "
                diameter="17"
                strokeWidth="2"
              ></mat-spinner>
            </button>
          </div>
        </div>
        <div class="float-right">
          <h6 style="padding-top: 7px">
            Document Count
            <span class="badge badge-secondary" [@fadeInX]="docCount | async">
              <span
                *ngIf="docCount | async; else none"
                class="d-inline-block"
                >{{ docCount | async }}</span
              >
              <ng-template #none>
                <span class="d-inline-block">0</span>
              </ng-template>
            </span>
          </h6>
        </div>
      </div>
    </div>
  </div>

  <ng-template #actionButtons>
    <div class="input-group-append">
      <button
        title="Expand/Collapse"
        *ngIf="!isEmptyInput && !showAdvanceFilterUi"
        class="btn btn-{{ config.themeClient }}-primary"
        type="button"
        (click)="areaExpanded = !areaExpanded"
      >
        <i
          class="fas"
          [ngClass]="{
            'fa-angle-down': !areaExpanded,
            'fa-angle-up': areaExpanded
          }"
        ></i>
      </button>
      <button
        title="search"
        [ngClass]="
          localStats.isSearching && activeActionOf('SEARCH')
            ? 'btn-is-working'
            : 'btn-' + config.themeClient + '-primary'
        "
        type="button"
        class="btn"
        (click)="actionClicked('SEARCH')"
        [disabled]="localStats.isSearching"
      >
        <i *ngIf="!activeActionOf('SEARCH')" class="fas fa-search"></i>
        <mat-spinner
          *ngIf="localStats.isSearching && activeActionOf('SEARCH')"
          diameter="17"
          strokeWidth="2"
        ></mat-spinner>
      </button>
      <button
        title="Clear syntax"
        *ngIf="!isEmptyInput"
        class="btn btn-{{ config.themeClient }}-primary"
        type="button"
        (click)="onClearSearchSyntax()"
      >
        <i class="fas fa-backspace"></i>
      </button>
      <button
        title="Search Term Help"
        class="btn btn-{{ config.themeClient }}-primary"
        type="button"
        (click)="actionClicked('SEARCH_SHOW_GUIDANCE')"
      >
        <i class="fas fa-question"></i>
      </button>
      <button
        title="Advanced Search"
        class="btn btn-{{ config.themeClient }}-primary"
        type="button"
        (click)="forQueryBuilder.emit($event)"
        *ngIf="!showAdvanceFilterUi"
      >
        Advanced Search
      </button>
    </div>
  </ng-template>

  <div class="row" [@animateHeight]="errorRes">
    <div class="col-md-12" *ngIf="errorRes?.error?.message">
      <div class="alert alert-danger mb-2 d-flex justify-content-between">
        <div>{{ errorRes?.error?.message }}</div>
        <div>
          <i (click)="errorRes = null" class="fas fa-times fa-lg v-close"></i>
        </div>
      </div>
    </div>
  </div>

  <div
    *ngIf="allBreadcrumb?.length > 1"
    [@animateHeight]="allBreadcrumb.length"
  >
    <nav
      aria-label="breadcrumb"
      class="w-100 d-block position-relative"
      *ngIf="allBreadcrumb?.length > 1"
    >
      <ol class="breadcrumb" [@fadeInX]="allBreadcrumb.length">
        <li
          class="breadcrumb-item d-flex align-items-center"
          *ngFor="let item of allBreadcrumb; index as i; let last = last"
        >
          <mat-spinner
            *ngIf="localStats.isLinkLoading.get(item?.uuid)"
            diameter="15"
            strokeWidth="2"
          ></mat-spinner>
          <a
            matTooltipClass="bg-white text-dark mx-1 mt-1 mb-1 mat-elevation-z2"
            matTooltipPosition="right"
            [matTooltip]="item.isInitialSearch ? 'Home' : item.displayText"
            href="javascript:void(0);"
            (click)="actionClicked('SEARCH_BREADCRUMB_LINK', item)"
            class="v-link"
          >
            <span *ngIf="!localStats.isLinkLoading.get(item?.uuid)">
              {{
                i > 0
                  ? (item.displayText | slice: 0:20) +
                    (item?.displayText?.length > 20 ? '...' : '')
                  : ''
              }}
              <i *ngIf="i == 0" class="fas fa-home fa-lg"></i>
            </span>
          </a>
        </li>
      </ol>
    </nav>
  </div>
</div>

<!-- Available lazy component's template refs -->
<ng-template #lazyCrateTagTplRef>
  <div
    class="row mx-0"
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div
      class="modal-header d-flex justify-content-between col-12"
      cdkDragHandle
    >
      <h4 class="modal-title">Create New Tag</h4>
      <button type="button" class="close" [mat-dialog-close]="false">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body create-tag-modal">
      <ng-container
        *ngComponentOutlet="
          lazyComponents.get('CREATE_TAG')?.component | async;
          injector: lazyComponents.get('CREATE_TAG')?.injector;
          ngModuleFactory: lazyComponents.get('CREATE_TAG')?.factory
        "
      >
      </ng-container>
    </div>
  </div>
</ng-template>

<!-- Available lazy component's template refs -->
<ng-template #lazyAddUpdateWidget>
  <ng-container
    *ngComponentOutlet="
      lazyComponents.get('WIDGET_ADD')?.component | async;
      injector: lazyComponents.get('WIDGET_ADD')?.injector;
      ngModuleFactory: lazyComponents.get('WIDGET_ADD')?.factory
    "
  >
  </ng-container>
</ng-template>

<!-- Alert user to ensure at least one bar should checked before performing include/exclude search -->
<ng-template #confirmSelection>
  <div class="modal-content">
    <div class="modal-header">
      <h4 class="modal-title pull-left">Notice</h4>
      <button
        type="button"
        class="close pull-right close-confirm"
        aria-label="Close"
      >
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="row mx-0">
      <div class="col-12 px-0">
        <div class="alert alert-info mb-0">
          Please make selection in widget to apply filtering changes in the
          dashboard.
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn btn-{{ config.themeClient }}-primary  close-confirm">
        OK
      </button>
    </div>
  </div>
</ng-template>

<!-- Discovery Report -->
<ng-template #discoveryReport>
  <div
    class="row mx-0 bg-light discovery-report-dialog"
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div class="modal-header d-flex col-12" cdkDragHandle>
      <h4 class="modal-title pull-left">{{ discoveryReportType }} Report</h4>
      <button
        type="button"
        class="close pull-right close-confirm"
        [mat-dialog-close]="false"
      >
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="row w-100 mx-0 m-3" *ngIf="discoveryReportType === 'Discovery'">
      <div class="col-md-4">Choose Upload</div>
      <div class="col-md-8">
        <mat-select
          [formControl]="discoveryReportUploadNameCtrl"
          class="form-control"
          placeholder="Select Upload Name"
          multiple
        >
          <mat-option
            [value]="u.name"
            *ngFor="let u of discoveryReportUploadNames"
          >
            {{ u.name }}
          </mat-option>
        </mat-select>
      </div>
    </div>
    <div
      class="row w-100 mx-0 mt-2 mb-2"
      *ngIf="discoveryReportType === 'Export'"
    >
      <div class="col-md-4">Choose Export</div>
      <div class="col-md-8">
        <mat-select
          [formControl]="discoveryReportExportCtrl"
          class="form-control"
          placeholder="Select Export Name"
        >
          <mat-option
            [value]="e.exportId"
            *ngFor="let e of discoveryReportExports"
          >
            {{ e.exportName }}
          </mat-option>
        </mat-select>
      </div>
    </div>
    <div class="row w-100 mx-0 mb-2">
      <div class="col-md-12" style="text-align: right">
        <button
          type="button"
          class="btn btn-{{ config.themeClient }}-primary btn-sm mr-3"
          (click)="generateReport(discoveryReportType)"
          [mat-dialog-close]="false"
        >
          Generate Report
        </button>
        <button
          type="button"
          class="btn btn-{{ config.themeClient }}-secondary btn-sm"
          [mat-dialog-close]="false"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #custodianMediaRef>
  <ng-container
    *ngComponentOutlet="
      lazyComponents.get('CUSTODIAN_MEDIA')?.component | async;
      injector: lazyComponents.get('CUSTODIAN_MEDIA')?.injector;
      ngModuleFactory: lazyComponents.get('CUSTODIAN_MEDIA')?.factory
    "
  >
  </ng-container>
</ng-template>

<ng-template #folderScopeRef>
  <ng-container
    *ngComponentOutlet="
      lazyComponents.get('FOLDER_SCOPE')?.component | async;
      injector: lazyComponents.get('FOLDER_SCOPE')?.injector;
      ngModuleFactory: lazyComponents.get('FOLDER_SCOPE')?.factory
    "
  >
  </ng-container>
</ng-template>

<ng-template #layoutScreen>
  <div
    class="row mx-0"
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div
      class="modal-header d-flex justify-content-between col-12"
      cdkDragHandle
    >
      <h4 class="modal-title">
        {{
          activeActionOf('LAYOUT_SAVE')
            ? 'Save'
            : activeActionOf('LAYOUT_LOAD')
            ? 'Load'
            : ''
        }}
        Layout
      </h4>
      <button type="button" class="close" [mat-dialog-close]="false">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body" [formGroup]="layoutForm">
      <div class="form-group row" *ngIf="activeActionOf('LAYOUT_SAVE')">
        <label class="col-sm-4 col-form-label">Layout Options</label>
        <div class="col-sm-8">
          <div class="form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              id="new"
              [value]="true"
              formControlName="option"
            />
            <label class="form-check-label" for="new"> New </label>
          </div>
          <div class="form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              id="update"
              [value]="false"
              formControlName="option"
            />
            <label class="form-check-label" for="update"> Update </label>
          </div>
        </div>
      </div>
      <div
        class="form-group row"
        *ngIf="activeActionOf('LAYOUT_SAVE') && layoutForm.get('option').value"
      >
        <label for="name" class="col-sm-4 col-form-label">Layout Name</label>
        <div class="col-sm-8">
          <input
            type="text"
            class="form-control"
            id="name"
            placeholder="Layout Name"
            formControlName="name"
          />
        </div>
      </div>
      <div
        class="form-group row"
        *ngIf="
          activeActionOf('LAYOUT_LOAD') ||
          (activeActionOf('LAYOUT_SAVE') && !layoutForm.get('option').value)
        "
      >
        <label for="name" class="col-sm-4 col-form-label">Choose Layout </label>
        <div class="col-sm-8">
          <mat-select
            placeholder="Select a layout.."
            formControlName="layout"
            class="form-control"
            [disabled]="activeActionOf('LAYOUT_SAVE')"
          >
            <mat-option *ngFor="let l of layoutList" [value]="l.name"
              >{{ l.name }}
            </mat-option>
          </mat-select>
          <span
            class="text-danger"
            *ngIf="
              !layoutForm.get('layout').value && layoutButtonText === 'Update'
            "
            >Layout name is required</span
          >
        </div>
      </div>
      <div class="row">
        <div class="col d-flex justify-content-end align-items-end">
          <button
            class="btn btn-{{ config.themeClient }}-primary mx-3"
            type="button"
            [disabled]="
              !layoutForm.get('layout').value && layoutButtonText === 'Update'
            "
            [mat-dialog-close]="true"
          >
            {{ layoutButtonText }}
          </button>
          <button [mat-dialog-close]="false" class="btn btn-grey" type="button">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #searchHelpTemplate>
  <ng-container
    [ngComponentOutlet]="searchHelpComponent | async"
    [ngComponentOutletInjector]="searchHelpInjector"
    [ngComponentOutletNgModuleFactory]="searchHelpModule"
  >
  </ng-container>
</ng-template>
<dx-popup
  [showTitle]="false"
  [showCloseButton]="false"
  [resizeEnabled]="false"
  [width]="800"
  [height]="650"
  (onInitialized)="popupInitialized($event)"
>
  <app-media-processing-status-dialog (cancel)="closePopup()">
  </app-media-processing-status-dialog>
</dx-popup>
