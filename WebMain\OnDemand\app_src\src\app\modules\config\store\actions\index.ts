import { Action } from '@ngrx/store'
import { LicenseValidityModel, ProjectInfo } from '../../models/index'

/**
 * General action type enums for Config module
 */
export enum ConfigActionTypes {
  FetchBaseSettings = '[Config] Fetch Base Settings',
  FetchControlSettings = '[Config] Fetch Control Settings',
  SetBaseSettings = '[Config] Set Base Settings',
  SetControlSettings = '[Config] Set Control Settings',
  FetchHelpLinks = '[Config] Fetch Help Links',
  SetHelpLinks = '[Config] Set Hellp Links',
  FetchTheme = '[Config] Fetch Theme',
  SetCssThemeVariables = '[Config] Set CSS Theme Variables',
  FetchProjectInfo = '[Config] Fetch Project Info',
  SetProjectInfo = '[Config] Set Project Info',
  SetServerSideSession = '[Config] Set Server Side Session',
  FetchUserRights = '[Config] Fetch User Rights',
  SetUserRights = '[Config] Set User Rights',
  ResetProjectInfo = '[Reset] Reset Current Project Info',
  FetchLicenseValidity = '[Config] Fetch License Validity',
  SetLicenseValidity = '[Config] Set License Validity'
}

/**
 *  Fetches base settings from the server.
 */
export class FetchBaseSettings implements Action {
  readonly type = ConfigActionTypes.FetchBaseSettings
}

/**
 * Fetches control settings from the server.
 */
export class FetchControlSettings implements Action {
  readonly type = ConfigActionTypes.FetchControlSettings
}

/**
 *  Sets base settings in the store.
 */
export class SetBaseSettings implements Action {
  readonly type = ConfigActionTypes.SetBaseSettings

  constructor(readonly baseSettings: any) {}
}

export class FetchHelpLinks implements Action {
  readonly type = ConfigActionTypes.FetchHelpLinks
}

export class SetHelpLinks implements Action {
  readonly type = ConfigActionTypes.SetHelpLinks

  constructor(readonly helpLinks: any) {}
}

/**
 *  Sets control settings in the store.
 */
export class SetControlSettings implements Action {
  readonly type = ConfigActionTypes.SetControlSettings

  constructor(readonly controlSettings: any) {}
}

/**
 *  Fetches theme for the app.
 */
export class FetchTheme implements Action {
  readonly type = ConfigActionTypes.FetchTheme

  constructor(readonly path: string) {}
}

export class SetCssThemeVariables implements Action {
  readonly type = ConfigActionTypes.SetCssThemeVariables

  constructor(readonly cssThemeVariables: any) {}
}

/**
 * Fetches project info from the server.
 */
export class FetchProjectInfo implements Action {
  readonly type = ConfigActionTypes.FetchProjectInfo

  constructor(readonly projectId: number) {}
}

/**
 * Sets project info in the store.
 */
export class SetProjectInfo implements Action {
  readonly type = ConfigActionTypes.SetProjectInfo

  constructor(readonly projectInfo: ProjectInfo) {}
}

/**
 * Sets the server side session values.
 * This action is only required as long as we rely on old VOD and a few ASP.NET server side scripts.
 */
export class SetServerSideSession implements Action {
  readonly type = ConfigActionTypes.SetServerSideSession

  constructor(readonly session: Map<string, any>) {}
}

/**
 * Clears out the previous state of project info by setting to `null`.
 * Recommended place to call this method is, a section who lists all the project
 * which derives the ID to fetch its information for other sections/components etc.,
 */
export class ResetActiveProjectInfo implements Action {
  readonly type = ConfigActionTypes.ResetProjectInfo
}

export class FetchLicenseValidity implements Action {
  readonly type = ConfigActionTypes.FetchLicenseValidity
}

export class SetLicenseValidity implements Action {
  readonly type = ConfigActionTypes.SetLicenseValidity

  constructor(readonly licenseValidity: LicenseValidityModel) {}
}

/**
 * General Action types for Config module
 */
export type ConfigActions =
  | FetchBaseSettings
  | SetBaseSettings
  | FetchControlSettings
  | SetControlSettings
  | FetchTheme
  | SetCssThemeVariables
  | FetchProjectInfo
  | SetProjectInfo
  | SetServerSideSession
  | ResetActiveProjectInfo
  | FetchHelpLinks
  | SetHelpLinks
  | FetchLicenseValidity
  | SetLicenseValidity
