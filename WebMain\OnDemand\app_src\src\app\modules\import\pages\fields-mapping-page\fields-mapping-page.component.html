<div id="fields-mapping-page">
  <div *ngIf="formErrorMessage" class="alert alert-danger">
    {{ formErrorMessage }}
    <button type="button" (click)="formErrorMessage = null" class="close">
      <span aria-hidden="true" class="fa fa-times"></span>
    </button>
  </div>
  <div class="mb-2">
    <div class="float-left">
      <div class="section-title">Field Mapping</div>
    </div>
    <button
      class="btn btn-{{ client }}-primary btn-round shadow-none float-right"
      title="Previous"
      (click)="onPreviousClick()"
    >
      <fa-icon [icon]="['fas', 'angle-left']"></fa-icon>
    </button>
    <div class="clearfix"></div>
  </div>

  <mat-accordion class="example-headers-align overlay-wrap" multi>
    <mat-expansion-panel
      *ngIf="processLoadFile"
      [expanded]="processLoadFile"
      [disabled]="!processLoadFile"
    >
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{ isOverlay ? 'Overlay' : 'Import' }} Field Mapping
        </mat-panel-title>
      </mat-expansion-panel-header>
      <div class="">
        <div class="container-fluid d-flex h-100 flex-column p-0">
          <div class="row padded-row">
            <div class="col-auto mr-auto mb-2">
              <div id="field-mapping-switcher" class="d-flex">
                <span
                  class="btn btn-secondary d-flex align-items-center"
                  *ngIf="mapVenioFields === false"
                >
                  Load File Field
                </span>
                <span
                  class="btn btn-secondary d-flex align-items-center"
                  *ngIf="mapVenioFields === true"
                >
                  Venio Field
                </span>
                <button
                  (click)="toggleFieldMapper()"
                  type="button"
                  class="btn btn-{{ client }}-primary mx-2"
                >
                  <fa-icon [icon]="['fas', 'exchange-alt']"></fa-icon>
                </button>
                <span
                  class="btn btn-secondary d-flex align-items-center"
                  *ngIf="mapVenioFields === false"
                >
                  Venio Field
                </span>
                <span
                  class="btn btn-secondary d-flex align-items-center"
                  *ngIf="mapVenioFields === true"
                >
                  Load File Field
                </span>
              </div>
            </div>
            <div class="col-auto mb-2">
              <div class="filter-container">
                <i class="fa fa-filter"></i>
                <ng-select
                  class="custom"
                  [ngStyle]="{ width: '150px', 'margin-left': '10px' }"
                  [clearable]="false"
                  [searchable]="false"
                  [items]="mappedFilters"
                  [(ngModel)]="mappedFilter"
                >
                </ng-select>
                <div [ngStyle]="{ width: '250px', 'margin-left': '10px' }">
                  <div class="has-search">
                    <fa-icon
                      [icon]="['fas', 'search']"
                      class="form-control-feedback"
                    ></fa-icon>
                    <input
                      (keyup)="onSearchChanged($event)"
                      type="text"
                      class="form-control"
                      placeholder="Search Field For Mapping"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="col-auto mb-2">
              <div class="btn-toolbar" role="toolbar">
                <div class="btn-group" role="group">
                  <button
                    type="button"
                    class="btn btn-{{ client }}-secondary btn-icon mr-2"
                    title="Create Custom Fields"
                    (click)="onCreateCustomFieldsClicked()"
                  >
                    <i class="fa fa-plus"></i>
                  </button>
                </div>
                <div
                  class="btn-group"
                  role="group"
                  *ngIf="allowToCreateEditTemplate$ | async"
                >
                  <button
                    type="button"
                    class="btn btn-{{ client }}-secondary btn-icon"
                    title="Save As Template"
                    (click)="onSaveTemplateClick()"
                  >
                    <i class="fa fa-save"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="row d-flex flex-fill">
            <div class="col">
              <div id="field-mapping-container">
                <div id="field-mapping-header-container">
                  <div class="card" id="field-mapping-table-header">
                    <div class="container-fluid">
                      <div class="row">
                        <div class="col-3 my-auto">
                          <div *ngIf="mapVenioFields === false">
                            <span class="font-weight-bold"
                              >Load File Field</span
                            >
                          </div>
                          <div *ngIf="mapVenioFields === true">
                            <span class="font-weight-bold">Venio Field</span>
                          </div>
                        </div>
                        <div class="col-3 my-auto">
                          <div *ngIf="mapVenioFields === false">
                            <span class="font-weight-bold">Venio Field(s)</span>
                          </div>
                          <div *ngIf="mapVenioFields === true">
                            <span class="font-weight-bold"
                              >Load File Field</span
                            >
                          </div>
                        </div>
                        <div class="col-6 my-auto left-bordered">
                          <div>
                            <span class="font-weight-bold"
                              >Preview Samples From Load File</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="field-mapping-body-container">
                  <div class="card p-0" id="field-mapping-table-body">
                    <div
                      *ngIf="mapVenioFields === false"
                      class="container-fluid h-100 venio-load-row-container"
                    >
                      <div
                        class="row row-grid venio-to-load-row"
                        [class.mapped-row]="mapFieldsLoadToVenioSelect.hasValue"
                        *ngFor="
                          let field of summaryLoadFileFields
                            | filterMappedFields
                              : mappedFilter
                              : configUnmappedLoadFileFields
                              : searchTerm;
                          let i = index
                        "
                      >
                        <div class="col-3">
                          <div class="container-fluid h-100">
                            <div class="row h-100">
                              <div class="col my-auto">
                                <span>{{ field }}</span>
                                <code
                                  class="example"
                                  *ngIf="configNativeFilePathField === field"
                                >
                                  <span>NATIVE FILE PATH FIELD</span>
                                </code>
                                <code
                                  class="example"
                                  *ngIf="configFullTextFilePathField === field"
                                >
                                  <span>FULLTEXT FILE PATH FIELD</span>
                                </code>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-3">
                          <ng-select
                            #mapFieldsLoadToVenioSelect
                            [items]="configUnmappedVenioFields"
                            [multiple]="true"
                            [hideSelected]="true"
                            [clearSearchOnAdd]="true"
                            [closeOnSelect]="true"
                            [searchable]="true"
                            [disabled]="
                              configNativeFilePathField === field ||
                              configFullTextFilePathField === field
                            "
                            (add)="onVenioFieldAdd(field, $event)"
                            (remove)="onVenioFieldRemove(field, $event)"
                            (clear)="onVenioFieldClear(field)"
                            [ngModel]="configLoadFileToVenioMapping[field]"
                            placeholder="Search/Select Venio Field"
                          >
                            <ng-template ng-option-tmp let-item="item">
                              <span>{{ item }} </span>
                              <fa-icon
                                *ngIf="
                                  paramsMandatoryVenioFields.includes(item) &&
                                  !isOverlay
                                "
                                [size]="'xs'"
                                [icon]="['fas', 'asterisk']"
                              >
                              </fa-icon>
                            </ng-template>
                          </ng-select>
                        </div>
                        <div class="col-6">
                          <div class="container-fluid">
                            <div class="row sample-container">
                              <div
                                *ngIf="
                                  fieldSamples(field).length === 0;
                                  else hasSamples
                                "
                                class="col no-sample"
                              >
                                <span class="no-sample-label">No Samples</span>
                              </div>
                              <ng-template #hasSamples>
                                <div
                                  *ngFor="let sample of fieldSamples(field)"
                                  class="col sample"
                                  style="width: 1px"
                                >
                                  <span
                                    [tooltip]="popTemplate"
                                    triggers="mouseenter mouseleave click"
                                    placement="left"
                                    >{{
                                      sample.length > 100
                                        ? (sample | slice: 0:100) + '...'
                                        : sample
                                    }}</span
                                  >
                                  <ng-template #popTemplate>
                                    <div
                                      style="
                                        max-height: 150px !important;
                                        width: 150px !important;
                                        overflow-y: auto;
                                        pointer-events: all;
                                      "
                                    >
                                      {{ sample }}
                                    </div>
                                  </ng-template>
                                </div>
                              </ng-template>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div
                      *ngIf="mapVenioFields === true"
                      class="container-fluid h-100 venio-load-row-container"
                    >
                      <div
                        class="row row-grid load-to-venio-row"
                        [class.mapped-row]="mapFieldsVenioToLoadSelect.hasValue"
                        *ngFor="
                          let field of paramsVenioFields
                            | filterMappedFields
                              : mappedFilter
                              : configUnmappedVenioFields
                              : searchTerm;
                          let i = index
                        "
                      >
                        <div class="col-3">
                          <div class="container-fluid h-100">
                            <div class="row h-100">
                              <div class="col my-auto">
                                <span>{{ field }} </span>
                                <div *ngIf="!isOverlay">
                                  <fa-icon
                                    *ngIf="
                                      paramsMandatoryVenioFields.includes(field)
                                    "
                                    [size]="'xs'"
                                    [icon]="['fas', 'asterisk']"
                                  ></fa-icon>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-3">
                          <ng-select
                            #mapFieldsVenioToLoadSelect
                            [items]="summaryLoadFileFields"
                            [multiple]="false"
                            [closeOnSelect]="true"
                            [searchable]="true"
                            (change)="onLoadFileFieldChange(field, $event)"
                            [ngModel]="configVenioToLoadFileMapping[field]"
                            placeholder="Search/Select Load File Field"
                          >
                            <ng-template ng-option-tmp let-item="item">
                              {{ item }}
                              <span
                                *ngIf="configLoadFileToVenioMapping[item]"
                                class="badge badge-pill badge-{{
                                  client
                                }}-secondary"
                              >
                                {{ configLoadFileToVenioMapping[item].length }}
                              </span>
                            </ng-template>
                          </ng-select>
                        </div>
                        <div class="col-6">
                          <div class="container-fluid">
                            <div class="row sample-container">
                              <div
                                *ngIf="
                                  samples.has(field) &&
                                    samples.get(field)?.length === 0;
                                  else hasSamples
                                "
                                class="col no-sample"
                              >
                                <span class="no-sample-label">No Samples</span>
                              </div>
                              <ng-template #hasSamples>
                                <div
                                  *ngFor="let sample of samples.get(field)"
                                  class="col sample"
                                  style="width: 1px"
                                >
                                  <span
                                    [tooltip]="popTemplate"
                                    triggers="click"
                                    placement="left"
                                    >{{
                                      sample.length > 100
                                        ? (sample | slice: 0:100) + '...'
                                        : sample
                                    }}</span
                                  >
                                  <ng-template #popTemplate>
                                    <div
                                      style="
                                        height: auto;
                                        max-height: 150px;
                                        width: auto;
                                        max-width: 100px;
                                        overflow-x: auto;
                                        overflow-y: auto;
                                      "
                                    >
                                      {{ sample }}
                                    </div>
                                  </ng-template>
                                </div>
                              </ng-template>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="card-body modal-body mt-3 overley-identifier-wrap"
        *ngIf="isOverlay"
      >
        <form [formGroup]="overlayForm">
          <div class="container-fluid">
            <div class="row">
              <div class="col-6 form-group row">
                <label class="col-md-4" for="lblFont"
                  >Overlay Field Identifier<span class="text-danger"
                    >*</span
                  ></label
                >
                <div class="col-md-8">
                  <select
                    class="form-control"
                    id="overlayFieldIdentifier"
                    formControlName="overlayFieldIdentifier"
                    placeholder="Select a field..."
                    [ngClass]="{
                      'is-invalid': displayMessage?.overlayFieldIdentifier
                    }"
                  >
                    <option
                      *ngFor="let field of identifierFields"
                      [value]="field"
                    >
                      {{ field }}
                    </option>
                  </select>
                  <span
                    class="invalid-feedback"
                    *ngIf="displayMessage?.overlayFieldIdentifier"
                  >
                    {{ displayMessage?.overlayFieldIdentifier }}
                  </span>
                </div>
              </div>
            </div>
            <div class="row form-group">
              <div class="col-6 col-md-6 col-xs-12">
                Match Record
                <div class="custom-control custom-radio">
                  <input
                    id="Replace"
                    type="radio"
                    class="custom-control-input"
                    formControlName="isReplaceOverlay"
                    [value]="true"
                  />
                  <label class="custom-control-label" for="Replace"
                    >Replace Field Content</label
                  >
                </div>
                <div class="custom-control custom-radio">
                  <input
                    id="Append"
                    type="radio"
                    class="custom-control-input"
                    formControlName="isReplaceOverlay"
                    [value]="false"
                  />
                  <label class="custom-control-label mr-3" for="Append"
                    >Append to existing field containing user delimiter
                  </label>
                  <span class="inline">
                    <select
                      class="form-control"
                      id="delimiter"
                      formControlName="overlayDelimiter"
                      placeholder="Select a field..."
                    >
                      <option [value]="de" *ngFor="let de of delimiters">
                        {{ de }}
                      </option>
                    </select>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </form>
        <div class="note-text col-md-12">
          <strong>Note:</strong
          ><span>
            All selected fields in Mapped Field List are overlayed using overlay
            field identifier but itself it is not completed.</span
          ><br />
        </div>
      </div>
    </mat-expansion-panel>
    <mat-expansion-panel
      *ngIf="isOverlay && processImage"
      [expanded]="processImage"
      [disabled]="!processImage"
    >
      <mat-expansion-panel-header>
        <mat-panel-title> Image Overlay Options </mat-panel-title>
      </mat-expansion-panel-header>
      <div [formGroup]="imageOverlayConfigForm">
        <div>
          <div class="row form-group">
            <div class="col-6 col-md-6 col-xs-12">
              Map With
              <div class="custom-control custom-radio">
                <input
                  id="fileId"
                  type="radio"
                  class="custom-control-input"
                  formControlName="overlayReferenceFieldName"
                  [value]="overlayReferenceFieldNameType.FileID"
                />
                <label class="custom-control-label" for="fileId">File Id</label>
              </div>
              <div class="custom-control custom-radio">
                <input
                  id="controlNumber"
                  type="radio"
                  class="custom-control-input"
                  formControlName="overlayReferenceFieldName"
                  [value]="overlayReferenceFieldNameType.ControlNumber"
                />
                <label class="custom-control-label mr-3" for="controlNumber"
                  >Control Number
                </label>
              </div>
              <div class="custom-control custom-radio">
                <input
                  id="batesNumber"
                  type="radio"
                  class="custom-control-input"
                  formControlName="overlayReferenceFieldName"
                  [value]="overlayReferenceFieldNameType.BatesNumber"
                />
                <label class="custom-control-label mr-3" for="batesNumber"
                  >Bates Number
                </label>
              </div>
              <div class="custom-control custom-radio">
                <input
                  id="customField"
                  type="radio"
                  class="custom-control-input"
                  formControlName="overlayReferenceFieldName"
                  [value]="overlayReferenceFieldNameType.CustomField"
                />
                <label class="custom-control-label mr-3" for="customField"
                  >Custom Field
                </label>
                <div
                  class="inline row-grid load-to-venio-row"
                  *ngIf="
                    imageOverlayConfigForm.value?.overlayReferenceFieldName ===
                    overlayReferenceFieldNameType.CustomField
                  "
                >
                  <ng-select
                    [multiple]="false"
                    [hideSelected]="true"
                    formControlName="customFieldName"
                    [clearSearchOnAdd]="true"
                    [closeOnSelect]="true"
                    [searchable]="true"
                    placeholder="Search/Select Venio Field"
                  >
                    <ng-option *ngFor="let c of customFields" [value]="c">{{
                      c
                    }}</ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div>
          <hr />
          <div class="row form-group">
            <div class="col-6 col-md-6 col-xs-12">
              <div class="custom-control custom-checkbox">
                <input
                  id="copyImage"
                  type="checkbox"
                  class="custom-control-input"
                  formControlName="copyImageToProjectLocation"
                  [value]="true"
                />
                <label class="custom-control-label" for="copyImage"
                  >Copy image file to project location</label
                >
              </div>
              <div class="custom-control custom-checkbox">
                <input
                  id="autoTiffOcr"
                  type="checkbox"
                  class="custom-control-input"
                  formControlName="autoTiffOcr"
                  [value]="false"
                />
                <label class="custom-control-label mr-3" for="autoTiffOcr">
                  Automatically Image-OCR files without text
                </label>
              </div>
              <div class="custom-control custom-checkbox">
                <input
                  id="pageLevelReplacement"
                  type="checkbox"
                  class="custom-control-input"
                  formControlName="pageLevelReplacement"
                  [value]="false"
                />
                <label
                  class="custom-control-label mr-3"
                  for="pageLevelReplacement"
                  >Replace / Insert only the pages from load file
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </mat-expansion-panel>

    <form [formGroup]="custodianMediaForm" *ngIf="!isOverlay">
      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>Add Custodian</mat-expansion-panel-header>
        <div class="row" formGroupName="custodian">
          <div class="col-md-12 py-2">
            <div class="row">
              <div class="col-sm-6 col-md-6 col-lg-6 pt-3">
                <mat-radio-group
                  aria-label="Select an option"
                  formControlName="custodianFromLoadFileField"
                >
                  <mat-radio-button [value]="true"
                    >Import data to custodian(s) mapped with selected load file
                    field</mat-radio-button
                  >
                  <div class="pt-5">
                    <mat-radio-button class="pt-3" [value]="false"
                      >Import All data to custodian</mat-radio-button
                    >
                  </div>
                </mat-radio-group>
              </div>
              <div class="col-sm-6 col-md-6 col-lg-6">
                <div class="row">
                  <div class="col-md-12">
                    <div class="form-group">
                      <label
                        >Select load file field <span class="required">*</span>
                      </label>
                      <mat-select
                        disableRipple
                        class="form-control"
                        placeholder="Select"
                        formControlName="custodianLoadFileField"
                        [appDisableControl]="
                          custodianMediaForm.value.custodian
                            .custodianFromLoadFileField === false
                        "
                      >
                        <mat-option
                          *ngFor="let field of summaryLoadFileFields"
                          [value]="field"
                        >
                          {{ field }}
                        </mat-option>
                      </mat-select>
                      <div
                        class="text-danger"
                        *ngIf="
                          (custodianMediaForm
                            .get('custodian')
                            .get('custodianLoadFileField').invalid &&
                            (custodianMediaForm
                              .get('custodian')
                              .get('custodianLoadFileField').dirty ||
                              custodianMediaForm
                                .get('custodian')
                                .get('custodianLoadFileField').touched) &&
                            custodianMediaForm.value.custodian
                              .custodianFromLoadFileField === true) ||
                          (custodianMediaFormSubmitted &&
                            custodianMediaForm.value.custodian
                              .custodianFromLoadFileField === true &&
                            custodianMediaForm
                              .get('custodian')
                              .get('custodianLoadFileField').invalid)
                        "
                      >
                        Please select load file field to import data to
                        custodian.
                      </div>
                    </div>
                  </div>

                  <div class="col-md-12">
                    <div class="form-group">
                      <label
                        >Select or Input Custodian Name
                        <span class="required">*</span></label
                      >
                      <input
                        [appDisableControl]="
                          custodianMediaForm.value.custodian
                            .custodianFromLoadFileField === true
                        "
                        class="form-control"
                        placeholder="Select or Input"
                        formControlName="custodianName"
                        [matAutocomplete]="autoComplete"
                      />
                      <mat-autocomplete #autoComplete="matAutocomplete">
                        <mat-option
                          style="
                            font-size: 12px !important;
                            height: 20px !important;
                          "
                          *ngFor="let option of custodianNameOptions | async"
                          [value]="option.custodianName"
                        >
                          {{ option.custodianName }}
                        </mat-option>
                      </mat-autocomplete>
                      <div
                        class="text-danger"
                        *ngIf="
                          (custodianMediaForm
                            .get('custodian')
                            .get('custodianName').invalid &&
                            (custodianMediaForm
                              .get('custodian')
                              .get('custodianName').dirty ||
                              custodianMediaForm
                                .get('custodian')
                                .get('custodianName').touched) &&
                            custodianMediaForm.value.custodian
                              .custodianFromLoadFileField === false) ||
                          (custodianMediaFormSubmitted &&
                            custodianMediaForm.value.custodian
                              .custodianFromLoadFileField === false &&
                            custodianMediaForm
                              .get('custodian')
                              .get('custodianName').invalid)
                        "
                      >
                        Please provide custodian name.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-expansion-panel>

      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>Add Media</mat-expansion-panel-header>
        <div class="row" formGroupName="media">
          <div class="col-md-12 py-2">
            <div class="row">
              <div class="col-sm-6 col-md-6 col-lg-6 pt-3">
                <mat-radio-group
                  aria-label="Select an option"
                  formControlName="mediaFromLoadFileField"
                >
                  <mat-radio-button [value]="true"
                    >Import data to the media mapped with selected load file
                    field</mat-radio-button
                  >
                  <div class="pt-5">
                    <mat-radio-button class="pt-3" [value]="false"
                      >Import All data to media</mat-radio-button
                    >
                  </div>
                </mat-radio-group>
              </div>
              <div class="col-sm-6 col-md-6 col-lg-6">
                <div class="row">
                  <div class="col-md-12">
                    <div class="form-group">
                      <mat-label
                        >Select load file field
                        <span class="required">*</span></mat-label
                      >
                      <mat-select
                        class="form-control"
                        placeholder="Select"
                        disableRipple
                        formControlName="mediaLoadFileField"
                        [appDisableControl]="
                          custodianMediaForm.value.media
                            .mediaFromLoadFileField === false
                        "
                      >
                        <mat-option
                          *ngFor="let field of summaryLoadFileFields"
                          [value]="field"
                        >
                          {{ field }}
                        </mat-option>
                      </mat-select>
                      <div
                        class="text-danger"
                        *ngIf="
                          (custodianMediaForm
                            .get('media')
                            .get('mediaLoadFileField').invalid &&
                            (custodianMediaForm
                              .get('media')
                              .get('mediaLoadFileField').dirty ||
                              custodianMediaForm
                                .get('media')
                                .get('mediaLoadFileField').touched) &&
                            custodianMediaForm.value.media
                              .mediaFromLoadFileField === true) ||
                          (custodianMediaFormSubmitted &&
                            custodianMediaForm.value.media
                              .mediaFromLoadFileField === true &&
                            custodianMediaForm
                              .get('media')
                              .get('mediaLoadFileField').invalid)
                        "
                      >
                        Please select load file field to import data to media.
                      </div>
                    </div>
                  </div>
                  <div class="col-md-12">
                    <div class="form-group">
                      <label>Media Name <span class="required">*</span></label>
                      <input
                        class="form-control"
                        formControlName="mediaName"
                        [appDisableControl]="
                          custodianMediaForm.value.media
                            .mediaFromLoadFileField === true
                        "
                        placeholder="Enter Media Name"
                      />
                      <div
                        class="text-danger"
                        *ngIf="
                          (custodianMediaForm.get('media').get('mediaName')
                            .invalid &&
                            (custodianMediaForm.get('media').get('mediaName')
                              .dirty ||
                              custodianMediaForm.get('media').get('mediaName')
                                .touched) &&
                            custodianMediaForm.value.media
                              .mediaFromLoadFileField === false) ||
                          (custodianMediaFormSubmitted &&
                            custodianMediaForm.value.media
                              .mediaFromLoadFileField === false &&
                            custodianMediaForm.get('media').get('mediaName')
                              .invalid)
                        "
                      >
                        Please provide media name.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-expansion-panel>
    </form>
  </mat-accordion>
  <div class="row float-right mt-3 mr-0" role="group">
    <div class="mt-2 pt-1 pr-3">
      <label
        class="checkbox-wrap mb-2"
      >
        <input
          type="checkbox"
          [disabled]="disableValidatingFileExistence"
          [checked]="validateFileExistence"
          (change)="onValidateFileExistenceChange($event.target.checked)"
        />Validate File Existence
        <span class="check-mark"></span>
      </label>
    </div>
    <button
      class="btn btn-{{ client }}-primary mr-2"
      (click)="onValidateClick()"
    >
      <span
        *ngIf="validateSpinner$ | async"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-hidden="true"
      ></span>
      <span *ngIf="!(validateSpinner$ | async)">Validate</span>
    </button>
    <button class="btn btn-{{ client }}-primary" (click)="onImportClick()">
      <span
        *ngIf="
          (importSpinner$ | async) &&
          !sharedService.sharedProperties.isProgressShowing
        "
        class="spinner-border spinner-border-sm"
        role="status"
        aria-hidden="true"
      ></span>
      <span
        *ngIf="
          sharedService.sharedProperties.isProgressShowing
            ? true
            : !(importSpinner$ | async)
        "
        >{{ isOverlay ? 'Start Overlay' : 'Start Import' }}</span
      >
    </button>
  </div>
</div>
