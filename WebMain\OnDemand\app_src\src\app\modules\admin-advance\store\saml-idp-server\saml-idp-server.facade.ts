import {
  GroupRequestPayload,
  IdPGroupInsertRequestModel,
  SamlGridUiDataType,
  SamlSettingModel
} from '@admin-advance/models/saml-idp-server/saml-idp-server.model'
import {
  ApplySamlIdpServerSettingAction,
  FetchSamlIdpGroupsAction,
  FetchSamlIdpServerSettingAction,
  FetchSamlIdpServerSettingParseFromXmlMetafileAction,
  GetIdPGroupMappingSettingsStatusAction,
  InsertSamlIdpGroupsAction,
  SamlIdpServerSettingStateCleanupAction,
  SamlIdpServerStateSelector,
  StoreSamlIdpServerSettingAction
} from '@admin-advance/store'
import { Injectable } from '@angular/core'
import { Store } from '@ngxs/store'

@Injectable()
export class SamlIdpServerFacade {
  constructor(private store: Store) {}

  selectSamlIdpServerFetchResponse$ = this.store.select(
    SamlIdpServerStateSelector.sliceOf('fetchSamlSettingResponse')
  )

  selectSamlIdpServerApplyResponse$ = this.store.select(
    SamlIdpServerStateSelector.sliceOf('applySamlSettingResponse')
  )

  selectStoreSamlIdpServerData$ = this.store.select(
    SamlIdpServerStateSelector.sliceOf('storeSamlIdpServerSetting')
  )

  selectSamlIdpGroupsData$ = this.store.select(
    SamlIdpServerStateSelector.sliceOf('samlIdpGroupsResponse')
  )

  selectSamlIdpServerParsedXmlMetafileResponse$ = this.store.select(
    SamlIdpServerStateSelector.sliceOf(
      'fetchSamlSettingParseXmlMetafileResponse'
    )
  )

  selectSamlIdPGroupMappingSettingsStatusResponse$ = this.store.select(
    SamlIdpServerStateSelector.sliceOf(
      'samlIdPGroupMappingSettingsStatusResponse'
    )
  )

  fetchSamlIdpServerSettingData = (clientId?: number): void => {
    this.store.dispatch(new FetchSamlIdpServerSettingAction(clientId))
  }

  fetchSamlIdpGroupData = (payload: GroupRequestPayload): void => {
    this.store.dispatch(new FetchSamlIdpGroupsAction(payload))
  }

  insertSamlIdpGroupData = (payload: IdPGroupInsertRequestModel): void => {
    this.store.dispatch(new InsertSamlIdpGroupsAction(payload))
  }

  fetchSamlIdpServerSettingParseXmlMetafile = (xmlMetafile: string): void => {
    this.store.dispatch(
      new FetchSamlIdpServerSettingParseFromXmlMetafileAction(xmlMetafile)
    )
  }

  storeSamlIdpServerSetting = (payload: Partial<SamlGridUiDataType>): void => {
    this.store.dispatch(new StoreSamlIdpServerSettingAction(payload))
  }

  applySamlIdpServerSettingData = (payload: SamlSettingModel): void => {
    this.store.dispatch(new ApplySamlIdpServerSettingAction(payload))
  }

  resetSamlServerAllStates = (): void => {
    this.store.dispatch(
      new SamlIdpServerSettingStateCleanupAction([
        'applySamlSettingResponse',
        'storeSamlIdpServerSetting',
        'samlIdpGroupsResponse',
        'fetchSamlSettingParseXmlMetafileResponse',
        'fetchSamlSettingResponse'
      ])
    )
  }

  GetIdPGroupMappingSettingsStatus = (): void => {
    this.store.dispatch(new GetIdPGroupMappingSettingsStatusAction())
  }
}
