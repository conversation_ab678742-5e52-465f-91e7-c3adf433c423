import {
  FetchReviewSetTagRateAction,
  ReviewSetStateSelector
} from '@admin-advance/store'
import {
  AfterViewInit,
  Component,
  OnDestroy,
  OnInit,
  ViewChild
} from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { Store } from '@ngxs/store'
import DevExpress from 'devextreme'
import { DxDataGridComponent } from 'devextreme-angular'
import Tooltip from 'devextreme/ui/tooltip'
import { Subject } from 'rxjs'
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators'
import { ReviewStatusModel } from '../../../../models'
import PivotGridDataSourceOptions = DevExpress.data.PivotGridDataSourceOptions

@Component({
  templateUrl: './tag-rate.component.html',
  styleUrls: ['../child/data-container.component.scss']
})
export class TagRateComponent implements AfterViewInit, OnInit, OnD<PERSON>roy {
  /**
   * Cleanup placeholder for the observers when the component get destroyed.
   */
  private readonly toDestroy$ = new Subject<void>()

  /**
   * Bar chart widget component
   */
  @ViewChild('pivot', { static: false })
  private readonly dxPivot: DxDataGridComponent

  /**
   * Chart data
   */
  data: PivotGridDataSourceOptions

  isTagRateLoading = true

  constructor(
    private router: Router,
    private store: Store,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.initEvent()
  }

  ngAfterViewInit(): void {
    this.initSlices()
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Init router event and load progress data on the widget.
   */
  private readonly initEvent = (): void => {
    const qp = this.route.snapshot.queryParams
    if (!!qp && qp['projectId'] > 0 && qp['reviewSetId'] > 0)
      this.store.dispatch(
        new FetchReviewSetTagRateAction(+qp['projectId'], +qp['reviewSetId'])
      )
    else {
      this.data = null
    }
  }

  /**
   * Init slices from store
   */
  private readonly initSlices = (): void => {
    this.store
      .select(ReviewSetStateSelector.SliceOf('reviewSetTagRate'))
      .pipe(
        distinctUntilChanged(),
        debounceTime(600),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (d) => {
          /* prepare data  pivot data source
           @link https://js.devexpress.com/Demos/WidgetsGallery/Demo/PivotGrid/Overview/Angular/Light/
           */

          // whether the filed is type string and row
          const isRowStringField = (field) =>
            field.area === 'row' && field.dataType === 'string'

          // Sorts two objects based on their lowercase string values in ascending order.
          const stringSort = (a, b) => {
            const aValue = a.value.toLowerCase()
            const bValue = b.value.toLowerCase()
            return aValue.localeCompare(bValue)
          }

          // Apply string sorting to relevant fields in preparation.
          const onFieldsPrepared = (fields) => {
            fields.forEach((field) => {
              if (isRowStringField(field)) {
                field.sortingMethod = stringSort
              }
            })
          }

          const prepareSource: PivotGridDataSourceOptions = {
            onFieldsPrepared,
            fields: [
              {
                caption: 'Tag Name',
                dataField: 'tagName',
                area: 'row',
                allowExpandAll: false
              },
              {
                dataField: 'percentage',
                area: 'data',
                dataType: 'string',
                summaryType: 'custom',
                // being called internally
                calculateCustomSummary: (options) => {
                  switch (options.summaryProcess) {
                    case 'start':
                      // Initializing "totalValue" here
                      break
                    case 'calculate':
                      // Modifying "totalValue" here
                      options.totalValue = options.value
                      break
                    case 'finalize':
                      // Assigning the final value to "totalValue" here
                      break
                  }
                }
              },
              {
                dataField: 'userName',
                area: 'column'
              }
            ],
            store: []
          }
          const items: ReviewStatusModel[] = []
          for (let i = 0; i < d.length; i++) {
            const rev = {
              ...d[i],
              // we have some calculation in percentage
              percentage: `${(
                (d[i].taggedDocCount / d[i].reviewedDocCount) *
                100
              )?.toFixed(2)}%`
            }
            items.push(rev)
          }
          prepareSource.store = items
          this.data = prepareSource
          if (items.some((s) => s)) {
            setTimeout(() => this.dxPivot.instance.repaint(), 1000)
          }

          this.isTagRateLoading = false
        }
      })
  }

  onCellPrepared(e) {
    if (e.area === 'column') {
      if (e?.cell?.text) {
        const container = document.createElement('div')
        e.cellElement.appendChild(container)
        new Tooltip(container, {
          target: e.cellElement,
          visible: false,
          showEvent: 'mouseenter',
          hideEvent: 'mouseleave click',
          contentTemplate: function (content) {
            const label = document.createElement('div')
            label.innerHTML = '<b>' + e?.cell?.text + '</b>'
            content.appendChild(label)
          }
        })
      }
    }
  }
}
