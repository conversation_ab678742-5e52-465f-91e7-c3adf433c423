<div class="v-widget-title d-flex justify-content-between">
  <div>Tag Status</div>
  <div class="btn-group btn-border-group">
    <button
      class="btn btn-sm widget-icon"
      matTooltip="Show/Hide Tags"
      type="button"
      (click)="onShowHideTags()"
    >
      <fa-icon [icon]="['fas', 'columns']"></fa-icon>
    </button>
  </div>
</div>
<div class="v-widgets-wrap pt-5">
  <ng-template [ngIf]="!isTagStatusLoading && data?.length < 1">
    <div class="row mb-3 mt-3 default-font-size">
      <div class="col-md-12 text-muted">No Data</div>
    </div>
  </ng-template>

  <ng-template [ngIf]="isTagStatusLoading">
    <div class="row">
      <div class="col-md-12">
        <app-content-placeholder
          [wrapperHeight]="70"
          [linesOnly]="true"
          [lineCount]="3"
          class="mb-1 d-block"
          [lineHeight]="8"
        >
        </app-content-placeholder>
      </div>
    </div>
  </ng-template>
  <ng-template [ngIf]="data?.length > 0">
    <div class="tag-status-static-height">
      <div>
        <dx-chart
          class="v-widget-max-height"
          id="chart"
          [dataSource]="data"
          [rotated]="true"
          palette="Soft Pastel"
          resolveLabelOverlapping="hide"
        >
          <dxo-legend [visible]="false"></dxo-legend>
          <dxi-series
            argumentField="tagName"
            rotated="true"
            valueField="taggedDocCount"
            type="bar"
          >
            <dxo-label [visible]="true" backgroundColor="#78b6d9">
              <dxo-font [size]="10"></dxo-font>
            </dxo-label>
          </dxi-series>
          <dxo-tooltip
            [zIndex]="1"
            [enabled]="true"
            [customizeTooltip]="customizeTooltip"
          >
          </dxo-tooltip>
          <!--    <dxo-zoom-and-pan panKey="ctrl" argumentAxis="both" valueAxis="both">-->
          <!-- or "zoom" | "pan" | "none" -->
          <!--    </dxo-zoom-and-pan>-->
          <!--    <dxo-scroll-bar [visible]="false"></dxo-scroll-bar>-->
        </dx-chart>
      </div>
    </div>
  </ng-template>
  <ng-template #showHideTags>
    <div class="modal-header">
      <span>Show/Hide Tags</span>
      <button
        type="button"
        class="close pull-right"
        aria-label="Close"
        (click)="dialogRef.close(false)"
      >
        <span aria-hidden="true" class="fa fa-times"></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="" id="columns-filter-container">
        <div class="row row-grid">
          <div class="col">
            <dx-list
              #listTags
              [dataSource]="allTags"
              [height]="400"
              [selectionMode]="'all'"
              keyExpr="tagId"
              displayExpr="tagName"
              [selectAllMode]="'allPages'"
              [showSelectionControls]="true"
              [searchEnabled]="true"
              searchExpr="tagName"
              searchMode="contains"
              [selectedItemKeys]="selectedTags"
            >
            </dx-list>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button
        type="button"
        class="btn btn-{{ config.themeClient }}-primary"
        [disabled]="listTags?.instance?.option('selectedItemKeys')?.length == 0"
        (click)="save()"
      >
        Save
        <!-- Ok -->
      </button>
      <button
        type="button"
        class="btn btn-grey"
        (click)="dialogRef.close(false)"
      >
        <i class="fa fa-times"></i>
        Cancel
      </button>
    </div>
  </ng-template>
</div>
