import { ProjectInfo } from '@config/models'
import { createSelector } from '@ngrx/store'
import { StringUtils } from '@shared/utils/string-utils'
import * as _ from 'lodash'
import { User } from '../../../auth/models/user.model'
import { getUserDetails } from '../../../auth/store/selectors/access.selectors'
import { ConfigState, getConfigState, State } from '../reducers'

/**
 * Returns the entire config state.
 */
export const getConfig = createSelector(
  getConfigState,
  (state: State) => state?.configState
)

/**
 * Returns the base settings loaded at the startup of the config module. Return empty object if not set.
 */
export const getBaseSettings = createSelector(getConfig, (state: ConfigState) =>
  state.baseSettings ? state.baseSettings : {}
)

/**
 * Returns the value of a specific property name from the base settings.
 * @param property Specific property name from the base settings
 */
export const getBaseSetting = (property: string) =>
  createSelector(getBaseSettings, (baseSettings) => {
    return baseSettings[property] ? baseSettings[property] : null
  })

/**
 * Returns the entire control settigns from the store. Return an empty object if not set.
 */
export const getControlSettings = createSelector(
  getConfig,
  (state: ConfigState) => (state?.controlSettings ? state?.controlSettings : {})
)

/**
 * Return the value of a specific property/setting name from the control settings.
 * Useful if you want to subscribe to the value of a specific setting.
 * @param property Specific property name from the control settings
 */
export const getControlSetting = (property: string) =>
  createSelector(getControlSettings, (controlSettings) => {
    return controlSettings[property] !== undefined
      ? controlSettings[property]
      : null
  })

/**
 * Return the help links from store
 */
export const getHelpLinks = createSelector(getConfig, (state: ConfigState) =>
  state.helpLinks ? state.helpLinks : {}
)

/**
 * Returns the entire theme variables. These are the CSS custom properties (variables) loaded from theme properties file.
 */
export const getCssThemeVariables = createSelector(
  getConfig,
  (state: ConfigState) =>
    state?.cssThemeVariables ? state?.cssThemeVariables : {}
)

/**
 * Returns the value of a specific variable name from the theme properties.
 * @param variableName CSS theme variable name prefixed with `--` eg. --client
 */
export const getCssThemeVariableValue = (variableName: string) =>
  createSelector(getCssThemeVariables, (cssThemeVariables) => {
    return cssThemeVariables[variableName]
      ? cssThemeVariables[variableName]
      : null
  })

/**
 * Return client name from the store which can be used for theming.
 * This value is taken out from the theme properties file which is loaded into the store during startup.
 */
export const getThemeClient = createSelector(
  getCssThemeVariableValue('--client'),
  (client: string) => {
    return StringUtils.isNullOrEmpty(client) ? 'venio' : client
  }
)

/**
 * Return ProjectInfo from the store. Returns an empty object if not set.
 */
export const getProjectInfo = createSelector(getConfig, (state: ConfigState) =>
  state?.projectInfo ? state?.projectInfo : ({} as ProjectInfo)
)

/**
 * Select a specific property from the ProjectInfo. Useful if you want to subscribe to only a specific property.
 * @param property Property name from the ProjectInfo object
 */
export const getProjectInfoProperty = (property: string) =>
  createSelector(getProjectInfo, (projectInfo) => {
    return projectInfo[property] ? projectInfo[property] : null
  })

/**
 * Returns true if the ProjectInfo in the store has been set and must be non-empty.
 */
export const hasProjectInfoLoaded = createSelector(
  getConfig,
  (state: ConfigState) =>
    state.projectInfo &&
    !_.isEmpty(state.projectInfo) &&
    state?.projectInfo?.projectId !== null
)

/**
 * Returns true if the UserDetails in the store has been set and must be non-empty.
 */
export const hasUserDetailsLoaded = createSelector(
  getUserDetails,
  (userDetails: User) => userDetails && !_.isEmpty(userDetails)
)

/**
 * Returns true if the UserDetails and UserRights in the store have been set and must be non-empty.
 */
export const hasUserDataLoaded = createSelector(
  getConfig,
  getUserDetails,
  (state: ConfigState, userDetails: User) =>
    userDetails && !_.isEmpty(userDetails)
)

/**
 * Returns true if the UserDetails and ProjectInfo in the store have been set and must be non-empty.
 */
export const hasUserDetailsAndProjectInfoLoaded = createSelector(
  hasUserDetailsLoaded,
  hasProjectInfoLoaded,
  (userDetailsLoaded: boolean, projectInfoLoaded: boolean) =>
    userDetailsLoaded && projectInfoLoaded
)

/**
 * Returns true if the ProjectInfo, UserDetails and UserRights have been set and are non-empty.
 */
export const hasProjectAndUserDataLoaded = createSelector(
  hasProjectInfoLoaded,
  hasUserDataLoaded,
  (projectInfoLoaded: boolean, userDataLoaded: boolean) =>
    projectInfoLoaded && userDataLoaded
)

export const isImageTypePdf = createSelector(
  getProjectInfo,
  (projectInfo: ProjectInfo) => projectInfo.isImageTypePdf
)

export const getIsTranscriptEnabled = createSelector(
  getProjectInfo,
  (projectInfo: ProjectInfo) => projectInfo.enableTranscriptViewer
)

export const getLicenseValidity = createSelector(
  getConfig,
  (state: ConfigState) => state?.licenceValidity
)
