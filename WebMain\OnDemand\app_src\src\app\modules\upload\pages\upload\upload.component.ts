// #region    ------------------------------[import]-------------------------------------------------------------//
import { C<PERSON><PERSON>ianSelector } from '@admin-advance/store/custodian/custodian.selectors'
import { SelectionModel } from '@angular/cdk/collections'
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Inject,
  Injector,
  OnDestroy,
  OnInit,
  Optional,
  TemplateRef,
  Type,
  ViewChild
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { BroadcastService, MsalService } from '@azure/msal-angular'
import { ProjectInfo } from '@config/models'
import { FetchProjectInfo } from '@config/store/actions'
import { NgSelectComponent } from '@ng-select/ng-select'
import { Action, select, Store } from '@ngrx/store'
import { Select, Store as XsStore } from '@ngxs/store'
import { animateHeight, fadeInUpDown } from '@shared/animation'
import { SearchQueryModule } from '@shared/models'
import { StartupStateSelector } from '@stores/selectors'
import { AgGridAngular } from 'ag-grid-angular'
import { DxDataGridComponent, DxTreeListComponent } from 'devextreme-angular'
import * as $ from 'jquery'
import { JsonConvert } from 'json2typescript'
import * as _ from 'lodash'
import { BsModalService } from 'ngx-bootstrap/modal'
import * as plupload from 'node_modules/plupload'
import { EMPTY, interval, Observable, of, Subject, Subscription } from 'rxjs'
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs/operators'
import { UserRights } from 'src/app/helpers/user-rights'
import { ICustodianMedia } from 'src/app/modules/admin-advance/models/custodian/custodian.model'
import { RenameMediaName } from 'src/app/modules/admin-advance/store/custodian'
import { ReportsMenuBuilderService } from 'src/app/modules/application-nav/services/reports-menu-builder.service'
import { ConfigService } from 'src/app/modules/config/services/config.service'
import { ConfigState } from 'src/app/modules/config/store/reducers'
import {
  getControlSetting,
  getIsTranscriptEnabled,
  getThemeClient
} from 'src/app/modules/config/store/selectors'
import { ConfirmationDialogComponent } from 'src/app/modules/launchpad/components/confirmation-dialog/confirmation-dialog.component'
import { LaunchpadService } from 'src/app/modules/launchpad/services/launchpad.service'
import { NotificationService } from 'src/app/services/notification.service'
import { LoggingService } from '../../../../services/logging.service'
import { UploadApiService } from '../../../../services/upload-api.service'
import { UtilityService } from '../../../../services/utility.service'
import { GlobalErrorAction } from '../../../../store/actions'
import {
  GetRepository,
  GetRepositoryHierarchy
} from '../../../../stores/actions'
import * as fromProductionStateActions from '../../../../stores/actions/production.actions'
import * as UActions from '../../../../stores/actions/upload.actions'
import {
  SearchQueryOption,
  SettingsInfo,
  VODRSettings
} from '../../../../stores/models'
import {
  ProductionSelectors,
  UploadStateSelector
} from '../../../../stores/selectors'
import {
  MediaData,
  SelectedUserNodeInfo
} from '../../../admin-advance/models/media/media.modal'
import { Client, User } from '../../../auth/models/user.model'
import {
  getClientInfo,
  getUserDetails
} from '../../../auth/store/selectors/access.selectors'
import { getProjectInfo } from '../../../config/store/selectors'
import { AwsS3UploadComponent } from '../../components/aws-s3-upload/aws-s3-upload.component'
import { BatchMediaUploadContainerComponent } from '../../components/batch-media-upload/components/batch-media-upload-container/batch-media-upload-container.component'
import { FileTypeListComponent } from '../../components/file-type-list/file-type-list.component'
import { MediaOverviewComponent } from '../../components/media-overview/media-overview.component'
import {
  AddedFilesList,
  AWSS3FilesList,
  CustodianMediaStausModel,
  MediaSourceType,
  MSTeamHierarchyModel,
  MSteamModel,
  NsfUserIdFile,
  QueuedAWSDataModel,
  QueuedMSTeamHierarchyModel,
  QueuedRepositoryModel,
  RepositoryFilesList,
  UploadFileDetails,
  UploadHistoryStatusModel
} from '../../models'
import { VenioS3Item } from '../../models/aws-data.model'
import {
  GetValidDirectoriesForImportRequestModel,
  RepositoryHierarchyModel,
  RepositoryModel
} from '../../models/repository.model'
import { UploadService } from '../../services/upload.service'
import {
  ClearStartUploadResponse,
  ClearStatusResponse,
  DeleteCustodianMedia,
  DeleteCustodianMediaSuccessful,
  DropTempTable,
  FileUploadProgressStatus,
  GetCustodianMediaStatus,
  GetCustodianNameList,
  GetUploadedHistory,
  GetUploadMediaStatus,
  SendUploadCompleteMessage,
  StartUpload,
  UpdateCustodianMediaStatus
} from '../../store/actions'
import {
  custodianMediaStatus,
  custodianNameList,
  deleteCustodianMediaResponse,
  startUploadResponse,
  uploadHistorySuccess,
  uploadMediaStatusResponse
} from '../../store/selectors/upload.selectors'
import { QueueAWSS3ItemAction } from '../../stores/actions/aws-data-upload.actions'
import * as fromLazyUploadStateActions from '../../stores/actions/upload.actions'
import { BatchMediaUploadFacade } from '../../stores/batch-media-upload.facade'
import { ImportsStatus as ImportStatus } from '../../stores/models'
import { UploadStatesSelector } from '../../stores/selectors'
import { AWSDataUploadSelector } from '../../stores/selectors/aws-data-upload.selectors'

// #region    ------------------------------[import]-------------------------------------------------------------//

/**
 * Upload source data type
 */
enum SourceType {
  /**
   * no source type
   */
  None,

  /**
   * Unprocessed RAW data
   */
  Unstructured,

  /**
   * Third party production data
   */
  Structured,

  /**
   * Transcript
   */
  Transcript
}

/**
 * types of the actions.
 */
type ActionTypes = 'analyze' | 'review' | 'production'

@Component({
  selector: 'app-upload',
  templateUrl: './upload.component.html',
  styleUrls: ['./upload.component.scss'],
  animations: [fadeInUpDown, animateHeight]
})
export class UploadComponent implements OnInit, AfterViewInit, OnDestroy {
  mediaData: MediaData[]

  repositoryList: RepositoryModel[]

  repositoryHierarchyList: RepositoryHierarchyModel[] = []

  invalidRepositoryList: RepositoryHierarchyModel[] = []

  selectedRepository: number[] = []

  private readonly toDestroy$ = new Subject<void>()

  private destroy$: Subject<any>

  // UI top right true: Structured, false: Unstructured
  // Used in onProcessNUpload (uploadfiles click event in UI) -> this.addedFilesList.IsStructured
  // -> tbl_vod_UploadedFileInfo.Import
  isStructuredData = false

  isOpenOrClose: boolean

  totalMedia = 0

  mediaUploaded = 0

  totalUploadedFiles = 0

  mediaProcessed = 0

  currentFileUploadProgress: number

  currentlyUploadingId = ''

  currentlyUploadingMSTeamIds = []

  uploadedMSTeamIds = []

  failedMSTeamIds = []

  internalUserId = -1

  externalUserId = -1

  userDetails: User

  client: Client

  filesUploaded = []

  addedFilesList: AddedFilesList[] = []

  repositoryFilesList: RepositoryFilesList[] = []

  awsS3FileList: AWSS3FilesList[] = []

  queuedRepositoryModel: QueuedRepositoryModel[] = []

  queuedAWSDataModel: QueuedAWSDataModel[] = []

  repositoryCustodianName: string

  custodianNames: Array<string>

  mstrThemeName: string

  companyName: string

  projectName: string

  uploadStatus: string

  processingStatus: string

  currentlyCancelledID = ''

  cancelMessage = ''

  custodianMediaStatusModel: CustodianMediaStausModel

  mUploader: any

  triggerGetCustodianMediaStatus: any

  triggerGetUploadMediaStatus: any

  isVodrEnabled = !this.configService.isVodEnabled

  settingsInfo: SettingsInfo

  queryParamsSubscription: any

  uploadChunkSize = 15

  uploadRetryCount = 5

  uploadRetryInterval = 5

  unsubscribed$ = new Subject<void>()

  /**
   * Grab created case (project) id from the query param so we can use it to fetch information for this component.
   * NOTE: This will grab ID from the param at once.
   */
  projectId = +this.route.snapshot.queryParams['projectId']

  existingProjectId: number

  // RVOD Related variables
  addDataToExistingCase: boolean

  overrideSettingsInfo: boolean

  isContinuedAfterControlNumberConflict: boolean

  settingId: number

  structuredData: boolean

  /**
   * source type selection
   */
  sourceType = SourceType

  /**
   * source type selection
   */
  mediaSourceType = MediaSourceType

  /**
   * Currently selected source type by user.
   * Initially none are selected.
   */
  selectedSourceType: SourceType = SourceType.None

  selectedMediaSourceType: MediaSourceType = MediaSourceType.SOURCE_FILE

  selectedSocialMedia: string

  @ViewChild('agGrid') agGrid: AgGridAngular

  @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent

  /**
   * Upload history data
   */
  rowData: UploadHistoryStatusModel[] = []

  /**
   *  User selected rows from the upload history grid.
   */
  selection = new SelectionModel<UploadHistoryStatusModel>(true, [])

  /**
   * For showing spinner icon on the upload button
   */
  isUploading: boolean

  // when finish uplad then hide upload detail
  isFinishProcessed: boolean

  // instance of template that will be rendered on DOM
  displayTemplate = null

  // use to show message when upload invitation handle response
  @ViewChild('alertMessage')
  private readonly alertMessage: TemplateRef<any>

  invitationHandleMessage: string

  /**
   * When an user add a file, a task has been performed to validate the
   * format and pull out the invalid object from the inserted file collection
   * which then used for display the message to the end user.
   */
  invalidFileFound: string

  /**
   * Static service of app config.
   */
  config = ConfigService

  public projectSettingReport: any

  /**
   * Control setting flag to process all uploaded files in single media.
   */
  processAllFilesInSingleMedia: boolean

  /**
   * Forensic image file formats
   */
  forensicImageFileFormat = 'ISO|E\\d+|L\\d+'

  /**
   * Transcript management
   */

  /**
   * Select the right to 'ALLOW_TO_UPLOAD_TRANSCRIPT' from the store.
   */
  @Select(
    StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_UPLOAD_TRANSCRIPT)
  )
  allowToUploadTranscript$: Observable<boolean>

  /**
   * check if transcript based on rights
   */
  showTranscriptUpload = false

  /**
   * check if transcript based on selection
   */
  isTranscriptData = false

  /**
   * check if MSTeam based on selection
   */
  isMSteam = false

  @ViewChild('treeListMSTeamRef', { static: false })
  treeList: DxTreeListComponent

  /**
   * check if transcript based on control setting
   */
  showTranscript: boolean

  /**
   * check if fallback ingestion engine
   */
  isFallback: boolean

  /**
   * Enum types of user rights validate by accessing statically.
   */
  readonly userRightTypes = UserRights

  settings: any

  loggedIn = false

  loginSuccess = false

  click = false

  subscription: Subscription

  token: string

  /**
   * Is overlay true or false
   */
  isOverlay = false

  /**
   * Control whether or not to show loader.
   */
  loading = false

  private msteamModel: MSteamModel = new MSteamModel()

  selectedUserNodeInfo: SelectedUserNodeInfo = new SelectedUserNodeInfo()

  tempData: MediaData[]

  recursiveSelectionEnabled = true

  primaryNodeList: MediaData[] = []

  selectedMSTeamRowsData: MediaData[] = []

  finalSelectedMSTeamRowsData: MediaData[] = []

  msTeamHierarchiesGroupedByUser: any[] = []

  secondaryNodeList: MediaData[] = []

  selectedNodeKeys: any[] = []

  existingUserList: MediaData[] = []

  expandedRowsKeyList: string[] = []

  triggerDrillMSTeamHierarchy: any[] = []

  removedMSTeamDummyNodes = []

  /** true when the control setting to enable second review set view */
  enableReview2: boolean

  isRepository = false

  isBatchMediaUi: boolean

  isAWSS3UI = false

  isBatchMediaContainerLoading: boolean

  batchMediaLazyComponent: Type<BatchMediaUploadContainerComponent>

  awsS3UploadLazyComponent: Type<AwsS3UploadComponent>

  showRepositoryNodeOptions = false

  @ViewChild('treeListRef', { static: false })
  treeListRepository: DxTreeListComponent

  selectedRowsData = []

  uploadBtnName = 'Upload & Process'

  repositoryUploadSuccess: boolean

  isCustodianNameVisible: boolean

  custodianNameRequired: boolean

  mediaNameRequired: boolean

  expandedRepositoryRowsKeyList: string[]

  tempRepositoryHierarchyData: RepositoryHierarchyModel[]

  repositoryLoadingMessage: string

  repositoryHierarchyLoadingMessage: string

  tempHierarchyIds: string[] = []

  repositoryHierarchyLoader = false

  repositoryLoader = false

  triggerDrillRepositoryHierarchy: any[] = []

  removedRepostioryDummyNodes = []

  parentRepositoryOrFolders: any = []

  childRepositoryOrFolders: any = []

  parentRepositoryItemsMapper: any = []

  childRepositoryItemsMapper: any = []

  projectInfo: ProjectInfo

  /**
   * is Reposiotyr upload
   */
  isRepositoryUpload = false

  /**
   * fsid for uploading in repository
   */
  fsid = -1

  /**
   * selected path for upload
   */
  selectedPath: string

  /**
   * modal header
   */
  dialogHeader: string

  /**
   * file name
   */
  fileName: string

  /**
   * external user id
   */
  extUsertId = +this.route.snapshot.queryParams['extUserId']

  /**
   * template ref of confirmation modal
   */
  @ViewChild('confirmationModal')
  private readonly warningContent: TemplateRef<any>

  /**
   * template ref of upload pop up
   */
  @ViewChild('modalContent')
  private readonly modalContent: TemplateRef<any>

  /**
   * button text
   */
  buttonText: string

  /**
   * template ref of remove view all fields by template Id
   */
  @ViewChild('tplMediaOverview')
  private readonly mediaOverviewTemplateRef: TemplateRef<any>

  /**
   * Lazy component to load on user demand for mediaOverviewComponent
   */
  mediaOverviewComponent: Promise<Type<MediaOverviewComponent>>

  /**
   *  Injector provider for lazy component
   */
  mediaOverviewInjector: Injector

  //Store if license if of LimitedSerive or SelfService
  isLimitedServiceLicense = false

  bucketHieracyListAtParent: VenioS3Item[] = []

  selectedS3ItemsAtParent: VenioS3Item[] = []

  isEnterKeyPressed = false

  showProcessingStatus = false

  @ViewChild('invalidRepositories')
  private readonly invalidRepositoyDialog: TemplateRef<any>

  @Select(
    StartupStateSelector.hasGlobalRight(
      UserRights.ALLOW_TO_ADD_DATA_FROM_REPOSITORY
    )
  )
  allowToAddDataFromRepository$: Observable<boolean>

  // user id file
  nsfUserIdFiles: NsfUserIdFile[] = []

  /**
   * For a certain period, we will redirect between the old UI and new UI.
   * We store the base path for the new UI to redirect when the new UI feature is enabled.
   * Both apps are hosted on the same origin in production,
   * which means that if one app obtains a JWT token and stores it in storage, the other app can easily access it.
   * However, the key used must be the same for both apps.
   * @private
   */
  private newUiBaseurl = ''

  public allImportStatuses: ImportStatus[]

  public filteredImportStatuses: ImportStatus[]

  public currentImportFilterSelection = 'All'

  private importStatusRefreshInterval = 2000

  isServiceUpload = false

  constructor(
    @Optional() @Inject('UploadData') data,
    private cdr: ChangeDetectorRef,
    private configService: ConfigService,
    private uploadService: UploadService,
    private uploadApiService: UploadApiService,
    private modalService: BsModalService,
    private toastr: NotificationService,
    private logger: LoggingService,
    private launchpadService: LaunchpadService,
    private bsModalRef: BsModalService,
    private dialog: MatDialog,
    private router: Router,
    private configStore: Store<ConfigState>,
    private store: Store<Action>,
    private route: ActivatedRoute,
    private xsStore: XsStore,
    private reportsMenuBuilderService: ReportsMenuBuilderService,
    private broadcastService: BroadcastService,
    private authService: MsalService,
    private utilityService: UtilityService,
    private injector: Injector,
    private batchMediaUploadFacade: BatchMediaUploadFacade
  ) {
    this.isOverlay = this.router.url.includes('/overlay')
    this.showProcessingStatus = this.route.snapshot.queryParams['status']
    this.isServiceUpload = this.route.snapshot.queryParams['serviceUpload']
    this.isVodrEnabled = Boolean(this.isServiceUpload)
    if (this.showProcessingStatus) {
      this.selectedSource(
        SourceType.Unstructured,
        MediaSourceType.SOURCE_FOLDER
      )
    }
    if (this.isOverlay) {
      this.projectId = data?.projectId

      this.selectedSource(SourceType.Structured, MediaSourceType.SOURCE_FOLDER)
    }

    this.isRepositoryUpload = this.router.url.includes('/management')
    if (this.isRepositoryUpload) {
      this.projectId = data?.projectId ?? -1
      this.selectedSource(
        SourceType.Unstructured,
        MediaSourceType.SOURCE_FOLDER
      )
      this.fsid = +this.route.snapshot.queryParams['fsid']
      this.selectedRepository.push(this.fsid)
      this.onClickLoadHierarchy()
    }
    this.destroy$ = new Subject<any>()

    localStorage.setItem('VodBaseUrl', this.configService.getVodBaseUrl())
    if (!this.isRepositoryUpload)
      // Get selected project info
      this.store.dispatch(new FetchProjectInfo(this.projectId))

    /**** menuData of reportsMenuBuilderService return all report list   */
    this.reportsMenuBuilderService.menuData
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((reportsMenu) => {
        this.projectSettingReport = this.getReportById(
          reportsMenu,
          'ProjectSettings'
        )
      })
    const tempStore = {
      load: function (loadOptions) {
        // return http.get("https://js.devexpress.com/Demos/Mvc/api/treeListData?parentIds=" + loadOptions.parentIds)
        //            .toPromise();
        this.xsStore
          .dispatch(new UActions.GetMediaData(this.msteamModel))
          .pipe(
            switchMap(() =>
              this.xsStore.selectOnce(UploadStateSelector.mediaDataResponse)
            ),
            takeUntil(this.unsubscribed$)
          )
          .subscribe((response: MediaData[]) => {
            this.loading = false
            this.mediaData = response
            // return void
          })
          .toPromise()
      }
    }
  }

  #selectVodVersionAndSetNewUiRedirectionPath(): void {
    this.store
      .pipe(
        select(getControlSetting('VOD_VERSION')),
        takeUntil(this.toDestroy$)
      )
      .subscribe((VOD_VERSION: number) => {
        /**
         * This method of redirecting users from the old UI to the new one is only temporary.
         * The redirect will be removed once the updated UI is fully functional.
         */
        const isNewUiEnabled = Number(VOD_VERSION) === 3

        if (isNewUiEnabled) {
          this.newUiBaseurl = `/review-next`
        } else {
          this.newUiBaseurl = ''
        }
      })
  }

  onBatchMediaClicked(): void {
    this.addedFilesList = []
    this.repositoryFilesList = []
    this.awsS3FileList = []
    this.nsfUserIdFiles = []
    this.isBatchMediaContainerLoading = true
    this.isBatchMediaUi = true
    this.isAWSS3UI = false
    this.isRepository = false
    import(
      '../../components/batch-media-upload/components/batch-media-upload-container/batch-media-upload-container.component'
    ).then(({ BatchMediaUploadContainerComponent }) => {
      this.batchMediaLazyComponent = BatchMediaUploadContainerComponent
      this.isBatchMediaContainerLoading = false
    })
  }

  onClickLoadHierarchy() {
    /**
     * Get repository hierarchy list
     */
    if (this.selectedRepository.length > 0) {
      const sortedSelectedRepostiory: number[] = this.selectedRepository.sort(
        (n1, n2) => n1 - n2
      )
      const selectedSocialMedia =
        this.selectedSocialMedia === '' ? 'none' : this.selectedSocialMedia

      this.repositoryHierarchyLoader = true
      this.repositoryHierarchyLoadingMessage =
        'Loading Repository Hierarchy ...'
      this.xsStore
        .dispatch(
          new GetRepositoryHierarchy(
            sortedSelectedRepostiory,
            this.selectedSourceType === SourceType.Structured,
            this.selectedSourceType === SourceType.Transcript,
            selectedSocialMedia,
            0,
            false //isForManualNotification
          )
        )
        .pipe(
          switchMap(() =>
            this.xsStore.select(UploadStateSelector.getRepositoryHierarchy)
          ),
          filter((response) => !!response),
          take(1)
        )
        .subscribe((response: RepositoryHierarchyModel[]) => {
          if (response && response.length > 0) {
            const filteredRepositoryRowsKeys = response.filter(
              (x) => x.parentId == '-1'
            )
            this.expandedRepositoryRowsKeyList = filteredRepositoryRowsKeys.map(
              function (a) {
                return a.id
              }
            )

            const tempRepositoryHierarchyList: RepositoryHierarchyModel[] =
              JSON.parse(JSON.stringify(response))

            this.repositoryHierarchyList = tempRepositoryHierarchyList.filter(
              (x) => x.hasSuccesfulConnection === true
            )

            this.invalidRepositoryList = tempRepositoryHierarchyList.filter(
              (x) => x.hasSuccesfulConnection === false
            )
            if (this.invalidRepositoryList?.length > 0) {
              this.dialog.open(this.invalidRepositoyDialog, {
                closeOnNavigation: true,
                autoFocus: false,
                width: '500px'
              })
            }

            const dummyRepositoryNodeData = []
            let dummyId = 1
            this.repositoryHierarchyList.forEach(function (obj) {
              obj.isLoading = false
              obj.isLoadedAll = false
              obj.isExpanded = false
              obj.showNodeMenuOptions = false
              if (obj.parentId === '-1') {
                obj.isLoaded = true
                obj.isExpanded = true
              }
              if (obj.parentId !== '-1' && obj.type === 'FOLDER') {
                const dummyNode = {
                  fsid: '0',
                  name: '',
                  id: dummyId.toString(),
                  parentId: obj.id,
                  type: 'Dummy',
                  repositoryDisplayName: 'Dummy',
                  repositoryRootFolderName: 'Dummy',
                  relativePath: 'Dummy',
                  isLoading: false,
                  isLoaded: false,
                  isLoadedAll: false,
                  isCancelling: false,
                  showNodeMenuOptions: false,
                  isExpanded: false
                }
                dummyId = dummyId + 1

                dummyRepositoryNodeData.push(dummyNode)
              }
            })

            if (dummyRepositoryNodeData.length > 0) {
              this.repositoryHierarchyList = [
                ...this.repositoryHierarchyList,
                ...dummyRepositoryNodeData
              ]
            }
          } else {
            this.repositoryHierarchyList = []
          }
          this.repositoryHierarchyLoader = false
        })
    }
  }

  editorPreparing(e) {
    if (e.dataField === 'Head_ID' && e.row.data.ID === 1) {
      e.cancel = true
    }
  }

  /**
   * Checks whether the supplied argument is valid and returns boolean.
   * @param access an static type (enum) data to get validate.
   */
  readonly isExcludeRights = (access: UserRights): Observable<boolean> =>
    this.xsStore.select(StartupStateSelector.hasGlobalRight(access))

  /**
   * User right to Import
   */
  @Select(StartupStateSelector.hasGroupRight(UserRights.ALLOW_TO_IMPORT))
  allowToImport$: Observable<boolean>

  countryKeyCreator(params) {
    const countryObject = params.value
    return countryObject.name
  }

  countryValueGetter(params) {
    const countryName = params.data.mediaName
    // let countryCode = countryName.substring(0, 2).toUpperCase();
    return {
      name: countryName
      // code: countryCode
    }
  }

  openAWS() {
    this.triggerDrillRepositoryHierarchy = []

    this.removedRepostioryDummyNodes = []

    this.parentRepositoryOrFolders = []

    this.childRepositoryOrFolders = []

    this.parentRepositoryItemsMapper = []

    this.childRepositoryItemsMapper = []

    this.addedFilesList = []
    this.repositoryFilesList = []
    this.awsS3FileList = []
    this.nsfUserIdFiles = []
    this.isBatchMediaContainerLoading = true
    this.isBatchMediaUi = false
    this.isAWSS3UI = true
    this.isRepository = false
    this.filesUploaded = []
    this.uploadBtnName = 'Process'

    // cleanup for next init (GC will take care)
    this.mUploader = null
    this.isFinishProcessed = false
    this.currentlyUploadingId = ''
    this.currentFileUploadProgress = 0

    this.fnGetCustodianMediaStatus()
    this.fnGetUploadMediaStatus()
    this.fnStartUploadResponse()
    this.fnDeleteResponse()
  }

  openupload(isRepo) {
    this.isBatchMediaUi = false
    this.isAWSS3UI = false
    this.isRepository = isRepo
    if (!isRepo) {
      this.uploadBtnName = 'Upload & Process'
    } else {
      this.triggerDrillRepositoryHierarchy = []
      this.removedRepostioryDummyNodes = []
      this.parentRepositoryOrFolders = []
      this.childRepositoryOrFolders = []
      this.parentRepositoryItemsMapper = []
      this.childRepositoryItemsMapper = []

      this.filesUploaded = []
      this.uploadBtnName = 'Process'
      this.repositoryLoader = true
      this.repositoryLoadingMessage = 'Loading Repositories ...'

      this.loadRepositories(false)
    }

    this.fnGetCustodianMediaStatus()
    this.fnGetUploadMediaStatus()
    this.fnStartUploadResponse()
    this.fnDeleteResponse()

    // cleanup for next init (GC will take care)
    this.mUploader = null
    this.isFinishProcessed = false
    this.currentlyUploadingId = ''
    this.currentFileUploadProgress = 0
    this.addedFilesList = []
    this.repositoryFilesList = []
    this.awsS3FileList = []
    this.nsfUserIdFiles = []

    if (!this.isRepository) {
      setTimeout(() => {
        this.mUploader = this.uploaderObject()
        this.mUploader.init()
      }, 1000)
    }
  }

  loadRepositories(isRefresh: boolean): void {
    this.repositoryLoader = true
    this.repositoryLoadingMessage = 'Loading Repositories ...'

    this.xsStore
      .dispatch(new GetRepository(true))
      .pipe(
        switchMap(() =>
          this.xsStore.selectOnce(UploadStateSelector.getRepository)
        ),
        filter((response) => !!response),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((response: RepositoryModel[]) => {
        if (response && response.length > 0) {
          this.repositoryList = response
          if (!isRefresh) {
            const uploadRepository = response.find(
              (x) => x.repositoryType === 'UPLOAD'
            )
            if (uploadRepository) {
              if (!this.selectedRepository.includes(uploadRepository.fsid)) {
                this.selectedRepository.push(uploadRepository.fsid)
              }
              this.onClickLoadHierarchy()
            }
          }
        } else {
          this.repositoryList = []
        }
        this.repositoryLoader = false
      })
  }

  ngOnInit() {
    this.#selectVodVersionAndSetNewUiRedirectionPath()
    //self service or limited service
    this.configService
      .fetchLicenseStatus$('VENIO ONDEMAND', 'LIMITED SERVICE')
      .subscribe((isEnabled: boolean) => {
        this.isLimitedServiceLicense = isEnabled
        this.cdr.markForCheck()
      })

    this.projectName = localStorage.ProjectName
    this.companyName = this.configService.companyName

    this.queryParamsSubscription = this.route.queryParams.subscribe(
      (params) => {
        // upload invitation token and handle process
        const userWiseToken = params['userWiseToken']
        if (userWiseToken) {
          this.checkToken(userWiseToken)
        }

        this.addDataToExistingCase = params['existingCase']
          ? params['existingCase'].toLowerCase() === 'true'
          : false
        this.overrideSettingsInfo = params['overrideSettings']
          ? params['overrideSettings'].toLowerCase() === 'true'
          : false
        this.isContinuedAfterControlNumberConflict = params['afterConflict']
          ? params['afterConflict'].toLowerCase() === 'true'
          : false
        this.settingId = +params['settingId']
        this.structuredData = params['structuredData']
          ? params['structuredData'].toLowerCase() === 'true'
          : false

        this.isOverlay = this.router.url.includes('/overlay')
        this.isRepositoryUpload = this.router.url.includes('/management')
        if (!this.isOverlay && !this.isRepositoryUpload) {
          this.projectId = params['projectId'] ?? -1
          if (this.existingProjectId == null) {
            this.existingProjectId = this.projectId
          }
          if (this.existingProjectId != this.projectId) {
            setTimeout(() => {
              window.location.reload()
            }, 1000)
          }
        }
      }
    )
    //Check if the flag is enabled /disabled for transcript upload
    this.configStore
      .pipe(select(getIsTranscriptEnabled), takeUntil(this.unsubscribed$))
      .subscribe((isEnabled: boolean) => {
        this.showTranscript = isEnabled
      })
    this.configStore
      .pipe(
        select(getControlSetting('ENABLE_REVIEW_2')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((isEnabled: boolean) => {
        this.enableReview2 = isEnabled
      })
    this.checkPermission()
    this.configStore
      .pipe(select(getUserDetails), takeUntil(this.unsubscribed$))
      .subscribe((userDetails: User) => {
        this.userDetails = userDetails
      })

    this.configStore
      .pipe(select(getClientInfo), takeUntil(this.unsubscribed$))
      .subscribe((client: Client) => {
        this.client = client
      })

    this.xsStore
      .select(UploadStatesSelector.getRepositoryUploadSuccess)
      .pipe(
        filter((x) => !!x && x.length > 0),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((res) => {
        if (res) {
          this.repositoryFilesList = []
          this.awsS3FileList = []
          //this.repositoryList = []
          this.nsfUserIdFiles = []
          this.isRepository = true
          this.filesUploaded = res
          this.callUpdateCustodianMedia()
          // clearing collectedRepositoryUploadInfo in state to prevent this subscribe from being called when the component is reinitialized
          this.xsStore.dispatch(
            new fromLazyUploadStateActions.ClearCollectedRepositoryUploadInfo()
          )
        }
      })
    if (!this.isRepositoryUpload) {
      this.xsStore
        .select(ProductionSelectors.vodrSettings)
        .pipe(
          map((settings) => {
            if (!settings || _.isEmpty(settings)) {
              if (this.isVodrEnabled && this.projectId) {
                this.xsStore.dispatch(
                  new fromProductionStateActions.FetchVODRSettings(
                    this.projectId,
                    0,
                    this.externalUserId,
                    this.addDataToExistingCase
                  )
                )
              }
            }
            return settings
          }),
          takeUntil(this.unsubscribed$)
        )
        .subscribe((settings: SettingsInfo) => {
          this.settingsInfo = settings
        })
    }

    // Get Theme name of client ui css class in UI
    this.configStore
      .pipe(select(getThemeClient), takeUntil(this.unsubscribed$))
      .subscribe((strThemeName: string) => {
        this.mstrThemeName = strThemeName
      })

    this.internalUserId = +localStorage.getItem('UserId')
    this.externalUserId = +localStorage.getItem('UserId')
    if (localStorage.getItem('DocShareUserRole') != null) {
      if (
        localStorage.getItem('DocShareUserRole').toLocaleLowerCase() ===
        'external'
      ) {
        this.internalUserId = -1
      } else {
        this.externalUserId = -1
      }
    }
    if (!this.isRepositoryUpload) {
      // get list Custodian for select
      this.store.dispatch(new GetCustodianNameList(this.projectId))
    }
    this.store
      .pipe(
        select(custodianNameList),
        takeUntil(this.unsubscribed$),
        map((res: any) => res),
        filter((res) => !!res)
      )
      .subscribe((res) => {
        this.custodianNames = res
      })

    this.configStore
      .pipe(
        select(
          getControlSetting('UPLOAD_CHUNK_SIZE'),
          takeUntil(this.unsubscribed$)
        )
      )
      .subscribe((chunkSize: number) => {
        if (chunkSize) this.uploadChunkSize = chunkSize
      })

    this.configStore
      .pipe(
        select(
          getControlSetting('UPLOAD_RETRY_COUNT'),
          takeUntil(this.unsubscribed$)
        )
      )
      .subscribe((retryCount: number) => {
        if (retryCount) this.uploadRetryCount = retryCount
      })

    this.configStore
      .pipe(
        select(
          getControlSetting('UPLOAD_RETRY_INTERVAL'),
          takeUntil(this.unsubscribed$)
        )
      )
      .subscribe((retryInterval: number) => {
        if (retryInterval) this.uploadRetryInterval = retryInterval
      })

    this.configStore
      .pipe(
        select(
          getControlSetting('PROCESS_ALL_VOD_FILES_IN_SINGLE_MEDIA'),
          takeUntil(this.unsubscribed$)
        )
      )
      .subscribe((processAllFilesInSingleMedia: boolean) => {
        this.processAllFilesInSingleMedia = processAllFilesInSingleMedia
      })
    if (!this.isRepositoryUpload) {
      this.xsStore
        .dispatch(new UActions.FetchIsFallbackIngestionEngine(+this.projectId))
        .pipe(
          switchMap(() =>
            this.xsStore.selectOnce(
              UploadStateSelector.isFallbackIngestionEngineResponse
            )
          ),
          filter((response) => !!response),
          takeUntil(this.unsubscribed$)
        )
        .subscribe((response: boolean) => {
          if (response) {
            this.isFallback = response
          }
        })
    }
    this.configStore
      .pipe(
        select(
          getControlSetting('MIP_SUPPORTED_EXTENSION'),
          takeUntil(this.unsubscribed$)
        )
      )
      .subscribe((mipSupportedFileFormats: string) => {
        const mipFileFormatsArray = mipSupportedFileFormats?.split(',')
        mipFileFormatsArray.forEach((extension: string) => {
          this.forensicImageFileFormat += `|${extension.replace(
            /\d+$/,
            '\\d+'
          )}`
        })
      })
    //Called after upload and process completes or fails and gives info
    this.uploadService.getMSTeamUploadNProcessStatus.subscribe((results) => {
      this.addedFilesList = []
      if (results != null && results.length > 0) {
        results.forEach((item) => {
          if (item.Source === 'MSTEAM') {
            // Remove uploaded fileid from progress list 'currentlyUploadingMSTeamIds'
            const index = this.currentlyUploadingMSTeamIds.findIndex(
              (x) => x === item.FileId
            )
            if (index > -1) {
              this.currentlyUploadingMSTeamIds.splice(index, 1)
            }

            if (item.Status === 'SUCCESS') {
              // Add successfully uploaded fileid into 'uploadedMSTeamIds'
              if (!this.uploadedMSTeamIds.includes(item.FileId)) {
                this.uploadedMSTeamIds.push(item.FileId)

                this.toastr.showSuccess(
                  "Media '" +
                    item.MediaName +
                    "' has been successfully uploaded and queued for processing.",
                  true
                )
              }
            } else if (item.Status === 'FAILED') {
              // Add failed uploaded fileid into 'failedMSTeamIds'
              if (!this.failedMSTeamIds.includes(item.FileId)) {
                this.failedMSTeamIds.push(item.FileId)

                this.toastr.showError(
                  "Media '" +
                    item.MediaName +
                    "' has been failed during upload.",
                  true
                )
              }
            }
          }
        })
      }
    })

    //Removes the loading after user is expanded to get teams
    this.uploadApiService.getMSTeamLoadingStatus.subscribe((dt) => {
      this.loading = false
    })

    this.store
      .pipe(select(getProjectInfo), takeUntil(this.unsubscribed$))
      .subscribe((projectInfo) => {
        this.projectInfo = projectInfo
        this.isVodrEnabled = Boolean(this.projectInfo.isServiceTypeCase)
      })

    this.selectBatchMediaAddToProcess()

    this.getImportStatus()
    this.handleUploadMediaStatusResponse()
    this.handleCustodianMediaStatusResponse()
    this.scheduleImportStatusRefresh()
    this.scheduleMediaStatusRefresh()
  }

  /**
   * Calls api to fetch import status every few seconds when in sturctured upload
   */
  private scheduleImportStatusRefresh(): void {
    interval(this.importStatusRefreshInterval)
      .pipe(
        filter(() => this.selectedSourceType === SourceType.Structured),
        switchMap(() => this.fetchImportStatus()),
        takeUntil(this.unsubscribed$)
      )
      .subscribe()
  }

  /**
   * Fetches import status for current project
   * @returns {Observable<any>} observable
   */
  private fetchImportStatus(): Observable<any> {
    return this.xsStore
      .dispatch(
        new fromLazyUploadStateActions.FetchImportStatus(+this.projectId)
      )
      .pipe(
        catchError((error) => {
          console.error('Error fetching import status:', error)
          return EMPTY
        })
      )
  }

  /**
   * Handles response from import status api calls and updates details and filter
   */
  private getImportStatus() {
    this.xsStore
      .select(UploadStatesSelector.getImportStatus)
      .pipe(
        filter((imp) => !!imp),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((importStatuses) => {
        this.cdr.markForCheck()
        this.allImportStatuses = this.populateImportDetails(importStatuses)
        this.updateFilteredImportStatuses()
      })
  }

  /**
   * Appends import details property with status and progress percentage
   * @param imports import entry
   * @returns import records appended with detail containing status and progress percentage
   */
  private populateImportDetails(imports: ImportStatus[]): ImportStatus[] {
    return [
      ...imports.map(
        (imp): ImportStatus => ({
          ...imp,
          importDetails: [
            {
              title: 'Upload Status',
              completePercentage: imp.uploadProgress
            },
            {
              title: 'Extraction Status',
              completePercentage: imp.extractionProgress
            },
            {
              title: 'Analysis Status',
              completePercentage: imp.analysisProgress
            },
            {
              title: 'Validation Status',
              completePercentage: imp.validationProgress
            },
            {
              title: 'Import Status',
              completePercentage: imp.importProgress
            }
          ]
        })
      )
    ]
  }

  /**
   * Updates filters to show only specific import status entries
   */
  public updateFilteredImportStatuses(): void {
    this.filteredImportStatuses = this.allImportStatuses.filter((x) => {
      switch (this.currentImportFilterSelection) {
        case 'Upload':
          return x.importStatus === 'Upload_InProgress'

        case 'Extraction':
          return x.importStatus === 'Extraction_InProgress'

        case 'Analysis':
          return (
            x.importStatus === 'Analysis_Completed' ||
            x.importStatus === 'Analysis_InProgress'
          )

        case 'Validation':
          return (
            x.importStatus === 'Validation_Completed' ||
            x.importStatus === 'Validation_InProgress'
          )

        case 'Import':
          return x.importStatus === 'Import_InProgress'

        case 'Completed':
          return x.importStatus === 'Completed'

        default:
          return true
      }
    })
  }

  /**
   * Handles filter menu selection and updates filtered list
   * @param status Status for which to filter entries
   */
  public filterImportStatus(status: string): void {
    this.currentImportFilterSelection = status
    this.updateFilteredImportStatuses()
  }

  public getStageName(status: string): string {
    if (status.toLocaleLowerCase() === 'upload') return 'Upload Stage'
    if (status.toLocaleLowerCase() === 'extraction') return 'Extraction Stage'
    if (status.toLocaleLowerCase() === 'analysis') return 'Analysis Stage'
    if (status.toLocaleLowerCase() === 'validation') return 'Validation Stage'
    if (status.toLocaleLowerCase() === 'import') return 'Import Stage'
    return status
  }

  /**
   * Enable/disables import wizard button
   * @param importStatus current import status item
   * @returns flag indicating if the import link button should be enabled
   */
  public enableImportLink(importStatus: ImportStatus): boolean {
    return (
      importStatus.importStatus === 'Analysis_Completed' ||
      importStatus.importStatus === 'Validation_Completed'
    )
  }

  /**
   * Handles import wizard link click
   * @param importStatus current import status item
   */
  public importLinkAction(importStatus: ImportStatus): void {
    this.router.navigateByUrl(
      '/import/load_file?projectId=' +
        this.projectId +
        '&importId=' +
        importStatus.importId +
        '&isOverlay=' +
        (importStatus.isOverlay ? '1' : '0')
    )
  }

  private selectBatchMediaAddToProcess(): void {
    this.batchMediaUploadFacade.selectAddMediaToProcess$
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.store.dispatch(
          new GetCustodianMediaStatus(
            +this.projectId,
            localStorage.StatusSessionId,
            this.isVodrEnabled,
            this.internalUserId
          )
        )
      })
  }

  /** check permission of the user */
  private checkPermission() {
    //Check if the user has the right for transcript upload
    this.xsStore
      .select(
        StartupStateSelector.hasGroupRight(
          UserRights.ALLOW_TO_UPLOAD_TRANSCRIPT
        )
      )
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((hasRight: boolean) => {
        if (hasRight || hasRight == null || hasRight === undefined) {
          this.showTranscriptUpload = hasRight
        } else {
          this.showTranscriptUpload = hasRight
        }
      })
  }

  // upload invitation token and handle process
  checkToken(userWiseToken) {
    this.xsStore
      .dispatch(
        new UActions.FetchInvitationHandle(this.projectId, userWiseToken)
      )
      .pipe(
        switchMap(() =>
          this.xsStore.selectOnce(UploadStateSelector.invitationHandleResponse)
        ),
        filter((response) => !!response),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((response: string) => {
        if (response) {
          this.invitationHandle(response)
        }
      })
  }

  // upload invitation handle response and show message
  invitationHandle(response: string) {
    response = response.toUpperCase()
    this.invitationHandleMessage = ''

    if (response === 'PROJECT_DOES_NOT_EXIST' || response === 'EXPIRED') {
      this.invitationHandleMessage =
        response === 'PROJECT_DOES_NOT_EXIST'
          ? 'The Project does not exists.'
          : 'Upload link has expired. This link cannot be further used to upload data.'
    } else if (response === 'UNAUTHORIZED_ACCESS') {
      this.invitationHandleMessage = `You don't have the right to access this page.`
      this.toastr.showError(this.invitationHandleMessage)
    } else if (response !== 'SUCCESS') {
      this.invitationHandleMessage = 'The upload invitation link is not valid.'
    }

    if (this.invitationHandleMessage) {
      this.toastr.showError(this.invitationHandleMessage)
      this.router.navigate(['/launchpad/caselaunchpad'])
    }
  }

  // #region    ------------------------------[upload control]-------------------------------------------------------------//
  uploaderObject() {
    const self = this

    const uploader = new plupload.Uploader({
      runtimes: 'html5,silverlight,html4',
      browse_button: 'pickfiles', // you can pass an id...
      container: document.getElementById('uploader'), // ... or DOM Element itself
      url:
        this.configService.getVodBaseUrl() +
        '/VodWebService.asmx/ChunkUploadFile',
      //flash_swf_url: '../js/Moxie.swf',
      silverlight_xap_url: '../js/Moxie.xap',
      max_file_size: 0,
      chunk_size: `${this.uploadChunkSize}mb`,
      max_retries: this.uploadRetryCount,
      max_retries_time: this.uploadRetryInterval,

      unique_names: false,
      drop_element: 'dropArea',
      multiple_queues: true,
      // Rename files by clicking on their titles
      rename: true,
      // Sort files
      sortable: true,

      // Enable ability to drag'n'drop files onto the widget (currently only HTML5 supports that)
      dragdrop: true,

      // Views to activate
      views: {
        list: true,
        thumbs: true, // Show thumbs
        active: 'thumbs'
      },
      init: {
        PostInit: function () {
          document.getElementById('filelist').innerHTML = ''
        },
        // upload control event
        FilesAdded: (up, files) => {
          self.uploaderObjectFilesAdded(up, files, self)
        },
        BeforeUpload: function (up, file) {
          self.uploaderObjectBeforeUpload(up, file, self)
        },
        UploadProgress: (up, file) => {
          self.uploaderObjectUploadProgress(up, file, self)
        },
        FileUploaded: function (up, file) {
          self.uploaderObjectFileUploaded(up, file, self)
        },
        UploadComplete: (up, file) => {
          // when all files are uploaded, set the status to completed and dispatch the action
          // the `uploaded` property sometimes returns a value less than the total number of files so, we
          // have increment by 1 to get the correct value. the issue might be coming from the plupload library
          const uploadProgressStatus =
            up.total.uploaded === up.files.length ||
            up.total.uploaded + 1 === up.files.length
              ? 'completed'
              : 'in-progress'
          this.store.dispatch(
            new FileUploadProgressStatus({
              uploadProgressStatus
            })
          )

          self.uploaderObjectUploadComplete(up, file, self, 200)

          // clear upload list after all download is completed
          self.filesUploaded = []
        },
        UploadFile: function (up, file) {
          self.uploaderObjectUploadFile(up, file, self)
        },
        Error: function (up, err) {
          if (err.code === -601) {
            self.toastr.showError(
              err.message + '\nSelected file format is not supported.'
            )
          } else if (err.code === -600) {
            self.toastr.showError(
              'Error: Multiple file with different extension is selected' +
                '\nAll selected file format is not supported.'
            )
          } else {
            self.toastr.showError('\nError #' + err.code + ': ' + err.message)
          }
        }
      }
    })

    return uploader
  }

  private getSupportedFileFormat(): string {
    let uploadSupportedFileFormat
    if (this.selectedSourceType === SourceType.Transcript) {
      // supported file formats for transcript.
      uploadSupportedFileFormat = `ptf|pcf`
    } else {
      if (this.isRepositoryUpload) {
        // supported file formats including txt and csv load files
        uploadSupportedFileFormat = `txt|csv|zip|zipx|rar|7z|gz|lzh|obd|cab|tar|dbx|dxl|mbox|ns2|nsf|ost|pst|olm|ISO|E\\d+|L\\d+|${this.forensicImageFileFormat}`
      } else {
        // supported file formats.
        uploadSupportedFileFormat = `zip|zipx|rar|7z|gz|lzh|obd|cab|tar|dbx|dxl|mbox|ns2|nsf|ost|pst|olm|ISO|E\\d+|L\\d+|${this.forensicImageFileFormat}`
      }
    }
    return uploadSupportedFileFormat
  }

  /**
   * Finds out the invalid file format and report to the end user to select valid file.
   */
  private readonly fileExtensionValidation = () => {
    const uploadSupportedFileFormat = this.getSupportedFileFormat()

    const filePattern = new RegExp(`(${uploadSupportedFileFormat})$`, 'i')

    // for supporting forensic image multipart file with extension LAA,LAB,..LZZ...MAA,MAB,...MZZ or EAA,EAB,..,EZZ,FAA,FAB,...FZZ..ZZZ
    const validForensicparts = this.getValidForensicParts(
      this.mUploader.files.map((f) => f.name)
    )

    const invalidFound = this.mUploader.files.find((f) => {
      if (validForensicparts.includes(f.name.toUpperCase()) === false) {
        return !filePattern.test(f.name.split('.').pop())
      }
    })
    // if found, remove from `plupload`
    const index = this.mUploader.files.findIndex(
      (a) => a.name === invalidFound?.name
    )
    if (index > -1) {
      this.mUploader.removeFile(this.mUploader.files[index])
    }
    // toggle the invalid message with selected file name
    this.invalidFileFound = invalidFound?.name
    return invalidFound ?? false
  }

  uploaderObjectFilesAdded(up, files, self) {
    const tempFileArray: AddedFilesList[] = []

    // do not add invalid files
    if (this.fileExtensionValidation()) return false

    plupload.each(files, function (file) {
      const fileInfo: AddedFilesList = {
        FileId: file.id,
        FileName: file.name,
        FileSizeFormatted: self.formatBytes(file.size, 2),
        FileSize: file.size,
        CustodianName: null,
        IsStructured: false,
        mediaSourceType: MediaSourceType.SOURCE_FILE,
        FullName: file.name,
        Extension: self.getFileExtension(file.name),
        Name: file.name,
        IsForensicImageMultipart: 'N',
        MediaName: null,
        Password: ''
      }
      tempFileArray.push(fileInfo)
    })
    if (self.addedFilesList) {
      self.addedFilesList = self.addedFilesList.concat(tempFileArray)
    } else {
      self.addedFilesList = tempFileArray
    }

    return true
  }

  uploaderObjectBeforeUpload(up, file, self) {
    file.IsForensicImageMultipart = file.IsForensicImageMultipart
      ? file.IsForensicImageMultipart
      : 'false'

    $.ajax({
      type: 'POST',
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
      url:
        self.configService.getVodBaseUrl() +
        '/VodWebService.asmx/InitializeUpload',
      data:
        '{UploadingFileID:"' +
        file.id +
        '" ,uploadToProjectLoc:"' +
        localStorage.getItem('UploadInProjectLocation') +
        '",IsForensicImageMultipart:"' +
        file.IsForensicImageMultipart +
        '"}',
      timeout: 1000 * 600,
      async: false,
      success: function (data) {
        if (data.d !== 'PASS') {
          self.toastr.showError(data.d)
        }
        //up.start()
      },
      error: function (data) {
        self.toastr.showError(
          'Failed to insert uploaded file information into the table'
        )
      }
    })
  }

  uploaderObjectUploadProgress(up, file, self) {
    if (document.getElementById(file.id + '-progressbar')) {
      self.currentlyUploadingId = file.id

      $.ajax({
        type: 'POST',
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        url:
          self.configService.getVodBaseUrl() +
          '/VodWebService.asmx/GetUploadedFileProgressStatus',
        data: '{UploadingFileId:"' + file.id + '"}',
        timeout: 1000 * 600,
        async: false,
        success: function (data) {
          if (data?.d) {
            self.currentFileUploadProgress = data.d.UploadProgressPercent
            document.getElementById(file.id + '-progress-digit').textContent =
              data.d.UploadProgressPercent.toString() + '%'
          }
        },
        error: function (data) {}
      })
    }
  }

  uploaderObjectFileUploaded(up, file, self) {
    $.ajax({
      type: 'POST',
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
      url:
        self.configService.getVodBaseUrl() +
        '/VodWebService.asmx/LoadUploadedFileToSessionAndTable',
      data:
        '{Filename:"' +
        file.name +
        '" ,filesize:"' +
        file.size +
        '" ,fileid:"' +
        file.id +
        '" ,uploadToProjectLoc:"' +
        localStorage.getItem('UploadInProjectLocation') +
        '"}',
      timeout: 1000 * 600,
      async: false,
      success: function (data) {
        const str = '{' + data.d + '}'
        try {
          const obj = JSON.parse(str)
          obj.IsForensicImageMultipart = 'N'
          //set forensic image multi part
          if (self.filesUploaded.length > 0) {
            const custFiles = self.filesUploaded.filter(
              (file) => file.CustodianName === obj.CustodianName
            )

            const forensicImagePattern = new RegExp(
              `(${self.forensicImageFileFormat})$`,
              'i'
            )

            const multiPartPattern = new RegExp(
              `(${obj.Extension.replace(/\d+$/, '\\d+')})$`,
              'i'
            )

            const validForensiceparts = self.getValidForensicParts([
              ...custFiles.map((f) => f.FileName),
              obj.FileName
            ])

            custFiles.forEach((element) => {
              if (
                obj.FileName.replace(obj.Extension, '') ===
                  element.FileName.replace(element.Extension, '') &&
                ((forensicImagePattern.test(obj.Extension) &&
                  multiPartPattern.test(element.Extension)) ||
                  (validForensiceparts.includes(obj.FileName.toUpperCase()) &&
                    !self.initialForensicImageExtensions.includes(
                      obj.Extension
                    )))
              ) {
                obj.IsForensicImageMultipart = 'Y'
              }
            })
          }

          self.filesUploaded = self.filesUploaded.concat(obj)
        } catch (e) {
          console.error('error ', e)
        }

        if (data.d === null && self.cancelMessage === '') {
          self.toastr.showError(
            'Failed to insert uploaded file information into the project'
          )
        }
      },
      error: function (data) {
        self.toastr.showError(
          'Failed to insert uploaded file information into the table'
        )
      }
    })
    let index
    self.addedFilesList.forEach((fl, i) => {
      if (file.id === fl.FileId) {
        index = i
      }
    })
    self.addedFilesList.splice(index, 1)

    //upload another file in queue
    this.uploadNext()
  }

  uploaderObjectUploadComplete(up, file, self, httpCode) {
    $.ajax({
      type: 'POST',
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
      url:
        self.configService.getVodBaseUrl() +
        '/VodWebService.asmx/ClearTimeStamp',
      data: '{UploadingFileID:"' + file.id + '"}',
      timeout: 1000 * 600,
      async: false
    })
    self.callUpdateCustodianMedia()

    const files =
      httpCode === 200 ? up.files : up.files.filter((f) => f.id === file.id)
    self.sendUploadCompleteMessage(files, httpCode)
    if (self.currentlyUploadingId === file.id) {
      self.currentlyUploadingId = ''
    }
  }

  /**
   * binded event for PlUpload's UploadFile event to implement retry inverval for chunck retry
   * @param up
   * @param file
   * @param self
   */
  uploaderObjectUploadFile(up, file, self): void {
    let url = up.settings.url
    const chunkSize = up.settings.chunk_size
    let retries = up.settings.max_retries
    const features = up.features
    let offset = 0
    let blob

    const runtimeOptions = {
      runtime_order: up.settings.runtimes,
      required_caps: up.settings.required_features,
      preferred_caps: up.preferred_caps,
      swf_url: up.settings.flash_swf_url,
      xap_url: up.settings.silverlight_xap_url
    }

    if (file.loaded) {
      offset = file.loaded = chunkSize
        ? chunkSize * Math.floor(file.loaded / chunkSize)
        : 0
    }

    function handleError() {
      if (retries-- > 0) {
        const retryInterval =
          (up.settings.max_retries - retries) *
          up.settings.max_retries_time *
          1000

        _.delay(uploadNextChunk, retryInterval)
      } else {
        // reset all progress
        file.loaded = offset

        self.uploaderObjectUploadComplete(up, file, self, plupload.HTTP_ERROR)
        self.cancelUpload(file.id, true)
      }
    }

    function uploadNextChunk() {
      let chunkBlob, curChunkSize
      const args = {
        name: '',
        chunk: 0,
        chunks: 0,
        offset: 0,
        total: 0
      }

      // make sure that file wasn't cancelled and upload is not stopped in general
      if (file.status !== plupload.UPLOADING || up.state === plupload.STOPPED) {
        return
      }

      // send additional 'name' parameter only if required
      if (up.settings.send_file_name) {
        args['name'] = file.target_name || file.name
      }

      if (chunkSize && features.chunks && blob.size > chunkSize) {
        curChunkSize = Math.min(chunkSize, blob.size - offset)
        chunkBlob = blob.slice(offset, offset + curChunkSize)
      } else {
        curChunkSize = blob.size
        chunkBlob = blob
      }

      // If chunking is enabled add corresponding args, no matter if file is bigger than chunk or smaller
      if (chunkSize && features.chunks) {
        // Setup query string arguments
        if (up.settings.send_chunk_number) {
          args['chunk'] = Math.ceil(offset / chunkSize)
          args['chunks'] = Math.ceil(blob.size / chunkSize)
        } else {
          // keep support for experimental chunk format, just in case
          args['offset'] = offset
          args['total'] = blob.size
        }
      }

      if (up.trigger('BeforeChunkUpload', file, args, chunkBlob, offset)) {
        uploadChunk(args, chunkBlob, curChunkSize)
      }
    }

    function uploadChunk(args, chunkBlob, curChunkSize) {
      let formData
      const xhr = new plupload.moxie.xhr.XMLHttpRequest()

      // Do we have upload progress support
      if (xhr.upload) {
        xhr.upload.onprogress = function (e) {
          file.loaded = Math.min(file.size, offset + e.loaded)
          up.trigger('UploadProgress', file)
        }
      }

      xhr.onload = function () {
        //returns error message if space on the disk is full
        if (
          xhr.status === 500 &&
          xhr.response.includes('There is not enough space on the disk.')
        ) {
          self.toastr.showError(
            'The project location disk is full. No space to Upload data.'
          )
          return
        }

        // check if upload made itself through
        if (xhr.status < 200 || xhr.status >= 400) {
          handleError()
          return
        }

        // reset the counter
        retries = up.settings.max_retries

        // Handle chunk response
        if (curChunkSize < blob.size) {
          chunkBlob.destroy()

          offset += curChunkSize
          file.loaded = Math.min(offset, blob.size)

          up.trigger('ChunkUploaded', file, {
            offset: file.loaded,
            total: blob.size,
            response: xhr.responseText,
            status: xhr.status,
            responseHeaders: xhr.getAllResponseHeaders()
          })

          // stock Android browser doesn't fire upload progress events, but in chunking mode we can fake them
          if (plupload.ua.browser === 'Android Browser') {
            up.trigger('UploadProgress', file)
          }
        } else {
          file.loaded = file.size
        }

        // Free memory
        chunkBlob = formData = null

        // Check if file is uploaded
        if (!offset || offset >= blob.size) {
          // If file was modified, destory the copy
          if (file.size != file.origSize) {
            blob.destroy()
            blob = null
          }
          up.trigger('UploadProgress', file)

          file.status = plupload.DONE
          file.completeTimestamp = +new Date()
          self.uploaderObjectFileUploaded(up, file, self)
        } else {
          // Still chunks left
          // run detached, otherwise event handlers interfere
          _.delay(uploadNextChunk, 1)
        }
      }

      xhr.onerror = function () {
        handleError()
      }

      xhr.onloadend = function () {
        xhr.destroy()
      }

      // Build multipart request
      if (up.settings.multipart && features.multipart) {
        xhr.open(up.settings.http_method, url, true)

        // Set custom headers
        plupload.each(up.settings.headers, function (value, name) {
          xhr.setRequestHeader(name, value)
        })

        formData = new plupload.moxie.xhr.FormData()

        // Add multipart params
        plupload.each(
          plupload.extend(args, up.settings.multipart_params),
          function (value, name) {
            formData.append(name, value)
          }
        )

        // Add file and send it
        formData.append(up.settings.file_data_name, chunkBlob)
        xhr.send(formData, runtimeOptions)
      } else {
        // if no multipart, send as binary stream
        url = plupload.buildUrl(
          up.settings.url,
          plupload.extend(args, up.settings.multipart_params)
        )

        xhr.open(up.settings.http_method, url, true)

        // Set custom headers
        plupload.each(up.settings.headers, function (value, name) {
          xhr.setRequestHeader(name, value)
        })

        // do not set Content-Type, if it was defined previously (see #1203)
        if (!xhr.hasRequestHeader('Content-Type')) {
          xhr.setRequestHeader('Content-Type', 'application/octet-stream') // Binary stream header
        }

        xhr.send(chunkBlob, runtimeOptions)
      }
    }

    blob = file.getSource()

    // Start uploading chunks
    uploadNextChunk()
  }

  start(): void {
    if (this.mUploader.state != plupload.STARTED) {
      this.mUploader.state = plupload.STARTED
      this.mUploader.trigger('StateChanged')

      this.uploadNext()
    }
  }

  uploadNext(): void {
    let file,
      count = 0,
      i = 0

    if (this.mUploader.state == plupload.STARTED) {
      // Find first QUEUED file
      const files = this.mUploader.files.sort((a, b) =>
        a.name > b.name ? 1 : -1
      )
      for (i = 0; i < this.mUploader.files.length; i++) {
        if (!file && files[i].status == plupload.QUEUED) {
          file = files[i]
          if (this.mUploader.trigger('BeforeUpload', file)) {
            file.status = plupload.UPLOADING
            //this.mUploader.trigger('UploadFile', file)
            this.uploaderObjectUploadFile(this.mUploader, file, this)
          }
        } else {
          count++
        }
      }

      // All files are DONE or FAILED
      if (count == this.mUploader.files.length) {
        if (this.mUploader.state !== plupload.STOPPED) {
          this.mUploader.state = plupload.STOPPED
          this.mUploader.trigger('StateChanged')
        }
        this.mUploader.trigger('UploadComplete', this.mUploader.files)
      }
    }
  }

  // #endregion ------------------------------[upload control]-------------------------------------------------------------//

  // #region ------------------------------[call in  ngOnInit]-----------------------------------------------------------//
  // Get custodian media status
  fnGetCustodianMediaStatus() {
    if (!this.isRepositoryUpload) {
      this.store.dispatch(
        new GetCustodianMediaStatus(
          +this.projectId,
          localStorage.StatusSessionId,
          this.isVodrEnabled,
          this.internalUserId
        )
      )
    }
  }

  handleCustodianMediaStatusResponse() {
    this.store
      .pipe(
        select(custodianMediaStatus),
        filter((res) => !!res && this.selectedSourceType !== SourceType.None),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((res: CustodianMediaStausModel) => {
        this.custodianMediaStatusModel = res
        this.store.dispatch(
          new GetUploadMediaStatus(
            this.projectId,
            localStorage.StatusSessionId,
            this.isTranscriptData,
            this.isRepositoryUpload,
            this.fsid
          )
        )
      })
  }

  uploadFileIds: string[] = []

  readonly showMediaStatus = (uploadFileId: string): boolean =>
    this.uploadFileIds.includes(uploadFileId)

  scheduleMediaStatusRefresh(): void {
    const intervalTimer = interval(15000)
    this.triggerGetUploadMediaStatus = intervalTimer
      .pipe(
        filter(() => this.selectedSourceType !== SourceType.None),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((results) => {
        this.store.dispatch(
          new GetUploadMediaStatus(
            this.projectId,
            localStorage.StatusSessionId,
            this.isTranscriptData,
            this.isRepositoryUpload,
            this.fsid
          )
        )
      })
  }

  handleUploadMediaStatusResponse() {
    this.store
      .pipe(
        select(uploadMediaStatusResponse),
        takeUntil(this.unsubscribed$),
        map((res: any) => res),
        filter((res) => !!res)
      )
      .subscribe((results) => {
        this.totalUploadedFiles = results.ListUploadInfo.length
        this.totalMedia = results.ListMediaInfo.length

        const uploadedInfo = results.ListUploadInfo.filter((item) => {
          return item.UploadStatus === 2
        })
        this.mediaUploaded = uploadedInfo.length
        this.uploadStatus =
          'Completed ' +
          uploadedInfo.length +
          ' out of ' +
          results.ListUploadInfo.length +
          ' file(s)'

        this.isFinishProcessed = false
        const processedInfo = results.ListMediaInfo.filter((item) => {
          return item.MediaStatus === '2'
        })
        if (processedInfo != null) {
          this.mediaProcessed = processedInfo.length
          this.processingStatus =
            'Completed ' +
            this.mediaProcessed +
            ' out of ' +
            results.ListMediaInfo.length +
            ' media(s)'

          //  check to set finish upload
          let isProcessingFinished = false
          if (!this.isRepository) {
            if (
              this.mediaUploaded > 0 &&
              uploadedInfo.length === results.ListUploadInfo.length &&
              this.mediaProcessed === results.ListMediaInfo.length
            ) {
              isProcessingFinished = true
            }
          } else {
            if (this.mediaProcessed === results.ListMediaInfo.length) {
              isProcessingFinished = true
            }
          }

          if (isProcessingFinished) {
            this.isFinishProcessed = true
            this.statusAccordionStatusEvent(false)
          }
        } else {
          this.mediaProcessed = 0
        }
      })
  }

  // Get upload media status
  fnGetUploadMediaStatus() {
    if (this.selectedSourceType === SourceType.Structured) return
    this.store.dispatch(
      new GetUploadMediaStatus(
        this.projectId,
        localStorage.StatusSessionId ?? null,
        this.isTranscriptData,
        this.isRepositoryUpload,
        this.fsid
      )
    )
  }

  fnStartUploadResponse() {
    this.store
      .pipe(
        select(startUploadResponse),
        takeUntil(this.unsubscribed$),
        map((res: any) => res),
        filter((res) => !!res)
      )
      .subscribe((res) => {
        // clearing startUploadResponse in state to prevent the subscribe method being called on initialization
        this.store.dispatch(new ClearStartUploadResponse())
        if (res[0].hasError) {
          this.toastr.showError(
            'An error has occurred. Error details : ' + res[1]
          )
        } else {
          localStorage.setItem('StatusSessionId', res[0].SessionId)
          if (!this.isRepositoryUpload) {
            this.store.dispatch(
              new GetCustodianMediaStatus(
                +this.projectId,
                localStorage.StatusSessionId,
                this.isVodrEnabled,
                this.internalUserId
              )
            )
          }
          if (this.mUploader) {
            this.start()
          }
        }
      })
  }
  // #endregion ------------------------------[call in  ngOnInit]-----------------------------------------------------------//

  ngOnDestroy() {
    if (this.triggerGetCustodianMediaStatus) {
      this.triggerGetCustodianMediaStatus.unsubscribe()
    }
    if (this.triggerGetUploadMediaStatus) {
      this.triggerGetUploadMediaStatus.unsubscribe()
    }
    if (this.queryParamsSubscription) {
      this.queryParamsSubscription.unsubscribe()
    }

    this.unsubscribed$.next()
    this.unsubscribed$.complete()

    if (
      this.router.routerState.snapshot.url.indexOf('/service_request') === -1
    ) {
      // Should we really destroy production state when moving out of upload page? For now, yes.
      // NOTE: Don't move this before this.unsubscribed$.complete(). The reason is,
      // If the subject isn't complete and we try to clear the store, the selector will re-fetch VODR settings.
      this.xsStore.dispatch(
        new fromProductionStateActions.ResetProductionState()
      )
    }
    this.store.dispatch(new ClearStatusResponse())

    // when the upload page is destroyed, reset the upload progress status to `none`
    this.store.dispatch(
      new FileUploadProgressStatus({
        uploadProgressStatus: 'none'
      })
    )
  }

  ngAfterViewInit() {
    if (this.structuredData) {
      setTimeout(() => {
        this.selectedSource(SourceType.Structured, MediaSourceType.SOURCE_FILE)
      })
      // } else {
      //   // if user haven't got Import right then direct to unstructured
      //   this.allowToImport$.subscribe((e) => {
      //     if (!e) {
      //       setTimeout(() => {
      //         this.selectedSource(SourceType.Unstructured)
      //       })
      //     }
      //   })
    }
  }

  // Used in uploaderObjectFilesAdded
  getFileExtension(filename) {
    return /[.]/.exec(filename) ? /[^.]+$/.exec(filename)[0] : undefined
  }

  // Function to convert size in bytes to KB, MB, GB ..
  // Used in uploaderObjectFilesAdded
  formatBytes(bytes, decimals) {
    if (bytes === 0) {
      return '0 Byte'
    }
    const k = 1024
    const dm = decimals + 1 || 3
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  }

  //removes user, team  and channel from selectedMSTeamRowsData if user is already in  addedFilesList
  removeInvalidUserInfo(invalidSelectedUserIdList) {
    invalidSelectedUserIdList.forEach((x) => {
      this.selectedMSTeamRowsData = this.selectedMSTeamRowsData.filter(
        (selDt) => selDt.ID != x
      ) //removes user
      const invalidTeamList = this.selectedMSTeamRowsData.filter(
        (selDt) => selDt.Head_ID == x
      ) //gets invalid teamlist
      this.selectedMSTeamRowsData = this.selectedMSTeamRowsData.filter(
        (selDt) => selDt.Head_ID != x
      ) //removes invalid team
      invalidTeamList.forEach((tmDt) => {
        this.selectedMSTeamRowsData = this.selectedMSTeamRowsData.filter(
          (selDt) => selDt.Head_ID != tmDt.ID
        ) //removes invalid channel
      })
    })
    this.finalSelectedMSTeamRowsData = this.selectedMSTeamRowsData
    this.msTeamHierarchiesGroupedByUser =
      this.getMSTeamHierarchiesGroupedByUser(this.finalSelectedMSTeamRowsData)
  }

  getMSTeamHierarchiesGroupedByUser(finalSelectedMSTeamRowsData) {
    const filteredMSTeamHierarchies: any = []
    const usersOnly = finalSelectedMSTeamRowsData.filter(
      (element) => element.NodeType === 'User'
    )

    usersOnly.forEach((user) => {
      const filteredMSTeamHierarchiesPerUser: any = []

      // Hold individual user node information
      filteredMSTeamHierarchiesPerUser.push(user)

      // Get all the associated team nodes for the user from passed hierarchies i.e from 'msTeamHierarchies'
      const userTeamsOnly = finalSelectedMSTeamRowsData.filter(
        (item) => item.Head_ID === user.ID
      )
      if (userTeamsOnly != null && userTeamsOnly.length > 0) {
        userTeamsOnly.forEach((team) => {
          filteredMSTeamHierarchiesPerUser.push(team)

          // Get all the associated channel nodes for the team from passed hierarchies i.e from 'msTeamHierarchies'
          const teamChannelsOnly = finalSelectedMSTeamRowsData.filter(
            (item) => item.Head_ID === team.ID
          )

          if (teamChannelsOnly != null && teamChannelsOnly.length > 0) {
            teamChannelsOnly.forEach((channel) => {
              filteredMSTeamHierarchiesPerUser.push(channel)
            })
          }
        })
      }

      filteredMSTeamHierarchies.push(filteredMSTeamHierarchiesPerUser)
    })

    return filteredMSTeamHierarchies
  }

  onAddTeamChannelClicked(key, addSpecificNode = false) {
    this.finalSelectedMSTeamRowsData = []
    this.getSelectedData(key, addSpecificNode)

    this.finalSelectedMSTeamRowsData = this.finalSelectedMSTeamRowsData.filter(
      (element) => element.NodeType !== 'Root' && element.ID > 0
    )

    if (this.finalSelectedMSTeamRowsData.length > 0) {
      this.msTeamHierarchiesGroupedByUser =
        this.getMSTeamHierarchiesGroupedByUser(this.finalSelectedMSTeamRowsData)

      let confirmMessage = '<div class="repository-validation-message">'
      let promptConfirmDialog = false

      const commonFileFolderList = []
      this.msTeamHierarchiesGroupedByUser.forEach((specificUserHierarchies) => {
        const tempMSTeamRelativePathDetails = this.getRelativePath(
          specificUserHierarchies
        )

        const tempUser = specificUserHierarchies.find(
          (element) => element.NodeType == 'User'
        )

        const tempAddedFileList = this.addedFilesList.find(
          (x) => x.FileId === tempUser.ID
        )

        if (tempAddedFileList) {
          // Validates if file/folder(s) are already added for processing.
          const tempCommonFileFolderList = tempMSTeamRelativePathDetails.filter(
            (e) => {
              return tempAddedFileList.MSTeamRelativePathDetails.some(
                (item) => item.Id === e.Id
              )
            }
          )

          if (
            tempCommonFileFolderList != null &&
            tempCommonFileFolderList.length > 0
          ) {
            tempCommonFileFolderList.forEach((commonItem) => {
              commonFileFolderList.push(commonItem)
            })
            promptConfirmDialog = true
          }
        }
      })

      if (promptConfirmDialog) {
        if (commonFileFolderList != null && commonFileFolderList.length > 0) {
          confirmMessage = confirmMessage.concat(
            'Following selected MSTeam hierarchies have already been added for processing -<br>'
          )
          commonFileFolderList.forEach(function (x) {
            confirmMessage = confirmMessage.concat(
              '&nbsp; <i> - ' + x.Path + '</i> <br>'
            )
          })
          confirmMessage = confirmMessage.concat('<br>')
        }

        confirmMessage = confirmMessage.concat('</div>')
        confirmMessage = confirmMessage.concat('<br>Do you want to continue?')
        confirmMessage = confirmMessage.concat(
          "<br><br><p class='repository-note'> * Already added item(s) will not be added for processing.</p>"
        )

        this.showConfirmationModal(confirmMessage)
          .pipe(
            filter((yes) => yes),
            takeUntil(this.unsubscribed$)
          )
          .subscribe(() => {
            let tempFinalSelectedMSTeamRowsData = JSON.parse(
              JSON.stringify(this.finalSelectedMSTeamRowsData)
            )

            commonFileFolderList.forEach((commonItem) => {
              const userId = commonItem.UserId
              const teamId = commonItem.TeamId
              const channelId = commonItem.ChannelId

              // If only user is selected or entire user including its all children is selected
              if (
                userId.length > 0 &&
                teamId.length === 0 &&
                channelId.length === 0
              ) {
                tempFinalSelectedMSTeamRowsData =
                  tempFinalSelectedMSTeamRowsData.filter((item) => {
                    return item.ID !== userId
                  })
              } else if (
                userId.length > 0 &&
                teamId.length > 0 &&
                channelId.length === 0
              ) {
                tempFinalSelectedMSTeamRowsData =
                  tempFinalSelectedMSTeamRowsData.filter((item) => {
                    return item.ID !== teamId && item.ID !== userId
                  })
              } else if (
                userId.length > 0 &&
                teamId.length > 0 &&
                channelId.length > 0
              ) {
                tempFinalSelectedMSTeamRowsData =
                  tempFinalSelectedMSTeamRowsData.filter((item) => {
                    return (
                      item.ID !== channelId &&
                      item.ID !== teamId &&
                      item.ID !== userId
                    )
                  })
              }
            })

            const tempMSTeamHierarchiesGroupedByUser =
              this.getMSTeamHierarchiesGroupedByUser(
                tempFinalSelectedMSTeamRowsData
              )
            this.addMSTeamItems(tempMSTeamHierarchiesGroupedByUser)
            if (!addSpecificNode) this.treeList.instance.deselectAll()
          })
      } else {
        this.addMSTeamItems(this.msTeamHierarchiesGroupedByUser)
        if (!addSpecificNode) this.treeList.instance.deselectAll()
      }
    }

    // if (this.existingUserList.length > 0) {
    //   this.existingUserList = []
    //   this.toastr.showError(
    //     'One or more entries of the same user have already been added. To add multiple teams/channels of the user, please remove the existing entry of the user first.',
    //     true
    //   )
    // }
  }

  addMSTeamItems(msTeamHierarchiesGroupedByUser) {
    msTeamHierarchiesGroupedByUser.forEach((specificUserHierarchies) => {
      const tempFileArray: AddedFilesList[] = []

      const user = specificUserHierarchies.find(
        (element) => element.NodeType == 'User'
      )

      // adds unique data
      const fileInfo: AddedFilesList = {
        FileId: user.ID,
        FileName: user.UserName,
        FileSizeFormatted: null,
        FileSize: 0,
        CustodianName: user.UserName,
        IsStructured: false,
        FullName: user.UserName,
        Extension: '',
        Name: user.UserName,
        IsForensicImageMultipart: 'N',
        MSTeamRelativePathDetails: this.getRelativePath(
          specificUserHierarchies
        ),
        MSTeamRelativeId: this.generateUUIDv4(),
        MediaName: '',
        Password: ''
      }

      tempFileArray.push(fileInfo)

      if (this.addedFilesList) {
        this.addedFilesList = this.addedFilesList.concat(tempFileArray)
      } else {
        this.addedFilesList = tempFileArray
      }
    })
  }

  getRelativePath(specificUserHierarchies: any) {
    const MSTeamRelativePathDetailsList: any[] = []
    let MSTeamRelativePathDetails: any = {
      Id: '',
      Path: '',
      UserId: '',
      TeamId: '',
      ChannelId: ''
    }

    const user = specificUserHierarchies.find(
      (element) => element.NodeType === 'User'
    )

    const teams = specificUserHierarchies.filter(
      (element) => element.NodeType === 'Team'
    )

    const channels = specificUserHierarchies.filter(
      (element) => element.NodeType === 'Channel'
    )

    const userRelativeId = 'U-' + user.ID

    if (teams.length === 0 && channels.length === 0) {
      MSTeamRelativePathDetails = {
        Id: userRelativeId,
        Path: user.UserName,
        UserId: user.ID,
        TeamId: '',
        ChannelId: ''
      }
      MSTeamRelativePathDetailsList.push(MSTeamRelativePathDetails)
    } else if (teams.length > 0 && channels.length === 0) {
      const totalUserTeamsCount = this.mediaData.filter(
        (x) => x.Head_ID === user.ID
      ).length

      if (totalUserTeamsCount === teams.length) {
        MSTeamRelativePathDetails = {
          Id: userRelativeId,
          Path: user.UserName,
          UserId: user.ID,
          TeamId: '',
          ChannelId: ''
        }
        MSTeamRelativePathDetailsList.push(MSTeamRelativePathDetails)
      } else {
        const teamRelativeIdPrefix = userRelativeId.concat('-T')
        teams.forEach((team) => {
          const teamRelativeId = teamRelativeIdPrefix.concat('-' + team.ID)

          MSTeamRelativePathDetails = {
            Id: teamRelativeId,
            Path: user.UserName + '/' + team.UserName,
            UserId: user.ID,
            TeamId: team.ID,
            ChannelId: ''
          }
          MSTeamRelativePathDetailsList.push(MSTeamRelativePathDetails)
        })
      }
    } else {
      const totalUserTeamsCount = this.mediaData.filter(
        (x) => x.Head_ID === user.ID
      ).length

      if (
        totalUserTeamsCount === teams.length &&
        this.checkIfTotalChannelsCountMatchWithSelectedForEachTeam(
          teams,
          channels
        )
      ) {
        MSTeamRelativePathDetails = {
          Id: userRelativeId,
          Path: user.UserName,
          UserId: user.ID,
          TeamId: '',
          ChannelId: ''
        }
        MSTeamRelativePathDetailsList.push(MSTeamRelativePathDetails)
      } else {
        const teamRelativeIdPrefix = userRelativeId.concat('-T')
        teams.forEach((team) => {
          const selectedTeamAssociatedChannelsCount = channels.filter(
            (x) => x.Head_ID === team.ID
          ).length

          const teamRelativeId = teamRelativeIdPrefix.concat('-' + team.ID)
          if (selectedTeamAssociatedChannelsCount === 0) {
            MSTeamRelativePathDetails = {
              Id: teamRelativeId,
              Path: user.UserName + '/' + team.UserName,
              UserId: user.ID,
              TeamId: team.ID,
              ChannelId: ''
            }
            MSTeamRelativePathDetailsList.push(MSTeamRelativePathDetails)
          } else {
            const totalTeamChannelsCount = this.mediaData.filter(
              (y) => y.Head_ID === team.ID && y.ID > 0
            ).length

            if (
              totalTeamChannelsCount === selectedTeamAssociatedChannelsCount
            ) {
              MSTeamRelativePathDetails = {
                Id: teamRelativeId,
                Path: user.UserName + '/' + team.UserName,
                UserId: user.ID,
                TeamId: team.ID,
                ChannelId: ''
              }
              MSTeamRelativePathDetailsList.push(MSTeamRelativePathDetails)
            } else {
              const teamChannels = channels.filter(
                (y) => y.Head_ID === team.ID && y.ID > 0
              )

              const channelRelativeIdPrefix = teamRelativeId.concat('-C')
              teamChannels.forEach((channel) => {
                const channelRelativeId = channelRelativeIdPrefix.concat(
                  '-' + channel.ID
                )
                MSTeamRelativePathDetails = {
                  Id: channelRelativeId,
                  Path:
                    user.UserName +
                    '/' +
                    team.UserName +
                    '/' +
                    channel.UserName,
                  UserId: user.ID,
                  TeamId: team.ID,
                  ChannelId: channel.ID
                }
                MSTeamRelativePathDetailsList.push(MSTeamRelativePathDetails)
              })
            }
          }
        })
      }
    }

    return MSTeamRelativePathDetailsList
  }

  checkIfTotalChannelsCountMatchWithSelectedForEachTeam(
    teams: any,
    channels: any
  ): boolean {
    let isMatched = true
    teams.forEach((team) => {
      const totalTeamChannelsCount = this.mediaData.filter(
        (y) => y.Head_ID === team.ID && y.ID > 0
      ).length

      const selectedTeamAssociatedChannelsCount = channels.filter(
        (x) => x.Head_ID === team.ID
      ).length

      if (totalTeamChannelsCount !== selectedTeamAssociatedChannelsCount)
        isMatched = false
    })
    return isMatched
  }

  getMSTeamAddedItemTooltipText(MSTeamRelativePathDetails: any): string {
    let tooltipText = ''
    if (MSTeamRelativePathDetails != null) {
      MSTeamRelativePathDetails.forEach((pathDetails) => {
        const path = pathDetails.Path
        tooltipText = tooltipText.concat(`${path}` + '\n')
      })
    }

    return tooltipText
  }

  readonly trackByFn = (index, item) => item.id

  readonly trackByCustodianFn = (index, item) => item.created_date

  //msteam selected datat

  getSelectedData(key, addSpecificNode) {
    //stores node selected in UI
    this.primaryNodeList = []
    //stores node traversing to its higher node
    this.secondaryNodeList = []
    this.selectedMSTeamRowsData = []
    if (!addSpecificNode) {
      this.primaryNodeList = this.treeList.instance.getSelectedRowsData('all') //gets parents only if all the children are selected.
    } else {
      this.primaryNodeList.push(this.treeList.instance.getNodeByKey(key).data)
    }
    if (this.primaryNodeList.length > 0) {
      this.setSelectedRowData()
      this.selectedMSTeamRowsData.sort((a, b) => a.ID - b.ID)
      //removes current selection if already exists in addedfilesList
      //this.checkforUniqueUserSelection()
      if (this.finalSelectedMSTeamRowsData.length > 0) {
        this.finalSelectedMSTeamRowsData =
          this.finalSelectedMSTeamRowsData.concat(this.selectedMSTeamRowsData)
      } else {
        this.finalSelectedMSTeamRowsData = this.selectedMSTeamRowsData
      }
      this.finalSelectedMSTeamRowsData = this.getUniqueListById(
        this.finalSelectedMSTeamRowsData
      )
      this.finalSelectedMSTeamRowsData.sort((a, b) => a.ID - b.ID)
    }
  }

  checkforUniqueUserSelection() {
    const currentUserList = this.selectedMSTeamRowsData.filter(
      (x) => x.NodeType == 'User'
    )
    currentUserList.forEach((x) => {
      if (
        this.addedFilesList &&
        this.addedFilesList.some((element) => element.FileId == x.ID)
      ) {
        this.existingUserList.push(x)
        this.selectedMSTeamRowsData = this.selectedMSTeamRowsData.filter(
          (selDt) => selDt.ID != x.ID
        ) //removes user
        const invalidTeamList = this.selectedMSTeamRowsData.filter(
          (selDt) => selDt.Head_ID == x.ID
        ) //gets invalid teamlist
        this.selectedMSTeamRowsData = this.selectedMSTeamRowsData.filter(
          (selDt) => selDt.Head_ID != x.ID
        ) //removes invalid team
        invalidTeamList.forEach((tmDt) => {
          this.selectedMSTeamRowsData = this.selectedMSTeamRowsData.filter(
            (selDt) => selDt.Head_ID != tmDt.ID
          ) //removes invalid channel
        })
      }
    })
  }

  setSelectedRowData() {
    //gets unique list based on id
    this.primaryNodeList = this.getUniqueListById(this.primaryNodeList)
    this.primaryNodeList.forEach((x) => {
      //eliminate nodes from the list if already contains
      if (x.Head_ID > 0 && this.checkForDuplicateId(x.Head_ID)) {
        this.recursivelySelectNodes(x.Head_ID)
      }
    })
    this.selectedMSTeamRowsData = [
      ...this.primaryNodeList,
      ...this.secondaryNodeList
    ]
    this.selectedMSTeamRowsData = this.getUniqueListById(
      this.selectedMSTeamRowsData
    )
  }

  checkForDuplicateId(pId) {
    if (
      this.primaryNodeList.some((x) => x.ID == pId) ||
      this.secondaryNodeList.some((x) => x.ID == pId)
    ) {
      return false //already contains
    } else return true //unique
  }

  recursivelySelectNodes(idItem) {
    const tempNode = this.treeList.instance.getNodeByKey(idItem).data
    const tempParentId = tempNode.Head_ID
    //stores selected node by traversing.
    this.secondaryNodeList.push(tempNode)
    this.secondaryNodeList = this.getUniqueListById(this.secondaryNodeList)
    if (tempParentId > 0 && this.checkForDuplicateId(tempParentId)) {
      this.recursivelySelectNodes(tempParentId)
    }
  }

  getUniqueListById(arr) {
    const ids = arr.map((o) => o.ID)
    const filtered = arr.filter(({ ID }, index) => !ids.includes(ID, index + 1))
    return filtered
  }

  // Get the list of selected node
  onRepositorySelectionChanged(e) {
    if (e.addedItems.length > 0) {
      e.addedItems.forEach((item) => {
        if (!this.selectedRepository.includes(item.fsid)) {
          this.selectedRepository.push(item.fsid)
        }
      })
    } else if (e.removedItems.length > 0) {
      e.removedItems.forEach((item) => {
        const index: number = this.selectedRepository.indexOf(item.fsid, 0)
        if (index > -1) {
          this.selectedRepository.splice(index, 1)
        }
      })
    }
  }

  onRepositoryHierarchySelectionChanged(e: any) {
    this.selectedRowsData =
      this.treeListRepository.instance.getSelectedRowsData('all')
    this.checkIfParentFolderSelected()
  }

  checkIfParentFolderSelected() {
    const parentId = []
    this.selectedRowsData.forEach((item, index) => {
      if (item.type === 'FOLDER') {
        parentId.push(item.id)
      }
    })
    const tmpData = this.selectedRowsData

    // for structured upload, we're only selecting folder and supported file formats
    if (this.selectedSourceType === SourceType.Structured) {
      const supportedFormats = this.getSupportedFileFormat()
      const filePattern = new RegExp(`(${supportedFormats})$`, 'i')
      this.selectedRowsData = this.selectedRowsData.filter(
        (r) =>
          r.type === 'FOLDER' || (r.type === 'FILE' && filePattern.test(r.name))
      )
    } else {
      this.selectedRowsData = tmpData.filter(function (el) {
        return parentId.indexOf(el.parentId) < 0
      })
    }
  }

  onRepositoryHierarchyRowExpanding(key, drillType) {
    const selectedNode = this.repositoryHierarchyList.find((x) => x.id == key)
    if (
      (drillType === 0 && !selectedNode.isLoaded) ||
      (drillType === 1 && !selectedNode.isLoadedAll)
    ) {
      if (selectedNode.type == 'FOLDER') {
        const dummyChildNode = this.repositoryHierarchyList.find(
          (x) => x.fsid === '0' && x.parentId === key && x.type === 'Dummy'
        )
        if (dummyChildNode != null) {
          this.repositoryHierarchyList.splice(
            this.repositoryHierarchyList.findIndex(
              (x) => x.fsid === '0' && x.parentId === key && x.type === 'Dummy'
            ),
            1
          )
          this.removedRepostioryDummyNodes.push(dummyChildNode)
        }

        this.startExpand(selectedNode, drillType)
      }
    }
  }

  removeAllRepositoryHierarchyNodesRecursively(removableHierarchyNodes) {
    if (removableHierarchyNodes.length > 0) {
      removableHierarchyNodes.forEach((x) => {
        const index = this.repositoryHierarchyList.findIndex(
          (o) => o.id == x.id
        )
        this.repositoryHierarchyList.splice(index, 1)

        const removableHierarchyChildNodes =
          this.repositoryHierarchyList.filter((y) => {
            return y.parentId === x.id
          })

        this.removeAllRepositoryHierarchyNodesRecursively(
          removableHierarchyChildNodes
        )
      })
    }
  }

  startExpand(selectedNode, drillType) {
    this.repositoryHierarchyList = this.repositoryHierarchyList.map((obj) =>
      obj.id === selectedNode.id
        ? {
            ...obj,
            isLoading: true,
            isLoaded: selectedNode.isLoaded,
            isLoadedAll: false,
            isExpanded: false,
            showNodeMenuOptions: false
          }
        : obj
    )

    this.expandRepositoryHierarchyNode(selectedNode.id, drillType)
  }

  expandRepositoryHierarchyNode(key, drillType) {
    //gets repository hierarchy info to check if hierarchy row not expanded eariler
    const selectedRepostioryNodeInfo = this.repositoryHierarchyList.find(
      (x) => x.id == key
    )

    const selectedSocialMedia =
      this.selectedSocialMedia === '' ? 'none' : this.selectedSocialMedia

    const drillRepositoryHierarchyRequest = this.xsStore
      .dispatch(
        new UActions.DrillRepositoryHierarchy(
          0,
          selectedRepostioryNodeInfo,
          this.selectedSourceType === SourceType.Structured,
          this.selectedSourceType === SourceType.Transcript,
          selectedSocialMedia,
          drillType
        )
      )
      .pipe(
        switchMap(() =>
          this.xsStore.selectOnce(UploadStateSelector.drillRepositoryHierarchy)
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((response: RepositoryHierarchyModel[]) => {
        // remove the request object from request array
        this.triggerDrillRepositoryHierarchy.splice(
          this.triggerDrillRepositoryHierarchy.findIndex((x) => x.id === key),
          1
        )
        if (!this.expandedRepositoryRowsKeyList.includes(key)) {
          this.expandedRepositoryRowsKeyList.push(key)
        }

        this.tempHierarchyIds = []
        this.tempHierarchyIds = response
          .filter(function (a) {
            return a.type === 'FOLDER'
          })
          .map(function (a) {
            return a.id
          })

        this.populateRepositoryHierarchy(response, key, drillType)
      })

    const drillRepositoryHierarchyRequestObj = {
      id: key,
      request: drillRepositoryHierarchyRequest
    }
    this.triggerDrillRepositoryHierarchy.push(
      drillRepositoryHierarchyRequestObj
    )
  }

  populateRepositoryHierarchy(response, key, drillType) {
    this.tempRepositoryHierarchyData = [
      ...this.repositoryHierarchyList,
      ...response
    ]

    if (drillType === 0) {
      const dummyRepositoryNodeData = []
      this.tempRepositoryHierarchyData.forEach((x) => {
        if (x.parentId === key && x.type === 'FOLDER') {
          const dummyId = Math.random().toString(36).substring(7)

          const dummyNode = {
            fsid: '0',
            name: '',
            id: dummyId.toString(),
            parentId: x.id,
            type: 'Dummy',
            repositoryDisplayName: 'Dummy',
            repositoryRootFolderName: 'Dummy',
            relativePath: 'Dummy',
            isLoading: false,
            isLoaded: false,
            isLoadedAll: false,
            isCancelling: false,
            showNodeMenuOptions: false,
            isExpanded: false
          }

          dummyRepositoryNodeData.push(dummyNode)
        }
      })

      if (dummyRepositoryNodeData.length > 0) {
        this.tempRepositoryHierarchyData = [
          ...this.tempRepositoryHierarchyData,
          ...dummyRepositoryNodeData
        ]
      }
    } else {
      const selectedNode = this.tempRepositoryHierarchyData.find(
        (x) => x.id === key
      )
      this.removeRepositoryDummyNodesRecursively(selectedNode)
    }

    this.tempRepositoryHierarchyData = this.tempRepositoryHierarchyData.map(
      (obj) =>
        obj.id === key || this.tempHierarchyIds.includes(obj.id)
          ? {
              ...obj,
              isLoading: false,
              isLoaded:
                obj.id === key && drillType === 0 ? true : drillType === 1,
              isLoadedAll: this.isLoadedEntireRepositoryHierarchy(
                response,
                drillType
              ),
              isExpanded:
                obj.id === key && drillType === 0 ? true : drillType === 1,
              showNodeMenuOptions: false
            }
          : obj
    )

    this.tempRepositoryHierarchyData = _.uniqBy(
      this.tempRepositoryHierarchyData,
      'id'
    )

    this.repositoryHierarchyList = this.tempRepositoryHierarchyData

    if (this.expandedRepositoryRowsKeyList.length > 0) {
      this.expandedRepositoryRowsKeyList.forEach((x) => {
        if (!this.treeListRepository.instance.isRowExpanded(x) && x === key) {
          this.treeListRepository.instance.expandRow(x)
        }
      })
    }
  }

  isLoadedEntireRepositoryHierarchy(response, drillType) {
    // If drillType = 1, then all the childs will be loaded,
    // so isLoadedAll will be set to true for all childs
    if (drillType === 1) return true
    else {
      const containsFolder =
        response.filter((x) => x.type === 'FOLDER').length > 0
      // If drillType = 0 and expanded node has no folders
      // then set isLoadedAll flag to true
      if (!containsFolder) return true
      else return false
    }
  }

  removeRepositoryDummyNodesRecursively(selectedNode) {
    const childNodes = this.tempRepositoryHierarchyData.filter(
      (x) => x.parentId === selectedNode.id && x.type === 'FOLDER'
    )

    if (childNodes != null && childNodes.length > 0) {
      childNodes.forEach((childNode) => {
        if (!childNode.isLoaded) {
          const dummyChildNode = this.tempRepositoryHierarchyData.find(
            (x) =>
              x.fsid === '0' &&
              x.parentId === childNode.id &&
              x.type === 'Dummy'
          )
          if (dummyChildNode != null) {
            this.tempRepositoryHierarchyData.splice(
              this.tempRepositoryHierarchyData.findIndex(
                (x) =>
                  x.fsid === '0' &&
                  x.parentId === childNode.id &&
                  x.type === 'Dummy'
              ),
              1
            )
            this.removedRepostioryDummyNodes.push(dummyChildNode)
          }

          this.removeRepositoryDummyNodesRecursively(childNode)
        }
      })
    }
  }

  toggleRow(key: string, expandFlag: boolean) {
    const toggledNode = this.repositoryHierarchyList.find((x) => x.id === key)
    this.expandCollapseAllNodesRecursively(toggledNode, expandFlag)
  }

  expandCollapseAllNodesRecursively(parentNode, expandFlag: boolean) {
    const key = parentNode.id

    if (parentNode.type === 'FOLDER') {
      if (expandFlag) {
        if (!this.treeListRepository.instance.isRowExpanded(key)) {
          this.treeListRepository.instance.expandRow(key)
        }
        parentNode.isExpanded = true
      } else {
        if (this.treeListRepository.instance.isRowExpanded(key)) {
          this.treeListRepository.instance.collapseRow(key)
        }
        parentNode.isExpanded = false
      }

      const childNodes = this.repositoryHierarchyList.filter((y) => {
        return y.parentId === key
      })

      if (childNodes.length > 0) {
        childNodes.forEach((x) => {
          this.expandCollapseAllNodesRecursively(x, expandFlag)
        })
      }
    }
  }

  cancelRequest(key: string) {
    // after successful unsubscription, set isLoading flag to false
    const selectedNode = this.repositoryHierarchyList.find((x) => x.id === key)
    selectedNode.isLoading = false
    selectedNode.isCancelling = true

    //unsubscribe the request sent for cancellation
    const cancelledReq = this.triggerDrillRepositoryHierarchy.find(
      (x) => x.id === key
    )
    cancelledReq.request.unsubscribe()

    // remove the unsubscribed request object from request array
    this.triggerDrillRepositoryHierarchy.splice(
      this.triggerDrillRepositoryHierarchy.findIndex((x) => x.id === key),
      1
    )

    //if(this.treeListRepository.instance.isRowExpanded(key)) this.treeListRepository.instance.collapseRow(key)
    const dummyChildNode = this.removedRepostioryDummyNodes.find(
      (x) => x.fsid === '0' && x.parentId === key && x.type === 'Dummy'
    )
    if (dummyChildNode != null)
      this.repositoryHierarchyList.push(dummyChildNode)

    selectedNode.isCancelling = false
  }

  toggleMSTeamRow(key: number, expandFlag: boolean) {
    const toggledNode = this.mediaData.find((x) => x.ID === key)
    this.expandCollapseAllMSTeamNodesRecursively(toggledNode, expandFlag)
  }

  expandCollapseAllMSTeamNodesRecursively(parentNode, expandFlag: boolean) {
    const key = parentNode.ID

    const childNodes = this.mediaData.filter((y) => {
      return y.Head_ID === key
    })

    if (expandFlag) {
      if (!this.treeList.instance.isRowExpanded(key)) {
        this.treeList.instance.expandRow(key)
      }
      if (childNodes.length > 0) parentNode.IsExpanded = true
      else parentNode.IsExpanded = false
    } else {
      if (this.treeList.instance.isRowExpanded(key)) {
        this.treeList.instance.collapseRow(key)
      }

      parentNode.IsExpanded = false
    }

    if (childNodes.length > 0) {
      childNodes.forEach((x) => {
        this.expandCollapseAllMSTeamNodesRecursively(x, expandFlag)
      })
    }
  }

  cancelMSTeamRequest(key: number) {
    // after successful unsubscription, set isLoading flag to false
    const selectedNode = this.mediaData.find((x) => x.ID === key)
    selectedNode.IsLoading = false
    selectedNode.IsCancelling = true

    //unsubscribe the request sent for cancellation
    const cancelledReq = this.triggerDrillMSTeamHierarchy.find(
      (x) => x.id === key
    )
    cancelledReq.request.unsubscribe()

    // remove the unsubscribed request object from request array
    this.triggerDrillMSTeamHierarchy.splice(
      this.triggerDrillMSTeamHierarchy.findIndex((x) => x.id === key),
      1
    )

    if (this.treeList.instance.isRowExpanded(key))
      this.treeList.instance.collapseRow(key)
    const dummyChildNode = this.removedMSTeamDummyNodes.find(
      (x) => x.ID < 0 && x.Head_ID === key && x.NodeType === 'Dummy'
    )
    if (dummyChildNode != null) this.mediaData.push(dummyChildNode)

    selectedNode.IsCancelling = false
  }

  onAddingSpecificRepositoryItem(file: any) {
    const repositoryNode = this.treeListRepository.instance.getNodeByKey(
      file.id
    )
    const specificRowData = []
    specificRowData.push(repositoryNode.data)
    this.onAddingRepositoryItems(specificRowData)
  }

  /*
  Collect all parent folders for selected file or folder
  */
  collectAllParentRepositoryItemsRecursively(
    selectedItem,
    filteredHierarchyByFolders
  ) {
    const parentItem = filteredHierarchyByFolders.find(
      (x) => x.id === selectedItem.parentId
    )
    if (parentItem != null) {
      this.parentRepositoryOrFolders.push(parentItem)
      this.collectAllParentRepositoryItemsRecursively(
        parentItem,
        filteredHierarchyByFolders
      )
    }
  }

  /*
  Collect all child files or folders for selected file or folder
  */
  collectAllChildRepositoryItemsRecursively(
    selectedItem,
    filteredHierarchyByFSID
  ) {
    const childItems = filteredHierarchyByFSID.filter(
      (x) => x.parentId === selectedItem.id
    )

    if (childItems != null && childItems.length > 0) {
      childItems.forEach((childItem) => {
        if (childItem.type !== 'Dummy')
          this.childRepositoryOrFolders.push(childItem)
        if (childItem.type === 'FOLDER') {
          this.collectAllChildRepositoryItemsRecursively(
            childItem,
            filteredHierarchyByFSID
          )
        }
      })
    }
  }

  // Add the items from repository tree list
  onAddingRepositoryItems(specificRowData = null) {
    const selectedRows =
      specificRowData != null && specificRowData.length === 1
        ? specificRowData
        : this.selectedRowsData

    let confirmMessage = '<div class="repository-validation-message">'
    let promptConfirmDialog = false

    // Validates if file/folder(s) are already added for processing.
    const commonFileFolderList = selectedRows.filter((e) => {
      return this.repositoryFilesList.some((item) => item.Id === e.id)
    })
    if (commonFileFolderList != null && commonFileFolderList.length > 0) {
      promptConfirmDialog = true
      confirmMessage = confirmMessage.concat(
        'Following selected repository/file/folder(s) have already been added for processing -<br>'
      )
      commonFileFolderList.forEach(function (x) {
        const path =
          x.parentId === '-1'
            ? x.repositoryRootFolderName
            : x.repositoryRootFolderName + x.relativePath
        confirmMessage = confirmMessage.concat(
          '&nbsp; <i> - ' + path + '</i> <br>'
        )
      })
      confirmMessage = confirmMessage.concat('<br>')
    }

    this.parentRepositoryItemsMapper = []
    this.childRepositoryItemsMapper = []
    // Validates if parent folder is already added in case of trying to add child file or folder.
    if (
      this.repositoryFilesList != null &&
      this.repositoryFilesList.length > 0
    ) {
      selectedRows.forEach((selectedItem) => {
        // Filter by fsid
        const filteredHierarchyByFSID = this.repositoryHierarchyList.filter(
          (x) => {
            return x.fsid === selectedItem.fsid
          }
        )
        // List out all the parent folders for selected file/folders
        const filteredHierarchyByFolders = filteredHierarchyByFSID.filter(
          (x) => {
            return x.type === 'FOLDER'
          }
        )

        this.collectAllParentRepositoryItemsRecursively(
          selectedItem,
          filteredHierarchyByFolders
        )
        const parentRepositoryItemMapper = {
          key: selectedItem,
          value: this.parentRepositoryOrFolders
        }
        this.parentRepositoryItemsMapper.push(parentRepositoryItemMapper)

        this.collectAllChildRepositoryItemsRecursively(
          selectedItem,
          filteredHierarchyByFSID
        )
        const childRepositoryItemMapper = {
          key: selectedItem,
          value: this.childRepositoryOrFolders
        }
        this.childRepositoryItemsMapper.push(childRepositoryItemMapper)

        this.parentRepositoryOrFolders = []
        this.childRepositoryOrFolders = []
      })

      let intermediateParentRepositoryOrFolders = []
      this.parentRepositoryItemsMapper.forEach((item) => {
        intermediateParentRepositoryOrFolders = [
          ...intermediateParentRepositoryOrFolders,
          ...item.value
        ]
      })

      intermediateParentRepositoryOrFolders = _.uniqBy(
        intermediateParentRepositoryOrFolders,
        'id'
      )

      const commonParentFolderList =
        this.selectedSourceType === SourceType.Structured // to disable warning for structured source related to parent already being added for processing
          ? []
          : intermediateParentRepositoryOrFolders.filter((e) => {
              return this.repositoryFilesList.some((item) => item.Id === e.id)
            })

      if (commonParentFolderList.length > 0) {
        promptConfirmDialog = true
        confirmMessage = confirmMessage.concat(
          'Parent folder of the following selected file/folder(s) have already been added for processing -<br>'
        )

        const tempRepositoryFileList = this.repositoryFilesList
        this.parentRepositoryItemsMapper.forEach(function (x) {
          if (x.value.length > 0) {
            const commonParentRepositoryList = x.value.filter((e) => {
              return tempRepositoryFileList.some((item) => item.Id === e.id)
            })

            if (commonParentRepositoryList.length > 0) {
              const path =
                x.key.repositoryRootFolderName +
                '\\' +
                x.key.relativePath.slice(1)
              confirmMessage = confirmMessage.concat(
                '&nbsp; <i> - ' + path + '</i> <br>'
              )
            }
          }
        })
        confirmMessage = confirmMessage.concat('<br>')
      }

      if (this.selectedSourceType !== SourceType.Structured) {
        // to disable warning for structured source related to child already being added for processing)
        let intermediateChildRepositoryOrFolders = []
        this.childRepositoryItemsMapper.forEach((item) => {
          intermediateChildRepositoryOrFolders = [
            ...intermediateChildRepositoryOrFolders,
            ...item.value
          ]
        })

        intermediateChildRepositoryOrFolders = _.uniqBy(
          intermediateChildRepositoryOrFolders,
          'id'
        )
        const commonChildFileFolderList =
          intermediateChildRepositoryOrFolders.filter((e) => {
            return this.repositoryFilesList.some((item) => item.Id === e.id)
          })
        if (commonChildFileFolderList.length > 0) {
          promptConfirmDialog = true
          confirmMessage = confirmMessage.concat(
            'The child file/folder(s) of the following selected folder(s) have already been added for processing. The child file/folder(s) will be removed from processing list. -<br>'
          )
          this.childRepositoryItemsMapper.forEach(function (x) {
            if (x.value.length > 0) {
              const path =
                x.key.parentId === '-1'
                  ? x.key.repositoryRootFolderName
                  : x.key.repositoryRootFolderName +
                    '\\' +
                    x.key.relativePath.slice(1)
              confirmMessage = confirmMessage.concat(
                '&nbsp; <i> - ' + path + '</i> <br>'
              )
            }
          })
          confirmMessage = confirmMessage.concat('<br>')
        }
      }
    }

    const selectedRootRepositoryList = selectedRows.filter((x) => {
      return x.parentId === '-1'
    })

    // Show confirmation if all the childs need to be processed if entire repository is selected for processing
    if (
      selectedRootRepositoryList != null &&
      selectedRootRepositoryList.length > 0
    ) {
      const addedRootRepositoryList = this.repositoryFilesList.filter((x) => {
        return x.ParentId === '-1'
      })

      const differenceRootRepositoryList = selectedRootRepositoryList.filter(
        (e) => {
          return !addedRootRepositoryList.some((item) => item.Id === e.id)
        }
      )

      if (differenceRootRepositoryList.length > 0) {
        promptConfirmDialog = true
        confirmMessage = confirmMessage.concat(
          'All the file/folder(s) will be processed for following selected repositories -<br>'
        )
        differenceRootRepositoryList.forEach(function (x) {
          confirmMessage = confirmMessage.concat(
            '&nbsp; <i> - ' + x.repositoryRootFolderName + '</i> <br>'
          )
        })
        confirmMessage = confirmMessage.concat('<br>')
      }
    }

    if (promptConfirmDialog) {
      confirmMessage = confirmMessage.concat('</div>')
      confirmMessage = confirmMessage.concat('<br>Do you want to continue?')
      confirmMessage = confirmMessage.concat(
        "<br><br><p class='repository-note'> * Already added file/folder(s) will not be added for processing.</p>"
      )

      this.showConfirmationModal(confirmMessage)
        .pipe(
          filter((yes) => yes),
          takeUntil(this.unsubscribed$)
        )
        .subscribe(() => {
          this.addRepositoryItems(selectedRows, specificRowData !== null)
        })
    } else {
      this.addRepositoryItems(selectedRows, specificRowData !== null)
    }
  }

  async addRepositoryItems(selectedRows, addSpecificRowData) {
    const tempRepositoryFileArray: RepositoryFilesList[] = []
    this.isCustodianNameVisible = true

    // Validate all selected rows for structured upload before adding to the list
    const validItems =
      this.selectedSourceType === SourceType.Structured
        ? await this.getValidSelectionForImport(selectedRows)
        : selectedRows

    validItems.forEach((file) => {
      const guid = Math.floor(Math.random() * 100000000 + 1)
      const repositoryFileInfo: RepositoryFilesList = {
        FSID: file.fsid,
        Id: file.id,
        ParentId: file.parentId,
        Type: file.type,
        Name: file.name,
        RepositoryDisplayName: file.repositoryDisplayName,
        RepositoryRootFolderName: file.repositoryRootFolderName,
        CustodianName: null,
        RelativePath: file.parentId === '-1' ? file.name : file.relativePath,
        IsCustodianNameVisible: this.isCustodianNameVisible,
        GUID: guid,
        MediaName: null,
        Password: ''
      }
      if (this.repositoryFilesList.length > 0) {
        const rIndex = this.repositoryFilesList.findIndex(
          (f) => f.Id === file.id
        )
        if (!(rIndex > -1)) {
          // for structured upload we are going to add child directories as a separate selection rather than just selecting common parent since each of those folders represent different import
          if (this.selectedSourceType === SourceType.Structured) {
            tempRepositoryFileArray.push(repositoryFileInfo)
          } else {
            //Get common parent folders between folders that are already added for processing and folders that are going to be added
            // so that we can ignore adding those commonn folders in processing list
            const intermediateParentRepositoryOrFolders =
              this.parentRepositoryItemsMapper.find(
                (x) => x.key.id === file.id
              ).value
            const commonParentFolderList =
              intermediateParentRepositoryOrFolders.filter((e) => {
                return this.repositoryFilesList.some((item) => item.Id === e.id)
              })

            //Get common child file/folders between file/folders that are already added for processing and file/folders that are going to be added
            // so that we can ignore adding those commonn file/folders in processing list
            const intermediateChildRepositoryOrFolders =
              this.childRepositoryItemsMapper.find(
                (x) => x.key.id === file.id
              ).value
            const commonChildFileFolderList =
              intermediateChildRepositoryOrFolders.filter((e) => {
                return this.repositoryFilesList.some((item) => item.Id === e.id)
              })

            if (
              !(
                commonParentFolderList != null &&
                commonParentFolderList.length > 0
              ) &&
              !(
                commonChildFileFolderList != null &&
                commonChildFileFolderList.length > 0
              )
            ) {
              tempRepositoryFileArray.push(repositoryFileInfo)
              this.isCustodianNameVisible = false
            }

            // If user is trying to add parent folder whose child file/folders have already been added
            // Then remove all those childs and add parent folder for processing
            if (
              commonChildFileFolderList != null &&
              commonChildFileFolderList.length > 0
            ) {
              commonChildFileFolderList.forEach((item) => {
                this.repositoryFilesList.splice(
                  this.repositoryFilesList.findIndex((x) => x.Id === item.id),
                  1
                )
              })

              tempRepositoryFileArray.push(repositoryFileInfo)
            }
          }
        }
      } else {
        tempRepositoryFileArray.push(repositoryFileInfo)
        this.isCustodianNameVisible = false
      }
    })

    if (this.repositoryFilesList) {
      this.repositoryFilesList = _.union(
        this.repositoryFilesList,
        tempRepositoryFileArray
      )
    } else {
      this.repositoryFilesList = tempRepositoryFileArray
    }
    if (!addSpecificRowData) this.treeListRepository.instance.clearSelection()
  }

  /**
   * gets list of valid directory with load files from API and also adds supported file types to the list
   * @param selectedRows selected rows
   * @returns list of supported files and directories for import
   */
  private getValidSelectionForImport(
    selectedRows: any[]
  ): Promise<RepositoryHierarchyModel[]> {
    return new Promise((resolve, reject) => {
      this.repositoryHierarchyLoader = true
      this.repositoryHierarchyLoadingMessage = 'Verifying selected items...'

      // check if selected files are of supported format
      const supportedFormats = this.getSupportedFileFormat()
      const filePattern = new RegExp(`(${supportedFormats})$`, 'i')
      let supportedItems = selectedRows.filter(
        (r) => r.type === 'FILE' && filePattern.test(r.name)
      )

      // check if only files were selected. if so skip calling API.
      const selectedIds = selectedRows
        .filter((x) => x.type === 'FOLDER')
        .map((x) => x.id)
      const selectedNodes = this.repositoryHierarchyList.filter((x) =>
        selectedIds.includes(x.id)
      )
      if (selectedNodes.length === 0) {
        resolve(supportedItems)
        this.repositoryHierarchyLoader = false
        return
      }

      // check if folder has load file using API
      const requestModel: GetValidDirectoriesForImportRequestModel = {
        repositoryHierarchies: selectedNodes
      }

      this.xsStore
        .dispatch(new UActions.GetValidRepoItemsForImport(0, requestModel))
        .pipe(
          switchMap(() =>
            this.xsStore.selectOnce(
              UploadStateSelector.getValidRepoItemsForImport
            )
          ),
          takeUntil(this.unsubscribed$)
        )
        .subscribe(
          (response: RepositoryHierarchyModel[]) => {
            const alreadySelectedItemIds = this.repositoryFilesList.map(
              (x) => x.Id
            )
            const idOfLoadFileDirectories = response.map((x) => x.id)
            supportedItems = supportedItems.filter(
              (r) =>
                !alreadySelectedItemIds.includes(r.id) && // filtering out already added items
                !idOfLoadFileDirectories.includes(r.parentId) // filtering out supported files if it is present in directory with load file. it needs to be selected separately.
            )
            const listOfValidItems =
              response?.length > 0
                ? supportedItems.concat(response)
                : supportedItems
            this.repositoryHierarchyLoader = false
            resolve(listOfValidItems)
          },
          (error) => {
            console.error(error)
            this.repositoryHierarchyLoader = false
            resolve([])
          }
        )
    })
  }

  groupRepositoryFilesByCustodian() {
    if (!(this.repositoryFilesList.length > 0)) {
      this.queuedRepositoryModel = []
      return
    }
    this.custodianNameRequired = false
    this.mediaNameRequired = false
    const grouped = _.groupBy(this.repositoryFilesList, (item) => item.GUID)
    for (const item in grouped) {
      const tempRepositoryFiles: RepositoryFilesList[] = []
      if (grouped[item].length > 1) {
        this.custodianNameRequired = true
        this.mediaNameRequired = true
      }
      let itemCountWithCustodianName = 0
      let itemCountWithMediaName = 0
      grouped[item].forEach((file) => {
        if (!file.MediaName) {
          file.MediaName =
            grouped[item][0].GUID == file.GUID
              ? grouped[item][0].MediaName
              : null
        }
        const repositoryFileInfo: RepositoryFilesList = {
          FSID: file.FSID,
          Id: file.Id,
          ParentId: file.ParentId,
          Type: file.Type,
          Name: file.Name,
          RepositoryDisplayName: file.RepositoryDisplayName,
          RepositoryRootFolderName: file.RepositoryRootFolderName,
          CustodianName: file.CustodianName,
          RelativePath: file.RelativePath,
          IsCustodianNameVisible: file.IsCustodianNameVisible,
          GUID: file.GUID,
          MediaName: file.MediaName,
          Password: file.Password
        }
        if (file.CustodianName) {
          itemCountWithCustodianName = itemCountWithCustodianName + 1
        }
        if (file.MediaName) {
          itemCountWithMediaName = itemCountWithMediaName + 1
        }
        tempRepositoryFiles.push(repositoryFileInfo)
      })

      if (
        grouped[item].length > 1 &&
        itemCountWithCustodianName !== grouped[item].length
      )
        this.custodianNameRequired = true
      else this.custodianNameRequired = false

      if (
        grouped[item].length > 1 &&
        itemCountWithMediaName !== grouped[item].length
      )
        this.mediaNameRequired = true
      else this.mediaNameRequired = false

      const repositoryFiles: QueuedRepositoryModel = {
        CustodianName:
          grouped[item].length > 0 ? tempRepositoryFiles[0].CustodianName : '',
        MediaName:
          grouped[item].length > 0 ? tempRepositoryFiles[0].MediaName : '',
        RepositoryHierarchies: tempRepositoryFiles,
        MediaSourceType: this.selectedMediaSourceType
      }
      if (this.custodianNameRequired) {
        this.toastr.showError('Please provide custodian name', true)
        return
      }

      if (this.mediaNameRequired) {
        this.toastr.showError('Please provide media name', true)
        return
      }
      this.queuedRepositoryModel.push(repositoryFiles)
    }
  }

  // Generate client side GUID
  generateUUIDv4(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      }
    )
  }

  // Upload files click event in UI
  onProcessNUpload() {
    //msTeam
    if (this.isMSteam) {
      if (this.addedFilesList && this.addedFilesList.length > 0) {
        this.isUploading = true
        // when upload button is clicked, set the upload status to in-progress
        this.store.dispatch(
          new FileUploadProgressStatus({
            uploadProgressStatus: 'in-progress'
          })
        )

        // If there are multiple entries (more than 1) in addedFileList, we need to generate GUID (for sessionId) in client side
        // as we are sending request for each entry in addedFileList
        // This will prevent from creating multiple GUIDs in server side
        // and hence Upload UI issue won't happen (i.e not updating immediate upload status UI)
        let sessionId = localStorage.getItem('StatusSessionId')
        if (this.addedFilesList.length > 1 && !sessionId) {
          sessionId = this.generateUUIDv4()
        }

        this.addedFilesList.forEach((addedItem) => {
          const msTeamHierarchies =
            this.createQueuedMSTeamHierarchiesData(addedItem)

          //dispatch
          this.xsStore
            .dispatch(
              new UActions.UploadMSTeamData(
                this.msteamModel,
                msTeamHierarchies,
                this.projectId,
                sessionId
              )
            )
            .pipe(
              switchMap(() =>
                this.xsStore.selectOnce(
                  UploadStateSelector.msTeamDownloadResult
                )
              ),
              filter((response) => !!response),
              takeUntil(this.unsubscribed$)
            )
            .subscribe((res: any) => {
              if (res) {
                localStorage.setItem('StatusSessionId', res.SessionId)

                const uploadingFileIds = res.collectedRepositoryUploadInfo.map(
                  (x) => x.FileId
                )
                if (uploadingFileIds != null && uploadingFileIds.length > 0) {
                  uploadingFileIds.forEach((fileId) => {
                    if (!this.currentlyUploadingMSTeamIds.includes(fileId)) {
                      this.currentlyUploadingMSTeamIds.push(fileId)
                    }
                  })
                }

                this.fnGetCustodianMediaStatus()
                this.fnGetUploadMediaStatus()
                this.fnStartUploadResponse()
                this.fnDeleteResponse()

                const msTeamUploadProcessData = {
                  filesUploading: res.collectedRepositoryUploadInfo,
                  msTeamModel: this.msteamModel,
                  msTeamHierarchies: res.msTeamHierarchies
                }
                this.callUpdateCustodianMedia(true, msTeamUploadProcessData)

                this.addedFilesList = []
                this.selectedMSTeamRowsData = []
                this.finalSelectedMSTeamRowsData = []
                this.xsStore.dispatch(new UActions.ResetMSTeamDownloadResult())

                this.isUploading = false
              }
            })
        })
      } else {
        this.toastr.showWarning(
          'Please select at least one user, team and channel hierarchy.',
          true
        )
      }
    } else {
      //  set to open detail
      this.isOpenOrClose = true
      this.isFinishProcessed = false

      // for processing repository files
      if (this.isRepository) {
        this.groupRepositoryFilesByCustodian()
        if (this.custodianNameRequired) {
          return
        }
        if (this.mediaNameRequired) {
          return
        }
        this.xsStore
          .dispatch(
            new fromLazyUploadStateActions.ProcessRepository(
              this.projectId,
              this.queuedRepositoryModel,
              this.externalUserId,
              this.isStructuredData,
              this.isTranscriptData,
              this.nsfUserIdFiles
            )
          )
          //.pipe(take(1))
          .pipe(takeUntil(this.toDestroy$))
          .subscribe(
            () => {
              if (this.isTranscriptData) {
                this.toastr.showSuccess(
                  'The selected data has been added for transcription successfully.',
                  true
                )
              } else {
                this.toastr.showSuccess(
                  'The selected data has been added for processing successfully.',
                  true
                )
              }
              this.fnGetCustodianMediaStatus()
              this.fnGetUploadMediaStatus()
              this.fnStartUploadResponse()
              this.fnDeleteResponse()
            },
            (error) => {
              if (this.isTranscriptData) {
                this.toastr.showError(
                  'Error occured while adding selected data for transcription. ' +
                    error.error.message,
                  true
                )
              } else {
                this.toastr.showError(
                  'Error occured while adding selected data for processing. ' +
                    error.error.message,
                  true
                )
              }
            }
          )
      } else if (this.isAWSS3UI) {
        /***AWS start */
        this.groupRepositoryFilesByCustodianAWS()
        if (this.custodianNameRequired) {
          return
        }
        if (this.mediaNameRequired) {
          return
        }
        this.xsStore
          .dispatch(
            new QueueAWSS3ItemAction(this.projectId, this.queuedAWSDataModel)
          )
          .pipe(
            switchMap(() =>
              this.xsStore.select(
                AWSDataUploadSelector.SliceOf('mediaStatusResultModel')
              )
            ),
            takeUntil(this.unsubscribed$)
          )
          .subscribe(
            (res: any) => {
              if (res) {
                {
                  this.toastr.showSuccess(
                    'The selected data has been added for processing successfully.',
                    true
                  )
                }
                // this.fnGetCustodianMediaStatus()
                // this.fnGetUploadMediaStatus()
                // this.fnStartUploadResponse()
                // this.fnDeleteResponse()

                this.repositoryFilesList = []
                this.awsS3FileList = []
                this.nsfUserIdFiles = []
                this.isRepository = false
                this.isAWSS3UI = true
                this.filesUploaded = res.collectedS3UploadInfo
                localStorage.setItem('StatusSessionId', res.sessionId)
                this.callUpdateCustodianMedia()
                this.fnGetCustodianMediaStatus()
              }
            },
            (error) => {
              {
                this.toastr.showError(
                  'Error occured while adding selected data for processing. ' +
                    error.error.message,
                  true
                )
              }
            }
          )
      } else {
        /**AWs end */
        for (let i = 0; i < this.addedFilesList.length; i++) {
          this.addedFilesList[i].IsStructured = this.isStructuredData
          if (this.addedFilesList[i].CustodianName === null) {
            this.addedFilesList[i].CustodianName = ''
          }
        }
        // when upload button is clicked, set the upload status to in-progress
        this.store.dispatch(
          new FileUploadProgressStatus({
            uploadProgressStatus: 'in-progress'
          })
        )

        this.store.dispatch(
          new StartUpload(
            this.projectId,
            this.addedFilesList.map((f) => ({
              ...f,
              IsStructured: this.isStructuredData,
              mediaSourceType: this.selectedMediaSourceType,
              CustodianName: f.CustodianName ?? '',
              MediaName: f.MediaName ?? '',
              Password: f.Password ?? ''
            })),
            this.externalUserId,
            this.processAllFilesInSingleMedia,
            this.isTranscriptData,
            this.fsid,
            this.isRepositoryUpload,
            this.selectedPath,
            this.nsfUserIdFiles
          )
        )
        //for uploading transcripts
        if (this.isTranscriptData) {
          this.xsStore.dispatch(
            new fromLazyUploadStateActions.UploadTranscript(
              this.projectId,
              this.addedFilesList,
              this.externalUserId
            )
          )
        }
      }

      this.addedFilesList = []
      this.repositoryFilesList = []
      this.queuedRepositoryModel = []
      this.queuedAWSDataModel = []
      this.awsS3FileList = []
      this.nsfUserIdFiles = []

      // fetch import status after upload is clicked
      if (this.isStructuredData) setTimeout(() => this.fetchImportStatus(), 500)
    }
  }

  createQueuedMSTeamHierarchiesData(
    addedItem: AddedFilesList
  ): QueuedMSTeamHierarchyModel[] {
    const queuedMSTeamHierarchies: QueuedMSTeamHierarchyModel[] = []

    const queueableMSTeamHierarchies: MSTeamHierarchyModel[] = []
    addedItem.MSTeamRelativePathDetails.forEach((relativeData) => {
      let queueableUsers: MediaData[] = []
      let queueableTeams: MediaData[] = []
      let queueableChannels: MediaData[] = []

      if (relativeData.UserId.length > 0) {
        queueableUsers = this.mediaData.filter((x) => {
          return x.ID === relativeData.UserId
        })

        queueableUsers.forEach((user) => {
          const queueableMSTeamUserHierarchy: MSTeamHierarchyModel = {
            nodeName: user.UserName,
            nodeId: user.ID.toString(),
            nodeType: user.NodeType,
            nodeParentId: user.Head_ID.toString(),
            MSId: user.MSId
          }
          queueableMSTeamHierarchies.push(queueableMSTeamUserHierarchy)
        })
      }

      if (relativeData.TeamId.length > 0) {
        queueableTeams = this.mediaData.filter((x) => {
          return x.ID === relativeData.TeamId
        })

        queueableTeams.forEach((team) => {
          const queueableMSTeamTeamHierarchy: MSTeamHierarchyModel = {
            nodeName: team.UserName,
            nodeId: team.ID.toString(),
            nodeType: team.NodeType,
            nodeParentId: team.Head_ID.toString(),
            MSId: team.MSId
          }
          queueableMSTeamHierarchies.push(queueableMSTeamTeamHierarchy)
        })
      }

      if (relativeData.ChannelId.length > 0) {
        queueableChannels = this.mediaData.filter((x) => {
          return x.ID === relativeData.ChannelId
        })

        queueableChannels.forEach((channel) => {
          const queueableMSTeamChannelHierarchy: MSTeamHierarchyModel = {
            nodeName: channel.UserName,
            nodeId: channel.ID.toString(),
            nodeType: channel.NodeType,
            nodeParentId: channel.Head_ID.toString(),
            MSId: channel.MSId
          }
          queueableMSTeamHierarchies.push(queueableMSTeamChannelHierarchy)
        })
      }
    })

    const queuedMSTeamHierarchy: QueuedMSTeamHierarchyModel = {
      custodianName: addedItem.CustodianName,
      mediaName: '',
      msTeamHierarchies: queueableMSTeamHierarchies
    }

    queuedMSTeamHierarchies.push(queuedMSTeamHierarchy)

    return queuedMSTeamHierarchies
  }

  showRepositoryItemHoverMenu(flag: boolean, nodeId: string) {
    const hoveredNode = this.repositoryHierarchyList.find(
      (x) => x.id === nodeId
    )
    if (!flag) {
      if (hoveredNode.type === 'FOLDER') hoveredNode.showNodeMenuOptions = false
      document.getElementById(nodeId).style.display = 'none'
    } else {
      if (hoveredNode.type === 'FOLDER') {
        hoveredNode.showNodeMenuOptions = true
        if (this.treeListRepository.instance.isRowExpanded(nodeId)) {
          hoveredNode.isExpanded = true
        } else hoveredNode.isExpanded = false
      }

      document.getElementById(nodeId).style.display = 'flex'
    }
  }

  showMSTeamItemHoverMenu(flag: boolean, nodeId: number) {
    const hoveredNode = this.mediaData.find((x) => x.ID === nodeId)

    if (!flag) {
      if (hoveredNode.NodeType === 'User' || hoveredNode.NodeType === 'Team')
        hoveredNode.ShowNodeMenuOptions = false
      document.getElementById('ms_team_' + nodeId).style.display = 'none'
    } else {
      if (hoveredNode.NodeType === 'User' || hoveredNode.NodeType === 'Team') {
        hoveredNode.ShowNodeMenuOptions = true
        if (this.treeList.instance.isRowExpanded(nodeId)) {
          const childNodes = this.mediaData.filter((y) => {
            return y.Head_ID === nodeId
          })
          if (childNodes.length > 0) hoveredNode.IsExpanded = true
          else hoveredNode.IsExpanded = false
        } else hoveredNode.IsExpanded = false
      }

      document.getElementById('ms_team_' + nodeId).style.display = 'flex'
    }
  }

  onShowHideJobStatusDetail(uploadFileId: string) {
    this.uploadFileIds.includes(uploadFileId)
      ? this.uploadFileIds.splice(this.uploadFileIds.indexOf(uploadFileId), 1)
      : this.uploadFileIds.push(uploadFileId)
  }

  //  when show detail then start timer to check update detail status
  statusAccordionStatusEvent(opened: boolean) {
    this.isOpenOrClose = opened

    if (
      this.isOpenOrClose &&
      (!this.triggerGetCustodianMediaStatus ||
        this.triggerGetCustodianMediaStatus?.isStopped)
    ) {
      this.store.dispatch(
        new GetCustodianMediaStatus(
          +this.projectId,
          localStorage.StatusSessionId,
          this.isVodrEnabled,
          this.internalUserId
        )
      )
      const intervalTimer = interval(10000)
      this.triggerGetCustodianMediaStatus = intervalTimer
        .pipe(
          filter(() => this.selectedSourceType !== SourceType.None),
          takeUntil(this.unsubscribed$)
        )
        .subscribe((results) => {
          this.store.dispatch(
            new GetCustodianMediaStatus(
              +this.projectId,
              localStorage.StatusSessionId,
              this.isVodrEnabled,
              this.internalUserId
            )
          )
        })
    } else if (
      !this.isOpenOrClose &&
      this.triggerGetCustodianMediaStatus &&
      !this.triggerGetCustodianMediaStatus?.isStopped
    ) {
      // when finish all update then drop temp table use for upload so it will not display again when come back
      if (this.isFinishProcessed) {
        this.store.dispatch(
          new DropTempTable(
            this.projectId,
            localStorage.getItem('StatusSessionId')
          )
        )
      }

      this.triggerGetCustodianMediaStatus.unsubscribe()
    }
  }

  // Used in uploaderObjectUploadComplete
  callUpdateCustodianMedia(isMSTeam = false, msTeamUploadProcessData = null) {
    if (this.isVodrEnabled) {
      if (this.filesUploaded.length === 0) {
        return
      }

      try {
        const jsonConvert = new JsonConvert()

        const vodrSettings = new VODRSettings()
        vodrSettings.uploadedFileList = jsonConvert.deserializeArray(
          this.filesUploaded,
          UploadFileDetails
        )
        vodrSettings.settingsInfo = this.settingsInfo
        vodrSettings.addDataToExistingCase = this.addDataToExistingCase
        vodrSettings.overrideSettingsInfo = this.overrideSettingsInfo
        vodrSettings.isContinuedAfterControlNumberConflict =
          this.isContinuedAfterControlNumberConflict

        this.isUploading = true
        this.xsStore
          .dispatch(
            new fromLazyUploadStateActions.UpdateCustodianMediaStatusROD({
              projectId: +this.projectId,
              externalUserId: this.externalUserId,
              sessionId: localStorage.getItem('StatusSessionId'),
              settingId: isNaN(this.settingId) ? -1 : this.settingId,
              vodrSettings: vodrSettings,
              isFromUploadLink: false,
              isTranscript: this.isTranscriptData,
              mediaSourceType: this.selectedMediaSourceType,
              isRepository: this.isRepository
            })
          )
          .pipe(
            switchMap(() =>
              this.xsStore.select(
                UploadStatesSelector.getCustodianMediaStatusROD
              )
            ),
            takeUntil(this.destroy$),
            take(1),
            map((res) => {
              if (res === 'FAIL') {
                throw new Error(
                  'Files could not be processed and have failed to queue'
                )
              }
              return res
            })
          )
          .subscribe({
            next: (res) => {
              this.isUploading = false
              this.toastr.showSuccess('Files are queued for processing.', true)
              this.toastr.showSuccess(
                'Data added to the project successfully.',
                true
              )
              if (this.externalUserId === -1) {
                this.router.navigate(['/launchpad/dashboard'])
              }
            },
            error: (err) => {
              this.isUploading = false
              this.toastr.showError(err.message, true)
              this.toastr.showError('Error adding data to the project', true)
              this.logger.logError(err)
              if (this.externalUserId === -1) {
                this.router.navigate(['/launchpad/dashboard'])
              }
            }
          })
      } catch (e) {
        this.store.dispatch(new GlobalErrorAction(e, false, true))
      }
    } else {
      this.store.dispatch(
        new UpdateCustodianMediaStatus(
          +this.projectId,
          localStorage.getItem('StatusSessionId'),
          isMSTeam
            ? msTeamUploadProcessData.filesUploading
            : this.filesUploaded,
          this.externalUserId,
          this.isTranscriptData,
          this.selectedMediaSourceType,
          this.isOverlay,
          this.isRepository,
          isMSTeam,
          isMSTeam ? msTeamUploadProcessData.msTeamModel : null,
          isMSTeam ? msTeamUploadProcessData.msTeamHierarchies : null,
          this.isRepositoryUpload,
          this.isAWSS3UI
        )
      )
    }
  }

  // Used in uploaderObjectUploadComplete
  sendUploadCompleteMessage(uploadQueuedFiles: any, httpCode: number) {
    const uploadQueuedFilesInfo = []
    uploadQueuedFiles.forEach((uploadFile) => {
      const file = {
        FileName: uploadFile.name,
        UploadStatus: uploadFile.status,
        FileId: uploadFile.id,
        FileSize: uploadFile.origSize,
        IsTranscript: this.isTranscriptData
      }
      uploadQueuedFilesInfo.push(file)
    })
    let application = 'VOD'
    if (this.isVodrEnabled) {
      application = 'VODR'
    }
    this.store.dispatch(
      new SendUploadCompleteMessage(
        +this.projectId,
        localStorage.getItem('StatusSessionId'),
        application,
        uploadQueuedFilesInfo,
        +this.externalUserId,
        this.isTranscriptData,
        this.fsid,
        this.isRepositoryUpload,
        httpCode
      )
    )
  }

  // Table addedFilesList -> (click)="onRemoveFile(file.FileId)" click event in UI
  onRemoveFile(id: any, fileName: string, type: string, isRepository: boolean) {
    const fileType = isRepository ? type : 'file'
    const confirmMessage =
      'Are you sure you want to remove selected ' +
      fileType +
      ' <strong>' +
      fileName +
      '</strong> ?'
    this.showConfirmationModal(confirmMessage)
      .pipe(
        filter((yes) => yes),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        if (!this.isRepository && !this.isAWSS3UI) {
          const index = this.mUploader.files.findIndex((f) => f.id === id)
          if (index > -1) this.mUploader.files.splice(index, 1)
          const fIndex = this.addedFilesList.findIndex((f) => f.FileId === id)
          if (fIndex > -1) this.addedFilesList.splice(fIndex, 1)

          //remove nsf userid file
          const userIdIndex = this.nsfUserIdFiles?.indexOf(id, 0)
          if (userIdIndex > -1) {
            this.nsfUserIdFiles.splice(index, 1)
          }
        } else if (this.isRepository) {
          const rIndex = this.repositoryFilesList.findIndex((f) => f.Id === id)
          if (rIndex > -1) {
            const guid = this.repositoryFilesList[rIndex].GUID
            this.repositoryFilesList.splice(rIndex, 1)
            this.showCustodianColumn(guid)
          }
        } else {
          const rIndex = this.awsS3FileList.findIndex((f) => f.id === id)
          if (rIndex > -1) {
            const guid = this.awsS3FileList[rIndex].gUID
            this.awsS3FileList.splice(rIndex, 1)
            this.showCustodianColumn(guid)
          }
        }
      })
  }

  showCustodianColumn(guid) {
    if (this.isRepository) {
      const rIndex = this.repositoryFilesList.findIndex((f) => f.GUID === guid)
      if (rIndex > -1)
        this.repositoryFilesList[rIndex].IsCustodianNameVisible = true
    } else {
      const rIndex = this.awsS3FileList.findIndex((f) => f.gUID === guid)
      if (rIndex > -1) this.awsS3FileList[rIndex].isCustodianNameVisible = true
    }
  }

  showConfirmationModal(message: string): Observable<boolean> {
    const modal = this.modalService.show(ConfirmationDialogComponent, {
      class: 'modal-dialog-centered'
    })
    ;(<ConfirmationDialogComponent>modal.content).showConfirmationModal(
      this.companyName,
      message
    )
    return (<ConfirmationDialogComponent>modal.content).onClose
  }

  showConfirmationModalAWS(
    message: string,
    showOkOnly: boolean
  ): Observable<boolean> {
    const modal = this.modalService.show(ConfirmationDialogComponent, {
      class: 'modal-dialog-centered'
    })
    ;(<ConfirmationDialogComponent>modal.content).showConfirmationModal(
      this.companyName,
      message,
      showOkOnly
    )
    return (<ConfirmationDialogComponent>modal.content).onClose
  }

  // Used in UI <span [ngSwitch]="GetMediaJobStatus(media)">
  getMediaJobStatus(media: any) {
    if (
      media.currentlyInProgressJob !== undefined &&
      media.currentlyInProgressJob != null
    ) {
      return 'JOB_IN_PROGRESS'
    } else {
      return 'JOB_IN_QUEUE'
    }
  }

  // Used in UI {{getInQueueJob(media)}}
  getInQueueJob(media: any) {
    const notStartedJobs = media.jobList.filter((x) => {
      return x.status.toUpperCase() === 'NOT STARTED'
    })
    if (notStartedJobs != null && notStartedJobs.length > 0) {
      return notStartedJobs[0].taskName.toLowerCase()
    }
  }

  // Used in UI (click)="cancelUpload(media.UploadFileId)" (click)="cancelUpload(media.UploadFileId)"
  cancelUpload(id: any, isFailed: boolean) {
    this.mUploader.stop()
    const cId = this.client?.clientId ?? -1
    const files = this.mUploader.files.filter((file) => {
      return file.id === id
    })
    this.cancelMessage = ''
    if (files !== null && files.length > 0) {
      const fileName = files[0].name
      if (isFailed) {
        this.cancelMessage =
          "Upload of file '" + fileName + "' has been failed."
      } else {
        this.cancelMessage =
          "Upload of file '" + fileName + "' has been cancelled."
      }
    }

    this.mUploader.removeFile(id)
    this.currentlyUploadingId = ''
    this.currentlyCancelledID = id

    this.start()

    // Call ajax to delete upload file on the server
    const self = this
    $.ajax({
      type: 'POST',
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
      url:
        this.configService.getVodBaseUrl() +
        '/VodWebService.asmx/DeleteUploadedFile',
      data: '{UploadingFileId:"' + id + '" ,clientid:"' + cId + '"}',
      timeout: 1000 * 600,
      async: false,
      success: function (data) {
        // Edited: Sanh Huynh; Date: Apr 01, 2020; Ref #23595 - Missing Function Upload Pause/Cancel
        // delete data must after delete file on server from upload location (DeleteUploadedFile get file name to delete)
        self.store.dispatch(
          new DeleteCustodianMedia(
            id,
            self.projectId,
            localStorage.getItem('StatusSessionId'),
            isFailed ? false : true
          )
        )
      },
      error: function (data) {
        self.toastr.showError(
          'Failed to insert uploaded file information into the table'
        )
      }
    })
  }

  // Response for delete event
  fnDeleteResponse() {
    this.store
      .pipe(
        select(deleteCustodianMediaResponse),
        map((res: any) => res),
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((result) => {
        if (result === 'SUCCESS') {
          if (this.cancelMessage === '') {
            this.cancelMessage = 'Upload has been cancelled for 1 file.'
          }
          if (this.cancelMessage.includes('failed')) {
            this.toastr.showError(this.cancelMessage, true)
          } else this.toastr.showWarning(this.cancelMessage, true)

          /**
           * Reset the response so that the selector is subscribed everytime to show the
           * cancel message for every file cancellation
           */
          this.store.dispatch(new DeleteCustodianMediaSuccessful(null))

          this.store.dispatch(
            new GetCustodianMediaStatus(
              +this.projectId,
              localStorage.StatusSessionId,
              this.isVodrEnabled,
              this.internalUserId
            )
          )

          if (this.mUploader.files.length > 0 && this.mUploader.state === 1) {
            this.mUploader.refresh()
            this.start()
          }
        }
      })
  }

  // Used in UI <span *ngSwitchCase="'NOT PROCESSED'">
  checkIfListContainsId(id: any) {
    if (this.filesUploaded.length > 0) {
      const files = this.filesUploaded.filter((file) => {
        return file.FileID === id
      })

      if (files !== null && files.length > 0) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  }

  checkIfMSTeamListContainsUploadingId(id: string): boolean {
    if (this.currentlyUploadingMSTeamIds.includes(id)) {
      return true
    } else {
      return false
    }
  }

  checkIfMSTeamListContainsUploadedId(id: string): boolean {
    if (this.uploadedMSTeamIds.includes(id)) {
      return true
    } else {
      return false
    }
  }

  // Used in UI (click)="popUpFileTypeList()"
  popUpFileTypeList() {
    // Pop up error modal
    const initialState = {
      title: 'Supported File Type List',
      closeBtnName: 'Close'
    }
    this.bsModalRef.show(
      FileTypeListComponent,
      Object.assign(
        {
          initialState: {
            isTranscript: this.isTranscriptData
          }
        },
        { ignoreBackdropClick: false }
      )
    )
  }

  isUrlHttps(url: string): boolean {
    const regex = RegExp('^https://(.+)$')
    return regex.test(url)
  }

  /**
   * When an user selects the source type, we need to re-initialize the existing `fn(s)`
   * because of we are adding/removing elements on the DOM on demand by using `ngIf` directive.
   */
  selectedSource(
    selectedSource: SourceType,
    mediaSourceType: MediaSourceType
  ): void {
    this.isMSteam = mediaSourceType === MediaSourceType.SOCIAL_MEDIA_MSTEAM

    // preparation for file upload section.

    this.selectedMediaSourceType = mediaSourceType
    this.selectedSocialMedia =
      mediaSourceType === MediaSourceType.SOCIAL_MEDIA_FACEBOOK
        ? 'Facebook'
        : mediaSourceType === MediaSourceType.SOCIAL_MEDIA_SLACK
        ? 'Slack'
        : mediaSourceType === MediaSourceType.SOCIAL_MEDIA_CELLEBRITE
        ? 'Cellebrite'
        : mediaSourceType === MediaSourceType.SOCIAL_MEDIA_BLOOMBERG
        ? 'Bloomberg'
        : mediaSourceType === MediaSourceType.SOCIAL_MEDIA_TWITTER
        ? 'X'
        : mediaSourceType === MediaSourceType.SOCIAL_MEDIA_MSTEAM
        ? 'MS Teams'
        : mediaSourceType === MediaSourceType.RSMF
        ? 'Relativity Short Message Format'
        : ''
    if (this.isMSteam) {
      const currentUrl = window.location.href
      const apiUrl = this.configService.apiUrl
      const isCurrentUrlValid = this.isUrlHttps(currentUrl)
      const isApiUrlValid = this.isUrlHttps(apiUrl)
      let errorMessage = ''

      if (!isCurrentUrlValid || !isApiUrlValid) {
        errorMessage =
          'For MS Teams features, the environment and the Venio OnDemand application should use secure connection with https.'
      }

      if (errorMessage) {
        this.store.dispatch(
          new GlobalErrorAction(new Error(errorMessage), true, false)
        )
        return
      }

      //show login popup for MS team
      this.startMsTeamLogin(selectedSource)
    } else {
      this.selectedSourceType = selectedSource
      this.fnGetCustodianMediaStatus()
      this.fnGetUploadMediaStatus()
      this.fnStartUploadResponse()
      this.fnDeleteResponse()
    }

    this.isStructuredData = selectedSource === SourceType.Structured
    this.isTranscriptData = selectedSource === SourceType.Transcript
    this.uploadBtnName = this.isRepository
      ? 'Process'
      : this.isRepositoryUpload
      ? 'Upload'
      : 'Upload & Process'
    this.addedFilesList = []
    this.repositoryFilesList = []
    this.repositoryList = []
    this.awsS3FileList = []
    this.repositoryHierarchyList = []
    this.selectedRepository = []
    this.nsfUserIdFiles = []
    this.isRepository = false

    // cleanup for next init (GC will take care)
    this.mUploader = null
    this.isFinishProcessed = false
    this.currentlyUploadingId = ''
    this.currentFileUploadProgress = 0

    if (this.isLimitedServiceLicense) {
      this.isRepository = true
      this.cdr.markForCheck()
      this.openupload(true)
    }

    if (this.isStructuredData) {
      this.fetchImportStatus()
    }

    if (!this.isMSteam && !this.isLimitedServiceLicense) {
      setTimeout(() => {
        this.mUploader = this.uploaderObject()
        this.mUploader.init()
      }, 1000)
    }
  }

  startMsTeamLogin(selectedSource) {
    const requestObj = {
      scopes: [
        'user.read',
        'mailboxsettings.read',
        'calendars.readwrite',
        'Team.ReadBasic.All',
        'TeamSettings.ReadWrite.All',
        'User.Read.All',
        'Directory.Read.All',
        'User.ReadWrite.All',
        'Directory.ReadWrite.All',
        'Directory.AccessAsUser.All',
        'mail.read',
        'user.readbasic.all',
        'user.readwrite',
        'TeamMember.Read.All',
        'TeamMember.ReadWrite.All',
        'Group.Read.All',
        'Group.ReadWrite.All',
        'Files.Read',
        'Files.Read.All',
        'Files.ReadWrite',
        'Files.ReadWrite.All',
        'Sites.Read.All',
        'Sites.ReadWrite.All',
        'Channel.ReadBasic.All',
        'ChannelMessage.Read.All'
      ]
    }
    this.checkAccount()
    this.authService.handleRedirectCallback((authError, response) => {
      if (authError) {
        console.error('Redirect Error: ', authError.errorMessage)
        return
      }
    })
    if (!this.loggedIn) {
      //checks if browser is IE
      const isIE =
        window.navigator.userAgent.indexOf('MSIE ') > -1 ||
        window.navigator.userAgent.indexOf('Trident/') > -1
      if (isIE) {
        this.authService.loginRedirect()
      } else {
        this.authService
          .loginPopup(requestObj)
          .then((loginResponse) => {
            //Login Success callback code here
            this.authService
              .acquireTokenSilent(requestObj) //.acquireTokenPopup(requestObj)
              .then((tokenResponse) => {
                if (
                  tokenResponse.accessToken != undefined &&
                  tokenResponse.accessToken != null
                ) {
                  this.msteamModel.accessToken = tokenResponse.accessToken
                  this.msteamModel.loggedInUserId =
                    tokenResponse.account.accountIdentifier
                  this.loading = true
                  this.getMSData(this.msteamModel)
                  this.selectedSourceType = selectedSource
                  this.fnGetCustodianMediaStatus()
                  this.fnGetUploadMediaStatus()
                  this.fnStartUploadResponse()
                  this.fnDeleteResponse()
                }
              })
              .catch(function (error) {
                console.error(error)
              })
          })
          .catch(function (error) {
            console.error(error)
          })
      }
    } else {
      this.authService
        .acquireTokenSilent(requestObj) //.acquireTokenPopup(requestObj)
        .then((tokenResponse) => {
          //this.loginSuccess=true
          if (
            tokenResponse.accessToken != undefined &&
            tokenResponse.accessToken != null
          ) {
            this.msteamModel.accessToken = tokenResponse.accessToken
            this.msteamModel.loggedInUserId =
              tokenResponse.account.accountIdentifier
            this.loading = true
            this.getMSData(this.msteamModel)
            this.selectedSourceType = selectedSource
            this.fnGetCustodianMediaStatus()
            this.fnGetUploadMediaStatus()
            this.fnStartUploadResponse()
            this.fnDeleteResponse()
          }
        })
        .catch(function (error) {
          console.error(error)
        })
    }
  }

  /**
   * Also add the text (custodian name) when user type something in the input and blur out.
   * to meet the requirement of this @link https://redmine.veniosystems.com/issues/23457
   *
   * Though, this implementation is not a bulletproof but,
   * instead of this @link https://github.com/ng-select/ng-select
   * we should be using one of these backed by same `Angular Team`
   * @link https://material.angular.io/components/autocomplete/overview
   * @link https://material.angular.io/components/chips/overview
   */
  custodianNameBlur(e: NgSelectComponent, index: number) {
    // capture input search term text
    const termValue = (e.searchInput.nativeElement.value || '').trim()
    const isCusIndex = this.custodianNames.indexOf(termValue) > -1
    const isSelectedIndex = e.selectedValues.indexOf(termValue) > -1
    if (this.isRepository) {
      this.applyCustodianName(
        this.repositoryFilesList[index].GUID,
        e.selectedValues[0] === undefined ? '' : e.selectedValues[0]
      )
    }
    if (this.isAWSS3UI) {
      this.applyCustodianName(
        this.awsS3FileList[index].gUID,
        e.selectedValues[0] === undefined ? '' : e.selectedValues[0]
      )
    }
    // do not add duplicate
    if (isCusIndex || isSelectedIndex || termValue.length <= 0) {
      return
    }

    // clear existing selected item and add new
    e.clearModel()
    this.custodianNames = [...this.custodianNames.slice(), termValue]
    e.selectedItems.push({ value: termValue, selected: true, label: termValue })

    // for the payload
    if (this.isRepository) {
      this.repositoryFilesList[index].CustodianName = termValue
      this.applyCustodianName(this.repositoryFilesList[index].GUID, termValue)
    } else if (this.isAWSS3UI) {
      this.awsS3FileList[index].custodianName = termValue
      this.applyCustodianName(this.awsS3FileList[index].gUID, termValue)
    } else {
      this.addedFilesList[index].CustodianName = termValue
    }
  }

  applyCustodianName(guid, custodianName) {
    if (this.isRepository) {
      this.repositoryFilesList.forEach((item) => {
        if (item.GUID === guid) {
          item.CustodianName = custodianName
        }
      })
    } else if (this.isAWSS3UI) {
      this.awsS3FileList.forEach((item) => {
        if (item.gUID === guid) {
          item.custodianName = custodianName
        }
      })
    }
  }

  /**
   * When side panel toggle and current state is visible, load and display the upload history.
   */
  populateHistory() {
    const subs = of(this.projectId)
      .pipe(
        distinctUntilChanged(),
        tap((id) => this.store.dispatch(new GetUploadedHistory(id))),
        switchMap(() => this.store.select(uploadHistorySuccess)),
        filter((d: any[]) => d && d.length > 0),
        debounceTime(1000),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({
        next: (res) => [
          (this.rowData = res.map((el) => ({ ...el, checked: false }))),
          subs.unsubscribe()
        ]
      })
  }

  /**
   * Counts total items by either media ID or custodian ID
   */
  total = (isMedia: boolean) =>
    [
      ...new Set(
        isMedia
          ? this.rowData.map((r) => r.mediaId)
          : this.rowData.map((r) => r.custodianId)
      )
    ].length

  /**
   * Prepare the toolbar (custom). The send to (pages) buttons.
   */
  toolbarPreparing(e) {
    e.toolbarOptions.items.unshift({
      location: 'before',
      template: 'actionButtons'
    })
  }

  onMediaNameEnterKeyPressed(mediaInfo) {
    this.isEnterKeyPressed = true
    this.renameMediaName(
      mediaInfo.custMedia,
      mediaInfo.custId,
      mediaInfo.mediaName
    )
  }

  onMediaNameFocusOut(mediaInfo) {
    if (!this.isEnterKeyPressed) {
      this.renameMediaName(
        mediaInfo.custMedia,
        mediaInfo.custId,
        mediaInfo.mediaName
      )
    }
    this.isEnterKeyPressed = false
  }

  renameMediaName(media: ICustodianMedia, custodianId: number, value: string) {
    if (media.mediaName === value) {
      return
    }

    const mdia: ICustodianMedia = <ICustodianMedia>{
      mediaId: media.mediaId,
      mediaName: value
    }
    this.pushChanges()
    this.xsStore
      .dispatch(new RenameMediaName(this.projectId, custodianId, mdia))
      .pipe(
        switchMap(() =>
          this.xsStore.select(
            CustodianSelector.SliceOf('custodianMediaResponse')
          )
        ),
        distinctUntilChanged(),
        debounceTime(800),
        takeUntil(this.destroy$),
        catchError((err) => {
          this.toastr.showError(err.message)
          this.pushChanges()
          return EMPTY
        })
      )
      .subscribe((res) => {
        this.populateHistory()
        this.pushChanges()
      })
  }

  // check for changes
  pushChanges() {
    this.cdr.markForCheck()
  }

  /**
   * Converts selected items to a query and sends it to the page by invoked action type.
   * @param sendTo Type of the action. @see `ActionTypes`
   */
  sendActionClicked(sendTo: ActionTypes) {
    if (this.selection.isEmpty()) {
      this.toastr.showError('Please select media first', true)
      return
    }

    const finalArgs: SearchQueryOption = {
      query:
        sendTo === 'production'
          ? this.selection.selected.map((m) => m.mediaId)
          : this.prepareQuery(),
      includePc: null,
      sourceModule: SearchQueryModule.UploadFileHistory
    }

    this.xsStore.dispatch(new UActions.InitUploadHistoryQueryAction(finalArgs))
    switch (sendTo) {
      case 'analyze':
        this.router.navigateByUrl('/analyze?projectId=' + this.projectId)
        break
      case 'production':
        this.router.navigateByUrl('/production?projectId=' + this.projectId)
        break
      case 'review':
        this.router.navigateByUrl(
          `${
            this.enableReview2
              ? '/review2'
              : this.newUiBaseurl
              ? '/review-next'
              : '/review'
          }?projectId=${this.projectId}`
        )
        break
    }
  }

  /**
   * Whether the number of selected elements matches the total number of rows.
   * Toggles checkbox between `intermediate`, `none` or `checked` when children selection get changed.
   * @param cell cell info
   */
  isAllSelected(cell: any) {
    const data =
      (cell.data && (cell.data.items || cell.data.collapsedItems)[0]) || []
    const custodianId = (data && data.custodianId) || 0
    const numSelected = this.selection.selected.filter(
      (s) => s.custodianId === custodianId
    ).length
    const numRows = this.rowData.filter(
      (s) => s.custodianId === custodianId
    ).length
    return numSelected === numRows
  }

  /**
   * Toggles parent (grouped row) selection changes.
   * Note: When the group row collapse, the `collapsedItems`
   * will hold the children otherwise the `items` property
   * @param cell cell info
   */
  masterToggle(cell: any) {
    const data =
      (cell.data && (cell.data.items || cell.data.collapsedItems)[0]) || []
    const custodianId = (data && data.custodianId) || 0
    const filtered = this.rowData.filter((s) => s.custodianId === custodianId)

    if (this.isAllSelected(cell)) {
      filtered.forEach((row) => this.selection.deselect(row))
    } else {
      filtered.forEach((row) => this.selection.select(row))
    }
  }

  /**
   * Checks whether an item exist on the selection but yet to select all children.
   * @param cell cell info
   */
  hasSelectedItem(cell: any) {
    const data =
      (cell.data && (cell.data.items || cell.data.collapsedItems)[0]) || []
    const custodianId = (data && data.custodianId) || 0
    const element = this.selection.selected.find(
      (c) => c.custodianId === custodianId
    )
    return this.selection.isSelected(element) && !this.isAllSelected(cell)
  }

  /**
   * Creates search query to use on other pages like analyze, review & production.
   */
  private prepareQuery() {
    const data = _.chain(this.selection.selected)
      // Group the elements of Array based by `custodianName` property
      .groupBy('custodianName')
      // `key` is group's name (custodianName), `value` is the array of objects
      .map((value, key) => ({
        Custodian: key,
        Media: value.map((m) => m.mediaName),
        // flag whether the selected items from group are equal so we can shorthand the query.
        isAll:
          this.rowData.filter((r) => r.custodianName === key).length ===
          value.length
      }))
      .value()
    const finalQuery = data
      .map((el) => {
        // shorthand if all items are selected
        return el.isAll
          ? `CUSTODIAN_NAME="${el.Custodian}"`
          : // partially selected items
            el.Media.map(
              (media) =>
                `(CUSTODIAN_NAME="${el.Custodian}" AND MEDIA_NAME="${media}")`
            ).join(' OR ')
      })
      .join(' OR ')
    return finalQuery
  }

  /****** returns report object if reportId is found from reportMenu Array */
  getReportById(reportMenu: any[], reportId: string) {
    const find = (report: any) => {
      if (report.id === reportId) {
        return report
      } else {
        if (Object.prototype.hasOwnProperty.call(report, 'items')) {
          for (let j = 0; j < report.items?.length; j++) {
            const matchedReport = find(report.items[j])
            if (matchedReport) {
              return matchedReport
            }
          }
        }
        return null
      }
    }

    if (reportMenu?.length) {
      for (let i = 0; i < reportMenu.length; i++) {
        const report = reportMenu[i]
        const mainReport = find(report)
        if (mainReport) {
          return mainReport
        }
      }
    }
    return null
  }

  openReport(report: any) {
    const reportType = report?.id
    if (reportType) {
      window.open(
        this.configService.getWebBaseUrl() +
          '/Reports/Report.aspx?Type=' +
          reportType +
          '&HdrDis=1'
      )
    }
  }

  getMSData(msteamModel: MSteamModel) {
    this.xsStore
      .dispatch(new UActions.GetMediaData(msteamModel))
      .pipe(
        switchMap(() =>
          this.xsStore.selectOnce(UploadStateSelector.mediaDataResponse)
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((response: MediaData[]) => {
        this.mediaData = JSON.parse(JSON.stringify(response))

        const dummyUserData = []
        let dummyId = -1
        this.mediaData.forEach(function (obj) {
          obj.IsLoading = false
          obj.IsCancelling = false
          obj.ShowNodeMenuOptions = false
          obj.HasExpandedEarlier = false
          if (obj.ID.toString() === '1') {
            obj.IsExpanded = true
          } else obj.IsExpanded = false

          if (obj.NodeType === 'User') {
            const dummyNode = {
              ID: dummyId,
              Head_ID: obj.ID,
              UserName: '',
              Prefix: '',
              CustodianName: '',
              MediaName: '',
              FetchStatus: '',
              Email: '',
              MediaNote: '',
              NodeType: 'Dummy',
              MSId: 'Dummy',
              HasExpandedEarlier: false,
              IsLoading: false,
              IsCancelling: false,
              ShowNodeMenuOptions: false,
              IsExpanded: false
            }
            dummyId = dummyId - 1

            dummyUserData.push(dummyNode)
          }
        })

        if (dummyUserData.length > 0) {
          this.mediaData = [...this.mediaData, ...dummyUserData]
        }

        if (!this.expandedRowsKeyList.includes('1'))
          this.expandedRowsKeyList.push('1')
        this.loading = false
      })
  }

  logout() {
    this.authService.logout()
  }

  checkAccount() {
    const hasAccount = !!this.authService.getAccount()
    const hasIdToken =
      this.utilityService.getLocalStorage('msal.idtoken') != null &&
      this.utilityService.getLocalStorage('msal.idtoken') != undefined
    if (hasAccount && hasIdToken) {
      this.loggedIn = true
    }
  }

  allowUpdatingMSTeam(e) {
    if (e && e.row && e.row.data && e.row.data.NodeType == 'User') return true
    return false
  }

  //gets teams and channel only if it is user else expands node
  onRowExpanding(key) {
    const selectedNode = this.mediaData.find((x) => x.ID == key)
    if (selectedNode.NodeType == 'User' || selectedNode.NodeType == 'Team') {
      if (!selectedNode.HasExpandedEarlier) {
        const dummyChildNode = this.mediaData.find(
          (x) => x.ID < 0 && x.Head_ID === key && x.NodeType === 'Dummy'
        )
        if (dummyChildNode != null) {
          this.mediaData.splice(
            this.mediaData.findIndex(
              (x) => x.ID < 0 && x.Head_ID == key && x.NodeType === 'Dummy'
            ),
            1
          )

          this.removedMSTeamDummyNodes.push(dummyChildNode)
        }
      }

      this.getTeamChannel(key, selectedNode.NodeType)
    } else {
      const toggledNode = this.mediaData.find((x) => x.ID === key)
      toggledNode.IsExpanded = true
    }
  }

  onRowCollapsing(key) {
    const toggledNode = this.mediaData.find((x) => x.ID === key)
    toggledNode.IsExpanded = false
  }

  getTeamChannel(key, nodeType: string) {
    //gets team and channel info from backend if USER row not expanded eariler
    if (!this.checkIfExpandedEarlier(key)) {
      this.mediaData = this.mediaData.map((obj) =>
        obj.ID === key
          ? {
              ...obj,
              IsLoading: true,
              ShowNodeMenuOptions: false,
              IsExpanded: false,
              HasExpandedEarlier: false
            }
          : obj
      )

      this.populateUserNode(key, nodeType)
      const drillMSTeamyHierarchyRequest = this.xsStore
        .dispatch(
          new UActions.GetTeamChannelData(
            this.selectedUserNodeInfo,
            this.msteamModel
          )
        )
        .pipe(
          switchMap(() =>
            this.xsStore.selectOnce(UploadStateSelector.mediaDataResponse)
          ),
          takeUntil(this.unsubscribed$)
        )
        .subscribe((response: MediaData[]) => {
          // remove the request object from request array
          this.triggerDrillMSTeamHierarchy.splice(
            this.triggerDrillMSTeamHierarchy.findIndex((x) => x.id === key),
            1
          )

          this.mediaData = this.mediaData.map((obj) =>
            obj.ID === key
              ? {
                  ...obj,
                  HasExpandedEarlier: true,
                  IsLoading: false,
                  ShowNodeMenuOptions: false,
                  IsExpanded: true
                }
              : obj
          )

          const dummyUserData = []
          let dummyId = -100000
          response.forEach(function (obj) {
            if (obj.NodeType === 'Team') {
              const dummyNode = {
                ID: dummyId,
                Head_ID: obj.ID,
                UserName: '',
                Prefix: '',
                CustodianName: '',
                MediaName: '',
                FetchStatus: '',
                Email: '',
                MediaNote: '',
                NodeType: 'Dummy',
                MSId: 'Dummy',
                HasExpandedEarlier: false,
                IsLoading: false,
                IsCancelling: false,
                ShowNodeMenuOptions: false,
                IsExpanded: false
              }
              dummyId = dummyId - 1

              dummyUserData.push(dummyNode)
            }
          })

          if (dummyUserData.length > 0) {
            this.mediaData = [...this.mediaData, ...dummyUserData]
          }

          if (!this.treeList.instance.isRowExpanded(key)) {
            this.treeList.instance.expandRow(key)
          }

          this.populateMediaData(response, key)
        })

      const drillMSTeamHierarchyRequestObj = {
        id: key,
        request: drillMSTeamyHierarchyRequest
      }
      this.triggerDrillMSTeamHierarchy.push(drillMSTeamHierarchyRequestObj)
    }
  }

  populateMediaData(response: MediaData[], key): void {
    this.tempData = [...this.mediaData, ...response]

    this.tempData = this.tempData.map((obj) =>
      obj.ID === key
        ? {
            ...obj,
            IsLoading: false,
            IsCancelling: false,
            IsExpanded: true,
            ShowNodeMenuOptions: false
          }
        : obj
    )

    // when multiple nodes are in expanding state and if one of the node get response
    // but others are in still in expanded state, the spinner is not visible for other nodes which are in expanded state.
    this.tempData = this.tempData.map((obj) =>
      obj.ID !== key && !obj.IsLoading
        ? {
            ...obj,
            IsLoading: false,
            IsCancelling: false,
            IsExpanded: false,
            ShowNodeMenuOptions: false
          }
        : obj
    )

    this.mediaData = this.tempData

    const index = this.mediaData.findIndex((x) => x.ID < 0 && x.Head_ID === key)
    if (index > -1) this.mediaData.splice(index, 1)
  }

  //populates selectedUserNodeInfo  to fetch it to backend

  populateUserNode(key, nodeType: string) {
    if (nodeType === 'Team') {
      this.selectedUserNodeInfo.selectedUserMSId = this.mediaData.find(
        (x) => x.ID == key
      ).NodeParentId

      this.selectedUserNodeInfo.selectedTeamMSId = this.mediaData.find(
        (x) => x.ID == key
      ).MSId
    } else {
      this.selectedUserNodeInfo.selectedUserMSId = this.mediaData.find(
        (x) => x.ID == key
      ).MSId
      this.selectedUserNodeInfo.selectedTeamMSId = null
    }
    this.selectedUserNodeInfo.maxNodeId = Math.max(
      ...this.mediaData.map((o) => o.ID),
      0
    )
    this.selectedUserNodeInfo.userNodeId = key
  }

  //returns true if expanded earlier
  checkIfExpandedEarlier(key) {
    if (this.mediaData.find((x) => x.ID == key).HasExpandedEarlier) {
      return true
    }
    return false
  }

  onSelectionChanged(event) {
    //to enable/disable add template/channel button
    this.selectedNodeKeys = this.treeList.instance.getSelectedRowKeys()
  }

  onRemoveUsers(file: any) {
    let relativePaths = ''
    if (file.MSTeamRelativePathDetails.length > 0) {
      file.MSTeamRelativePathDetails.forEach((element) => {
        if (
          element.UserId.length > 0 &&
          (element.TeamId.length > 0 || element.ChannelId.length > 0)
        ) {
          relativePaths = relativePaths.concat('-' + element.Path + '<br>')
        }
      })
    }
    let confirmMessage =
      'Are you sure you want to remove <strong>' + file.Name + '</strong>'
    if (relativePaths.length > 0) {
      confirmMessage = confirmMessage.concat(' that has following entries')
    }
    confirmMessage = confirmMessage.concat(' ? <br>' + relativePaths)
    this.showConfirmationModal(confirmMessage).subscribe((result) => {
      if (result) {
        const invalidSelectedUserIdList = []
        invalidSelectedUserIdList.push(file.FileId)
        //removes the latest selection of user if it was selected previously
        this.removeInvalidUserInfo(invalidSelectedUserIdList)
        this.addedFilesList = this.addedFilesList.filter(
          (x) => x.MSTeamRelativeId != file.MSTeamRelativeId
        )
      }
    })
  }

  goBackToSourceTypeSelection() {
    this.selectedSourceType = SourceType.None
    if (this.isMSteam) {
      this.selectedMediaSourceType = MediaSourceType.SOURCE_FILE
      this.selectedSocialMedia = ''
      this.addedFilesList = []
      this.selectedMSTeamRowsData = []
      this.finalSelectedMSTeamRowsData = []
      this.expandedRowsKeyList = []
      if (this.treeList) {
        this.treeList.instance.deselectAll()
      }
    } else {
      this.isRepository = false
      this.repositoryFilesList = []
      this.repositoryList = []
      this.awsS3FileList = []
      this.nsfUserIdFiles = []
      if (this.treeListRepository) {
        this.treeListRepository.instance.deselectAll()
      }
    }
    this.isBatchMediaUi = false
    this.isAWSS3UI = false
  }

  //open popuup for upload
  openModal(key) {
    const selectedNode = this.repositoryHierarchyList.find((x) => x.id == key)
    this.selectedPath = selectedNode.relativePath
    this.isRepository = false
    const uploadDialogRef = this.dialog.open(this.modalContent, {
      closeOnNavigation: true,
      autoFocus: false,
      width: '60vw'
    })
    setTimeout(() => {
      this.mUploader = this.uploaderObject()
      this.mUploader.init()
    }, 1000)

    uploadDialogRef
      .afterClosed()
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({
        next: () => this.onClickLoadHierarchy()
      })
  }

  // actions for delete, create and edit folder name
  folderActions(id: any, action: string, type: string) {
    const selectedNode = this.repositoryHierarchyList.find((x) => x.id == id)
    const isFolder = type === 'FOLDER' ? true : false
    switch (action) {
      case 'CREATE': {
        let createPath = ''
        if (selectedNode.parentId != '-1') {
          createPath = selectedNode.relativePath
        }
        this.dialogHeader = 'Create new folder'
        this.buttonText = action
        const creatRef = this.dialog.open(this.warningContent, {
          closeOnNavigation: true,
          autoFocus: false,
          width: '500px'
        })
        creatRef
          .afterClosed()
          .pipe(
            takeUntil(this.toDestroy$),
            filter((yes) => yes),
            tap(() =>
              this.xsStore.dispatch(
                new UActions.CreateAction(this.fsid, createPath, this.fileName)
              )
            ),
            switchMap(() =>
              // Get the response for the delete action.
              this.xsStore.select(UploadStateSelector.createResponse)
            ),
            filter((res) => !!res?.status)
          )
          .subscribe({
            next: (res) => {
              if (res != null || res != undefined) {
                const status = (res.status || '').trim().toLowerCase()
                if (status === 'success') {
                  // If it's a success show the message and fetch the layouts so that our store will have the up-to-date list.
                  this.toastr.showSuccess('Folder created successfully.', true)
                  this.onClickLoadHierarchy()
                }
                this.fileName = ''
              }
            }
          })
        break
      }
      case 'RENAME': {
        const renamePath = selectedNode.relativePath
        this.dialogHeader = isFolder ? 'Rename Folder' : 'Rename File'
        this.buttonText = action
        const renameRef = this.dialog.open(this.warningContent, {
          closeOnNavigation: true,
          autoFocus: false,
          width: '500px'
        })
        renameRef
          .afterClosed()
          .pipe(
            takeUntil(this.toDestroy$),
            filter((yes) => yes),
            tap(() =>
              this.xsStore.dispatch(
                new UActions.RenameAction(
                  this.fsid,
                  renamePath,
                  this.fileName,
                  isFolder
                )
              )
            ),
            switchMap(() =>
              // Get the response for the delete action.
              this.xsStore.select(UploadStateSelector.renameResponse)
            ),
            filter((res) => !!res?.status)
          )
          .subscribe({
            next: (res) => {
              const status = (res.status || '').trim().toLowerCase()
              if (status === 'success') {
                // If it's a success show the message and fetch the layouts so that our store will have the up-to-date list.
                this.toastr.showSuccess('Folder renamed successfully.', true)
                this.onClickLoadHierarchy()
              }
              this.fileName = ''
            }
          })
        break
      }

      case 'REMOVE': {
        const removePath = selectedNode.relativePath
        const text = 'Are you sure you want to remove selected folder?'
        this.showConfirmationModal(text).subscribe((result) => {
          if (result) {
            this.xsStore
              .dispatch(
                new UActions.DeleteAction(this.fsid, removePath, isFolder)
              )
              .pipe(
                takeUntil(this.unsubscribed$),
                switchMap(() =>
                  this.xsStore.select(UploadStateSelector.deleteResponse)
                ),
                filter((res) => !!res?.status)
              )
              .subscribe({
                next: (res) => {
                  const status = (res.status || '').trim().toLowerCase()
                  if (status === 'success') {
                    // If it's a success show the message and fetch the layouts so that our store will have the up-to-date list.
                    this.toastr.showSuccess(
                      'Folder deleted successfully.',
                      true
                    )
                    this.onClickLoadHierarchy()
                  }
                }
              })
          }
        })
        break
      }
    }
  }

  async openMediaOverview(e: any) {
    const sortingDialogRef = this.dialog.open(this.mediaOverviewTemplateRef, {
      autoFocus: false,
      closeOnNavigation: true,
      width: '90%',
      maxWidth: '975px',
      maxHeight: '90vh'
    })

    this.mediaOverviewInjector = Injector.create({
      providers: [
        {
          provide: 'data',
          useValue: {
            projectId: this.projectInfo.projectId,
            custodianName: e.data?.custodianName,
            mediaName: e.data?.mediaName,
            mediaId: e.data?.mediaId,
            dialogRef: sortingDialogRef
          }
        }
      ],
      parent: this.injector
    })

    await import('../../components/media-overview/media-overview.module')

    this.mediaOverviewComponent = import(
      '../../components/media-overview/media-overview.component'
    ).then(({ MediaOverviewComponent }) => MediaOverviewComponent)
  }

  getSelectedS3Items(selectedS3Items: VenioS3Item[]) {
    this.selectedS3ItemsAtParent = selectedS3Items
    this.onAddingRepositoryItemsForAWS()
  }

  getBucketHierarchy(bucketHieracyList: VenioS3Item[]) {
    this.bucketHieracyListAtParent = bucketHieracyList
  }

  /////////////////AWS//////////////////////////
  onAddingRepositoryItemsForAWS(specificRowData = null) {
    const selectedRows = this.selectedS3ItemsAtParent
    // specificRowData != null && specificRowData.length === 1
    //   ? specificRowData
    //   : this.selectedRowsData

    let confirmMessage = '<div class="repository-validation-message">'
    let promptConfirmDialog = false
    let showOkOnly = false

    // Validates if file/folder(s) are already added for processing.
    const commonFileFolderList = selectedRows.filter((e) => {
      return this.awsS3FileList.some((item) => item.id === e.id)
    })

    if (selectedRows.length == commonFileFolderList.length) showOkOnly = true
    if (commonFileFolderList != null && commonFileFolderList.length > 0) {
      promptConfirmDialog = true
      confirmMessage = confirmMessage.concat(
        'Following selected bucket/file/folder(s) have already been added for processing -<br>'
      )
      commonFileFolderList.forEach(function (x) {
        const path =
          x.parentId === '0' ? x.bucketName : x.bucketName + x.relativePath
        confirmMessage = confirmMessage.concat(
          '&nbsp; <i> - ' + path + '</i> <br>'
        )
      })
      confirmMessage = confirmMessage.concat('<br>')
    }

    this.parentRepositoryItemsMapper = []
    this.childRepositoryItemsMapper = []
    // Validates if parent folder is already added in case of trying to add child file or folder.
    if (this.awsS3FileList != null && this.awsS3FileList.length > 0) {
      selectedRows.forEach((selectedItem) => {
        // // Filter by fsid
        const filteredHierarchyByFSID = this.bucketHieracyListAtParent.filter(
          (x) => {
            return x.s3ConfigurationId === selectedItem.s3ConfigurationId
          }
        )
        // List out all the parent folders for selected file/folders
        const filteredHierarchyByFolders =
          this.bucketHieracyListAtParent.filter((x) => {
            return x.s3ItemType === 'FOLDER'
          })

        this.collectAllParentRepositoryItemsRecursively(
          selectedItem,
          filteredHierarchyByFolders
        )
        const parentRepositoryItemMapper = {
          key: selectedItem,
          value: this.parentRepositoryOrFolders
        }
        this.parentRepositoryItemsMapper.push(parentRepositoryItemMapper)

        this.collectAllChildRepositoryItemsRecursively(
          selectedItem,
          filteredHierarchyByFSID
        )
        const childRepositoryItemMapper = {
          key: selectedItem,
          value: this.childRepositoryOrFolders
        }
        this.childRepositoryItemsMapper.push(childRepositoryItemMapper)

        this.parentRepositoryOrFolders = []
        this.childRepositoryOrFolders = []
      })

      let intermediateParentRepositoryOrFolders = []
      this.parentRepositoryItemsMapper.forEach((item) => {
        intermediateParentRepositoryOrFolders = [
          ...intermediateParentRepositoryOrFolders,
          ...item.value
        ]
      })

      intermediateParentRepositoryOrFolders = _.uniqBy(
        intermediateParentRepositoryOrFolders,
        'id'
      )

      const commonParentFolderList =
        intermediateParentRepositoryOrFolders.filter((e) => {
          return this.awsS3FileList.some((item) => item.id === e.id)
        })

      if (commonParentFolderList.length > 0) {
        promptConfirmDialog = true
        confirmMessage = confirmMessage.concat(
          'Parent folder of the following selected file/folder(s) have already been added for processing -<br>'
        )

        const tempRepositoryFileList = this.awsS3FileList
        this.parentRepositoryItemsMapper.forEach(function (x) {
          if (x.value.length > 0) {
            const commonParentRepositoryList = x.value.filter((e) => {
              return tempRepositoryFileList.some((item) => item.id === e.id)
            })

            if (commonParentRepositoryList.length > 0) {
              const path = x.key.bucketName + '\\' + x.key.relativePath.slice(1)
              confirmMessage = confirmMessage.concat(
                '&nbsp; <i> - ' + path + '</i> <br>'
              )
            }
          }
        })
        confirmMessage = confirmMessage.concat('<br>')
      }

      let intermediateChildRepositoryOrFolders = []
      this.childRepositoryItemsMapper.forEach((item) => {
        intermediateChildRepositoryOrFolders = [
          ...intermediateChildRepositoryOrFolders,
          ...item.value
        ]
      })

      intermediateChildRepositoryOrFolders = _.uniqBy(
        intermediateChildRepositoryOrFolders,
        'id'
      )
      const commonChildFileFolderList =
        intermediateChildRepositoryOrFolders.filter((e) => {
          return this.awsS3FileList.some((item) => item.id === e.id)
        })
      if (commonChildFileFolderList.length > 0) {
        promptConfirmDialog = true
        confirmMessage = confirmMessage.concat(
          'The child file/folder(s) of the following selected folder(s) have already been added for processing. The child file/folder(s) will be removed from processing list. -<br>'
        )
        this.childRepositoryItemsMapper.forEach(function (x) {
          if (x.value.length > 0) {
            const path =
              x.key.parentId === '-1'
                ? x.key.bucketName
                : x.key.bucketName + '\\' + x.key.relativePath.slice(1)
            confirmMessage = confirmMessage.concat(
              '&nbsp; <i> - ' + path + '</i> <br>'
            )
          }
        })
        confirmMessage = confirmMessage.concat('<br>')
      }
    }

    const selectedRootRepositoryList = selectedRows.filter((x) => {
      return x.parentId === '0'
    })

    // Show confirmation if all the childs need to be processed if entire repository is selected for processing
    if (
      selectedRootRepositoryList != null &&
      selectedRootRepositoryList.length > 0
    ) {
      const addedRootRepositoryList = this.awsS3FileList.filter((x) => {
        return x.parentId === '0'
      })

      const differenceRootRepositoryList = selectedRootRepositoryList.filter(
        (e) => {
          return !addedRootRepositoryList.some((item) => item.id === e.id)
        }
      )

      if (differenceRootRepositoryList.length > 0) {
        promptConfirmDialog = true
        confirmMessage = confirmMessage.concat(
          'All the file/folder(s) will be processed for following selected storages -<br>'
        )
        differenceRootRepositoryList.forEach(function (x) {
          confirmMessage = confirmMessage.concat(
            '&nbsp; <i> - ' + x.bucketName + '</i> <br>'
          )
        })
        confirmMessage = confirmMessage.concat('<br>')
      }
    }

    if (promptConfirmDialog) {
      confirmMessage = confirmMessage.concat('</div>')
      if (!showOkOnly)
        confirmMessage = confirmMessage.concat('<br>Do you want to continue?')
      confirmMessage = confirmMessage.concat(
        "<br><br><p class='repository-note'> * Already added file/folder(s) will not be added for processing.</p>"
      )

      this.showConfirmationModalAWS(confirmMessage, showOkOnly)
        .pipe(
          filter((yes) => yes),
          takeUntil(this.unsubscribed$)
        )
        .subscribe(() => {
          this.addRepositoryItemsAWS(selectedRows, specificRowData !== null)
        })
    } else {
      this.addRepositoryItemsAWS(selectedRows, specificRowData !== null)
    }
  }

  addRepositoryItemsAWS(selectedRows, addSpecificRowData) {
    const tempRepositoryFileArray: AWSS3FilesList[] = []
    this.isCustodianNameVisible = true
    const guid = Math.floor(Math.random() * 100000000 + 1)
    selectedRows.forEach((file) => {
      const repositoryFileInfo: AWSS3FilesList = {
        s3ConfigurationId: file.s3ConfigurationId,
        id: file.id,
        parentId: file.parentId,
        s3ItemType: file.s3ItemType,
        name: file.name,
        bucketName: file.bucketName,
        //RepositoryRootFolderName: file.repositoryRootFolderName,
        custodianName: null,
        relativePath: file.parentId === '0' ? file.name : file.relativePath,
        isCustodianNameVisible: this.isCustodianNameVisible,
        gUID: guid,
        parentItem: '',
        fullPath: ''
      }
      if (this.awsS3FileList.length > 0) {
        const rIndex = this.awsS3FileList.findIndex((f) => f.id === file.id)
        if (!(rIndex > -1)) {
          //Get common parent folders between folders that are already added for processing and folders that are going to be added
          // so that we can ignore adding those commonn folders in processing list
          const intermediateParentRepositoryOrFolders =
            this.parentRepositoryItemsMapper.find(
              (x) => x.key.id === file.id
            ).value
          const commonParentFolderList =
            intermediateParentRepositoryOrFolders.filter((e) => {
              return this.awsS3FileList.some((item) => item.id === e.id)
            })

          //Get common child file/folders between file/folders that are already added for processing and file/folders that are going to be added
          // so that we can ignore adding those commonn file/folders in processing list
          const intermediateChildRepositoryOrFolders =
            this.childRepositoryItemsMapper.find(
              (x) => x.key.id === file.id
            ).value
          const commonChildFileFolderList =
            intermediateChildRepositoryOrFolders.filter((e) => {
              return this.awsS3FileList.some((item) => item.id === e.id)
            })

          if (
            !(
              commonParentFolderList != null &&
              commonParentFolderList.length > 0
            ) &&
            !(
              commonChildFileFolderList != null &&
              commonChildFileFolderList.length > 0
            )
          ) {
            tempRepositoryFileArray.push(repositoryFileInfo)
            this.isCustodianNameVisible = false
          }

          // If user is trying to add parent folder whose child file/folders have already been added
          // Then remove all those childs and add parent folder for processing
          if (
            commonChildFileFolderList != null &&
            commonChildFileFolderList.length > 0
          ) {
            commonChildFileFolderList.forEach((item) => {
              this.awsS3FileList.splice(
                this.awsS3FileList.findIndex((x) => x.id === item.id),
                1
              )
            })

            tempRepositoryFileArray.push(repositoryFileInfo)
          }
        }
      } else {
        tempRepositoryFileArray.push(repositoryFileInfo)
        this.isCustodianNameVisible = false
      }
    })

    if (this.awsS3FileList) {
      this.awsS3FileList = _.union(this.awsS3FileList, tempRepositoryFileArray)
    } else {
      this.awsS3FileList = tempRepositoryFileArray
    }
    //if (!addSpecificRowData) this.treeListRepository.instance.clearSelection()
  }

  groupRepositoryFilesByCustodianAWS() {
    if (!(this.awsS3FileList.length > 0)) {
      this.queuedAWSDataModel = []
      return
    }
    this.custodianNameRequired = false
    this.mediaNameRequired = false

    const grouped = _.groupBy(this.awsS3FileList, (item) => item.gUID)

    for (const item in grouped) {
      const tempRepositoryFiles: AWSS3FilesList[] = []
      if (grouped[item].length > 1) {
        this.custodianNameRequired = true
        this.mediaNameRequired = true
      }
      let itemCountWithCustodianName = 0
      let itemCountWithMediaName = 0
      grouped[item].forEach((file) => {
        if (!file.mediaName) {
          file.mediaName =
            grouped[item][0].gUID == file.gUID
              ? grouped[item][0].mediaName
              : null
        }
        const repositoryFileInfo: AWSS3FilesList = {
          s3ConfigurationId: file.s3ConfigurationId,
          id: file.id,
          parentId: file.parentId,
          s3ItemType: file.s3ItemType,
          name: file.name,
          bucketName: file.bucketName,
          custodianName: file.custodianName,
          relativePath: file.relativePath,
          isCustodianNameVisible: file.isCustodianNameVisible,
          gUID: file.gUID,
          parentItem: '',
          fullPath: '',
          mediaName: file.mediaName
        }
        if (file.custodianName) {
          itemCountWithCustodianName = itemCountWithCustodianName + 1
        }
        if (file.mediaName) {
          itemCountWithMediaName = itemCountWithMediaName + 1
        }
        tempRepositoryFiles.push(repositoryFileInfo)
      })

      if (
        grouped[item].length > 1 &&
        itemCountWithCustodianName !== grouped[item].length
      )
        this.custodianNameRequired = true
      else this.custodianNameRequired = false
      if (
        grouped[item].length > 1 &&
        itemCountWithMediaName !== grouped[item].length
      )
        this.mediaNameRequired = true
      else this.mediaNameRequired = false

      const repositoryFiles: QueuedAWSDataModel = {
        custodianName:
          grouped[item].length > 0 ? tempRepositoryFiles[0].custodianName : '',
        mediaName:
          grouped[item].length > 0 ? tempRepositoryFiles[0].mediaName : '',
        s3ItemsHierarchies: tempRepositoryFiles
        // MediaSourceType: this.selectedMediaSourceType
      }
      if (this.custodianNameRequired) {
        this.toastr.showError('Please provide custodian name', true)
        return
      }
      if (this.mediaNameRequired) {
        this.toastr.showError('Please provide media name', true)
        return
      }
      this.queuedAWSDataModel.push(repositoryFiles)
    }
  }

  CheckIfAnyUploadedFileisNsf() {
    return this.addedFilesList?.some((x) => x.Extension.toLowerCase() === 'nsf')
  }

  CheckIfAnyRepositoryFileisNsf() {
    return this.repositoryFilesList.some((file) => this.IsLotusNotesFile(file))
  }

  CheckIfAnyRepositoryFileisEncryptable() {
    const archiveFileExtensions = `zip|zipx|rar|7z|gz|lzh|obd|cab|tar|dbx|dxl|mbox|ns2|nsf|ost|pst|olm|ISO|E\\d+|L\\d+|${this.forensicImageFileFormat}`
    const documentFileExtensons = `pdf|doc|docx|dotm|dot|docm|dotx|xltx|xltm|xlsx|xlsm|xlsb|xls|xlam|pptx|pptm|ppt|ppsx|potx|ppsm|potm`

    const filePattern = new RegExp(
      `(${archiveFileExtensions}|${documentFileExtensons})$`,
      'i'
    )

    return this.repositoryFilesList.some((f) =>
      filePattern.test(f.Name.split('.').pop())
    )
  }

  isFileisEcryptable(fileName) {
    const archiveFileExtensions = `zip|zipx|rar|7z|gz|lzh|obd|cab|tar|dbx|dxl|mbox|ns2|nsf|ost|pst|olm|ISO|E\\d+|L\\d+|${this.forensicImageFileFormat}`
    const documentFileExtensons = `pdf|doc|docx|dotm|dot|docm|dotx|xltx|xltm|xlsx|xlsm|xlsb|xls|xlam|pptx|pptm|ppt|ppsx|potx|ppsm|potm|odt|ods|ow|odp|pages|numbers`

    const filePattern = new RegExp(
      `(${archiveFileExtensions}|${documentFileExtensons})$`,
      'i'
    )

    return filePattern.test(fileName.split('.').pop())
  }

  handleFileInput(e, fileId: string): void {
    const file = e.target['files'][0]

    if (this.nsfUserIdFiles.findIndex((x) => x.fileId == fileId) != -1) {
      this.nsfUserIdFiles.find((x) => x.fileId == fileId).userIdFile = file
    } else {
      this.nsfUserIdFiles.push({ fileId, userIdFile: file })
    }
  }

  GetNsfUserIdFileName(fileId) {
    if (this.nsfUserIdFiles.findIndex((x) => x.fileId === fileId) != -1) {
      return this.nsfUserIdFiles.find((x) => x.fileId === fileId)?.userIdFile
        .name
    }
    return null
  }

  IsLotusNotesFile(file: RepositoryFilesList): boolean {
    if (file.Type.toLowerCase() !== 'file') {
      return false
    }

    const fileExt = file.Name.split('.').pop()
    if (fileExt) {
      return fileExt.toLowerCase() === 'nsf'
    }
    return false
  }

  initialForensicImageExtensions: string[] = ['L01', 'E01']

  getValidForensicParts(fileNames: string[]): string[] {
    const validList: string[] = []
    let areWeCheckingMultiPart = false
    let previousFileName = ''

    for (const fileName of this.orderFilenames(fileNames)) {
      if (this.isInitialForensicFile(fileName)) {
        validList.push(fileName.toUpperCase())
        areWeCheckingMultiPart = true
        previousFileName = fileName
        continue
      }

      if (areWeCheckingMultiPart) {
        const prevExt = this.getExtension(previousFileName)
        if (prevExt) {
          const nextExt = this.incrementForensicImageExtension(prevExt)
          if (this.getExtension(fileName) !== nextExt) {
            areWeCheckingMultiPart = false
          } else {
            validList.push(fileName.toUpperCase())
          }
        }
      }

      previousFileName = fileName
    }

    return validList
  }

  orderFilenames(filenames: string[]): string[] {
    return filenames.sort((a, b) => {
      const baseA = this.getBaseName(a)
      const baseB = this.getBaseName(b)
      const extA = this.getExtension(a)
      const extB = this.getExtension(b)

      if (baseA < baseB) return -1
      if (baseA > baseB) return 1
      return this.getOrderValue(extA) - this.getOrderValue(extB)
    })
  }

  getBaseName(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.')
    return lastDotIndex < 0 ? filename : filename.substring(0, lastDotIndex)
  }

  getExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.')
    return lastDotIndex < 0 ? '' : filename.substring(lastDotIndex + 1)
  }

  getOrderValue(part: string): number {
    if (!isNaN(Number(part))) {
      return Number(part)
    } else {
      let value = 0
      for (let i = 0; i < part.length; i++) {
        value = value * 26 + (part.charCodeAt(i) - 'A'.charCodeAt(0) + 1)
      }
      return value + 99 // Offset to distinguish from numeric values
    }
  }

  isInitialForensicFile(fileName: string): boolean {
    const extension = this.getExtension(fileName).toUpperCase()
    return this.initialForensicImageExtensions.includes(extension)
  }

  incrementForensicImageExtension(extension: string): string {
    if (!isNaN(Number(extension.substring(1)))) {
      const extensionNum = parseInt(extension.substring(1), 10)
      if (extensionNum + 1 <= 99) {
        return extension[0] + (extensionNum + 1).toString().padStart(2, '0')
      } else {
        return extension[0] + 'AA'
      }
    } else {
      const nextExtension = extension.split('')
      let incrementPreChar = true

      for (let i = nextExtension.length - 1; i >= 0; i--) {
        if (incrementPreChar) {
          if (nextExtension[i] === 'Z') {
            nextExtension[i] = 'A'
          } else {
            nextExtension[i] = String.fromCharCode(
              nextExtension[i].charCodeAt(0) + 1
            )
            incrementPreChar = false
          }
        }
      }

      return nextExtension.join('')
    }
  }

  /////////////////////////
}
