import { SamlIdpServerComponentService } from '@admin-advance/components/saml-idp-server-setting/saml-idp-server-component.service'
import { IdPProvider } from '@admin-advance/models/saml-idp-server/saml-idp-server.enum'
import {
  IdPGroupInsertRequestModel,
  LoadGroupTypes,
  XmlFileParseResponseModel
} from '@admin-advance/models/saml-idp-server/saml-idp-server.model'
import { SamlIdpServerFacade } from '@admin-advance/store'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild
} from '@angular/core'
import { AbstractControl, FormGroup } from '@angular/forms'
import { MatDialog } from '@angular/material/dialog'
import { ToastrService } from 'ngx-toastr'
import { of, Subject } from 'rxjs'
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil
} from 'rxjs/operators'
import { ResponseModel } from '../../../../../shared/models'
import {
  GroupRequestPayload,
  GroupResponse
} from '../../../../models/saml-idp-server/saml-idp-server.model'
import { SamlIdpServerSettingsService } from '../../../../services'

@Component({
  selector: 'app-saml-idp-server-setting-form',
  templateUrl: './saml-idp-server-setting-form.component.html',
  styleUrls: ['./saml-idp-server-setting-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'row mb-20'
  }
})
export class SamlIdpServerSettingFormComponent
  implements OnInit, OnChanges, OnDestroy, AfterViewInit
{
  confirmationMessage = 'test'

  @ViewChild('confirmUpdate')
  private readonly confirmUpdateIdPGroups: TemplateRef<any>

  @Output()
  readonly loadingChanged = new EventEmitter<boolean>()

  @Input()
  isParentEnable: boolean

  @Input() selectedProvider: IdPProvider | null = null

  @Input() selectedClientId: number | null = null

  private readonly toDestroy$ = new Subject<void>()

  samlIdpForm: FormGroup

  isLoading: boolean

  isXmlMetafileParsing: boolean

  loadButtonLabel: string

  readonly loadGroupTypes = LoadGroupTypes

  private isLoadFromMetaXml: boolean

  get loadGroupTypeControl(): AbstractControl {
    return this.samlIdpForm.get('loadGroupType')
  }

  constructor(
    private samlIdpServerFacade: SamlIdpServerFacade,
    private samlIdpServerComponentService: SamlIdpServerComponentService,
    private changeDetectorRef: ChangeDetectorRef,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private samlIdpServerSettingsService: SamlIdpServerSettingsService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    const isParentEnable = changes['isParentEnable']
    if (
      isParentEnable &&
      isParentEnable.currentValue !== isParentEnable.previousValue
    ) {
      this.toggleDisableFormStateWhenParentChanged()
    }
  }

  ngOnInit(): void {
    this.initForm()
    this.toggleDisableFormStateWhenParentChanged()
    this.setLoadButtonLabel()
    this.selectParsedXmlMetafileResponse()
    this.setGroupLoadSubscription()
  }

  ngAfterViewInit(): void {
    this.updateLoadButtonLabelWhenLoadTypeChanged()
    this.setSamlSettingSubscription()
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  private setLoadButtonLabel = (): void => {
    this.changeDetectorRef.markForCheck()
    const value: LoadGroupTypes = this.loadGroupTypeControl.value
    const label = 'Load' + (this.isLoading ? 'ing' : '') + ' groups'
    switch (value) {
      case LoadGroupTypes.TOKEN:
        this.loadButtonLabel = label
        break
      case LoadGroupTypes.FILE:
        this.loadButtonLabel = label + ' from file'
        break
      case LoadGroupTypes.PROFILE:
        this.loadButtonLabel = label
        break
    }
  }

  private updateLoadButtonLabelWhenLoadTypeChanged(): void {
    this.loadGroupTypeControl.valueChanges
      .pipe(
        debounceTime(100),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe(this.setLoadButtonLabel)
  }

  private toggleDisableFormStateWhenParentChanged(): void {
    this.isParentEnable ? this.samlIdpForm.enable() : this.samlIdpForm.disable()
  }

  private initForm(): void {
    this.samlIdpForm = this.samlIdpServerComponentService.initSamlForm()
    this.setFormValueChangeSubscription()
  }

  private setGroupLoadSubscription = (): void => {
    this.samlIdpServerFacade.selectSamlIdpGroupsData$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        const isError = (res) => res && (res.status || '').match(/error/gi)
        if (isError(res)) {
          this.toastrService.error(res.message)
        }
        this.changeDetectorRef.markForCheck()
        this.isLoading = false
        this.loadingChanged.emit(this.isLoading)
        this.setLoadButtonLabel()
      })
  }

  private patchParsedMetaFileInfo(data: XmlFileParseResponseModel): void {
    this.samlIdpForm.patchValue(data)
    this.samlIdpServerFacade.storeSamlIdpServerSetting({
      type: 'formData',
      samlIdpSettings: data
    })
  }

  private selectParsedXmlMetafileResponse(): void {
    this.samlIdpServerFacade.selectSamlIdpServerParsedXmlMetafileResponse$
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        this.changeDetectorRef.markForCheck()
        this.isXmlMetafileParsing = false

        const isError = (res) => res && (res.status || '').match(/error/gi)
        if (isError(res)) {
          this.toastrService.error(res.message)
        } else {
          this.patchParsedMetaFileInfo(res.data)
        }
      })
  }

  readonly loadSamlGroups = (): void => {
    this.changeDetectorRef.markForCheck()
    this.isLoading = true
    this.loadingChanged.emit(this.isLoading)
    this.setLoadButtonLabel()
    const formData = this.samlIdpForm.getRawValue()

    this.samlIdpServerSettingsService
      .checkMissingIdpGroups({
        ...formData,
        link: formData.groupAPIURL,
        clientId: this.selectedClientId,
        providerName: this.selectedProvider
      })
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$),
        catchError((error) => {
          this.toastrService.error(error?.error?.message)
          this.isLoading = false
          this.loadingChanged.emit(this.isLoading)
          this.setLoadButtonLabel()
          return of(null) // Return a null observable to prevent further execution
        })
      )
      .subscribe((response: ResponseModel) => {
        if (!response) {
          return // Exit if response is null (due to an error)
        }
        const idpGroupResponse: GroupResponse = response.data
        const idPGroupInsertRequestModel: IdPGroupInsertRequestModel = {
          idPGroups: idpGroupResponse.idPGroups,
          clientId: this.selectedClientId
        }
        if (idpGroupResponse.missingIdPGroups.hasMissingIdPGroups) {
          this.confirmationMessage = idpGroupResponse.missingIdPGroups.message
          const confirmDialog = this.dialog.open(this.confirmUpdateIdPGroups, {
            closeOnNavigation: true,
            autoFocus: false,
            width: '380px'
          })
          confirmDialog.afterClosed().subscribe((result) => {
            if (result) {
              this.samlIdpServerFacade.insertSamlIdpGroupData(
                idPGroupInsertRequestModel
              )
            } else {
              this.isLoading = false
              this.loadingChanged.emit(this.isLoading)
              this.setLoadButtonLabel()
            }
          })
        } else {
          //Need to insert as the new load group might have new groups without missing any old groups
          this.samlIdpServerFacade.insertSamlIdpGroupData(
            idPGroupInsertRequestModel
          )
        }
      })
  }

  readonly onXmlMetafileChange = (event: Event): void => {
    const input = event.target as HTMLInputElement
    const file = input?.files?.[0]
    if (!file) return

    this.isXmlMetafileParsing = true
    this.isLoadFromMetaXml = true

    file
      .text()
      .then((xml) =>
        this.samlIdpServerFacade.fetchSamlIdpServerSettingParseXmlMetafile(xml)
      )
  }

  private setFormValueChangeSubscription(): void {
    this.samlIdpForm.valueChanges
      .pipe(debounceTime(400), takeUntil(this.toDestroy$))
      .subscribe((values) => {
        this.samlIdpServerFacade.storeSamlIdpServerSetting({
          type: 'formData',
          samlIdpSettings: values
        })
      })
  }

  private setSamlSettingSubscription(): void {
    this.samlIdpServerFacade.selectSamlIdpServerFetchResponse$
      .pipe(
        filter((res) => !!res?.data),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe((values) => {
        if (values?.data?.idPGroupRequest?.token) {
          this.isLoading = true
          this.loadingChanged.emit(this.isLoading)
          this.setLoadButtonLabel()

          const groupRequestPayload: GroupRequestPayload = {
            token: values.data.idPGroupRequest.token,
            link: values.data.apilink,
            groupType:
              this.selectedProvider === IdPProvider.OKTA ? 'OKTA_GROUP' : null,
            tenantId: values.data.idPGroupRequest.tenantId,
            applicationClientId:
              values.data.idPGroupRequest.applicationClientId,
            applicationObjectId:
              values.data.idPGroupRequest.applicationObjectId,
            providerName: this.selectedProvider,
            clientId: this.selectedClientId,
            fetchGroupsFromDatabase: true
          }

          this.samlIdpServerFacade.fetchSamlIdpGroupData(groupRequestPayload)
        }

        this.samlIdpForm.patchValue({
          ...values.data,
          idpssoUrl: values.data.ssO_URL,
          groupAPIURL: values.data.apilink,
          idpIssuer: values.data.idpUser,
          token: values.data.ssoToken,
          tenantId: values.data.idPGroupRequest?.tenantId,
          applicationClientId: values.data.idPGroupRequest?.applicationClientId,
          applicationObjectId: values.data.idPGroupRequest?.applicationObjectId
        })
      })
  }
}
