import { HttpErrorResponse } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ConfigService } from '@config/services/config.service'
import { FetchControlSettings, FetchHelpLinks } from '@config/store/actions'
import { getControlSettings } from '@config/store/selectors'
import { Actions, Effect, ofType } from '@ngrx/effects'
import { Action, select, Store } from '@ngrx/store'
import { Store as XsStore } from '@ngxs/store'
import { ResetCachedProjectRightListAction } from '@root/modules/launchpad/store'
import { ResponseModel as ResponseModels } from '@shared/models'
import { NavigateToAction } from '@shared/store/actions/shared.actions'
import { AppStartupState } from '@stores/states/startups.state'
import { JsonConvert } from 'json2typescript'
import { isEmpty } from 'lodash'
import { RouterGo } from 'ngrx-router'
import { from, Observable, of } from 'rxjs'
import {
  catchError,
  concatMap,
  mergeMap,
  switchMap,
  tap,
  withLatestFrom
} from 'rxjs/operators'
import { NotificationService } from 'src/app/services/notification.service'
import { ErrorService } from '../../../../services/error.service'
import {
  GlobalErrorAction,
  GlobalSuccessAction
} from '../../../../store/actions'
import { TwoFactorAuthResponseModel } from '../../models/2fa-response.model'
import {
  ExtUserAuthResponseModel,
  HoldUserAuthResponseModel
} from '../../models/external-user-authentication.model'
import { Login } from '../../models/login.model'
import { ResponseModel } from '../../models/response.model'
import { SendPasswordResetLinkResponse } from '../../models/sendPasswordResetLink.model'
import { Client, User, UserModel } from '../../models/user.model'
import { AuthService } from '../../services/auth.service'
import {
  AccessActionTypes,
  AuthError,
  ChangePassword,
  ChangePasswordSuccess,
  EmailUpdate,
  ExtUserAuthError,
  FetchAuthToken,
  FetchAuthTokenSuccess,
  FetchClientInfo,
  FetchExtUser,
  FetchExtUserAuthToken,
  FetchExtUserDetails,
  FetchMaintenanceSetting,
  FetchUser,
  FetchUserDetails,
  FetchUserDetailsSuccess,
  FetchUserError,
  LogOut,
  PasswordComplexity,
  SendExtUserAuthNotification,
  SendExtUserAuthNotificationSuccess,
  SendHoldUserAuthNotification,
  SendHoldUserAuthNotificationSuccess,
  SendResetPasswordLink,
  SendResetPasswordLinkError,
  SendResetPasswordLinkSuccessful,
  SendTwoFactorAuthenticationNotification,
  SendTwoFactorAuthenticationNotificationSuccess,
  SetClientInfo,
  SetMaintenanceSetting,
  SetServerSideSession,
  SetUser,
  TwoFactorAuthenticationError,
  VerifyAuthenticationCode,
  VerifyAuthenticationCodeSuccess,
  VerifyExtUserAuthCode,
  VerifyExtUserAuthCodeSuccess,
  VerifyHoldUserAuthCode,
  VerifyHoldUserAuthCodeSuccess
} from '../actions'
import { AuthState } from '../reducers'
import { getUser } from '../selectors/access.selectors'

@Injectable()
export class AccessEffects {
  private jsonConvert: JsonConvert

  constructor(
    private actions$: Actions,
    private service: AuthService,
    private errorService: ErrorService,
    private store: Store<AuthState>,
    private notificationService: NotificationService,
    private xsStore: XsStore
  ) {
    this.jsonConvert = new JsonConvert()
  }

  @Effect()
  fetchAuthToken$: Observable<Action> = this.actions$.pipe(
    ofType<FetchAuthToken>(AccessActionTypes.FetchAuthToken),
    switchMap((action) => {
      try {
        return this.service.fetchToken(action.login).pipe(
          mergeMap((res: ResponseModel) => {
            return [new FetchAuthTokenSuccess(res)]
          }),
          catchError((err) =>
            from([new GlobalErrorAction(err, false, true), new AuthError(err)])
          )
        )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  @Effect()
  fetchAuthTokenSuccess$: Observable<Action> = this.actions$.pipe(
    ofType<FetchAuthTokenSuccess>(AccessActionTypes.FetchAuthTokenSuccess),
    withLatestFrom(this.store.select(getControlSettings)),
    switchMap((action, controlSettings) => {
      // For now is control settings is already there, do nothing
      if (isEmpty(controlSettings)) {
        return []
      } else {
        return [
          new FetchControlSettings(), // Fetch control settings from the server
          new FetchHelpLinks()
        ]
      }
    })
  )

  @Effect()
  fetchUserDetails$: Observable<Action> = this.actions$.pipe(
    ofType<FetchUserDetails>(AccessActionTypes.FetchUserDetails),
    switchMap((action) => {
      try {
        return this.service.fetchUserDetail(action.password).pipe(
          mergeMap((res: any) => {
            return [new FetchUserDetailsSuccess(res.data)]
          }),
          catchError((err) =>
            from([new AuthError(err), new GlobalErrorAction(err, false, true)])
          )
        )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  @Effect()
  fetchUser$: Observable<Action> = this.actions$.pipe(
    ofType<FetchUser>(AccessActionTypes.FetchUser),
    switchMap((action) => {
      return this.service.fetchUser$().pipe(
        mergeMap((res: any) => {
          try {
            const userDetails = this.jsonConvert.deserializeObject(
              res.data,
              User
            )

            // Set the user id for static access
            ConfigService.userId = userDetails?.userId || -1

            return [
              new GlobalSuccessAction(res.message, userDetails, false, false),
              new SetUser(userDetails),
              new FetchClientInfo(),
              new FetchControlSettings(), // Fetch control settings from the server
              new FetchHelpLinks()
            ]
          } catch (e) {
            return [
              new GlobalErrorAction(
                new Error('Error de-serializing user details.'),
                true,
                true
              )
            ]
          }
        }),
        catchError((err) =>
          from([
            new GlobalErrorAction(err, true, true),
            new FetchUserError(this.errorService.getServerMessage(err))
          ])
        )
      )
    })
  )

  @Effect()
  fetchClientInfo$: Observable<Action> = this.actions$.pipe(
    ofType<FetchClientInfo>(AccessActionTypes.FetchClientInfo),
    switchMap((action) => {
      return this.service.fetchClientInfo$().pipe(
        mergeMap((res: any) => {
          try {
            const clientInfo = this.jsonConvert.deserializeObject(
              res.data,
              Client
            )
            return [
              new GlobalSuccessAction(res.message, clientInfo, false, false),
              new SetClientInfo(clientInfo)
            ]
          } catch (e) {
            return [
              new GlobalErrorAction(
                new Error('Error de-serializing client Info.'),
                true,
                true
              )
            ]
          }
        }),
        catchError((err) => from([new GlobalErrorAction(err, true, true)]))
      )
    })
  )

  @Effect()
  sendTwoFactorAuthenticationNotification$: Observable<Action> = this.actions$.pipe(
    ofType<SendTwoFactorAuthenticationNotification>(
      AccessActionTypes.SendTwoFactorAuthNotification
    ),
    switchMap((action) => {
      try {
        return this.service.sendNotification(action.UserId, action.resend).pipe(
          concatMap((res: boolean) => {
            if (res === true) {
              return [
                new SendTwoFactorAuthenticationNotificationSuccess(res),
                new RouterGo({ path: ['/auth/two_factor_authentication'] })
              ]
            } else {
              return [
                new GlobalErrorAction(
                  new Error(
                    "Two-factor authentication notification wasn't sent."
                  ),
                  false,
                  true
                ),
                new RouterGo({ path: ['/auth/two_factor_authentication'] })
              ]
            }
          }),
          catchError((err) => from([new GlobalErrorAction(err, true, true)]))
        )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  @Effect()
  verifyAuthenticationCode$: Observable<Action> = this.actions$.pipe(
    ofType<VerifyAuthenticationCode>(
      AccessActionTypes.VerifyAuthenticationCode
    ),
    switchMap((action) => {
      try {
        return this.service
          .verifyAuthenticationCode(
            action.verificationCode,
            action.rememberUser
          )
          .pipe(
            withLatestFrom(this.store.pipe(select(getUser))),
            mergeMap(([res, user]: [TwoFactorAuthResponseModel, UserModel]) => {
              if (res.isVerificationSuccessful) {
                return [new VerifyAuthenticationCodeSuccess(res)]
              } else {
                return [
                  new TwoFactorAuthenticationError(
                    new HttpErrorResponse({ error: res.message, status: 401 })
                  )
                ]
              }
            }),
            catchError((err) =>
              from([
                new TwoFactorAuthenticationError(err),
                new GlobalErrorAction(err, false, true)
              ])
            )
          )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  @Effect()
  setServerSession: Observable<Action> = this.actions$.pipe(
    ofType<SetServerSideSession>(AccessActionTypes.SetServerSession),
    switchMap((action) => {
      return this.service.setServerSession(action.sessions).pipe(
        mergeMap((res: any) => {
          if (action.isAddonChkBoxChecked) {
            return [
              new GlobalSuccessAction(
                'Session set successfully',
                res,
                false,
                false
              ),
              new NavigateToAction('/auth/installaddons', action.isInAppNav)
            ]
          } else {
            // use for external user to get project list with share project id store in externalUserProjectId when login in the backend
            // if have project id then not navigate to default page, will open in external-user-authentication
            const projectId = +localStorage.getItem('externalUserProjectId')
            if (projectId > 0) {
              return [
                new GlobalSuccessAction(
                  'Session set successfully',
                  res,
                  false,
                  false
                )
              ]
            } else {
              return [
                new GlobalSuccessAction(
                  'Session set successfully',
                  res,
                  false,
                  false
                ),
                ...(action.shouldRedirect !== false
                  ? [new NavigateToAction(action.urlPath, action.isInAppNav)]
                  : [])
              ]
            }
          }
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, true)]))
      )
    })
  )

  @Effect()
  sendResetPasswordLink: Observable<Action> = this.actions$.pipe(
    ofType<SendResetPasswordLink>(AccessActionTypes.SendResetPasswordLink),
    switchMap((action) => {
      return this.service.sendResetPasswordLink(action.userName).pipe(
        mergeMap((res: SendPasswordResetLinkResponse) => {
          return [
            new GlobalSuccessAction(
              'Password Reset link sent successfully',
              res,
              false,
              false
            ),
            new SendResetPasswordLinkSuccessful(res)
          ]
        }),
        catchError((err) =>
          from([
            new GlobalErrorAction(err, false, true),
            new SendResetPasswordLinkError(err)
          ])
        )
      )
    })
  )

  @Effect()
  changePassword$: Observable<Action> = this.actions$.pipe(
    ofType<ChangePassword>(AccessActionTypes.ChangePassword),
    switchMap((action) => {
      return this.service
        .changePassword(action.passwordModel, action.token)
        .pipe(
          mergeMap((res) => {
            if (res['data'].isPasswordResetSuccessful) {
              this.notificationService.showSuccess(
                'Password has been changed successfully.',
                true
              )
              return [
                new GlobalSuccessAction(
                  'Password has been changed successfully.',
                  res
                ),
                new ChangePasswordSuccess(res),
                new NavigateToAction('/login', true)
              ]
            } else {
              this.notificationService.showError(
                'Errors occurred while changing the password',
                true
              )
              return [
                new GlobalErrorAction(
                  new Error('Errors occurred while changing the password'),
                  false,
                  false
                )
              ]
            }
          }),
          catchError((err) => {
            return from([
              new GlobalErrorAction(err, true, false, true),
              new PasswordComplexity(err)
            ])
          })
        )
    })
  )

  // ---------------external user verify security code and auto login---------------------------------------
  @Effect()
  sendExtUserAuthNotification$: Observable<Action> = this.actions$.pipe(
    ofType<SendExtUserAuthNotification>(
      AccessActionTypes.SendExtUserAuthNotification
    ),
    switchMap((action) => {
      try {
        return this.service
          .sendExtUserAuthNotification(action.UserId, action.resend)
          .pipe(
            mergeMap((res: ExtUserAuthResponseModel) => {
              if (res.isVerificationSuccessful) {
                return [new SendExtUserAuthNotificationSuccess(res)]
              } else {
                return [
                  new ExtUserAuthError(
                    new HttpErrorResponse({ error: res.message, status: 401 })
                  )
                ]
              }
            }),
            catchError((err) =>
              from([
                new ExtUserAuthError(err),
                new GlobalErrorAction(err, false, true)
              ])
            )
          )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  /*
   * 1. verify code -> 2. Create AuthToken -> 3. User Detail (login) -> 4. User -> DONE (check User in component)
   */
  @Effect()
  verifyExtUserAuthCode$: Observable<Action> = this.actions$.pipe(
    ofType<VerifyExtUserAuthCode>(AccessActionTypes.VerifyExtUserAuthCode),
    switchMap((action) => {
      try {
        return this.service
          .verifyExtUserAuthCode(
            action.UserId,
            action.verificationCode,
            action.userToken,
            action.tokenType
          )
          .pipe(
            mergeMap((res: ExtUserAuthResponseModel) => {
              if (res.isVerificationSuccessful) {
                const login: Login = {
                  userName: res.userName || '',
                  password: res.token || '',
                  userNameForgotten: null
                }
                return [
                  new FetchExtUserAuthToken(login, res.token, action.projectId),
                  new VerifyExtUserAuthCodeSuccess(res)
                ]
              } else {
                return [
                  new ExtUserAuthError(
                    new HttpErrorResponse({ error: res.message, status: 401 })
                  )
                ]
              }
            }),
            catchError((err) =>
              from([
                new ExtUserAuthError(err),
                new GlobalErrorAction(err, false, true)
              ])
            )
          )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  @Effect()
  fetchExtUserAuthToken$: Observable<Action> = this.actions$.pipe(
    ofType<FetchExtUserAuthToken>(AccessActionTypes.FetchExtUserAuthToken),
    switchMap((action) => {
      try {
        return this.service.fetchToken(action.login).pipe(
          switchMap((res: ResponseModel) => {
            return [
              new FetchAuthTokenSuccess(res),
              new FetchExtUserDetails(action.token, action.projectId)
            ]
          }),
          catchError((err) =>
            from([new GlobalErrorAction(err, false, true), new AuthError(err)])
          )
        )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  @Effect()
  fetchExtUserDetails$: Observable<Action> = this.actions$.pipe(
    ofType<FetchExtUserDetails>(AccessActionTypes.FetchExtUserDetails),
    switchMap((action) => {
      try {
        return this.service
          .fetchExtUserDetail(action.token, action.projectId)
          .pipe(
            mergeMap((res: any) => {
              return [
                new FetchExtUser(res.data.UserId),
                new FetchUserDetailsSuccess(res.data)
              ]
            }),
            catchError((err) =>
              from([
                new AuthError(err),
                new GlobalErrorAction(err, false, true)
              ])
            )
          )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  @Effect()
  fetchExtUser$: Observable<Action> = this.actions$.pipe(
    ofType<FetchExtUser>(AccessActionTypes.FetchExtUser),
    switchMap((action) => {
      return this.service.fetchExtUser$(action.userId).pipe(
        mergeMap((res: any) => {
          try {
            const userDetails = this.jsonConvert.deserializeObject(
              res.data,
              User
            )

            AppStartupState.userDetail = new JsonConvert().deserializeObject(
              res.data,
              User
            )

            // Set the user id for static access
            ConfigService.userId = userDetails?.userId || -1

            return [
              new GlobalSuccessAction(res.message, userDetails, false, false),
              new SetUser(userDetails),
              new FetchClientInfo()
            ]
          } catch (e) {
            return [
              new GlobalErrorAction(
                new Error('Error de-serializing user details.'),
                true,
                true
              )
            ]
          }
        }),
        catchError((err) =>
          from([
            new GlobalErrorAction(err, true, true),
            new FetchUserError(this.errorService.getServerMessage(err))
          ])
        )
      )
    })
  )

  @Effect()
  fetchMaintenanceSetting$: Observable<Action> = this.actions$.pipe(
    ofType<FetchMaintenanceSetting>(AccessActionTypes.FetchMaintenanceSetting),
    switchMap((action) => {
      return this.service.fetchMaintenanceInfo$().pipe(
        mergeMap((res: ResponseModels) => {
          try {
            return [new SetMaintenanceSetting(res.data)]
          } catch (e) {
            return [
              new GlobalErrorAction(
                new Error('Error de-serializing setting Info.'),
                true,
                true
              )
            ]
          }
        }),
        catchError((err) => from([new GlobalErrorAction(err, true, true)]))
      )
    })
  )

  @Effect()
  sendHoldUserAuthNotification$: Observable<Action> = this.actions$.pipe(
    ofType<SendHoldUserAuthNotification>(
      AccessActionTypes.SendHoldUserAuthNotification
    ),
    switchMap((action) => {
      try {
        return this.service
          .sendHoldUserAuthNotification(action.custId, action.resend)
          .pipe(
            mergeMap((res: HoldUserAuthResponseModel) => {
              if (res.isVerificationSuccessful) {
                return [new SendHoldUserAuthNotificationSuccess(res)]
              } else {
                return [
                  new ExtUserAuthError(
                    new HttpErrorResponse({ error: res.message, status: 401 })
                  )
                ]
              }
            }),
            catchError((err) =>
              from([
                new ExtUserAuthError(err),
                new GlobalErrorAction(err, false, true)
              ])
            )
          )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  @Effect()
  verifyHoldUserAuthCode$: Observable<Action> = this.actions$.pipe(
    ofType<VerifyHoldUserAuthCode>(AccessActionTypes.VerifyHoldUserAuthCode),
    switchMap((action) => {
      try {
        return this.service
          .verifyHoldUserAuthCode(
            action.custId,
            action.verificationCode,
            action.userToken,
            action.tokenType
          )
          .pipe(
            mergeMap((res: HoldUserAuthResponseModel) => {
              if (res.isVerificationSuccessful) {
                const login: Login = {
                  userName: res.custName,
                  password: res.token,
                  userNameForgotten: null
                }
                return [
                  new FetchExtUserAuthToken(login, res.token, 0),
                  new VerifyHoldUserAuthCodeSuccess(res)
                ]
              } else {
                return [
                  new ExtUserAuthError(
                    new HttpErrorResponse({ error: res.message, status: 401 })
                  )
                ]
              }
            }),
            catchError((err) =>
              from([
                new ExtUserAuthError(err),
                new GlobalErrorAction(err, false, true)
              ])
            )
          )
      } catch (e) {
        return of(new GlobalErrorAction(e, false, true))
      }
    })
  )

  @Effect()
  emailUpdate$: Observable<Action> = this.actions$.pipe(
    ofType<EmailUpdate>(AccessActionTypes.EmailUpdate),
    switchMap((action) => {
      return this.service.emailUpdate(action.userId, action.emailAddress).pipe(
        mergeMap((res) => {
          if (res['data']) {
            return [
              new GlobalSuccessAction(
                'Email has been changed successfully.',
                res
              )
            ]
          } else {
            return [
              new GlobalErrorAction(
                new Error('Errors occurred while changing the email'),
                false,
                false
              )
            ]
          }
        }),
        catchError((err) => {
          return from([new GlobalErrorAction(err, true, false, true)])
        })
      )
    })
  )
  // ---------------external user verify security code and auto login---------------------------------------

  @Effect({ dispatch: false })
  logOut$: Observable<Action> = this.actions$.pipe(
    ofType<LogOut>(AccessActionTypes.LogOut),
    tap(() => {
      this.service.logout()
      this.store.dispatch(new ResetCachedProjectRightListAction())
    })
  )
}
