<div class="row mb-3" [formGroup]="parentForm">
  <div class="col-sm-4">
    <label>Enable EDAI ECA</label>
  </div>
  <div class="col-sm-12">
    <mat-radio-group class="d-flex" [formControlName]="controlNames.isEnabled">
      <mat-radio-button class="mr-4" [value]="true"> Yes </mat-radio-button>
      <mat-radio-button [value]="false"> No </mat-radio-button>
    </mat-radio-group>
  </div>
</div>

<div
  class="row mb-3 mx-0"
  [formGroup]="parentForm"
  *ngIf="getEnabledControl()?.value"
>
  <div class="d-flex flex-wrap w-100">
    <div class="col-12 col-md-4 mb-3 px-2">
      <label>Add Background <span class="text-danger">*</span></label>
      <textarea
        class="form-control"
        rows="5"
        [formControlName]="controlNames.background"
      ></textarea>
      <div *ngIf="isBackgroundInvalid" class="text-danger mt-1 small">
        Background is required
      </div>
    </div>
    <div class="col-12 col-md-4 mb-3 px-2">
      <label>Relevant Description <span class="text-danger">*</span></label>
      <textarea
        class="form-control"
        rows="5"
        [formControlName]="controlNames.relevantDesc"
      ></textarea>
      <div *ngIf="isRelevantInvalid" class="text-danger mt-1 small">
        Relevant description is required
      </div>
    </div>
    <div class="col-12 col-md-4 px-2">
      <label>Non-relevant Description <span class="text-danger">*</span></label>
      <textarea
        class="form-control"
        rows="5"
        [formControlName]="controlNames.nonRelevantDesc"
      ></textarea>
      <div *ngIf="isNonRelevantInvalid" class="text-danger mt-1 small">
        Non-relevant description is required
      </div>
    </div>
  </div>
</div>
