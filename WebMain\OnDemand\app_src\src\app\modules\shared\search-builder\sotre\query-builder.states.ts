import { HttpErrorResponse } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Action, State, StateContext } from '@ngxs/store'
import { ADVANCE_SEARCH_UI_TYPES, ResponseModel } from '@shared/models'
import { SearchBuilderService } from '@shared/services/advance-search.service'
import { forkJoin, Observable, of } from 'rxjs'
import { catchError, tap } from 'rxjs/operators'
import {
  CombinedRuleList,
  FilterItem,
  FilterTypes,
  TagRuleDescription,
  TagRuleList
} from '../query-builder-static.model'
import { QueryBuilderStateExtension } from './query-builder-state.extension'
import {
  INITIAL_QUERY_BUILDER_STATE,
  QueryBuilderStateModel
} from './query-builder-state.model'
import {
  ClearGeneratedGroupQueryAction,
  FetchJobsAction,
  FetchProcessingServersAction,
  FetchSavedSearchOnlyAction,
  FetchSearchUiSetting,
  fetchTagRules,
  SaveSearchUiSetting,
  SelectedSyntaxFromEditorAction,
  SetMasterFieldsAction,
  SetMasterFilterUisAction,
  SetSearchDupOption,
  SetSearchResultDupOption,
  StoreCompleteQuerySyntaxAction,
  StoreQueryFilterItemAction,
  TriggerResetAdvancedSearchUiAction,
  UpdateFilterSettingAction,
  UpdateRenderedUiAction
} from './query-builder.actions'

@State<QueryBuilderStateModel>({
  name: 'queryBuilder',
  defaults: INITIAL_QUERY_BUILDER_STATE
})
@Injectable()
export class QueryBuilderStates extends QueryBuilderStateExtension {
  constructor(private sbs: SearchBuilderService) {
    super()
  }

  /**
   * Combines tag rules with their corresponding rule definitions.
   *
   * @param tagRuleLists - Array of tag rules from the /tagRuleLists endpoint
   * @param rules - Array of rule definitions from the /rules endpoint
   * @returns A Map where the key is baseTag (tagId) and the value is the combined rule
   */
  #combineTagRules(
    tagRuleLists: TagRuleList[],
    rules: TagRuleDescription[]
  ): Record<number, CombinedRuleList> {
    return tagRuleLists.reduce((acc, tagRule) => {
      const rule = rules.find((rule) => tagRule.tagRuleId === rule.ruleId)
      acc[tagRule.baseTag] = {
        ...tagRule,
        ...rule
      } as unknown as CombinedRuleList
      return acc
    }, {} as Record<number, CombinedRuleList>)
  }

  #refreshQueryItemUuidMap(ctx: StateContext<QueryBuilderStateModel>): void {
    const allItems = ctx.getState().querySyntaxItems || []
    const querySyntaxItemUuidMap = allItems.reduce((idMap, { type, id }) => {
      idMap[type] = (idMap[type] || []).concat(id)
      return idMap
    }, {})
    ctx.patchState({ querySyntaxItemUuidMap })
  }

  @Action(UpdateFilterSettingAction)
  updateConfig(
    ctx: StateContext<QueryBuilderStateModel>,
    { payload }: UpdateFilterSettingAction
  ): void {
    // TODO: need to dematerialize based on filter type.
    switch (payload.appliedTo) {
      case FilterTypes.FIELD:
        ctx.patchState({ fieldSearchConfig: payload.settingData })
        break
    }
  }

  @Action(UpdateRenderedUiAction, { cancelUncompleted: true })
  ui(
    ctx: StateContext<QueryBuilderStateModel>,
    { payload }: UpdateRenderedUiAction
  ): void {
    ctx.patchState({ masterFilterUi: payload.filter((u) => !u.isExclude) })
  }

  @Action(SetMasterFilterUisAction, { cancelUncompleted: true })
  masterUi(
    ctx: StateContext<QueryBuilderStateModel>,
    { UIs }: SetMasterFilterUisAction
  ): void {
    ctx.patchState({ masterFilterUi: UIs.filter((u) => !u.isExclude) })
  }

  @Action(SetMasterFieldsAction)
  masterFields(
    ctx: StateContext<QueryBuilderStateModel>,
    { fields }: SetMasterFieldsAction
  ): void {
    // if we have no fields yet, just use all fields.
    if (!ctx.getState()?.fieldSearchConfig?.fields?.some((f) => f)) {
      ctx.patchState({
        fieldSearchConfig: { fields: fields.map((f) => f.FieldName) }
      })
    }
    ctx.patchState({ masterFields: fields })
  }

  @Action(SaveSearchUiSetting, { cancelUncompleted: true })
  async saveUi(
    ctx: StateContext<QueryBuilderStateModel>,
    { options }: SaveSearchUiSetting
  ): Promise<ResponseModel> {
    // NOTE: to get data from these states, we need to
    // apply changes on these state first and then we populate data for API payload.

    // populates from state once it get applied.
    const fields = ctx.getState().fieldSearchConfig?.fields
    const uis = ctx.getState().masterFilterUi
    const configValue = {
      searchUIs: uis
    }

    // since we have two types of advance search UI now, we need to maintain
    // both settings of user pref so we use FBI changes from here and existing from indexed db store
    // which was set during apply or fetch if available..
    const fromOtherFBIUI = await this.sbs._advSearchUiTable
      .where('type')
      .equals(ADVANCE_SEARCH_UI_TYPES.FBI)
      .first()
    const parsed = fromOtherFBIUI?.setting
      ? JSON.parse(fromOtherFBIUI?.setting)
      : null

    // saves settings as string in the backend side.
    const data = JSON.stringify({
      settingData: {
        // this place is to add/update default settings
        [ADVANCE_SEARCH_UI_TYPES.DEFAULT]: options?.isDefault
          ? null
          : configValue,
        // since the same column is responsible for other default UI, need to send that too.
        [ADVANCE_SEARCH_UI_TYPES.FBI]: parsed,
        searchFields: fields
      }
    })

    return this.sbs
      .saveUserPref<ResponseModel>({
        searchUiSetting: data
      })
      .pipe(
        tap(async () => {
          // we are storing it into the indexed DB so we can share across other pages.
          await this.sbs.addOrUpdateAdvUiTable({
            type: ADVANCE_SEARCH_UI_TYPES.DEFAULT,
            setting: options?.isDefault ? null : JSON.stringify(configValue),
            searchFields: options?.isDefault ? null : JSON.stringify(fields)
          })
        }),
        catchError((ex: HttpErrorResponse) => {
          return of({
            message:
              ex.error?.message ||
              'Unable to save user settings.' + ' ' + ex.status,
            status: 'Error'
          } as ResponseModel)
        })
      )
      .toPromise()
  }

  @Action(FetchSearchUiSetting)
  fetchUi(
    ctx: StateContext<QueryBuilderStateModel>
  ): Observable<ResponseModel> {
    return this.sbs.fetchUserPref<ResponseModel>().pipe(
      tap((res) => {
        const d = JSON.parse(res?.data ?? '')
        this.sbs.syncAdvanceSearchUiToTable(d)
        const item = d.settingData
        const defaultUi = item[ADVANCE_SEARCH_UI_TYPES.DEFAULT]
        if (item.searchFields) {
          ctx.patchState({
            fieldSearchConfig: { fields: item.searchFields }
          })
        }
        if (!defaultUi) return

        if (defaultUi.searchUIs) {
          ctx.patchState({
            masterFilterUi: defaultUi.searchUIs
          })
        }
      }),
      catchError((ex: HttpErrorResponse) => {
        return of({
          message:
            ex.error?.message ||
            'Unable to save user settings.' + ' ' + ex.status,
          status: 'Error'
        } as ResponseModel)
      })
    )
  }

  @Action(SetSearchDupOption, { cancelUncompleted: true })
  setSearchDupOption(
    ctx: StateContext<QueryBuilderStateModel>,
    action: SetSearchDupOption
  ) {
    ctx.patchState({ searchDuplicateOption: action?.searchDuplicateOption })
  }

  @Action(SetSearchResultDupOption, { cancelUncompleted: true })
  setSearchResultDupOption(
    ctx: StateContext<QueryBuilderStateModel>,
    action: SetSearchResultDupOption
  ) {
    ctx.patchState({
      searchResultDuplicateOption: action?.searchResultDuplicateOption
    })
  }

  @Action(FetchProcessingServersAction)
  fetchProcessingServer(
    ctx: StateContext<QueryBuilderStateModel>,
    { projectId, enumIndex }: FetchProcessingServersAction
  ): Observable<ResponseModel> {
    const defaultValue = 'Any'
    return this.sbs
      .fetchProcessingServers<ResponseModel>(projectId, enumIndex)
      .pipe(
        tap((res) => {
          ctx.patchState({ processingServers: res.data || [defaultValue] })
        }),
        catchError(() => {
          ctx.patchState({ processingServers: [defaultValue] })
          return of(undefined)
        })
      )
  }

  @Action(FetchJobsAction)
  fetchJObs(
    ctx: StateContext<QueryBuilderStateModel>,
    { queryParam, projectId }: FetchJobsAction
  ): Observable<ResponseModel> {
    return this.sbs.fetchJobs<ResponseModel>(projectId, queryParam).pipe(
      tap((res) => {
        ctx.patchState({ jobs: res.data || [] })
      }),
      catchError(() => {
        ctx.patchState({ processingServers: [] })
        return of(undefined)
      })
    )
  }

  @Action(TriggerResetAdvancedSearchUiAction)
  triggerResetAdvancedSearchUi(
    ctx: StateContext<QueryBuilderStateModel>,
    { isResetAdvancedSearchUi }: TriggerResetAdvancedSearchUiAction
  ): void {
    ctx.patchState({
      isResetAdvancedSearchUi,
      querySyntaxItems: [],
      completeQuerySyntax: null
    })
    this.#refreshQueryItemUuidMap(ctx)
  }

  @Action(SelectedSyntaxFromEditorAction)
  selectedSyntaxFromEditor(
    ctx: StateContext<QueryBuilderStateModel>,
    { selectedSyntaxFromEditor }: SelectedSyntaxFromEditorAction
  ): void {
    ctx.patchState({ selectedSyntaxFromEditor })
  }

  @Action(ClearGeneratedGroupQueryAction)
  clearQueryFromGroup(
    ctx: StateContext<QueryBuilderStateModel>,
    { uuids }: ClearGeneratedGroupQueryAction
  ): void {
    const allItems = ctx.getState().querySyntaxItems || []
    const querySyntaxItems = allItems.filter((c) => !uuids.includes(c.id))
    ctx.patchState({ querySyntaxItems })
    this.#refreshQueryItemUuidMap(ctx)
  }

  @Action(StoreCompleteQuerySyntaxAction, { cancelUncompleted: true })
  storeGeneratedCompleteSyntax(
    ctx: StateContext<QueryBuilderStateModel>,
    { completeQuerySyntax }: StoreCompleteQuerySyntaxAction
  ): void {
    ctx.patchState({ completeQuerySyntax })
  }

  #removeOperatorPrefixFromFirstItem(items: FilterItem): FilterItem {
    const firstItem = items
    if (!firstItem) return items

    const wordsToRemove = ['or', 'and']
    const wordsToReplace = ['or not', 'and not']

    const regexPatternWithoutNot = new RegExp(
      `^\\s*(${wordsToRemove.join('|')})\\s+`,
      'i'
    )
    const regexPatternWithNot = new RegExp(
      `^\\s*(${wordsToReplace.join('|')})\\s+`,
      'i'
    )

    return {
      ...firstItem,
      query: firstItem.query
        .replace(regexPatternWithNot, 'NOT ')
        .replace(regexPatternWithoutNot, '')
        .trim()
    }
  }

  /**
   * Finds the index of the matching closing parenthesis for an opening parenthesis
   * at a given index, respecting string literals (single and double quotes)
   * and escape characters within those literals.
   *
   * @param str The string to search within.
   * @param openParenIndex The index of the opening parenthesis '('.
   * @returns The index of the matching closing parenthesis ')' or -1 if not found
   *          or if str[openParenIndex] is not '('.
   */
  #findMatchingParenIndex(str: string, openParenIndex: number): number {
    if (
      openParenIndex < 0 ||
      openParenIndex >= str.length ||
      str[openParenIndex] !== '('
    ) {
      return -1
    }

    let balance = 0
    let inDoubleQuote = false
    let inSingleQuote = false
    let escapeNextChar = false

    for (let i = openParenIndex; i < str.length; i++) {
      const char = str[i]

      if (escapeNextChar) {
        escapeNextChar = false
        // This character is escaped, treat as literal, no effect on balance or quote state.
      } else if (char === '\\') {
        escapeNextChar = true
      } else if (char === '"' && !inSingleQuote) {
        inDoubleQuote = !inDoubleQuote
      } else if (char === "'" && !inDoubleQuote) {
        inSingleQuote = !inSingleQuote
      } else if (!inDoubleQuote && !inSingleQuote) {
        // Only process parentheses if not inside any string literal
        if (char === '(') {
          balance++
        } else if (char === ')') {
          balance--
          if (balance === 0) {
            // Found the matching closing parenthesis for the one at openParenIndex
            return i
          }
        }
      }
    }
    return -1 // No matching closing parenthesis found
  }

  /**
   * Simplifies expressions by iteratively removing redundant parentheses.
   * A pair of parentheses `(Outer ... )Outer` is considered redundant if its content,
   * after trimming whitespace, is itself a single, complete parenthesized expression `(Inner ... )Inner`.
   * In such cases, `(Outer ... )Outer` (including any whitespace between the outer
   * and inner parentheses) is replaced by `(Inner ... )Inner`.
   * This process handles cases like `( (E) )` becoming `(E)`.
   * Parentheses crucial for logical grouping (e.g., `(A AND B) OR C`) are preserved.
   *
   * @param expression The input string expression.
   * @returns The expression string with redundant parentheses removed.
   */
  #simplifyParentheses(expression: string): string {
    let currentExpression = expression
    let madeChangeInThisPass: boolean

    do {
      madeChangeInThisPass = false
      let i = 0 // Scanner index for the currentExpression

      while (i < currentExpression.length) {
        if (currentExpression[i] === '(') {
          const openOuterIndex = i
          const closeOuterIndex = this.#findMatchingParenIndex(
            currentExpression,
            openOuterIndex
          )

          if (closeOuterIndex === -1) {
            // This indicates a malformed expression (unbalanced parentheses)
            // or the opening parenthesis was inside a string literal that
            // findMatchingParenIndex did not start parsing from (e.g. scan started mid-string).
            // For robustness, we'll just advance. If the input is guaranteed
            // to be well-formed, this branch might be less critical.
            i++
            continue
          }

          // Extract content strictly *between* the outer parentheses
          const content = currentExpression.substring(
            openOuterIndex + 1,
            closeOuterIndex
          )
          const trimmedContent = content.trim()

          // Check if trimmedContent is itself a single, balanced parenthesized expression
          if (
            trimmedContent.length > 0 && // Must not be empty, e.g. "()" -> content=""
            trimmedContent[0] === '('
          ) {
            // Find the matching ')' for trimmedContent's opening '(' *within trimmedContent itself*
            const closeInnerIndexInTrimmed = this.#findMatchingParenIndex(
              trimmedContent,
              0
            )

            // If this ')' is the very last character of trimmedContent,
            // it means trimmedContent is of the form (E_inner).
            if (closeInnerIndexInTrimmed === trimmedContent.length - 1) {
              // Redundancy found: The outer parentheses (and surrounding whitespace)
              // are redundant around trimmedContent.
              // Replace the substring from openOuterIndex to closeOuterIndex (inclusive)
              // with trimmedContent.
              // e.g., (OuterWS (Inner E_inner) InnerWS )Outer becomes (Inner E_inner)Inner

              const prefix = currentExpression.substring(0, openOuterIndex)
              const suffix = currentExpression.substring(closeOuterIndex + 1)

              currentExpression = prefix + trimmedContent + suffix
              madeChangeInThisPass = true

              // Restart scan from the beginning of the (now modified) string
              // because the modification might have enabled further simplifications
              // or shifted indices of subsequent characters.
              break // Exit the inner `while (i < ...)` loop to restart the pass
            }
          }
        }
        i++ // Move to the next character if no change or rule application occurred at current 'i'
      }
    } while (madeChangeInThisPass) // Loop again if any change was made in the entire pass

    return currentExpression
  }

  @Action(StoreQueryFilterItemAction)
  addUpdateQueryFilterItem(
    ctx: StateContext<QueryBuilderStateModel>,
    { queryFilterItem }: StoreQueryFilterItemAction
  ): void {
    if (!queryFilterItem) return

    const existingQuery = ctx.getState().completeQuerySyntax

    let finalQuery = ''

    if (!existingQuery?.trim()) {
      finalQuery =
        this.#removeOperatorPrefixFromFirstItem(queryFilterItem).query
    } else {
      finalQuery = `(${existingQuery}) ${queryFilterItem.query}`
    }
    const completeQuerySyntax = this.#simplifyParentheses(finalQuery)
    ctx.patchState({ completeQuerySyntax })
    this.#refreshQueryItemUuidMap(ctx)
  }

  @Action(FetchSavedSearchOnlyAction)
  fetchSavedSearchOnly(
    ctx: StateContext<QueryBuilderStateModel>,
    { projectId }: FetchSavedSearchOnlyAction
  ): Observable<ResponseModel> {
    return this.sbs.fetchSavedSearchOnly<ResponseModel>(projectId).pipe(
      tap((data) => {
        ctx.patchState({ savedSearchData: data?.data?.searchHistory })
      }),
      catchError(() => {
        ctx.patchState({ savedSearchData: null })
        return of(undefined)
      })
    )
  }

  @Action(fetchTagRules)
  fetchTagRules(
    ctx: StateContext<QueryBuilderStateModel>,
    { projectId }: fetchTagRules
  ): Observable<ResponseModel> {
    // Fetch both tag rules and rule definitions
    return forkJoin({
      tagRulesResponse: this.sbs.fetchTagRules<ResponseModel>(projectId),
      tagRuleListResponse: this.sbs.fetchTagRuleList<ResponseModel>(projectId)
    }).pipe(
      tap(({ tagRulesResponse, tagRuleListResponse }) => {
        const tagRules: TagRuleDescription[] = tagRulesResponse.data
        const tagRuleList: TagRuleList[] = tagRuleListResponse.data

        // Proceed with combining the data
        const combinedRules = this.#combineTagRules(tagRuleList, tagRules)

        ctx.patchState({ tagRuleList: combinedRules })
      }),
      catchError((error) => {
        // Handle error appropriately
        ctx.patchState({ tagRuleList: [] })
        // You can dispatch an error action or return an observable of undefined
        return of(undefined)
      })
    )
  }
}
