import { ProjectSetting } from '@root/modules/launchpad/models/case-template-settings.model'

/**
 * Form control names used within this component
 * Centralizing these to avoid string duplication and typos
 */
export const ControlNames = {
  /**
   * Enable EDAI ECA control name `ENABLE_EDAI_ECA`
   */
  isEnabled: ProjectSetting.ENABLE_EDAI_ECA,
  /**
   * Background description control name `EDAI_ECA_BACKGROUND`
   */
  background: ProjectSetting.EDAI_ECA_BACKGROUND,
  /**
   * Relevant description control name `EDAI_ECA_RELEVANT_DESCRIPTION`
   */
  relevantDesc: ProjectSetting.EDAI_ECA_RELEVANT_DESCRIPTION,
  /**
   * Non-relevant description control name `EDAI_ECA_NON_RELEVANT_DESCRIPTION`
   */
  nonRelevantDesc: ProjectSetting.EDAI_ECA_NON_RELEVANT_DESCRIPTION
}
