import { HttpErrorResponse } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Action, State, StateContext } from '@ngxs/store'
import { ResponseModel } from '@shared/models'
import { Observable, of } from 'rxjs'
import { catchError, tap } from 'rxjs/operators'
import { SamlIdpServerSettingsService } from '../../services'
import {
  initialSamlIdpServerState,
  samlIdpServerStateFeatureKey,
  SamlIdpServerStateModel
} from './saml-idp-server-state.model'
import {
  ApplySamlIdpServerSettingAction,
  FetchSamlIdpGroupsAction,
  FetchSamlIdpServerSettingAction,
  FetchSamlIdpServerSettingParseFromXmlMetafileAction,
  FetchSamlIdpUsersAction,
  GetIdPGroupMappingSettingsStatusAction,
  InsertSamlIdpGroupsAction,
  SamlIdpServerSettingStateCleanupAction,
  StoreSamlIdpServerSettingAction
} from './saml-idp-server.actions'

declare let decryptStr: any

@State<SamlIdpServerStateModel>({
  name: samlIdpServerStateFeatureKey,
  defaults: initialSamlIdpServerState
})
@Injectable()
export class SamlIdpServerStates {
  constructor(
    private samlIdpServerSettingsService: SamlIdpServerSettingsService
  ) {}

  /**
   *  reset to initial
   */
  @Action(SamlIdpServerSettingStateCleanupAction)
  cleanup(
    ctx: StateContext<SamlIdpServerStateModel>,
    { stateKey }: SamlIdpServerSettingStateCleanupAction
  ): void {
    if (Array.isArray(stateKey)) {
      stateKey.forEach((key) =>
        ctx.patchState({ [key]: initialSamlIdpServerState[key] })
      )
    } else {
      ctx.patchState({ [stateKey]: initialSamlIdpServerState[stateKey] })
    }
  }

  @Action(StoreSamlIdpServerSettingAction)
  storeSamlServer(
    ctx: StateContext<SamlIdpServerStateModel>,
    { payload: { samlIdpSettings, type } }: StoreSamlIdpServerSettingAction
  ): void {
    const state = ctx.getState().storeSamlIdpServerSetting
    ctx.patchState({
      storeSamlIdpServerSetting: {
        ...state,
        [type]: {
          ...state?.[type],
          ...samlIdpSettings
        }
      }
    })
  }

  @Action(FetchSamlIdpServerSettingParseFromXmlMetafileAction)
  parseXmlMetafile(
    ctx: StateContext<SamlIdpServerStateModel>,
    { xmlMetafile }: FetchSamlIdpServerSettingParseFromXmlMetafileAction
  ): Observable<ResponseModel> {
    return this.samlIdpServerSettingsService
      .fetchParseFromXmlMetafile<ResponseModel>(xmlMetafile)
      .pipe(
        tap((res) => {
          ctx.patchState({
            fetchSamlSettingParseXmlMetafileResponse: res
          })
        }),
        catchError((ex: HttpErrorResponse) => {
          ctx.patchState({
            fetchSamlSettingParseXmlMetafileResponse: ex.error
          })
          return of(undefined)
        })
      )
  }

  @Action(FetchSamlIdpGroupsAction)
  fetchGroups(
    ctx: StateContext<SamlIdpServerStateModel>,
    { payload }: FetchSamlIdpGroupsAction
  ): Observable<ResponseModel> {
    return this.samlIdpServerSettingsService
      .fetchIdpGroups<ResponseModel>(payload)
      .pipe(
        tap((res) => {
          ctx.patchState({
            samlIdpGroupsResponse: res
          })
        }),
        catchError((ex: HttpErrorResponse) => {
          ctx.patchState({
            samlIdpGroupsResponse: ex.error
          })
          return of(undefined)
        })
      )
  }

  @Action(InsertSamlIdpGroupsAction)
  insertGroups(
    ctx: StateContext<SamlIdpServerStateModel>,
    { payload }: InsertSamlIdpGroupsAction
  ): Observable<ResponseModel> {
    return this.samlIdpServerSettingsService
      .insertIdpGroups<ResponseModel>(payload)
      .pipe(
        tap((res) => {
          ctx.patchState({
            samlIdpGroupsResponse: res
          })
        }),
        catchError((ex: HttpErrorResponse) => {
          ctx.patchState({
            samlIdpGroupsResponse: ex.error
          })
          return of(undefined)
        })
      )
  }

  @Action(FetchSamlIdpServerSettingAction, { cancelUncompleted: true })
  fetchSamlServer(
    ctx: StateContext<SamlIdpServerStateModel>,
    action: FetchSamlIdpServerSettingAction
  ): Observable<ResponseModel> {
    return this.samlIdpServerSettingsService
      .fetchSamlIdpServerSettings<ResponseModel>(action.clientId)
      .pipe(
        tap((res) => {
          if (res?.data?.ssoToken) {
            const decryptedToken = decryptStr(
              res.data.ssoToken,
              'f0e1d2c3b4a5968778695a4b3c2d1f0e'
            )

            res.data.ssoToken = decryptedToken

            if (res?.data?.idPGroupRequest) {
              res.data.idPGroupRequest.token = decryptedToken
            }
          }
          ctx.patchState({ fetchSamlSettingResponse: res })
        }),
        catchError((ex: HttpErrorResponse) => {
          ctx.patchState({ fetchSamlSettingResponse: ex.error })
          return of(undefined)
        })
      )
  }

  @Action(ApplySamlIdpServerSettingAction, { cancelUncompleted: true })
  applySamlServer(
    ctx: StateContext<SamlIdpServerStateModel>,
    { payload }: ApplySamlIdpServerSettingAction
  ): Observable<ResponseModel> {
    return this.samlIdpServerSettingsService
      .applySamlIdpServerSettings<ResponseModel>(payload)
      .pipe(
        tap((res) => {
          ctx.patchState({ applySamlSettingResponse: res })
        }),
        catchError((ex: HttpErrorResponse) => {
          ctx.patchState({ applySamlSettingResponse: ex.error })
          return of(undefined)
        })
      )
  }

  @Action(FetchSamlIdpUsersAction)
  fetchUserss(
    ctx: StateContext<SamlIdpServerStateModel>,
    { payload }: FetchSamlIdpUsersAction
  ): Observable<ResponseModel> {
    return this.samlIdpServerSettingsService
      .fetchIdpUsers<ResponseModel>(payload)
      .pipe(
        tap((res) => {
          ctx.patchState({
            samlIdpUsersResponse: res
          })
        }),
        catchError((ex: HttpErrorResponse) => {
          ctx.patchState({
            samlIdpUsersResponse: ex.error
          })
          return of(undefined)
        })
      )
  }

  @Action(GetIdPGroupMappingSettingsStatusAction)
  GetIdPGroupMappingSettingsStatus(
    ctx: StateContext<SamlIdpServerStateModel>,
    action: GetIdPGroupMappingSettingsStatusAction
  ): Observable<ResponseModel> {
    return this.samlIdpServerSettingsService
      .GetIdPGroupMappingSettingsStatus<ResponseModel>()
      .pipe(
        tap((res) => {
          ctx.patchState({
            samlIdPGroupMappingSettingsStatusResponse: res
          })
        }),
        catchError((ex: HttpErrorResponse) => {
          ctx.patchState({
            samlIdPGroupMappingSettingsStatusResponse: ex.error
          })
          return of(undefined)
        })
      )
  }
}
