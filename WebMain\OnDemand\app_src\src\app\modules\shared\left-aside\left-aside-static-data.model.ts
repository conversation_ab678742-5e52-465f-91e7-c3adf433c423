import { Uuidv4Generator } from '@shared/utils/uuid-generator'
import { UserRights } from '../../../helpers/user-rights'

/**
 * Left aside data model
 */
export interface AsideDataModel {
  /**
   * Element unique Id
   */
  uuid?: string

  /**
   * Link display text
   */
  title: string

  /**
   * Angular or external link
   */
  link?: string

  /**
   * Icon to render, Currently using material icon.1
   */
  icon?: string

  disabled?: boolean

  /**
   * Children of this element
   */
  children?: AsideDataModel[]

  allowedPermission?: UserRights
}

/**
 * Static class to handle left aside data
 */
export class LeftAside {
  /**
   * Returns left aside data
   */
  static get NavData(): AsideDataModel[] {
    // add uuid recursively
    function addUuid(d: AsideDataModel[]) {
      return (d = d.map((el) => ({
        ...el,
        uuid: new Uuidv4Generator().uuid,
        children: el.children ? addUuid(el.children) : null
      })))
    }

    // add uuid and return mutated data
    return LeftAside.items.slice().map((el) => ({
      ...el,
      uuid: new Uuidv4Generator().uuid,
      children: el.children ? addUuid(el.children) : null
    }))
  }

  /**
   * Static data storage/source.
   */
  private static items: AsideDataModel[] = [
    // {
    //   title: 'Summary',
    //   link: '/admin/dashboard',
    //   icon: 'av_timer'
    // },
    {
      title: 'System Admin',
      icon: 'supervisor_account',
      link: '/admin/system/dashboard',
      children: [
        {
          title: 'User',
          icon: 'group_add',
          link: '/admin/system/user',
          children: [
            {
              title: 'Create',
              link: '/admin/system/user/create'
            },
            {
              title: 'Manage',
              link: '/admin/system/user/manage'
            }
          ]
        },
        {
          title: 'Notification',
          icon: 'notifications',
          link: '/admin/system/notification',
          allowedPermission: UserRights.ALLOW_TO_MANAGE_NOTIFICATION,
          children: [
            {
              title: 'Manage',
              link: '/admin/system/notification/manage'
            },
            {
              title: 'Manual Notification',
              link: '/admin/system/notification/manual-notification'
            }
          ]
        },
        {
          title: 'Role',
          icon: 'person_pin',
          link: '/admin/system/role',
          children: [
            {
              title: 'Create',
              link: '/admin/system/role/create'
            },
            {
              title: 'Manage',
              link: '/admin/system/role/manage'
            }
          ]
        },
        {
          title: 'Template',
          icon: 'my_library_books',
          children: [
            {
              title: 'Create',
              children: [
                {
                  title: 'Case Template',
                  link: '/admin/case/create/template/casesetting'
                },
                {
                  title: 'Review Set',
                  link: '/admin/system/template/create/review-set'
                },
                {
                  title: 'Production Field',
                  link: '/admin/system/template/create/production-field'
                }
              ]
            },
            {
              title: 'Manage',
              children: [
                {
                  title: 'Case Template',
                  link: '/admin/system/template/manage/manage-case-template'
                },
                {
                  title: 'Review Set',
                  link: '/admin/system/template/manage/review-set'
                },
                {
                  title: 'Production Field',
                  link: '/admin/system/template/manage/production-field'
                }
              ]
            }
          ]
        },
        {
          title: 'Layout',
          icon: 'tune',
          link: '/admin/system/layout',
          children: [
            {
              title: 'Create',
              link: '/admin/system/layout/create'
            },
            {
              title: 'Manage',
              link: '/admin/system/layout/manage'
            }
          ]
        },
        {
          title: 'Dynamic Folder',
          icon: 'folder',
          link: '/admin/system/dynamic-folder',
          children: [
            {
              title: 'Manage',
              link: '/admin/system/dynamic-folder/manage'
            }
          ]
        },
        {
          title: 'System Maintenance',
          icon: 'settings',
          link: '/admin/system/maintenance',
          children: [
            {
              title: 'Manage',
              link: '/admin/system/maintenance/manage'
            }
          ]
        },
        {
          title: 'Distributed Service',
          icon: 'build',
          link: '/admin/system/distributed/service',
          allowedPermission: UserRights.ALLOW_TO_MANAGE_DISTRIBUTED_SERVICE
        },
        {
          title: 'Job',
          icon: 'view_module',
          link: '/admin/system/job',
          children: [
            {
              title: 'Status',
              link: '/admin/system/job/status'
            },
            {
              title: 'Upload Status',
              link: '/admin/system/job/upload-status',
              allowedPermission: UserRights.ALLOW_TO_VIEW_CLIENT_DATA_TRANSFER
            }
          ]
        },
        {
          title: 'Audit History',
          icon: 'assignment',
          link: '/admin/system/audit-history'
        },
        {
          title: 'SlipSheet Template',
          icon: 'settings_applications',
          link: '/admin/system/slipsheet-template-settings',
          children: [
            {
              title: 'Create',
              link: '/admin/system/slipsheet-template-settings/create'
            },
            {
              title: 'Manage',
              link: '/admin/system/slipsheet-template-settings/manage'
            }
          ]
        },
        {
          title: 'Workflow Rule',
          icon: 'assignment_turned_in',
          link: '/admin/system/workflow-rule',
          children: [
            {
              title: 'Manage Plugin',
              link: '/admin/system/workflow-rule/manage-plugin'
            },
            {
              title: 'Manage Rule',
              link: '/admin/system/workflow-rule/manage-rule'
            }
          ]
        },
        {
          title: 'System Log',
          icon: 'event_note',
          link: '/admin/system/system-log'
        },
        {
          title: 'Billing',
          icon: 'attach_money',
          link: '/admin/system/billing',
          allowedPermission: UserRights.ALLOW_TO_GENERATE_BILLING_REPORT
        },
        {
          title: 'Repository',
          icon: 'view_list',
          link: '/admin/system/repository',
          children: [
            {
              title: 'Create',
              link: '/admin/system/repository/create'
            },
            {
              title: 'Manage',
              link: '/admin/system/repository/manage'
            }
          ]
        },
        {
          title: 'Email Alert Setup',
          icon: 'email',
          link: '/admin/system/smtp'
        },
        {
          title: 'Query Builder',
          icon: 'share',
          link: '/admin/system/query-editor',
          allowedPermission: UserRights.ALLOW_TO_ACCESS_QUERY_BUILDER
        },
        {
          title: 'Database Server',
          icon: 'dns',
          link: '/admin/system/database',
          allowedPermission: UserRights.CONFIGURE_DATABASE_SERVER
        },
        {
          title: 'Environment Settings',
          icon: 'room_preferences',
          link: '/admin/system/environment-settings'
        },
        {
          title: 'Search Server',
          icon: 'search',
          link: '/admin/system/search-server',
          children: [
            {
              title: 'Create',
              link: '/admin/system/search-server/create'
            },
            {
              title: 'Manage',
              link: '/admin/system/search-server/manage'
            }
          ]
        },
        {
          title: 'Client Management',
          icon: 'supervisor_account',
          link: '/admin/system/client-management',
          children: [
            {
              title: 'Create',
              link: '/admin/system/client-management/create'
            },
            {
              title: 'Manage',
              link: '/admin/system/client-management/manage'
            }
          ]
        },
        {
          title: 'Client Configuration',
          icon: 'settings_input_composite',
          children: [
            {
              title: 'Create',
              children: [
                {
                  title: 'AWS S3 Storage',
                  link: '/admin/system/client-configuration/create/aws-s3-storage'
                }
              ]
            },
            {
              title: 'Manage',
              children: [
                {
                  title: 'AWS S3 Storage',
                  link: '/admin/system/client-configuration/manage/aws-s3-storage'
                }
              ]
            }
          ]
        },
        {
          title: 'Connector',
          icon: 'cable',
          link: '/admin/system/connector',
          children: [
            {
              title: 'Create',
              children: [
                {
                  title: 'Environment',
                  link: '/admin/system/connector/create/environment'
                },
                {
                  title: 'Template',
                  link: '/admin/system/connector/create/template'
                }
              ]
            },
            {
              title: 'Manage',
              children: [
                {
                  title: 'Environment',
                  link: '/admin/system/connector/manage/environment'
                },
                {
                  title: 'Template',
                  link: '/admin/system/connector/manage/template'
                }
              ]
            }
          ]
        },
        {
          title: 'Login Management',
          icon: 'account_circle',
          link: '/admin/system/login-management',
          children: [
            {
              title: 'Login Setting',
              link: '/admin/system/login-management/login-setting'
            },
            {
              title: 'AD Login Setting',
              link: '/admin/system/login-management/ad-login-setting'
            },
            {
              title: 'SAML IDP Server Setting',
              link: '/admin/system/login-management/idp-server-setting'
            }
          ]
        },
        {
          title: 'Global Group',
          icon: 'verified_user',
          link: '/admin/system/global-group',
          children: [
            {
              title: 'Manage Rights',
              link: '/admin/system/global-group/manage-rights'
            },
            {
              title: 'Manage Fields',
              link: '/admin/system/global-group/manage-fields'
            }
          ]
        },
        {
          title: 'Spam Database',
          icon: 'storage',
          link: '/admin/system/spam-database',
          children: [
            {
              title: 'Update',
              link: '/admin/system/spam-database/update'
            }
          ],
          allowedPermission: UserRights.UPDATE_SPAM_DATABASE
        },
        {
          title: 'Global Production Field',
          icon: 'language',
          link: '/admin/system/global-production-field',
          children: [
            {
              title: 'Create',
              link: '/admin/system/global-production-field/create'
            },
            {
              title: 'Manage',
              link: '/admin/system/global-production-field/manage'
            }
          ]
        },
        {
          title: 'Terms of Service',
          icon: 'build',
          link: '/admin/system/terms-of-service'
        },
        {
          title: 'Privacy Policy',
          icon: 'shield',
          link: '/admin/system/privacy-policy'
        }
      ]
    },

    {
      title: 'Case Admin',
      link: '/admin/case/dashboard',
      icon: 'business_center',
      children: [
        {
          title: 'Case',
          icon: 'work',
          children: [
            {
              title: 'Create',
              link: '/admin/case/create/casesetting'
            },
            {
              title: 'Manage',
              link: '/admin/case/manage'
            },
            {
              title: 'Storage Report',
              link: '/admin/case/storage-report'
            }
          ]
        },
        {
          title: 'User Group',
          icon: 'group',
          children: [
            {
              title: 'Create',
              link: '/admin/case/user-group/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/user-group/manage'
            }
          ],
          allowedPermission: UserRights.ALLOW_TO_MANAGE_GROUP_ROLE_RIGHT
        },
        {
          title: 'Review Set',
          icon: 'pageview',
          children: [
            {
              title: 'Create',
              link: '/admin/case/review-set/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/review-set/manage'
            },
            {
              title: 'Dashboard',
              link: '/admin/case/review-set/dashboard'
            },
            {
              title: 'Reviewer Dashboard',
              link: '/admin/case/review-set/reviewer-dashboard'
            }
          ]
        },
        {
          title: 'Redaction Set',
          // link: '/admin/case/redaction-set',
          icon: 'view_agenda',
          children: [
            {
              title: 'Create',
              link: '/admin/case/redaction-set/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/redaction-set/manage'
            }
          ]
        },
        {
          title: 'Field',
          icon: 'insert_invitation',
          children: [
            // {
            //   title: 'Summary',
            //   link: '/admin/case/custom-field',
            // },
            {
              title: 'Create',
              link: '/admin/case/custom-field/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/custom-field/manage'
            },
            {
              title: 'Security',
              link: '/admin/case/custom-field/security'
            }
          ]
        },
        {
          title: 'Tag',
          icon: 'loyalty',
          link: '/admin/case/tag',
          children: [
            {
              title: 'Create',
              link: '/admin/case/tag/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/tag/manage'
            },
            {
              title: 'Propagation Settings',
              link: '/admin/case/tag/propagation-setting'
            },
            {
              title: 'Security',
              link: '/admin/case/tag/security'
            },
            {
              title: 'Import',
              link: '/admin/case/tag/import'
            },
            {
              title: 'Tag Rules',
              link: '/admin/case/tag/tagRules'
            }
          ]
        },
        {
          title: 'Document Share',
          icon: 'share',
          link: '/admin/case/doc-share',
          children: [
            {
              title: 'Manage',
              link: '/admin/case/doc-share/manage'
            }
          ]
        },
        {
          title: 'Highlight',
          icon: 'brush',
          link: '/admin/case/highlight',
          children: [
            {
              title: 'Create',
              link: '/admin/case/highlight/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/highlight/manage'
            }
          ]
        },
        {
          title: 'Sampling',
          icon: 'shuffle',
          link: '/admin/case/sampling',
          children: [
            {
              title: 'Create',
              link: '/admin/case/sampling/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/sampling/manage'
            }
          ]
        },
        {
          title: 'Transcript',
          icon: 'insert_drive_file',
          children: [
            {
              title: 'Manage',
              link: '/admin/case/transcript/manage'
            }
          ]
        },
        {
          title: 'Custodian',
          icon: 'perm_media',
          link: '/admin/case/custodian',
          children: [
            {
              title: 'Create',
              link: '/admin/case/custodian/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/custodian/manage'
            }
          ]
        },
        {
          title: 'Entity',
          icon: 'assignment',
          link: '/admin/case/custom-entity',
          children: [
            {
              title: 'Create',
              link: '/admin/case/custom-entity/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/custom-entity/manage'
            }
          ]
        },
        {
          title: 'Folder',
          icon: 'folder',
          link: '/admin/case/folder',
          children: [
            {
              title: 'Create',
              link: '/admin/case/folder/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/folder/manage'
            },
            {
              title: 'Auto Folder',
              link: '/admin/case/folder/auto-folder'
            }
          ]
        },
        {
          title: 'Password Bank',
          icon: 'vpn_key',
          link: '/admin/case/password-bank'
        },
        {
          title: 'Move Documents',
          icon: 'screen_share',
          link: '/admin/case/move-documents'
        },
        {
          title: 'Promote Project',
          icon: 'restore',
          link: '/admin/case/promote'
        },
        {
          title: 'Image Set',
          icon: 'photo_library',
          link: '/admin/case/image-set',
          children: [
            {
              title: 'Create',
              link: '/admin/case/image-set/create'
            },
            {
              title: 'Manage',
              link: '/admin/case/image-set/manage'
            }
          ]
        },
        {
          title: 'Overlay',
          icon: 'content_copy',
          link: '/admin/case/overlay/overlay'
        },
        {
          title: 'Health Checkup',
          icon: 'check_circle',
          link: '/admin/case/health-checkup'
        },
        {
          title: 'Document Restriction',
          icon: 'not_interested',
          link: '/admin/case/document-restriction'
        }
      ]
    }
  ]
}
