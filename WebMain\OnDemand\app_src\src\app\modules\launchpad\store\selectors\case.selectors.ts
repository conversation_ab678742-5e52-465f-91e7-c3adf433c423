import { createSelector } from '@ngrx/store'
import * as _ from 'lodash'
import { getLaunchpadState, LaunchpadState } from '../reducers'
import { CaseState } from '../reducers/case.reducers'

export const getCaseState = createSelector(
  getLaunchpadState,
  (state: LaunchpadState) => state.case
)

export const getCases = createSelector(
  getCaseState,
  (state: CaseState) => state.caseList
)

export const getCaseInfo = createSelector(
  getCaseState,
  (state: CaseState) => state.caseInfo
)

export const getCaseInfoEdit = createSelector(
  getCaseState,
  (state: CaseState) => {
    return state.caseInfoEdit
  }
)

export const lastIndex = createSelector(
  getCaseState,
  (state: CaseState) => state.lastIndex
)

export const isDataPullFinished = createSelector(
  getCaseState,
  (state: CaseState) => state.isDataPullFinished
)

export const getTimeZones = createSelector(
  getCaseState,
  (state: CaseState) => state.timeZones
)

export const getTimeZonesSorted = createSelector(
  getTimeZones,
  (timeZones: any) =>
    timeZones
      ? _.cloneDeep(timeZones).sort((a, b) =>
          a.DisplayName > b.DisplayName ? 1 : -1
        )
      : timeZones
)

export const newCase = createSelector(
  getCaseState,
  (state: CaseState) => state.newCase
)

export const updateCase = createSelector(
  getCaseState,
  (state: CaseState) => state.updateCase
)

export const getErrorMessage = createSelector(
  getCaseState,
  (state: CaseState) => state.errorMessage
)

export const projectMediaStatus = createSelector(
  getCaseState,
  (state: CaseState) => state.projectMediaStatus
)

export const caseRightListInfo = createSelector(
  getCaseState,
  (state: CaseState) => state.caseRightListInfo
)

export const caseRightList = createSelector(
  getCaseState,
  (state: CaseState) => state.caseRightListInfo.rightlists
)

export const usersListToInvite = createSelector(
  getCaseState,
  (state: CaseState) => state.usersListToInvite
)

export const isInvitationSent = createSelector(
  getCaseState,
  (state: CaseState) => state.isInvitationSent
)

export const extUsersListToInvite = createSelector(
  getCaseState,
  (state: CaseState) => state.extUsersListToInvite
)

export const projectTemplateSettings = createSelector(
  getCaseState,
  (state: CaseState) => state.projectTemplateSettings
)

export const getProjectTemplateList = createSelector(
  getCaseState,
  (state: CaseState) => state.projectTemplateList
)

export const transcriptStatus = createSelector(
  getCaseState,
  (state: CaseState) => state.transcriptStatus
)
export const getSupportedFileTypesForTranscribing = createSelector(
  getCaseState,
  (state: CaseState) => state.supportedFileTypesForTranscribing
)
export const getAccessKeys = createSelector(
  getCaseState,
  (state: CaseState) => state.transcribeAccessKeys
)
export const getImageFileExtensions = createSelector(
  getCaseState,
  (state: CaseState) => state.imageFileExtensions
)

export const newCaseTemplate = createSelector(
  getCaseState,
  (state: CaseState) => state.newCaseTemplate
)

export const updateCaseTemplateInfo = createSelector(
  getCaseState,
  (state: CaseState) => state.updateCaseTemplateInfo
)

export const isMediaStatusLoading = createSelector(
  getCaseState,
  (state: CaseState) => state.isMediaStatusLoading
)

export const unIndexMedia = createSelector(
  getCaseState,
  (state: CaseState) => state.unIndexMediaSuccessResponse
)

export const canFetchNewCase = createSelector(
  getCaseState,
  (state: CaseState) => state.canFetchNewCase
)
