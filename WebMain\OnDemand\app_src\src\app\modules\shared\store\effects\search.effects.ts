import { Injectable } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { select, Store } from '@ngrx/store'
import { Store as xsStore } from '@ngxs/store'
import { ReviewService } from '@review/services/review.service'
import { BreadCrumb } from '@shared/models/breadcrumb.model'
import { ModuleLoginService } from '@shared/services/module-login.service'
import {
  AddModuleLogin,
  ClearIndexedDb,
  ModuleLoginStateSelector
} from '@shared/xsStore'
import { CaseSelectors, StartupStateSelector } from '@stores/selectors'
import { JsonConvert } from 'json2typescript'
import * as _ from 'lodash'
import { cloneDeep } from 'lodash'
import { from, of } from 'rxjs'
import {
  catchError,
  concatMap,
  filter,
  mergeMap,
  switchMap,
  withLatestFrom
} from 'rxjs/operators'
import { SearchReportType, WidgetModel } from 'src/app/modules/analyze/models'
import { ReviewDataSourceType } from 'src/app/modules/review/models/review.model'
import {
  FetchSelectedReviewBatchInfo,
  ResetSelectedReviewSetInfo,
  ReviewSetStateSelector,
  SetBatchCheckoutResponse,
  SetReviewDataSourceType
} from 'src/app/modules/review/xs-store'
import {
  GlobalErrorAction,
  GlobalSuccessAction
} from '../../../../store/actions'
import { UpdateSearchRequestParam } from '../../../../stores/actions'
import * as AnalyzeActions from '../../../analyze/store/actions/index.actions'
import { getControlSetting } from '../../../config/store/selectors'
import * as fromDocumentsActions from '../../../review/store/actions/index.actions'
import {
  setSearchResults,
  setSelectedDocuments,
  unsetCurrentDocument
} from '../../../review/store/actions/index.actions'
import {
  NavigationType,
  SearchDupOption,
  SearchInputParams,
  SearchRequestModel,
  SearchResponseModel
} from '../../models/search.model'
import { SearchService } from '../../services/search.service'
import { updateBreadCrumbs } from '../actions'
import * as fromSearchActions from '../actions/search.actions'
import { SearchState } from '../reducers/search.reducer'
import { getBreadcrumbs } from '../selectors/filter.selector'
import {
  getBaseExpression,
  getSearchResponse
} from '../selectors/search.selector'

/**
 * Effects for handling searches, post search operations, fetching of search results, etc.
 */
@Injectable()
export class SearchEffects {
  private jsonConvert: JsonConvert

  constructor(
    private actions$: Actions,
    private service: SearchService,
    private store: Store<SearchState>,
    private xsStore: xsStore,
    private mlServive: ModuleLoginService,
    private reviewService: ReviewService,
    private route: ActivatedRoute
  ) {
    this.jsonConvert = new JsonConvert()
  }

  public get _documentShareToken(): string {
    return this.route.snapshot.queryParams['docShareToken']
  }

  /**
   * Calls the search service
   * Invokes postSearch action for getting search request response (The response is not search results)
   */
  search$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.search),
      concatMap((action) => {
        return of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getSearchResponse)),
            this.store.pipe(select(getBreadcrumbs)),
            this.store.pipe(select(getBaseExpression)),
            this.store.pipe(select(getControlSetting('VOD_VERSION')))
          )
        )
      }),
      switchMap(
        ([
          action,
          searchResponse,
          stateBreadcrumbs,
          baseExpression,
          vodVersion
        ]) => {
          if (!action.payload.isInitialSearch)
            this.reviewService.setDocumentTableFieldSortingInStore$.next(
              () => {}
            )
          this.store.dispatch(fromDocumentsActions.showAllProgressSpinners()) // Show all the progress spinners
          const media = this.xsStore.selectSnapshot(CaseSelectors.mediaStatus)
          const selectedMedias = this.xsStore.selectSnapshot(
            StartupStateSelector.SliceOf('selectedMediaScope')
          )
          const projectId: number = this.xsStore.selectSnapshot(
            StartupStateSelector.SliceOf('selectedProjectId')
          )
          const selectedFolders = this.xsStore.selectSnapshot(
            StartupStateSelector.SliceOf('selectedFolderScope')
          )

          const tempTables = searchResponse?.tempTables
          const inputs = action.payload

          let updatedInputs: SearchInputParams
          if (inputs.isForwardFilter || inputs.isBreadCrumbClicked) {
            updatedInputs = this.service.constructFilterQuery(
              stateBreadcrumbs ?? [],
              inputs,
              baseExpression
            )
          } else
            this.store.dispatch(
              fromSearchActions.updateSearchBaseExpression({
                payload: { expression: inputs.searchExpression }
              })
            )
          const searchRequest: SearchRequestModel = {
            includePC: inputs?.includePC ?? false,
            searchExpression: 'extension="xls"',
            // updatedInputs?.searchExpression ??
            // inputs?.searchExpression ??
            // 'fileid>0',
            isForwardFilter: inputs.isForwardFilter,
            projectId: projectId.toString(),
            lstMedia: inputs?.medialist?.length
              ? inputs?.medialist?.map((id) => String(id)) //apply media list if sent from inputs.
              : selectedMedias?.length
              ? selectedMedias.map((id) => String(id)) // apply selected media ids.
              : media?.isAllMediaProcessed
              ? media.mediaIds?.map((id) => `${id}`) // apply all processed media ids
              : media.processedMediaIds.map((id) => `${id}`), // apply processed media IDs only
            lstFolder: inputs?.folderList?.length
              ? inputs?.folderList?.map((id) => id)
              : selectedFolders?.length
              ? selectedFolders?.map((id) => id)
              : null,
            //userType: 'INTERNAL',
            userType:
              localStorage.getItem('DocShareUserRole')?.toLowerCase() ===
              'external'
                ? 'EXTERNAL'
                : 'INTERNAL',
            searchDuplicateOption:
              inputs?.searchDuplicateOption ?? SearchDupOption?.DEFAULT,
            searchGuid: tempTables ? tempTables.searchGuid : null,
            reviewSetId: inputs?.reviewSetId,
            viewType: this.xsStore.selectSnapshot(
              ReviewSetStateSelector.SliceOf('reviewViewType')
            ),
            baseGUID: inputs?.isResetBaseGuid
              ? ''
              : tempTables
              ? tempTables.baseGUID
              : '',
            batchId: this.xsStore.selectSnapshot(
              ReviewSetStateSelector.SliceOf('selectedReviewSetBatchInfo')
            )?.batchId,
            isSavedSearch: inputs?.isSavedSearch ?? false,
            isLoadFile: inputs?.isLoadFile ?? false,
            isSearchFromHistory: inputs?.isSearchFromHistory ?? false,
            searchId: inputs?.searchHistoryId,
            dynamicFolderId: inputs?.dynamicFolderId,
            isDynamicFolderGlobal: inputs?.isDynamicFolderGlobal ?? false,
            documentShareToken: this._documentShareToken,
            isSqlMode: inputs?.isSqlMode ?? false,
            searchQuery: localStorage.getItem('SystemBatestSearchQuery'),
            isFilterSearch: inputs?.isFilterSearch,
            reviewBatchAfterCALThreshold: inputs?.reviewBatchAfterCALThreshold,
            viewTagRuleConflictFiles: inputs?.viewTagRuleConflictFiles,
            navigationType: inputs?.navigationType ?? NavigationType.Media
          }
          this.store.dispatch(unsetCurrentDocument())
          this.xsStore.dispatch(new ClearIndexedDb('FulltextParts'))
          return this.service.search$(searchRequest).pipe(
            switchMap((response: any) => {
              this.xsStore.dispatch(
                new UpdateSearchRequestParam({
                  ...searchRequest,
                  tempTableResponse: response?.data?.tempTables,
                  docCount:
                    response?.data?.searchResultIntialParameters
                      ?.totalHitCount || 0
                })
              )
              //update breadcrumbs
              if (inputs.isBreadCrumbClicked) {
                const breadCrumbs = cloneDeep(stateBreadcrumbs)
                breadCrumbs.length = inputs.breadCrumbIndex + 1
                this.store.dispatch(
                  updateBreadCrumbs({ payload: { breadcrumbs: breadCrumbs } })
                )
              } else if (inputs.isForwardFilter) {
                const breadCrumbs: BreadCrumb[] = [
                  ...stateBreadcrumbs,
                  {
                    query: inputs.searchExpression,
                    filterText: inputs.filterText,
                    isInitialSearch: false,
                    docCount:
                      response?.data?.searchResultIntialParameters
                        ?.totalHitCount,
                    isFilterSearch: inputs.isFilterSearch
                  }
                ]
                this.store.dispatch(
                  updateBreadCrumbs({ payload: { breadcrumbs: breadCrumbs } })
                )
              } else {
                const breadCrumbs: BreadCrumb[] = [
                  {
                    query: inputs.searchExpression,
                    filterText: 'Home',
                    isInitialSearch: true
                  }
                ]
                this.store.dispatch(
                  updateBreadCrumbs({ payload: { breadcrumbs: breadCrumbs } })
                )
              }
              //end
              const searchResponse = response?.data
              if (searchResponse == undefined) {
                return [
                  new GlobalErrorAction(
                    new Error(
                      'No media has been added to the case. Please use upload page to add media in the case'
                    ),
                    true,
                    true
                  ),
                  fromDocumentsActions.hideAllProgressSpinners(),
                  fromSearchActions.clearSearchResponse(),
                  setSelectedDocuments({ payload: { selectedDocuments: [] } }),
                  setSearchResults({
                    payload: { searchResults: [] }
                  })
                ]
              } else {
                if (searchResponse?.error?.errorStatus) {
                  return [
                    new GlobalErrorAction(
                      new Error(searchResponse.error.errorMessage),
                      true,
                      true
                    ),
                    fromDocumentsActions.hideAllProgressSpinners()
                  ]
                } else {
                  const reviewSetId: number = this.xsStore.selectSnapshot(
                    ReviewSetStateSelector.SliceOf('selectedReviewSetId')
                  )
                  const projectLoginId: number = this.xsStore.selectSnapshot(
                    ModuleLoginStateSelector.SliceOf('projectLoginId')
                  )

                  if (reviewSetId > 0) {
                    const actions: any[] = [
                      new SetReviewDataSourceType(
                        ReviewDataSourceType.ReviewSet
                      ),
                      new FetchSelectedReviewBatchInfo(
                        projectId,
                        reviewSetId,
                        searchResponse?.searchResultIntialParameters?.batchId
                      ),
                      new SetBatchCheckoutResponse(searchResponse)
                    ]
                    if (vodVersion.toString() !== '3') {
                      actions.push(
                        new AddModuleLogin(
                          projectId,
                          this.mlServive.getModuleLoginModel(
                            1,
                            projectLoginId,
                            reviewSetId
                          )
                        )
                      )
                    }
                    this.xsStore.dispatch(actions)
                  } else {
                    this.xsStore.dispatch([
                      new SetReviewDataSourceType(ReviewDataSourceType.Search),
                      new ResetSelectedReviewSetInfo()
                    ])
                  }
                  return [
                    fromSearchActions.searchSuccess({
                      payload: { searchResponse: searchResponse }
                    }),
                    new GlobalSuccessAction(
                      'Search Request Response',
                      searchResponse,
                      false,
                      false
                    ),
                    fromSearchActions.postSearchReview()
                  ]
                }
              }
            }),
            catchError((err) => {
              if (
                err.error.status.toLowerCase() === 'success' &&
                err.error.data.error.errStatus &&
                inputs.reviewSetId > 0
              ) {
                this.xsStore.dispatch(
                  new SetBatchCheckoutResponse(err.error.data)
                )
                return from([
                  new GlobalErrorAction(err, false, true),
                  fromDocumentsActions.hideAllProgressSpinners() // Hide all the progress spinners
                ])
              } else {
                return from([
                  new GlobalErrorAction(err, true, true),
                  fromDocumentsActions.hideAllProgressSpinners() // Hide all the progress spinners
                ])
              }
            })
          )
        }
      )
    )
  )

  /**
   * Handles post search tasks such as setting temp tables details, search result parameters, and search error message in the store
   * Also sets the current document table page and resets documents selection to the first item of the page
   * i.e. deselects everything and selects only the first item of the page
   */
  postSearchReview$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.postSearchReview),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(this.store.pipe(select(getSearchResponse)))
        )
      ),
      filter(([action, searchResponse]) => !!searchResponse),
      switchMap(([action, searchResponse]) => {
        try {
          const actions = []
          if (searchResponse) {
            actions.push(
              fromDocumentsActions.setCurrentDocumentTablePage({
                payload: {
                  pageNumber:
                    searchResponse.searchResultIntialParameters.currentPage,
                  resetSelectionItem: 'first'
                }
              })
            )
          }
          // Dispatch all actions pushed to the actions array
          return actions
        } catch (e) {
          return [
            new GlobalErrorAction(
              new Error(
                'Error performing post search functionality. ' + e.stackTrace
              ),
              true,
              true
            )
          ]
        }
      })
    )
  )

  postSearchDashboard$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.postSearchDashboard),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(this.store.pipe(select(getSearchResponse)))
        )
      ),
      mergeMap(([action, searchResponse]) => {
        const widgetModel = action.payload.widgetModel
        const dashboards = action.payload.dashboards

        try {
          const actionsArr = []
          let tempModel = new WidgetModel()
          for (const widget of dashboards) {
            tempModel = _.cloneDeep(widgetModel)
            tempModel.searchTempTable =
              searchResponse.tempTables.searchResultTempTable
            tempModel.widgetName = widget.id
            tempModel.widgetType = widget.widgetType
            //user previously specified data size or limit
            if (widget.dataSize)
              tempModel.topLimit =
                widget.dataSize === -1
                  ? 'ALL'
                  : 'Top ' + widget.dataSize.toString()
            if (widget.widgetType?.toUpperCase() == 'DYNAMIC')
              tempModel.widgetName = widget.title
            else tempModel.widgetName = widget.id

            localStorage.setItem('SearchTempTable', tempModel.searchTempTable)
            actionsArr.push(
              AnalyzeActions.GetWidgetData({
                widgetModel: tempModel,
                pageNumber: 1,
                widgetId: widget.id,
                widgetType: widgetModel.widgetName
              })
            )
          }

          /*----------------------------------------------------------------------------
                     Add `search term status action with payload` in the action collection.
                    //  ---------------------------------------------------------------------------*/

          actionsArr.push(
            AnalyzeActions.GetSearchTermStatusData({
              projectId: tempModel.projectId,
              body: this.prepareSearchTermStatusBody(
                searchResponse,
                tempModel.projectId
              )
            })
          )

          return actionsArr
        } catch (e) {
          return [
            new GlobalErrorAction(
              new Error(
                'Error performing post search functionality. ' + e.stackTrace
              ),
              true,
              true
            )
          ]
        }
      })
    )
  )

  /**
   * creates payload body for `search term status` endpoint after receiving success response of dashboard search.
   */
  private prepareSearchTermStatusBody(
    response: SearchResponseModel,
    projectId: number
  ) {
    return {
      ProjectId: projectId,
      ReportType: SearchReportType.ProjectHitTermReport,
      SearchId: response.tempTables.searchId,
      TemptableName: response.tempTables.computedSearchTempTable
    }
  }
}
