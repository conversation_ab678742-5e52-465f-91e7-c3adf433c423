import { createSelector } from '@ngrx/store'
import { StringUtils } from '@shared/utils/string-utils'
import {
  FullTextConfig,
  ImageConfig,
  ImageLoadFileConfig,
  ImportConfig,
  LoadFileConfig,
  NativeConfig
} from '../../models/import-config'
import { getImportState, ImportState } from '../reducers'
import { ImportConfigState } from '../reducers/import-config.reducer'
import {
  getParamsMandatoryVenioFields,
  getParamsVenioFields
} from './import-params.selectors'

export const getImportConfigState = createSelector(
  getImportState,
  (importState: ImportState) => importState.importConfig
)

export const getImportConfig = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => importConfigState.importConfig
)

export const getConfigLoadFile = createSelector(
  getImportConfig,
  (importConfig: ImportConfig) => (importConfig ? importConfig.loadFile : null)
)

export const getConfigLoadFilePath = createSelector(
  getConfigLoadFile,
  (config: LoadFileConfig) => (config ? config.filePath : null)
)

export const getConfigLoadFileFormat = createSelector(
  getConfigLoadFile,
  (config: LoadFileConfig) => (config ? config.format : null)
)

export const getConfigLoadFileDateFormat = createSelector(
  getConfigLoadFile,
  (config: LoadFileConfig) => (config ? config.dateFormat : null)
)

export const getConfigLoadFileTimeZone = createSelector(
  getConfigLoadFile,
  (config: LoadFileConfig) => (config ? config.timeZone : null)
)

export const getConfigLoadFileProcessOption = createSelector(
  getConfigLoadFile,
  (config: LoadFileConfig) => (config ? config.processLoadFile : null)
)

export const getConfigImageLoadFile = createSelector(
  getImportConfig,
  (config: ImportConfig) => (config ? config.imageLoadFile : null)
)

export const getConfigImageLoadFilePath = createSelector(
  getConfigImageLoadFile,
  (config: ImageLoadFileConfig) => (config ? config.filePath : null)
)

export const getConfigNative = createSelector(
  getImportConfig,
  (config: ImportConfig) => (config ? config.native : null)
)

export const getConfigNativeProcessOption = createSelector(
  getConfigNative,
  (config: NativeConfig) => (config ? config.processNative : null)
)

export const getConfigNativeExtractFulltext = createSelector(
  getConfigNative,
  (config: NativeConfig) => (config ? config.extractFulltextFromNative : null)
)

export const getConfigAutoComputeFileSize = createSelector(
  getConfigNative,
  (config: NativeConfig) => (config ? config.autoComputeFileSize : null)
)

export const getConfigAutoComputeFileExtension = createSelector(
  getConfigNative,
  (config: NativeConfig) => (config ? config.autoComputeFileExtension : null)
)

export const getConfigAutoComputeFileType = createSelector(
  getConfigNative,
  (config: NativeConfig) => (config ? config.autoComputeFileType : null)
)

export const getConfigNativeFilePathField = createSelector(
  getConfigNative,
  (config: NativeConfig) => (config ? config.fieldWithPath : null)
)

export const getConfigNativeFolderPath = createSelector(
  getConfigNative,
  (config: NativeConfig) => (config ? config.folderPath : null)
)

export const getConfigFullText = createSelector(
  getImportConfig,
  (config: ImportConfig) => (config ? config.fullText : null)
)

export const getConfigFullTextProcessOption = createSelector(
  getConfigFullText,
  (config: FullTextConfig) => (config ? config.processFullText : null)
)

export const getConfigFullTextFilePathField = createSelector(
  getConfigFullText,
  (config: FullTextConfig) => (config ? config.fieldWithPath : null)
)

export const getConfigFullTextFolderPath = createSelector(
  getConfigFullText,
  (config: FullTextConfig) => (config ? config.folderPath : null)
)

export const getConfigHasFullTextInLoadFile = createSelector(
  getConfigFullText,
  (config: FullTextConfig) =>
    config ? config.hasExtractedTextInLoadFile : false
)

export const getConfigExtractedTextFields = createSelector(
  getConfigFullText,
  (config: FullTextConfig) => (config ? config.extractedTextFields : [])
)

export const getConfigImage = createSelector(
  getImportConfig,
  (config: ImportConfig) => (config ? config.image : null)
)

export const getConfigImageProcessOption = createSelector(
  getConfigImage,
  (config: ImageConfig) => (config ? config.processImage : null)
)

export const getConfigImageMappingField = createSelector(
  getConfigImage,
  (config: ImageConfig) => (config ? config.imageMappingField : null)
)

export const getConfigImageFolderPath = createSelector(
  getConfigImage,
  (config: ImageConfig) => (config ? config.folderPath : null)
)

export const getConfigVenioToLoadFileMapping = createSelector(
  getImportConfig,
  (config: ImportConfig) => (config ? config.venioToLoadFileMapping : {})
)

export const getConfigLoadFileToVenioMapping = createSelector(
  getImportConfigState,
  (config: ImportConfigState) => (config ? config.loadFileToVenioMapping : {})
)

export const getConfigValidateFileExistence = createSelector(
  getImportConfigState,
  (config: ImportConfigState) => (config ? config.validateFileExistence : false)
)

export const getConfigUnmappedVenioFields = createSelector(
  getParamsVenioFields,
  getConfigVenioToLoadFileMapping,
  (venioFields: string[], mapping: any) => {
    if (!venioFields) {
      return []
    }
    if (mapping) {
      const mappedVenioFields = Object.keys(mapping)
      return venioFields.filter((field) => !mappedVenioFields.includes(field))
    }
  }
)

export const getConfigUnmappedMandatoryVenioFields = createSelector(
  getParamsMandatoryVenioFields,
  getConfigUnmappedVenioFields,
  (mandatoryVenioFields: string[], unmappedVenioFields: string[]) => {
    if (!unmappedVenioFields || !mandatoryVenioFields) {
      return mandatoryVenioFields
    }
    return unmappedVenioFields.filter((field) =>
      mandatoryVenioFields.includes(field)
    )
  }
)

export const isAnalyzeSpinnerShowing = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => importConfigState.analyzeSpinner
)
export const isValidateSpinnerShowing = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => importConfigState.validateSpinner
)
export const isValidateFilePathsSpinnerShowing = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) =>
    importConfigState.validateFilePathsSpinner
)
export const isImportSpinnerShowing = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => importConfigState.importSpinner
)

export const getCustomFieldsCreated = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) =>
    importConfigState.customFieldsCreated
)
export const getCustomFieldsCreationError = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => [
    importConfigState.customFieldsCreationErrorMessage,
    importConfigState.customFieldsCreationError
  ]
)
export const isCustomFieldsSpinnerShowing = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) =>
    importConfigState.customFieldsSpinner
)

export const getImportStatus = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => importConfigState.importStatus
)
export const getImportProgress = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => importConfigState.importProgress
)

export const loadFileSelectionComplete = createSelector(
  getConfigLoadFilePath,
  getConfigLoadFileDateFormat,
  getConfigLoadFileTimeZone,
  (path: string, dateFormat: string, timeZone: string) => {
    return !(
      StringUtils.isNullOrEmpty(path) ||
      StringUtils.isNullOrEmpty(dateFormat) ||
      StringUtils.isNullOrEmpty(timeZone)
    )
  }
)

export const mapFilePathComplete = createSelector(
  getConfigFullText,
  getConfigNative,
  getConfigImage,
  (fullText: FullTextConfig, native: NativeConfig, image: ImageConfig) => {
    if (
      fullText.processFullText === undefined ||
      native.processNative === undefined ||
      image.processImage === undefined
    ) {
      return false
    }
    if (fullText.processFullText) {
      if (
        !fullText.hasExtractedTextInLoadFile &&
        (StringUtils.isNullOrEmpty(fullText.fieldWithPath) ||
          StringUtils.isNullOrEmpty(fullText.folderPath))
      ) {
        return false
      }
    }
    if (native.processNative) {
      if (
        StringUtils.isNullOrEmpty(native.fieldWithPath) &&
        StringUtils.isNullOrEmpty(native.folderPath)
      ) {
        return false
      }
    }
    if (image.processImage) {
      if (
        StringUtils.isNullOrEmpty(image.fieldWithPath) &&
        StringUtils.isNullOrEmpty(image.folderPath)
      ) {
        return false
      }
    }
    return true
  }
)

export const getConfigCustodian = createSelector(
  getImportConfig,
  (config: ImportConfig) => config.custodian
)

export const getImportTemplates = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => importConfigState.importTemplates
)

export const getImportTemplate = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => importConfigState.importTemplate
)

export const getImportTemplateExistance = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) =>
    importConfigState.importTemplateAlreadyExist
)

export const getDelimiters = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) => importConfigState.delimiters
)

export const getProcessCheckboxValueChange = createSelector(
  getImportConfigState,
  (importConfigState: ImportConfigState) =>
    importConfigState.processCheckboxValueChange
)
