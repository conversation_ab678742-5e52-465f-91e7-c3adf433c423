import {
  AfterViewInit,
  Component,
  Input,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewContainerRef
} from '@angular/core'
import { FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms'
import { ConfigState } from '@config/store/reducers'
import { getThemeClient } from '@config/store/selectors'
import { RouterReducerState } from '@ngrx/router-store'
import { select, Store } from '@ngrx/store'
import { getRouterState, RouterStateUrl } from '@root/store/reducers'
import * as moment from 'moment'
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal'
import { Subject } from 'rxjs'
import { filter, map, takeUntil } from 'rxjs/operators'
import {
  ImportTemplateConfig,
  LoadFileFormatConfig
} from '../../models/import-config'
import {
  ImageLoadFileStatusSummary,
  LoadFileStatusSummary,
  LoadFileSummary
} from '../../models/import-summary'
import {
  AddCustomDateFormat,
  ClearMapping,
  ConfigureDateFormat,
  ConfigureLoadFileFormat,
  ConfigureLoadFilePath,
  ConfigureLoadFileProcessOption,
  ConfigureTimeZone,
  FetchImportTemplateConfig
} from '../../store/actions'
import { ImportState } from '../../store/reducers'
import {
  getConfigImageProcessOption,
  getConfigLoadFileDateFormat,
  getConfigLoadFilePath,
  getConfigLoadFileProcessOption,
  getConfigLoadFileTimeZone,
  getImportTemplate
} from '../../store/selectors/import-config.selectors'
import {
  getParamsDateFormats,
  getParamsTimeZones
} from '../../store/selectors/import-params.selectors'
import {
  getSummaryImageLoadFileStatus,
  getSummaryLoadFile,
  getSummaryLoadFileStatus
} from '../../store/selectors/import-summary.selectors'
import { LoadFilePreviewComponent } from '../load-file-preview/load-file-preview.component'

@Component({
  selector: 'app-load-file-select',
  templateUrl: './load-file-select.component.html',
  styleUrls: ['./load-file-select.component.scss']
})
export class LoadFileSelectComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  client: string

  paramsDateFormats: string[]

  paramsTimeZones: string[]

  summaryLoadFileStatus: LoadFileStatusSummary

  summaryLoadFile: LoadFileSummary

  configLoadFilePath: string

  loadFileSelectFormGroup: FormGroup

  _formSubmitAttempt: boolean

  exampleDate: string

  previewModalRef: BsModalRef

  addDateFormatModalRef: BsModalRef

  processLoadFile: boolean

  imageLoadFileExist: boolean

  isOverlay = false

  processImage: boolean

  /**
   * ProjectId for importing the data
   */
  projectId: number

  /**
   * Id of the import
   */
  importId: number

  /**
   * Selected import template Id
   */
  templateID: number

  private unsubscribe$ = new Subject<void>()

  constructor(
    private configStore: Store<ConfigState>,
    private store: Store<ImportState>,
    private modalService: BsModalService,
    private viewContainerRef: ViewContainerRef,
    private formBuilder: FormBuilder
  ) {}

  get formSubmitAttempt(): boolean {
    return this._formSubmitAttempt
  }

  @Input()
  set formSubmitAttempt(formSubmitAttempt: boolean) {
    this._formSubmitAttempt = formSubmitAttempt
  }

  ngOnInit() {
    this.configStore
      .pipe(select(getThemeClient), takeUntil(this.unsubscribe$))
      .subscribe((client: string) => {
        this.client = client
      })

    this.store
      .pipe(
        select(getRouterState),
        takeUntil(this.unsubscribe$),
        map(
          (routerState: RouterReducerState<RouterStateUrl>) => routerState.state
        )
      )
      .subscribe((routerState: RouterStateUrl) => {
        this.projectId = routerState.queryParams.projectId
        this.importId = routerState.queryParams.importId

        this.isOverlay = routerState.queryParams.isOverlay == '1' ? true : false
        if (routerState.queryParams.extUserId) {
          this.isOverlay = false
        }
      })

    this.loadFileSelectFormGroup = this.formBuilder.group(
      {
        loadFileName: [null],
        dateFormat: [null, Validators.required],
        timeZone: [null, Validators.required]
      },
      {
        validator: this.validateIfChecked
      }
    )

    this.store
      .pipe(
        select(getParamsDateFormats),
        takeUntil(this.unsubscribe$),
        map((formats: string[]) => formats),
        filter((formats) => !!formats)
      )
      .subscribe((formats: string[]) => {
        this.paramsDateFormats = formats
      })

    this.store
      .pipe(
        select(getParamsTimeZones),
        takeUntil(this.unsubscribe$),
        map((timeZones: string[]) => timeZones),
        filter((timeZones) => !!timeZones)
      )
      .subscribe((timeZones: string[]) => {
        this.paramsTimeZones = timeZones
      })

    this.store
      .pipe(
        select(getConfigLoadFilePath),
        takeUntil(this.unsubscribe$),
        map((pathConfig: string) => pathConfig)
      )
      .subscribe((pathConfig: string) => {
        this.configLoadFilePath = pathConfig
        this.populateLoadFileName(this.configLoadFilePath)
      })

    this.store
      .pipe(
        select(getSummaryLoadFileStatus),
        takeUntil(this.unsubscribe$),
        map((statusSummary: LoadFileStatusSummary) => statusSummary)
      )
      .subscribe((statusSummary: LoadFileStatusSummary) => {
        this.summaryLoadFileStatus = statusSummary
      })

    this.store
      .pipe(
        select(getSummaryImageLoadFileStatus),
        takeUntil(this.unsubscribe$),
        map((statusSummary: ImageLoadFileStatusSummary) => statusSummary)
      )
      .subscribe((statusSummary: ImageLoadFileStatusSummary) => {
        this.imageLoadFileExist =
          statusSummary && statusSummary.found && statusSummary.all.length > 0
      })

    this.store
      .pipe(
        select(getConfigImageProcessOption),
        takeUntil(this.unsubscribe$),
        map((processConfig: boolean) => processConfig)
      )
      .subscribe((processImage) => {
        this.processImage = processImage
      })

    this.store
      .pipe(
        select(getConfigLoadFileDateFormat),
        takeUntil(this.unsubscribe$),
        map((dateFormat: string) => dateFormat)
      )
      .subscribe((dateFormat: string) => {
        this.populateDateFormat(dateFormat)
        this.createExampleDate(dateFormat)
      })

    this.store
      .pipe(
        select(getConfigLoadFileTimeZone),
        takeUntil(this.unsubscribe$),
        map((timeZone: string) => timeZone)
      )
      .subscribe((timeZone: string) => {
        this.populateTimeZone(timeZone)
      })

    this.store
      .pipe(
        select(getSummaryLoadFile),
        takeUntil(this.unsubscribe$),
        map((summary: LoadFileSummary) => summary)
      )
      .subscribe((summary: LoadFileSummary) => {
        this.summaryLoadFile = summary
      })

    this.store
      .pipe(
        select(getConfigLoadFileProcessOption),
        map((processConfig: boolean) => processConfig),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((processConfig: boolean) => {
        this.processLoadFile = processConfig
      })

    this.store
      .pipe(
        select(getImportTemplate),
        map((importTemplate: ImportTemplateConfig) => {
          const id = importTemplate?.templateId ?? 0
          return id
        }),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((id) => {
        this.templateID = id
      })
  }

  ngAfterViewInit(): void {}

  ngOnDestroy(): void {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()
  }

  get f() {
    return this.loadFileSelectFormGroup.controls
  }

  populateLoadFileName(name: string) {
    if (this.loadFileSelectFormGroup) {
      this.loadFileSelectFormGroup.patchValue({
        loadFileName: name
      })
    }
  }

  populateDateFormat(format: string) {
    if (this.loadFileSelectFormGroup) {
      this.loadFileSelectFormGroup.patchValue({ dateFormat: format })
    }
  }

  populateTimeZone(timeZone: string) {
    if (this.loadFileSelectFormGroup) {
      this.loadFileSelectFormGroup.patchValue({ timeZone: timeZone })
    }
  }

  createExampleDate(dateFormat: string) {
    this.exampleDate = moment().format(dateFormat)
  }

  openLoadFilePreviewModal() {
    this.previewModalRef = this.modalService.show(
      LoadFilePreviewComponent,
      Object.assign({}, { class: 'modal-lg-custom' })
    )
  }

  openAddDateFormatModal(template: TemplateRef<any>) {
    this.addDateFormatModalRef = this.modalService.show(
      template,
      Object.assign({}, { class: 'modal-lg' })
    )
  }

  onLoadFileChange(event) {
    this.store.dispatch(new ClearMapping())

    //If import templated selected
    if (this.templateID > 0) {
      this.store.dispatch(
        new FetchImportTemplateConfig({
          projectId: this.projectId,
          importTemplate: {
            templateId: this.templateID,
            templateName: event.templateName,
            templateDesc: event?.templateDesc
          },
          importId: this.importId,
          processImage: this.processImage,
          processLoadFile: this.processLoadFile,
          loadFileUpdate: {
            hasUpdated: true,
            filePath: event.target.textContent
          }
        })
      )
    } else {
      this.setLoadFileName(event.target.textContent)
    }
  }

  onDateFormatChange(event) {
    this.setLoadFileDateFormat(event.target.value)
  }

  onTimeZoneChange(event) {
    this.setTimeZone(event.target.value)
  }

  onAdvancedSettingsSet = (settings: any) => {
    this.setLoadFileFormat(settings.loadFileFormat)
  }

  onAddDateFormat = (dateFormat) => {
    this.store.dispatch(new AddCustomDateFormat(dateFormat))
    this.setLoadFileDateFormat(dateFormat)
    this.addDateFormatModalRef.hide()
  }

  setLoadFileName = (loadFilePath: string) => {
    this.store.dispatch(new ConfigureLoadFilePath(loadFilePath))
  }

  setLoadFileFormat(loadFileFormat: LoadFileFormatConfig) {
    this.store.dispatch(new ConfigureLoadFileFormat(loadFileFormat))
  }

  setLoadFileDateFormat(loadFileDateFormat: string) {
    this.store.dispatch(new ConfigureDateFormat(loadFileDateFormat))
  }

  setTimeZone(timeZone: string) {
    this.store.dispatch(new ConfigureTimeZone(timeZone))
  }

  onLoadFileSwitchChanged = (event) => {
    this.setLoadFileImportFlag(event.target.checked)
  }

  setLoadFileImportFlag(change: boolean) {
    this.store.dispatch(new ConfigureLoadFileProcessOption(change))
  }

  validateIfChecked: ValidatorFn = (fg: FormGroup) => {
    const fileName = fg.get('loadFileName').value
    if (!this.processLoadFile) {
      return null
    }
    if (this.processLoadFile === true && !fileName) {
      return {
        requiredIfChecked: true
      }
    }
    return null
  }
}
