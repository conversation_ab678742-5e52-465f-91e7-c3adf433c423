import {
  FetchReviewSetTagStatusAction,
  ReviewSetStateSelector
} from '@admin-advance/store'
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild
} from '@angular/core'
import { MatDialog, MatDialogRef } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { ConfigService } from '@config/services/config.service'
import { Store } from '@ngxs/store'
import { DxChartComponent, DxListComponent } from 'devextreme-angular'
import { Subject } from 'rxjs'
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  take,
  takeUntil,
  tap
} from 'rxjs/operators'
import { ReviewStatusModel } from '../../../../models/review-set/review-set.model'

@Component({
  templateUrl: './tag-status.component.html',
  styleUrls: ['../child/data-container.component.scss']
})
export class TagStatusComponent implements AfterViewInit, OnInit, OnD<PERSON>roy {
  /**
   * Cleanup placeholder for the observers when the component get destroyed.
   */
  private readonly toDestroy$ = new Subject<void>()

  /**
   * Bar chart widget component
   */
  @ViewChild(DxChartComponent)
  private readonly barChart: DxChartComponent

  /**
   * Chart data
   */
  data: ReviewStatusModel[] = []

  allTags: ReviewStatusModel[]

  selectedTags: number[]

  @ViewChild('showHideTags')
  private readonly showHideTagsRef: TemplateRef<any>

  @ViewChild('listTags')
  private readonly listTagsRef: DxListComponent

  config = ConfigService

  dialogRef: MatDialogRef<any, any>

  isTagStatusLoading = true

  constructor(
    private cdr: ChangeDetectorRef,
    private router: Router,
    private store: Store,
    private dialog: MatDialog,
    private route: ActivatedRoute
  ) {}

  /**
   * Customized tooltip text of chart series
   */
  customizeTooltip(arg: any): unknown {
    return {
      text: `${arg.point.data.tagName} : ${arg.point.data.taggedDocCount}`
    }
  }

  ngOnInit(): void {
    this.initEvent()
  }

  ngAfterViewInit(): void {
    this.initSlices()
  }

  ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Init router event and load progress data on the widget.
   */
  private readonly initEvent = (): void => {
    const qp = this.route.snapshot.queryParams
    if (!!qp && qp['projectId'] > 0 && qp['reviewSetId'] > 0)
      this.store.dispatch(
        new FetchReviewSetTagStatusAction(+qp['projectId'], +qp['reviewSetId'])
      )
    else {
      this.data = []
    }
  }

  /**
   * Init slices from store
   */
  private readonly initSlices = (): void => {
    this.cdr.markForCheck()
    if (this.barChart) {
      this.barChart?.instance?.refresh()
    }

    this.store
      .select(ReviewSetStateSelector.SliceOf('reviewSetTagStatus'))
      .pipe(
        // no further chain until we got new
        distinctUntilChanged(),
        debounceTime(600),
        tap((d) => {
          this.cdr.markForCheck()
          this.allTags = d
          this.selectedTags = d.slice(0, 5).map((t) => t.tagId)
          const tagData = d.slice(0, 5)
          this.data = this.sortTagsByTagNameDescending(tagData)
          this.isTagStatusLoading = false
        }),
        debounceTime(100),
        filter(() => !!this.barChart),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: () => {
          this.cdr.markForCheck()
          // once all set up, refresh the UI element
          this.barChart?.instance?.refresh()
        }
      })
  }

  onShowHideTags() {
    this.dialogRef = this.dialog.open(this.showHideTagsRef, {
      closeOnNavigation: true,
      autoFocus: false,
      width: '380px'
    })
    this.dialogRef
      .beforeClosed()
      .pipe(
        filter((res) => !!res),
        take(1)
      )
      .subscribe(() => {
        this.selectedTags = this.listTagsRef.selectedItemKeys
        const tagData = this.allTags.filter((tag) =>
          this.selectedTags.some((tagId) => tag.tagId == tagId)
        )
        this.data = this.sortTagsByTagNameDescending(tagData)
      })
  }

  save() {
    this.selectedTags = this.listTagsRef.selectedItemKeys
    this.dialogRef.close(true)
  }

  sortTagsByTagNameDescending(tags: any[]): any[] {
    return tags.slice().sort((a, b) => b.tagName.localeCompare(a.tagName))
  }
}
