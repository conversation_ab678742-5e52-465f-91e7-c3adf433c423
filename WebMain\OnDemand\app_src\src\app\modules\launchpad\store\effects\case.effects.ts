import { CaseModel } from '@admin-advance/models'
import { HttpErrorResponse } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Router } from '@angular/router'
import { RightModel } from '@config/models'
import { ConfigService } from '@config/services/config.service'
import { Actions, createEffect, Effect, ofType } from '@ngrx/effects'
import { Action, Store } from '@ngrx/store'
import { NavigateToAction } from '@shared/store/actions/shared.actions'
import { cloneDeep } from 'lodash'
import { from, Observable, of } from 'rxjs'
import {
  catchError,
  concatMap,
  map,
  mergeMap,
  switchMap,
  tap,
  toArray
} from 'rxjs/operators'
import { GlobalErrorAction, GlobalSuccessAction } from 'src/app/store/actions'
import { LaunchpadService } from '../../services/launchpad.service'
import {
  CaseActionTypes,
  CreateCase,
  CreateCaseError,
  CreateCaseServiceType,
  CreateCaseSuccess,
  CreateCaseTemplate,
  CreateCaseTemplateError,
  CreateCaseTemplateSuccess,
  FetchCaseInfo,
  FetchCaseInfoEdit,
  FetchCaseInfoEditSuccess,
  FetchCaseInfoSuccess,
  FetchCases,
  FetchCasesSuccess,
  FetchCaseTemplateSettingError,
  FetchCaseTemplateSettings,
  FetchCaseTemplateSettingsSuccess,
  FetchExtUsersGroupListSuccess,
  FetchProjectTemplates,
  FetchProjectTemplatesSuccess,
  FetchTemplateInfoEdit,
  FetchTemplateInfoEditSuccess,
  FetchUnIndexMediaStatus,
  FetchUnIndexMediaStatusSuccess,
  FetchUsersGroupListSuccess,
  GetDefaultProjectTemplate,
  GetDefaultProjectTemplateSuccess,
  GetExtUsersListToInvite,
  GetImageFileExtensions,
  GetImageFileExtensionsSuccessful,
  GetProjectMediaStatus,
  GetProjectMediaStatusSuccessful,
  GetProjectRightList,
  GetProjectRightListSuccessful,
  GetSupportedFileTypesForTranscribing,
  GetSupportedFileTypesForTranscribingSuccessful,
  GetTimeZones,
  GetTimeZonesSuccessful,
  GetTranscribeAccessKeys,
  GetTranscribeAccessKeysSuccessful,
  GetTranscriptStatus,
  GetTranscriptStatusSuccessful,
  GetUsersListToInvite,
  SendInvitation,
  SendInvitationSuccess,
  UpdateCase,
  UpdateCaseError,
  UpdateCaseSuccess,
  UpdateCaseTemplate,
  UpdateCaseTemplateError,
  UpdateCaseTemplateSuccess
} from '../actions'
import { CaseState } from '../reducers/case.reducers'

@Injectable()
export class CaseEffects {
  constructor(
    private actions$: Actions,
    private configService: ConfigService,
    private caseService: LaunchpadService,
    private store: Store<CaseState>,
    private router: Router
  ) {}

  private readonly mapPermissionsOfCases = (
    caseData: CaseModel,
    rightLists = {} as RightModel
  ): CaseModel => {
    if (!(Object.getOwnPropertyNames(rightLists).length > 0)) return caseData

    const { Invalid_Group_Right_List } = rightLists

    const project = cloneDeep(caseData)
    project.DisplayAnalyzeLink =
      !Invalid_Group_Right_List?.ALLOW_TO_VIEW_DASHBOARD
    project.DisplayReprocessingLink =
      !Invalid_Group_Right_List?.ALLOW_TO_REPROCESS
    project.DisplayUploadInviteLink =
      !Invalid_Group_Right_List?.ALLOW_TO_INVITE_DATA_UPLOAD
    project.DisplayUploadLink = !Invalid_Group_Right_List?.ADD_CUSTODIAN_MEDIA
    project.DisplayReviewLink = !Invalid_Group_Right_List?.ALLOW_SEARCH
    project.DisplayProductionLink =
      !Invalid_Group_Right_List?.ALLOW_EXPORT ||
      !Invalid_Group_Right_List?.ALLOW_TO_VIEW_EXPORT_STATUS ||
      !Invalid_Group_Right_List?.ALLOW_TO_DOWNLOAD_EXPORT_ARCHIVES
    return project
  }

  @Effect()
  fetchCaseInfo$: Observable<Action> = this.actions$.pipe(
    ofType<FetchCaseInfo>(CaseActionTypes.FetchCaseInfo),
    switchMap((action) => {
      return this.caseService.fetchCaseInfoById$(action.projectId).pipe(
        mergeMap((res: any) => {
          return [
            new FetchCaseInfoSuccess(res.data),
            new GlobalSuccessAction('Fetching Project Successfully', res)
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  fetchProjectTemplates$: Observable<Action> = this.actions$.pipe(
    ofType<FetchProjectTemplates>(CaseActionTypes.FetchProjectTemplates),
    switchMap(() => {
      return this.caseService.fetchProjectTemplates$().pipe(
        mergeMap((res: any) => {
          return [
            new FetchProjectTemplatesSuccess(res.data),
            new GlobalSuccessAction(
              'Fetching Project Template List Successfull',
              res
            )
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, true, true)]))
      )
    })
  )

  @Effect()
  createCase$: Observable<Action> = this.actions$.pipe(
    ofType<CreateCase>(CaseActionTypes.CreateCase),
    switchMap((action: CreateCase) => {
      return this.caseService.createCase$(action.payload).pipe(
        mergeMap((res: any) => {
          if (res) {
            return [
              // check response before dispatching success action
              new CreateCaseSuccess(res),
              new GlobalSuccessAction(
                `Case ${
                  res[0].ProjectName ? res[0].ProjectName : res[0].ProjectName
                } has been created successfully.`,
                null,
                true,
                false,
                true
              )
            ]
          }
        }),
        catchError((err: HttpErrorResponse) => {
          const msg: string = (err.error || {}).Message || ''
          return from([
            new GlobalErrorAction(err, true, true),
            // since the states are equal on same error text, we need to clear the previous first.
            new CreateCaseError(null),
            new CreateCaseError(msg)
          ])
        })
      )
    })
  )

  @Effect()
  createCaseServiceType$: Observable<Action> = this.actions$.pipe(
    ofType<CreateCaseServiceType>(CaseActionTypes.CreateCaseServiceType),
    switchMap((action: CreateCaseServiceType) => {
      return this.caseService.createCaseServiceType$(action.payload).pipe(
        mergeMap((res: any) => {
          if (res) {
            return [
              // check response before dispatching success action
              new CreateCaseSuccess(res),
              new GlobalSuccessAction(
                `Case ${
                  res[0].ProjectName ? res[0].ProjectName : res[0].ProjectName
                } has been created successfully.`,
                null,
                true,
                false,
                true
              )
            ]
          }
        }),
        catchError((err: HttpErrorResponse) => {
          const msg: string = (err.error || {}).Message || ''
          return from([
            new GlobalErrorAction(err, true, true),
            // since the states are equal on same error text, we need to clear the previous first.
            new CreateCaseError(msg)
          ])
        })
      )
    })
  )

  @Effect()
  updateCase$: Observable<Action> = this.actions$.pipe(
    ofType<UpdateCase>(CaseActionTypes.UpdateCase),
    switchMap((action: UpdateCase) => {
      return this.caseService
        .updateCase$(action.projectId, action.payload)
        .pipe(
          mergeMap((res: any) => {
            if (res) {
              return [
                new UpdateCaseSuccess(res),
                new GlobalSuccessAction(
                  `Case has been updated successfully.`,
                  null,
                  true,
                  false,
                  true
                ),
                new NavigateToAction(`/admin/case/manage`, true)
              ]
            }
          }),
          catchError((err: HttpErrorResponse) => {
            const msg: string = (err.error || {}).Message || ''
            return from([
              new GlobalErrorAction(err, true, true),
              // since the states are equal on same error text, we need to clear the previous first.
              new UpdateCaseError(null),
              new UpdateCaseError(msg)
            ])
          })
        )
    })
  )

  @Effect()
  fetchCaseInfoEdit$: Observable<Action> = this.actions$.pipe(
    ofType<FetchCaseInfoEdit>(CaseActionTypes.FetchCaseInfoEdit),
    switchMap((action: FetchCaseInfoEdit) => {
      return this.caseService.getProjectInfoForEdit$(action.projectId).pipe(
        mergeMap((res: any) => {
          if (res) {
            return [
              new FetchCaseInfoEditSuccess(res),
              new GlobalSuccessAction(`Fetched Case Info Successfully`, res)
            ]
          }
        }),
        catchError((err: HttpErrorResponse) => {
          const msg: string = (err.error || {}).Message || ''
          return from([new GlobalErrorAction(err, true, true)])
        })
      )
    })
  )

  @Effect()
  getTimeZones$: Observable<Action> = this.actions$.pipe(
    ofType<GetTimeZones>(CaseActionTypes.GetTimeZones),
    switchMap(() => {
      return this.caseService.getTimeZones$().pipe(
        mergeMap((res: any) => {
          return [
            new GetTimeZonesSuccessful(res),
            new GlobalSuccessAction('Creating cases successful', res)
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  getImageFileExtensions$: Observable<Action> = this.actions$.pipe(
    ofType<GetImageFileExtensions>(CaseActionTypes.GetImageFileExtensions),
    switchMap(() => {
      return this.caseService.getImageFileExtensions$().pipe(
        mergeMap((res: any) => {
          return [
            new GetImageFileExtensionsSuccessful(res),
            new GlobalSuccessAction('Creating cases successful', res)
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  getProjectMediaStatus$: Observable<Action> = this.actions$.pipe(
    ofType<GetProjectMediaStatus>(CaseActionTypes.GetProjectMediaStatus),
    switchMap(({ projectId }) => {
      return this.caseService.getProjectMediaStatus$(projectId).pipe(
        mergeMap((res: any) => {
          return [
            new GetProjectMediaStatusSuccessful(res),
            new GlobalSuccessAction(
              'Getting project media status successful',
              res
            )
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  getProjectRightList$: Observable<Action> = this.actions$.pipe(
    ofType<GetProjectRightList>(CaseActionTypes.GetProjectRightList),
    switchMap(({ projectId }) =>
      this.caseService.getProjectRightList$(
        projectId,
        localStorage.getItem('ModuleString'),
        true
      )
    ),
    mergeMap((response) => {
      return [
        new GetProjectRightListSuccessful(response, true),
        new GlobalSuccessAction('Getting project right list successful', null)
      ]
    }),
    catchError((err) => from([new GlobalErrorAction(err, false, false)]))
  )

  /**
   * Fetches the rights for a given case.
   * @param {CaseModel} caseData - The case to fetch rights for.
   * @returns {Observable<{ right: RightModel; caseData: CaseModel }>} - An Observable that emits an object containing the case and its rights.
   */
  private fetchRightsForCase(
    caseData: CaseModel
  ): Observable<{ right: RightModel; caseData: CaseModel }> {
    // Fetch the rights and store them in the cache.
    return this.caseService
      .getProjectRightList$(
        caseData.ProjectId,
        localStorage.getItem('ModuleString'),
        true
      )
      .pipe(
        map((right) => {
          const rights = right?.data?.rightLists
          return { right: rights, caseData }
        }),
        catchError((err) => of({ right: null, caseData })) // If fetching rights fails, set rights to null and continue.
      )
  }

  /**
   * Maps an array of cases with their rights.
   * Fetches rights for up to N cases at a time.
   * @param {CaseModel[]} cases - The cases to map with rights.
   * @returns {Observable<CaseModel[]>} - An Observable that emits an array of cases mapped with their rights.
   */
  private mapCasesWithRights(cases: CaseModel[]): Observable<CaseModel[]> {
    return from(cases).pipe(
      concatMap((caseData) => this.fetchRightsForCase(caseData)), // Fetch rights for each case in order.
      map(({ right, caseData }) => this.mapPermissionsOfCases(caseData, right)), // Map each case with its rights.
      toArray() // Collect all mapped cases into an array.
    )
  }

  /**
   * NgRx effect that fetches cases and maps them with their rights.
   *
   * Listens for FetchCases actions and responds by fetching the requested cases, mapping each case with its rights, and dispatching a FetchCasesSuccess action with the mapped cases.
   * If fetching the cases or their rights fails, it dispatches a GlobalErrorAction action.
   */
  @Effect()
  fetchCases$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType<FetchCases>(CaseActionTypes.FetchCases),
      switchMap((action) =>
        this.caseService
          .fetchCases$(
            action.payload.start,
            action.payload.end,
            action.payload.sortBy,
            action.payload.isDesc,
            action.payload.searchTerm,
            action.payload.clientIdString
          )
          .pipe(
            mergeMap((cases) => this.mapCasesWithRights(cases)), // Map the fetched cases with their rights.
            mergeMap((casesMappedWithRights) => [
              new FetchCasesSuccess(casesMappedWithRights), // Dispatch FetchCasesSuccess action with the mapped cases.
              new GlobalSuccessAction(
                'Getting project right list successful',
                null
              ) // Dispatch GlobalSuccessAction action.
            ]),
            catchError((err) => of(new GlobalErrorAction(err, false, false))) // If an error occurs, dispatch GlobalErrorAction action.
          )
      )
    )
  )

  @Effect()
  getUsersListToInvite$: Observable<Action> = this.actions$.pipe(
    ofType<GetUsersListToInvite>(CaseActionTypes.GetUsersListToInvite),
    switchMap((action) => {
      return this.caseService.getUsersList$(action.ProjectId).pipe(
        mergeMap((res: any) => {
          return [
            new FetchUsersGroupListSuccess(res),
            new GlobalSuccessAction('Fetching user group list successful', res)
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  getExtUsersListToInvite$: Observable<Action> = this.actions$.pipe(
    ofType<GetExtUsersListToInvite>(CaseActionTypes.GetExtUsersListToInvite),
    switchMap((action) => {
      return this.caseService.getExtUsersList$().pipe(
        mergeMap((res: any) => {
          if (localStorage.getItem('isExternalUserUploadInviteEnabled')) {
            return [
              new FetchExtUsersGroupListSuccess(res),
              new GlobalSuccessAction(
                'Fetching external user group list successful',
                res
              )
            ]
          } else {
            return [
              new FetchExtUsersGroupListSuccess(res),
              new GlobalSuccessAction(
                'Fetching external user group list successful',
                res
              )
            ]
          }
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  sendInvitation$: Observable<Action> = this.actions$.pipe(
    ofType<SendInvitation>(CaseActionTypes.SendInvitation),
    switchMap((action) => {
      return this.caseService.sendInvitation$(action.payload).pipe(
        mergeMap((res: any) => {
          return [
            new SendInvitationSuccess(res),
            new GlobalSuccessAction('Sending invitation successful', res)
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  fetchCaseTemplateSettings$: Observable<Action> = this.actions$.pipe(
    ofType<FetchCaseTemplateSettings>(
      CaseActionTypes.FetchCaseTemplateSettings
    ),
    switchMap((action: FetchCaseTemplateSettings) => {
      return this.caseService
        .getProjectTemplateSettings$(action.templateId)
        .pipe(
          mergeMap((res: any) => {
            return [new FetchCaseTemplateSettingsSuccess(res.data)]
          }),
          catchError((err) =>
            from([
              new FetchCaseTemplateSettingError(),
              new GlobalErrorAction(err, true, true)
            ])
          )
        )
    })
  )

  @Effect({ dispatch: false })
  fetchCaseTemplateSettingError$: Observable<Action> = this.actions$.pipe(
    ofType<FetchCaseTemplateSettingError>(
      CaseActionTypes.FetchCaseTemplateSettingsError
    ),
    tap(() => this.router.navigate(['/launchpad', 'caselaunchpad']))
  )

  @Effect()
  getTranscriptStatus$: Observable<Action> = this.actions$.pipe(
    ofType<GetTranscriptStatus>(CaseActionTypes.GetTranscriptStatus),
    switchMap((action) => {
      return this.caseService.getTranscriptStatus$(action.projectId).pipe(
        mergeMap((res: any) => {
          return [
            new GetTranscriptStatusSuccessful(res.data),
            new GlobalSuccessAction(
              'Getting project media status successful',
              res.data
            )
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  getSupportedFileTypesForTranscribing$: Observable<Action> = this.actions$.pipe(
    ofType<GetSupportedFileTypesForTranscribing>(
      CaseActionTypes.GetSupportedFileTypesForTranscribing
    ),
    switchMap((action) => {
      return this.caseService.getSupportedFileTypesForTranscribing$().pipe(
        mergeMap((res) => {
          return [
            new GetSupportedFileTypesForTranscribingSuccessful(res),
            new GlobalSuccessAction(
              'Getting Supported File Types For Transcribing   successful',
              res
            )
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  getTranscribeAccessKeys$: Observable<Action> = this.actions$.pipe(
    ofType<GetTranscribeAccessKeys>(CaseActionTypes.GetTranscribeAccessKeys),
    switchMap((action) => {
      return this.caseService.getTranscribeAccessKeys$().pipe(
        mergeMap((res) => {
          return [
            new GetTranscribeAccessKeysSuccessful(res),
            new GlobalSuccessAction(
              'Getting Transcribe access keys successful',
              res
            )
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  getDefaultProjectTemplate$: Observable<Action> = this.actions$.pipe(
    ofType<GetDefaultProjectTemplate>(
      CaseActionTypes.GetDefaultProjectTemplate
    ),
    switchMap(() => {
      return this.caseService.getDefaultProjectTemplate$().pipe(
        mergeMap((res: any) => {
          return [
            new GetDefaultProjectTemplateSuccess(res.data),
            new GlobalSuccessAction('Creating cases successful', res.data)
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )

  @Effect()
  createCaseTemplate$: Observable<Action> = this.actions$.pipe(
    ofType<CreateCaseTemplate>(CaseActionTypes.CreateCaseTemplate),
    switchMap((action: CreateCaseTemplate) => {
      return this.caseService.createCaseTemplate$(action.payload).pipe(
        mergeMap((res: any) => {
          if (res) {
            return [
              // check response before dispatching success action
              new CreateCaseTemplateSuccess(res),
              new GlobalSuccessAction(res.message, null, true, false, true)
            ]
          }
        }),
        catchError((err: HttpErrorResponse) => {
          const msg: string = (err.error || {}).Message || ''
          return from([
            new GlobalErrorAction(err, true, true),
            // since the states are equal on same error text, we need to clear the previous first.
            new CreateCaseTemplateError(null),
            new CreateCaseTemplateError(msg)
          ])
        })
      )
    })
  )

  @Effect()
  fetchTemplateInfoEdit$: Observable<Action> = this.actions$.pipe(
    ofType<FetchTemplateInfoEdit>(CaseActionTypes.FetchTemplateInfoEdit),
    switchMap((action: FetchTemplateInfoEdit) => {
      return this.caseService
        .getProjectTemplateSettings$(action.templateId)
        .pipe(
          mergeMap((res: any) => {
            if (res) {
              return [
                new FetchTemplateInfoEditSuccess(res),
                new GlobalSuccessAction(
                  `Fetched Case template Info Successfully`,
                  res
                )
              ]
            }
          }),
          catchError((err: HttpErrorResponse) => {
            const msg: string = (err.error || {}).Message || ''
            return from([new GlobalErrorAction(err, true, true)])
          })
        )
    })
  )

  @Effect()
  updateCaseTemplate$: Observable<Action> = this.actions$.pipe(
    ofType<UpdateCaseTemplate>(CaseActionTypes.UpdateCaseTemplate),
    switchMap((action: UpdateCaseTemplate) => {
      return this.caseService
        .updateCaseTemplate$(action.templateId, action.payload)
        .pipe(
          mergeMap((res: any) => {
            if (res) {
              return [
                new UpdateCaseTemplateSuccess(res),
                new GlobalSuccessAction(res.message, null, true, false, true),
                new NavigateToAction(
                  `/admin/system/template/manage/manage-case-template`,
                  true
                )
              ]
            }
          }),
          catchError((err: HttpErrorResponse) => {
            const msg: string = (err.error || {}).Message || ''
            return from([
              new GlobalErrorAction(err, true, true),
              // since the states are equal on same error text, we need to clear the previous first.
              new UpdateCaseTemplateError(null),
              new UpdateCaseTemplateError(msg)
            ])
          })
        )
    })
  )

  @Effect()
  fetchProjectMediaStatus$: Observable<Action> = this.actions$.pipe(
    ofType<FetchUnIndexMediaStatus>(CaseActionTypes.FetchUnIndexMediaStatus),
    switchMap(({ projectId }) => {
      return this.caseService.fetchUnIndexMedia$(projectId).pipe(
        mergeMap((unIndexMediaSuccessResponse: any) => {
          return [
            new FetchUnIndexMediaStatusSuccess(unIndexMediaSuccessResponse)
          ]
        }),
        catchError((err) => from([new GlobalErrorAction(err, false, false)]))
      )
    })
  )
}
