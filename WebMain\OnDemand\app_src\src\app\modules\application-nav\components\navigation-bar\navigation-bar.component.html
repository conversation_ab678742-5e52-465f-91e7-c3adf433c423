<div [ngClass]="{ v3: vodVersion === 3 }">
  <nav
    class="navbar navbar-expand-md navbar-light fixed-top bg-light"
    [ngClass]="{ 'review2-nav': isReview2Nav }"
  >
    <a
      [class]="'navbar-brand mb-0 h1 ' + (vodVersion === 3 ? '' : 'px-3 py-0')"
      style="cursor: pointer"
      (click)="goToLaunchpad()"
    >
      <ng-container>
        <img
          src="{{ logoFullPath }}"
          height="38"
          alt="Venio Systems"
          class="py-0"
          [ngClass]="vodVersion === 3 ? 'px-4' : 'px-2'"
        />
        <!--       <span *ngIf="configService.isVodEnabled" class="title-venio">Venio</span>-->
        <!--      <span *ngIf="configService.isVodEnabled" class="title-one">One</span>-->
      </ng-container>
    </a>
    <div
      class="menu-container"
      *ngIf="
        isFBIReview &&
        !router.url.startsWith('/launchpad') &&
        !router.url.startsWith('/notifications') &&
        !(router.url.includes('/admin/') || router.url.includes('/review-next'))
      "
    >
      <div>
        <ng-container *ngTemplateOutlet="review2StaticMenuTpl"></ng-container>
      </div>
      <div *ngIf="isQuickLinkNavigationEnabled && (hasMediaScopeId$ | async)">
        <ng-container *ngTemplateOutlet="quickLinkNavigationTpl"></ng-container>
      </div>
      <div
        *ngIf="
          !enableLimitedOnDemandAccess &&
          configService.isVodEnabled &&
          userDetails?.globalRoleName !== 'Reviewer' &&
          reports[0]?.visible
        "
      >
        <ng-container *ngTemplateOutlet="reportsNavigationTpl"></ng-container>
      </div>
    </div>
    <div class="center-row w-100">
      <!-- Password expiry warning -->
      <div class="relative-position">
        <div
          *ngIf="
            router.url === '/launchpad/caselaunchpad' &&
            notifyForPasswordChange &&
            passwordExpiryMessage &&
            !hidePasswordExpiryWarning
          "
          class="expiration-warning"
        >
          <span
            >{{ passwordExpiryMessage }},
            <a href="javascript:;" (click)="resetPassword()">click&nbsp;here</a>
            to reset the password.</span
          >
          <button
            class="close-expiration-warning-button"
            (click)="closePasswordWarning()"
          ></button>
        </div>
      </div>

      <!-- License expiry warning -->
      <div class="relative-position">
        <div
          *ngIf="
            router.url === '/launchpad/caselaunchpad' &&
            notifyForLicenseExpiry &&
            formattedLicenseExpiryDate &&
            !hideLicenseExpiryWarning
          "
          class="expiration-warning"
        >
          <span>
            Venio License will expire by
            {{ formattedLicenseExpiryDate }}. Please renew.</span
          >
          <button
            class="close-expiration-warning-button"
            (click)="closeLicenseWarning()"
          ></button>
        </div>
      </div>
    </div>
    <button
      class="navbar-toggler"
      type="button"
      data-toggle="collapse"
      data-target="#navbarNavAltMarkup"
      aria-controls="navbarNavAltMarkup"
      aria-expanded="false"
      aria-label="Toggle navigation"
    >
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
      <ul class="navbar-nav ml-auto">
        <li
          *ngIf="
            !router.url.startsWith('/launchpad') &&
            projectInfo &&
            projectInfo.projectName &&
            !router.url.startsWith('/admin') &&
            !router.url.startsWith('/review-next')
          "
          class="nav-item"
        >
          <span
            class="case-select-span"
            *ngIf="!isReviewSetView && !isReview2Nav"
            ><span class="case-label"> Case: </span>
            <mat-select
              [formControl]="projectCtrl"
              class="form-control"
              placeholder="Select a case"
              (selectionChange)="projectValueChanged($event)"
              [matTooltip]="projectInfo.projectName"
            >
              <mat-option>
                <ngx-mat-select-search
                  class="search-project"
                  [formControl]="projectFilterCtrl"
                  placeholderLabel="Select a case"
                  noEntriesFoundLabel="'no matching case found'"
                >
                  <span class="material-icons" ngxMatSelectSearchClear>
                    close
                  </span>
                </ngx-mat-select-search>
              </mat-option>
              <mat-option
                [value]="p.projectId"
                *ngFor="let p of filteredProjects | async"
                >{{ p.projectName }}
              </mat-option>
            </mat-select>
          </span>
          <span *ngIf="isReviewSetView">
            <span class="review-set-name"
              >Review Set: {{ reviewSetInformation.name }}
            </span>
            <span class="review-set-name"
              >Case: {{ projectInfo.projectName }}
            </span>
          </span>
        </li>

        <ng-template [ngIf]="_isNotificationAllowed">
          <li
            class="nav-item dropdown"
            dropdown
            [insideClick]="true"
            #dropdown="bs-dropdown"
            (onShown)="onNotificationDropdownShown()"
          >
            <a
              dropdownToggle
              class="nav-link"
              [ngClass]="{ 'dropdown-toggle': vodVersion !== 3 }"
              style="cursor: pointer"
            >
              <fa-icon
                [icon]="['fas', 'bell']"
                style="font-size: 28px"
                [style.color]="vodVersion === 3 ? '#1EBADC' : '#ffe529'"
              ></fa-icon>
              <span
                class="notification-counter"
                *ngIf="notificationsCount > 0"
                [ngClass]="{ 'v-notification-counter': vodVersion === 3 }"
              >
                {{ notificationsCount > 50 ? '50+' : notificationsCount }}
              </span>
            </a>
            <div
              id="notification-menu"
              class="
                dropdown-menu dropdown-menu-right
                notification-dropdown-content
              "
              role="menu"
              *dropdownMenu
            >
              <div class="modal-header">
                <span>Notifications</span
                ><span *ngIf="notificationsCount > 0">
                  ({{ notificationsCount }})</span
                >
              </div>
              <div class="modal-body p-0">
                <div
                  *ngIf="notifications && notifications.length > 0; else noText"
                  class="notifications-scroll"
                  infiniteScroll
                  [infiniteScrollDistance]="2"
                  [infiniteScrollThrottle]="50"
                  (scrolled)="onScroll()"
                  [alwaysCallback]="true"
                  [scrollWindow]="false"
                >
                  <ul class="p-0">
                    <li
                      (click)="
                        onNotificationClicked(notification.inAppNotificationId)
                      "
                      role="menuitem"
                      *ngFor="
                        let notification of notifications
                          | slice: 0:notificationsToShow
                      "
                    >
                      <a
                        class="dropdown-item dropdown-no-bg"
                        [ngClass]="{
                          'notification-not-viewed':
                            !notification.isNotificationViewed
                        }"
                      >
                        <div class="notification-title-container">
                          <b class="notification-title">{{
                            notification.notificationTitle
                          }}</b>
                          <span class="notification-time">
                            -
                            <fa-icon
                              [icon]="['fas', 'clock']"
                              size="sm"
                            ></fa-icon
                            >&nbsp;
                            {{ notification.queuedDateSpan }}
                          </span>
                        </div>
                        <div id="notification-content-container">
                          <div
                            [innerHTML]="
                              notification.notificationContent
                                | sanitize: 'html'
                            "
                          ></div>
                        </div>
                      </a>
                    </li>
                  </ul>
                </div>
                <ng-template #noText>
                  <div
                    style="
                      height: 80px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    "
                  >
                    <div class="d-inline p-2 bg-secondary text-white">
                      No Notifications
                    </div>
                  </div>
                </ng-template>
              </div>
              <div
                *ngIf="notifications && notifications.length > 0"
                class="modal-footer justify-content-center p-2"
              >
                <div class="btn-group-sm" dropdown>
                  <button
                    id="btn-operator"
                    type="button"
                    (click)="onViewAllNotifications()"
                    class="btn btn-outline-{{ client }}-primary"
                  >
                    View All Notifications
                  </button>
                </div>
              </div>
            </div>
          </li>
        </ng-template>

        <li class="nav-item">
          <a
            class="nav-link"
            href="javascript:void(0)"
            (click)="openVODHelpURL()"
            role="button"
          >
            <fa-icon [icon]="['fas', 'question']" size="sm"></fa-icon>
          </a>
        </li>
        <li
          *ngIf="router.url.startsWith('/review2')"
          [@fadeInOut]
          class="nav-item"
        >
          <a
            class="nav-link"
            [style.font-size]="'small'"
            href="javascript:void(0)"
            (click)="toggleJobStatusView()"
            role="button"
          >
            Jobs
          </a>
        </li>
        <li class="nav-item dropdown bg-grey">
          <a
            class="nav-link dropdown-toggle d-flex align-items-center"
            href="#"
            id="navbar-dropdown"
            role="button"
            data-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
          >
            <i class="fa fa-user-circle"></i>
            <span>
              {{
                !router.url.startsWith('/launchpad/custodian-portal')
                  ? userDetails?.fullName || 'User'
                  : 'User'
              }}</span
            >
          </a>
          <div
            class="dropdown-menu dropdown-menu-right"
            aria-labelledby="navbar-dropdown"
          >
            <a class="dropdown-item">
              <span
                >Welcome!
                {{
                  !router.url.startsWith('/launchpad/custodian-portal')
                    ? userDetails?.fullName || 'User'
                    : 'User'
                }}</span
              >
            </a>
            <div class="dropdown-divider"></div>
            <a
              class="dropdown-item"
              (click)="openUserSettings()"
              *ngIf="!hideForExternal"
            >
              <fa-icon
                class="pr-3"
                [icon]="['fas', 'cogs']"
                size="sm"
              ></fa-icon>
              User Settings
            </a>
            <a
              *ngIf="!this.isAdminSettingsInvalid && !hideForExternal"
              class="dropdown-item"
              routerLink="/admin/dashboard"
            >
              <fa-icon
                class="pr-3"
                [icon]="['fas', 'user']"
                size="sm"
              ></fa-icon>
              Admin Settings
            </a>
            <a class="dropdown-item" (click)="logOff()">
              <fa-icon
                class="pr-3"
                [icon]="['fas', 'sign-out-alt']"
                size="sm"
              ></fa-icon>
              Log off
            </a>
          </div>
        </li>
      </ul>
    </div>
  </nav>
  <div
    *ngIf="!isFBIReview && !router.url.includes('/admin/')"
    [ngClass]="{
      'side-nav-collapse': isFBIReview || router.url.includes('/caselaunchpad'),
      'side-nav': !isFBIReview || !router.url.includes('/caselaunchpad')
    }"
  >
    <ng-container *ngIf="(manageLegalHold | async) && legalHoldLicense">
      <!-- <ul *ngIf="router.url.includes('/caselaunchpad') || router.url.includes('/draft')
            || router.url.includes('/template') || router.url.includes('/questionnaire')
            ||  router.url.includes('/active-directory-setting')"
    > -->
      <ul
        *ngIf="
          router.url.includes('/caselaunchpad') ||
          router.url.includes('/launchpad')
        "
      >
        <li
          [routerLink]="['/launchpad/template']"
          [ngClass]="{ active: router.url.includes('/template') }"
        >
          <a class="nav-link" [routerLink]="['/launchpad/template']">
            <mat-icon>view_quilt</mat-icon>
            <span class="custom-tooltip">Template</span>
          </a>
        </li>
        <li
          [routerLink]="['/launchpad/questionnaire']"
          [ngClass]="{ active: router.url.includes('/questionnaire') }"
        >
          <a class="nav-link" [routerLink]="['/launchpad/questionnaire']">
            <mat-icon> forum</mat-icon>
            <span class="custom-tooltip">Questionnaire</span>
          </a>
        </li>
        <li
          [routerLink]="['/launchpad/active-directory-setting']"
          [ngClass]="{
            active: router.url.includes('/active-directory-setting')
          }"
        >
          <a
            class="nav-link"
            [routerLink]="['/launchpad/active-directory-setting']"
          >
            <mat-icon> settings</mat-icon>
            <span class="custom-tooltip">Active Directory Setting</span>
          </a>
        </li>
        <!-- <li class=""
    >
      <dx-menu #menu
               class="nav-dx-menu d-flex justify-content-center"
               adaptivityEnabled="true"
               [dataSource]="reports"
               displayExpr="name"
               itemsExpr="items"
               [showFirstSubmenuMode]="{name: 'onHover', delay: {show: 0, hide: 500}}"
               orientation="horizontal"
               submenuDirection="auto"
               [hideSubmenuOnMouseLeave]="false"
               (onItemClick)="itemClick($event)"
               itemTemplate="items"
      >
        <div *dxTemplate="let item of 'items'">
          <a *ngIf="item.name === 'Reports'; else submenu"
             class="nav-link dropdown-toggle"
          >
            <fa-icon class="pr-3"
                     [icon]="['fas', 'file']"
            ></fa-icon>
            <span class="custom-tooltip">Reports</span> </a>
          <ng-template #submenu>
            <div class="dx-item-content dx-menu-item-content">
              <span class="dx-menu-item-text">{{item.name}}</span>
              <span *ngIf="item.items"
                    class="dx-menu-item-popout-container"
              >
              <div class="dx-menu-item-popout"></div>
            </span>
            </div>
          </ng-template>
        </div>
      </dx-menu>
    </li> -->
      </ul>
    </ng-container>
    <ul *ngIf="!hideForExternal && !router.url.includes('/review2')">
      <li
        class=""
        routerLink="/launchpad/service_request"
        [ngClass]="{
          active: router.url.startsWith('/launchpad/service_request')
        }"
        *ngIf="
          !configService.isVodEnabled &&
          (allowToUpload$ | async) &&
          !router.url.startsWith('/notifications')
        "
      >
        <a class="nav-link">
          <fa-icon class="pr-3" [icon]="['fas', 'gavel']"></fa-icon>
          <span class="custom-tooltip">Submit Request</span>
        </a>
      </li>
      <li
        class=""
        style="outline: none"
        (click)="navigateToNextPage('add')"
        [ngClass]="{ active: router.url.startsWith('/upload') }"
        *ngIf="
          !router.url.startsWith('/launchpad') &&
          (allowToUpload$ | async) &&
          !router.url.startsWith('/notifications') &&
          userDetails?.globalRoleName !== 'Reviewer'
        "
      >
        <a class="nav-link" #up>
          <ng-container *ngIf="vodVersion !== 3">
            <fa-icon class="pr-3" [icon]="['fas', 'upload']"></fa-icon>
          </ng-container>
          <ng-container *ngIf="vodVersion === 3">
            <div
              appSvgLoader
              svgUrl="assets/images/file-upload.svg"
              height="28px"
              width="30px"
              hoverColor="#FFBB12"
              [isSelectedState]="router.url.match('/upload')"
              [color]="router.url.includes('/upload') ? '#FFBB12' : ''"
              [parentElement]="up"
            ></div>
          </ng-container>
          <span class="custom-tooltip">Upload</span>
        </a>
      </li>
      <li
        class=""
        (click)="navigateToNextPage('analyze')"
        [ngClass]="{
          active:
            router.url.startsWith('/analyze?media=1') ||
            router.url.startsWith('/analyze')
        }"
        *ngIf="
          !enableLimitedOnDemandAccess &&
          (allowToAnalyze$ | async) &&
          !router.url.startsWith('/launchpad') &&
          !router.url.startsWith('/notifications') &&
          userDetails?.globalRoleName !== 'Reviewer'
        "
      >
        <a class="nav-link" #ana>
          <ng-container *ngIf="vodVersion !== 3">
            <fa-icon class="pr-3" [icon]="['fas', 'chart-line']"></fa-icon>
          </ng-container>
          <ng-container *ngIf="vodVersion === 3">
            <div
              appSvgLoader
              svgUrl="assets/images/icon-o-analysis-stats.svg"
              height="28px"
              width="28px"
              hoverColor="#FFBB12"
              [isSelectedState]="router.url.match('/analyze')"
              [color]="router.url.includes('/analyze') ? '#FFBB12' : ''"
              [parentElement]="ana"
            ></div>
          </ng-container>
          <span class="custom-tooltip">Analyze</span>
        </a>
      </li>
      <li
        class=""
        (click)="navigateToNextPage('review')"
        [ngClass]="{
          active:
            router.url.startsWith('/review?media=1') ||
            router.url.startsWith('/review2?media=1') ||
            router.url.match('/review2') ||
            router.url.match('/review-next')
        }"
        *ngIf="
          !enableLimitedOnDemandAccess &&
          ((!isVodrEnabled ||
            (isVodrEnabled && projectInfo?.isFilteringServiceCase)) &&
            allowToReview$ | async) &&
          !router.url.startsWith('/launchpad') &&
          !router.url.startsWith('/notifications') &&
          userDetails?.globalRoleName !== 'Reviewer'
        "
      >
        <a class="nav-link" #review>
          <ng-container *ngIf="vodVersion !== 3">
            <fa-icon class="pr-3" [icon]="['fas', 'search']"></fa-icon>
          </ng-container>
          <ng-container *ngIf="vodVersion === 3">
            <div
              appSvgLoader
              svgUrl="assets/images/icon-rate-review-note.svg"
              height="28px"
              width="28px"
              hoverColor="#FFBB12"
              [isSelectedState]="router.url.match('/review')"
              [color]="router.url.includes('/review') ? '#FFBB12' : ''"
              [parentElement]="review"
            ></div>
          </ng-container>
          <span class="custom-tooltip">Review</span>
        </a>
      </li>

      <li
        class=""
        (click)="navigateToNextPage('produce')"
        [ngClass]="{ active: router.url.startsWith('/production') }"
        *ngIf="
          !enableLimitedOnDemandAccess &&
          ((allowProduction$ | async) ||
            (allowToViewProductionStatus$ | async)) &&
          !router.url.startsWith('/launchpad') &&
          !router.url.startsWith('/notifications') &&
          userDetails?.globalRoleName !== 'Reviewer'
        "
      >
        <a class="nav-link" #produce>
          <ng-container *ngIf="vodVersion !== 3">
            <fa-icon class="pr-3" [icon]="['fas', 'download']"></fa-icon>
          </ng-container>
          <ng-container *ngIf="vodVersion === 3">
            <div
              appSvgLoader
              svgUrl="assets/images/download.svg"
              height="28px"
              width="30px"
              hoverColor="#FFBB12"
              [isSelectedState]="router.url.match('/production')"
              [color]="router.url.includes('/production') ? '#FFBB12' : ''"
              [parentElement]="produce"
            ></div>
          </ng-container>
          <span
            class="custom-tooltip"
            *ngIf="configService.isVodEnabled; else download"
            >Produce</span
          >
          <ng-template #download
            ><span class="custom-tooltip">Download</span></ng-template
          >
        </a>
      </li>
      <li *ngIf="isQuickLinkNavigationEnabled && (hasMediaScopeId$ | async)">
        <ng-container *ngTemplateOutlet="quickLinkNavigationTpl"></ng-container>
      </li>
      <li
        class=""
        *ngIf="
          !enableLimitedOnDemandAccess &&
          !router.url.startsWith('/launchpad') &&
          !router.url.startsWith('/notifications') &&
          configService.isVodEnabled &&
          userDetails?.globalRoleName !== 'Reviewer' &&
          reports[0]?.visible
        "
      >
        <ng-container *ngTemplateOutlet="reportsNavigationTpl"></ng-container>
        <!-- <dx-menu
        #menu
        class="nav-dx-menu d-flex justify-content-center"
        adaptivityEnabled="true"
        [dataSource]="reports"
        displayExpr="name"
        itemsExpr="items"
        [showFirstSubmenuMode]="{
          name: 'onHover',
          delay: { show: 0, hide: 500 }
        }"
        orientation="horizontal"
        submenuDirection="auto"
        [hideSubmenuOnMouseLeave]="true"
        (onItemClick)="itemClick($event)"
        itemTemplate="items"
      >
        <div *dxTemplate="let item of 'items'">
          <a
            *ngIf="item.name === 'Reports'; else submenu"
            class="nav-link dropdown-toggle"
          >
            <fa-icon class="pr-3" [icon]="['fas', 'file']"></fa-icon>
            <span class="custom-tooltip">Reports</span>
          </a>
          <ng-template #submenu>
            <div class="dx-item-content dx-menu-item-content">
              <span class="dx-menu-item-text">{{ item.name }}</span>
              <span *ngIf="item.items" class="dx-menu-item-popout-container">
                <div class="dx-menu-item-popout"></div>
              </span>
            </div>
          </ng-template>
        </div>
      </dx-menu> -->
      </li>
    </ul>
  </div>

  <ng-template #helpUrlTmplt let-data>
    <div class="">
      <div class="modal-header d-flex col-12">
        <h4 class="modal-title pull-left">{{ data?.title }}&nbsp;!</h4>
        <button
          type="button"
          class="close pull-right close-confirm"
          [mat-dialog-close]="false"
        >
          <span aria-hidden="true" class="fa fa-times"></span>
        </button>
      </div>
      <div class="col-12 h5 text-center mb-0 no-url-label modal-body">
        <mat-icon>info</mat-icon>&nbsp;&nbsp;
        <span>{{ data?.message }}</span>
      </div>
      <div class="modal-footer mb-0">
        <button
          type="button"
          class="btn btn-primary float-right close-confirm"
          [mat-dialog-close]="false"
        >
          Ok
        </button>
      </div>
    </div>
  </ng-template>

  <ng-template #quickLinkNavigationTpl>
    <dx-menu
      class="nav-dx-menu d-flex justify-content-center hover-nav-menu"
      [dataSource]="quickLinkNavigationData"
      displayExpr="displayText"
      itemsExpr="items"
      [showFirstSubmenuMode]="{
        name: 'onHover',
        delay: { show: 0, hide: isFBIReview ? 0 : 500 }
      }"
      orientation="horizontal"
      submenuDirection="leftOrTop"
      [hideSubmenuOnMouseLeave]="true"
      itemTemplate="quick-link-template"
      (onItemClick)="quickLinkItemClicked($event)"
    >
      <ng-container *dxTemplate="let quickLink of 'quick-link-template'">
        <a
          *ngIf="quickLink.linkType === 'NONE'; else subQuickLinks"
          [ngClass]="{
            'custom-menu': isFBIReview,
            'nav-link': true,
            active: activeMenuItem === 'Quick Links'
          }"
          class="dropdown-toggle"
          #links
        >
          <ng-container *ngIf="vodVersion !== 3 || isFBIReview">
            <fa-icon
              [ngClass]="isFBIReview ? 'pr-1' : 'pr-3'"
              [icon]="['fas', 'bars']"
            ></fa-icon>
          </ng-container>
          <ng-container *ngIf="vodVersion === 3 && !isFBIReview">
            <div
              appSvgLoader
              svgUrl="assets/images/links.svg"
              height="28px"
              width="28px"
              hoverColor="#FFBB12"
              applyEffectsTo="stroke"
              [parentElement]="links"
            ></div>
          </ng-container>
          <span [ngClass]="{ 'custom-tooltip': !isFBIReview }"
            >Quick Links</span
          >
        </a>
        <ng-template #subQuickLinks>
          <div class="dx-item-content dx-menu-item-content menu-item-content">
            <span class="dx-menu-item-text menu-items">{{
              quickLink.displayText
            }}</span>
            <span *ngIf="quickLink.items" class="dx-menu-item-popout-container">
              <div class="dx-menu-item-popout"></div>
            </span>
          </div>
        </ng-template>
      </ng-container>
    </dx-menu>
  </ng-template>

  <ng-template #reportsNavigationTpl>
    <dx-menu
      class="nav-dx-menu d-flex justify-content-center"
      [ngClass]="{ 'hover-nav-menu': isFBIReview }"
      adaptivityEnabled="!isFBIReview"
      [dataSource]="reports"
      displayExpr="name"
      itemsExpr="items"
      [showFirstSubmenuMode]="{
        name: 'onHover',
        delay: { show: 0, hide: isFBIReview ? 0 : 500 }
      }"
      orientation="horizontal"
      [submenuDirection]="!isFBIReview ? 'auto' : 'leftOrTop'"
      [hideSubmenuOnMouseLeave]="true"
      itemTemplate="items"
      (onItemClick)="itemClick($event)"
    >
      <ng-container *dxTemplate="let item of 'items'">
        <a
          *ngIf="item.name === 'Reports'; else submenu"
          [ngClass]="{
            'custom-menu': isFBIReview,
            'nav-link': true,
            active: activeMenuItem === 'Reports'
          }"
          class="dropdown-toggle"
          #reports
        >
          <ng-container *ngIf="vodVersion !== 3 || isFBIReview">
            <fa-icon
              [ngClass]="isFBIReview ? 'pr-1' : 'pr-3'"
              [icon]="['fas', 'file']"
            ></fa-icon>
          </ng-container>
          <ng-container *ngIf="vodVersion === 3 && !isFBIReview">
            <div
              appSvgLoader
              svgUrl="assets/images/icon-o-graph-raising.svg"
              height="28px"
              width="28px"
              hoverColor="#FFBB12"
              [parentElement]="reports"
            ></div>
          </ng-container>
          <span [ngClass]="{ 'custom-tooltip': !isFBIReview }">Reports</span>
        </a>
        <ng-template #submenu>
          <div
            class="dx-item-content dx-menu-item-content"
            [ngClass]="{ 'menu-item-content': isFBIReview }"
          >
            <span
              class="dx-menu-item-text"
              [ngClass]="{ 'menu-items': isFBIReview }"
              >{{ item.name }}</span
            >
            <span *ngIf="item.items" class="dx-menu-item-popout-container">
              <div class="dx-menu-item-popout"></div>
            </span>
          </div>
        </ng-template>
      </ng-container>
    </dx-menu>
  </ng-template>
  <ng-template #review2StaticMenuTpl>
    <dx-menu
      class="nav-dx-menu d-flex justify-content-center hover-nav-menu"
      [dataSource]="review2MenuItems"
      displayExpr="name"
      itemsExpr="items"
      [showFirstSubmenuMode]="{
        name: 'onHover',
        delay: { show: 0, hide: isFBIReview ? 0 : 500 }
      }"
      itemMargin="2"
      orientation="horizontal"
      submenuDirection="leftOrTop"
      [hideSubmenuOnMouseLeave]="true"
      itemTemplate="items"
    >
      <ng-container *dxTemplate="let item of 'items'">
        <a
          [ngClass]="{
            'custom-menu': isFBIReview,
            'nav-link': true,
            active: activeMenuItem?.toLowerCase() === item.name?.toLowerCase()
          }"
          (click)="navigateToNextPage(item.navigateToNextPage, item.name)"
        >
          <fa-icon
            [ngClass]="isFBIReview ? 'pr-1' : 'pr-3'"
            [icon]="['fas', item.icon]"
          ></fa-icon>
          <span [ngClass]="{ 'custom-tooltip': !isFBIReview }">
            {{ item.name }}</span
          ></a
        >
      </ng-container>
    </dx-menu>
  </ng-template>
</div>
