import { CaseModel } from '@admin-advance/models'
import { CaseStateSelector, FetchCaseListAction } from '@admin-advance/store'
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
  ViewChild
} from '@angular/core'
import {
  AbstractControl,
  AsyncValidatorFn,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { select, Store } from '@ngrx/store'
import * as XS from '@ngxs/store'
import { Store as XSStore } from '@ngxs/store'
import { LoggingService } from '@root/services/logging.service'
import { NotificationService } from '@root/services/notification.service'
import { TimeZone, TimeZoneDescription } from '@shared/models'
import { StringUtils } from '@shared/utils/string-utils'
import * as fromRootActions from '@stores/actions'
import { SetVODRSettings } from '@stores/actions'
import * as fromCaseStateModels from '@stores/models'
import {
  ColorConversion,
  ControlNumberEndorsement,
  ControlNumberSetting,
  FilterOptions,
  ImageConversionOption,
  PDFServiceOption,
  PrintBinding,
  PrintServiceOption,
  PrintSet,
  ProductionOptions,
  ServiceRequestType,
  ServiceType,
  ServiceTypeDescription,
  SERVICE_TYPE_CONSTANTS,
  SettingsInfo,
  VODRSettings
} from '@stores/models'
import { CaseSelectors, ProductionSelectors } from '@stores/selectors'
import _ from 'lodash'
import { BsModalService } from 'ngx-bootstrap/modal'
import { Observable, of, Subject } from 'rxjs'
import {
  distinctUntilChanged,
  filter,
  map,
  take,
  takeUntil
} from 'rxjs/operators'
import {
  checkIfControlNumberHasConflict,
  clearControlNumberConflictResponse
} from '../../stores/actions/production.actions'
import { ConfirmationDialogComponent } from '../launchpad/components/confirmation-dialog/confirmation-dialog.component'
import {
  CreateCaseError,
  CreateCaseServiceType,
  CreateCaseSuccess
} from '../launchpad/store'
import {
  getErrorMessage,
  newCase
} from '../launchpad/store/selectors/case.selectors'
import { ResponseModel } from '../shared/models/response.model'
import { ServiceUploadDetailComponent } from './components/service-upload-detail/service-upload-detail.component'
enum CaseSortType {
  Id = 'projectId',
  Name = 'projectName'
}

@Component({
  selector: 'app-service-upload-container',
  templateUrl: './service-upload-container.component.html',
  styleUrls: ['./service-upload-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ServiceUploadContainerComponent implements OnInit {
  @ViewChild(ServiceUploadDetailComponent) child: ServiceUploadDetailComponent

  currentStep = 1

  timeZone$: Observable<fromCaseStateModels.TimeZone[]>

  settingsForm: FormGroup

  serviceTypeList: any[]

  exportTemplates: Observable<fromCaseStateModels.ExportFieldTemplate[]>

  /**
   * VODR Settings
   */
  settings: SettingsInfo

  continueFromPreviousControlNumber = false

  private unsubscribed$ = new Subject<void>()

  openAllAccordions: boolean

  serviceInfoData: SettingsInfo

  defaultSettingsInfo: SettingsInfo

  showSpinner = false

  /**
   * Sort by type {@link CaseSortType}
   */
  sortType: CaseSortType = CaseSortType.Id

  /**
   * Sort in descending order?
   */
  sortDesc = true

  /**
   * Start index for the list of cases.
   */
  pageIndex = 1

  /**
   * Size of the batch of cases to be fetched.
   */
  batchSize = 100

  caseList: CaseModel[]

  existingCaseId: number

  existingCase = false

  private EXISTING_CASE_SERVICE_TYPE = {
    serviceTypeDisplayName: 'Add data to an existing case',
    serviceTypeId: SERVICE_TYPE_CONSTANTS.VODR_ADD_DATA_EXISTING_PROJECT,
    serviceTypeName: SERVICE_TYPE_CONSTANTS.VODR_ADD_DATA_EXISTING_PROJECT
  }

  isCaseCreationFlow: boolean

  private isContinuedAfterControlNumberConflict = false

  controlNumberConflictResponse$: Observable<ResponseModel>

  constructor(
    public dialogRef: MatDialogRef<ServiceUploadContainerComponent>,
    private fb: FormBuilder,
    private store: Store,
    private xsStore: XSStore,
    private cdr: ChangeDetectorRef,
    private xSStore: XS.Store,
    private route: ActivatedRoute,
    private router: Router,
    private notification: NotificationService,
    private logger: LoggingService,
    private modalService: BsModalService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.existingCaseId = data?.projectId
  }

  ngOnInit(): void {
    this.xsStore.dispatch(new fromRootActions.FetchTimeZones())
    // Fetch export templates
    this.xsStore.dispatch(new fromRootActions.FetchExportFieldTemplates(-1))

    this.timeZone$ = this.xsStore.select(CaseSelectors.timeZonesSorted)
    this.exportTemplates = this.xsStore
      .select(ProductionSelectors.exportTemplates)
      .pipe(map((filterFn) => filterFn(-1)))

    this.xsStore
      .select(CaseSelectors.serviceTypeDefaultData)
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        if (res) {
          this.defaultSettingsInfo = res
        }
      })

    const param = this.prepareParam(this.pageIndex, this.batchSize)
    this.xsStore.dispatch(new FetchCaseListAction(param))
    this.initializeForm()
    this.xsStore
      .select(CaseStateSelector.SliceOf('casesList'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe({
        next: (res: CaseModel[]) => {
          this.caseList = res
          if (this.existingCaseId && this.settingsForm) {
            this.settingsForm.patchValue({ selectedCase: this.existingCaseId })
            this.cdr.detectChanges()
          }
        }
      })
    this.fetchServiceTypeList()
    this.checkSessionIsExpired()

    this.xsStore
      .select(CaseSelectors.fetchExistingCaseData)
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        if (res) {
          this.updateDefaultValues(res)
        }
      })
    if (this.existingCaseId) {
      this.xsStore.dispatch(
        new fromRootActions.FetchExistingCaseData(this.existingCaseId)
      )
    }

    this.controlNumberConflictResponse$ = this.xsStore.select(
      ProductionSelectors.controlNumberConflictResponse
    )
    this.#handleControlNumberConflictResponse()
  }

  ngAfterViewInit(): void {
    this.setupExistingCaseSubscription()
    this.isCaseCreationFlow = this.data?.isCaseCreationFlow ?? true
    this.cdr.detectChanges()
  }

  private updateDefaultValues(data: any) {
    this.settingsForm.patchValue({
      exportTemplateName: data.exportTemplateName,
      approvePreProcessPage_CostEstimate:
        data.approvePreProcessPage_CostEstimate,
      generalSettings: {
        deduplicationOption: data.imageConversionOption.deduplicationOption,
        timeZone: data.tzTimeZone,
        csvExcelHandling: data.imageConversionOption.csV_Excel_option,
        discoveryExceptionHandling: data.enableDiscoveryExceptionHandling,
        passwords: '',
        autoGenerateImagesAfterIngestion:
          data.imageConversionOption.autoGenerateImagesAfterIngestion,
        ignoreAutoTiffJobsForMediaProcessingStatus:
          data.imageConversionOption.ignoreAutoTiffJobsForMediaProcessingStatus
      },
      imageConversionSettings: {
        imageColorConversion: {
          imageFileType:
            data.imageConversionOption.imageColorConversion.imageFileType,
          pdfFiles: data.imageConversionOption.imageColorConversion.pdfFiles,
          powerpoint: data.imageConversionOption.imageColorConversion.powerpoint
        },
        passwordList: this.#getFormValueFromPasswords(
          data.imageConversionOption.passwordList
        )
      },
      controlNumberAndEndorsementSettings: {
        sortOrder: data.controlNumber_Endorsement.sortOrder,
        exportLocation: data.controlNumber_Endorsement.exportLocation,
        ControlNumberSetting: {
          controlNumberPrefix:
            data.controlNumber_Endorsement.controlNumberSetting.prefix,
          controlNumberDelimiter:
            data.controlNumber_Endorsement.controlNumberSetting.prefixDelimiter,
          controlNumberStartingNumber:
            data.controlNumber_Endorsement.controlNumberSetting.startNumber,
          endorseControlNumber:
            data.controlNumber_Endorsement.controlNumberSetting
              .endorseControlNumber,
          controlNumberLocation:
            data.controlNumber_Endorsement.controlNumberSetting
              .controlNumberLocation,
          endorseOptionalMessage:
            data.controlNumber_Endorsement.controlNumberSetting
              .endorseOptionalMessage,
          messageText:
            data.controlNumber_Endorsement.controlNumberSetting.messageText,
          messageTextLocation:
            data.controlNumber_Endorsement.controlNumberSetting
              .optionalMessageLocation,
          volumeId:
            data.controlNumber_Endorsement.controlNumberSetting.volumnId,
          paddingLength:
            data.controlNumber_Endorsement.controlNumberSetting.paddingLength,
          continueFromPreviousControlNumber:
            data.controlNumber_Endorsement.controlNumberSetting
              .continueFromPreviousControlNumber,
          advancedEndorsementSetting:
            data.controlNumber_Endorsement.controlNumberSetting
              .advancedEndorsementSetting,
          prefixDelimiterValue:
            data.controlNumber_Endorsement.controlNumberSetting
              .prefixDelimiterValue
        }
      },
      productionSettings: {
        fieldTemplateId: data.productionOptions?.fieldTemplateID,
        filterOptions: {
          excludeProducedDocuments:
            data.productionOptions.filterOptions.excludeProducedDocuments,
          excludeNativeForRedactedDocuments:
            data.productionOptions.filterOptions
              .excludeNativeForRedactedDocuments
        },
        savedSearchesForExpressions:
          data.productionOptions.savedSearchesForExpressions
      },
      pdfServiceSettings: {
        pdfType: data.pdfServiceOption.pdfType,
        pdfFamilyFileHandling: data.pdfServiceOption.pdfFamilyFileHandling,
        pdfFileNamingConvention: data.pdfServiceOption.pdfFileNamingConvention
      },
      thirdPartyBillingOption: {
        thirdPartyBillingEnabled:
          data.thirdPartyBillingOption.thirdPartyBillingEnabled,
        company: data.thirdPartyBillingOption.company,
        billingAddress: data.thirdPartyBillingOption.billingAddress,
        billingCaseName: data.thirdPartyBillingOption.billingCaseName,
        contactPerson: data.thirdPartyBillingOption.contactPerson,
        contactPhone: data.thirdPartyBillingOption.contactPhone,
        contactEmail: data.thirdPartyBillingOption.contactEmail
      },
      webURL: data.webURL,
      clientMatterNo: '',
      createImage: data.createImage,
      dataRetentionRequest: data.dataRetentionRequest,
      editableCustomFieldList: data.editableCustomFieldList,
      productionSourceId: data.productionSourceId,
      enableDiscoveryExceptionHandling: data.enableDiscoveryExceptionHandling,
      autoQueueForEntityExtraction: data.autoQueueForEntityExtraction
    })

    if (this.existingCase) {
      this.settingsForm.patchValue({
        caseName: data?.caseName,
        ServiceRequestType:
          SERVICE_TYPE_CONSTANTS.VODR_ADD_DATA_EXISTING_PROJECT,
        serviceRequestTypeExisting: data.serviceRequestType
      })
    } else {
      this.settingsForm.patchValue({
        ServiceRequestType: data.serviceRequestType
      })
    }
  }

  initializeForm(): void {
    this.settingsForm = this.fb.group({
      caseName: [
        '',
        {
          validators: Validators.required,
          asyncValidators: [this.uniqueProjectNameValidator()],
          updateOn: 'blur'
        }
      ],
      selectedCase: [],
      ServiceRequestType: [null, Validators.required],
      serviceRequestTypeExisting: [null],
      exportTemplateName: [],
      overrideSetting: [false],
      approvePreProcessPage_CostEstimate: [false],
      generalSettings: this.fb.group(
        {
          deduplicationOption: [2],
          timeZone: [188],
          csvExcelHandling: [0],
          discoveryExceptionHandling: [true],
          passwords: [''],
          autoGenerateImagesAfterIngestion: [true],
          ignoreAutoTiffJobsForMediaProcessingStatus: [false]
        },
        { validator: this.validateGeneralSettingsForm }
      ),
      imageConversionSettings: this.fb.group({
        imageColorConversion: this.fb.group({
          imageFileType: [1],
          pdfFiles: [1],
          powerpoint: [1]
        }),
        passwordList: ['']
      }),
      controlNumberAndEndorsementSettings: this.fb.group({
        sortOrder: ['RELATIVE_FILE_PATH'],
        exportLocation: [1],
        ControlNumberSetting: this.fb.group(
          {
            controlNumberPrefix: [''],
            controlNumberDelimiter: [0],
            controlNumberStartingNumber: [1],
            endorseControlNumber: [false],
            controlNumberLocation: [5],
            endorseOptionalMessage: [false],
            messageText: [''],
            messageTextLocation: [3],
            volumeId: [''],
            paddingLength: [8],
            continueFromPreviousControlNumber: [false],
            advancedEndorsementSetting: [null],
            prefixDelimiterValue: ['']
          },
          { validator: this.validateControlNumberAndEndorsementSettings }
        )
      }),
      productionSettings: this.fb.group({
        fieldTemplateId: [''],
        filterOptions: this.fb.group({
          excludeProducedDocuments: [false],
          excludeNativeForRedactedDocuments: [false]
        }),
        savedSearchesForExpressions: [null]
      }),
      pdfServiceSettings: this.fb.group({
        pdfType: [0],
        pdfFamilyFileHandling: [0],
        pdfFileNamingConvention: [0]
      }),
      thirdPartyBillingOption: this.fb.group({
        thirdPartyBillingEnabled: [false],
        company: [null],
        billingAddress: [null],
        billingCaseName: [null],
        contactPerson: [null],
        contactPhone: [null],
        contactEmail: [null]
      }),
      webURL: ['http://localhost/VenioWeb'],
      clientMatterNo: [''],
      createImage: [true],
      dataRetentionRequest: [0],
      editableCustomFieldList: [null],
      productionSourceId: [''],
      enableDiscoveryExceptionHandling: [true],
      autoQueueForEntityExtraction: [false]
    })
  }

  fetchServiceTypeList() {
    this.xsStore.dispatch(new fromRootActions.FetchServiceTypeList())

    this.xsStore
      .select(CaseSelectors.serviceTypes)
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((result: any) => {
        if (result?.data) {
          this.serviceTypeList = _.cloneDeep(
            this.transformData(result?.data)?.serviceTypes
          )
          this.serviceTypeList.push(this.EXISTING_CASE_SERVICE_TYPE)
        }
      })
  }

  nextStep(): void {
    if (!this.validateSettingsForm()) {
      return // If validation fails, stop the method execution here.
    }
    // if creating new case or not overriding settings then no need to check for control number conflict
    if (
      !this.existingCase ||
      !this.settingsForm.get('overrideSetting')?.value ||
      this.currentStep !== 1
    ) {
      this.progressToNextStep()
    }
    // else call API to check for control number conflict
    else {
      this.showSpinner = true
      const settingsInfo = _.cloneDeep(this.getSettingsFromTheForm())
      this.xSStore.dispatch(
        new checkIfControlNumberHasConflict(this.existingCaseId, settingsInfo)
      )
    }
  }

  #handleControlNumberConflictResponse(): void {
    this.controlNumberConflictResponse$
      .pipe(
        filter(
          (controlNumberConflict) =>
            controlNumberConflict !== undefined &&
            controlNumberConflict !== null
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((controlNumberConflict: ResponseModel) => {
        this.showSpinner = false
        this.xsStore.dispatch(new clearControlNumberConflictResponse())

        // show error message if API throws an error
        if (controlNumberConflict.status?.toLowerCase() === 'error') {
          this.showConfirmationModal(
            controlNumberConflict.message ||
              'Error occurred while checking for control number conflict',
            'Error'
          )
        }
        // if there is conflict in control number then show confirmation dialog to continue with existing control number
        else if (
          typeof controlNumberConflict.data === 'boolean' &&
          controlNumberConflict.data === true
        ) {
          this.#confirmControlNumberContinuation()
        }
        // if there is no control number conflict then continue to next step
        else {
          this.progressToNextStep()
        }
      })
  }

  #confirmControlNumberContinuation(): void {
    const message =
      'There is conflict in control number. Do you want to continue with existing control number?'
    const title = 'Control number conflict'
    this.showConfirmationModal(message, title)
      .pipe(take(1), takeUntil(this.unsubscribed$))
      .subscribe((result) => {
        if (typeof result === 'boolean' && result === true) {
          this.isContinuedAfterControlNumberConflict = true
          this.progressToNextStep()
        }
      })
  }

  private progressToNextStep(): void {
    this.updateCurrentStep()
    this.toggleSpinner()

    const settingsInfo: any = _.cloneDeep(this.getSettingsFromTheForm())
    settingsInfo.controlNumber_Endorsement.controlNumberSetting.volumnId =
      settingsInfo.controlNumber_Endorsement.controlNumberSetting.volumeId
    if (this.existingCase) {
      settingsInfo.serviceRequestType =
        this.settingsForm?.value?.serviceRequestTypeExisting
    }
    this.processServiceInfoData(settingsInfo)

    if (this.currentStep === 3) {
      this.finalizeStep(settingsInfo)
    }
    this.cdr.detectChanges()
  }

  private validateSettingsForm(): boolean {
    if (this.currentStep === 1) {
      if (this.settingsForm.invalid) {
        this.settingsForm.markAllAsTouched()
        this.openAllAccordions = true
        return false // Return false to indicate failure in validation.
      }
      this.openAllAccordions = false
    }
    return true // Return true to indicate success in validation or that no validation was needed.
  }

  private updateCurrentStep(): void {
    this.currentStep =
      this.currentStep < 3 ? this.currentStep + 1 : this.currentStep
  }

  private toggleSpinner(): void {
    this.showSpinner = this.currentStep === 3
  }

  private processServiceInfoData(settingsInfo: any): void {
    const settingsInfoData = this.removeData(settingsInfo)
    this.serviceInfoData = settingsInfoData
    this.serviceInfoData.clientMatterNo = ''
  }

  private finalizeStep(settingsInfo: SettingsInfo): void {
    this.cdr.detectChanges()

    if (this.existingCase) {
      this.xSStore
        .dispatch(new SetVODRSettings(settingsInfo))
        .pipe(take(1))
        .subscribe(() => {
          const override = this.settingsForm.get('overrideSetting')?.value
          this.navigateTo(this.existingCaseId, this.existingCase, override, -1)
          this.closeDialogAndCleanUp()
        })
    } else {
      this.handleNewCase(settingsInfo) // Keep the dialog open if creating a new case until the async operation completes
    }
  }

  private navigateTo(
    projectId: number,
    isExistingCase: boolean,
    overrideSettings: boolean,
    settingId: number
  ): void {
    this.router
      .navigate(['/upload'], {
        queryParams: {
          projectId: projectId,
          existingCase: isExistingCase,
          overrideSettings: overrideSettings,
          settingId: settingId,
          afterConflict: this.isContinuedAfterControlNumberConflict,
          structuredData: false // Automatically selects structured data type.
        }
      })
      .then(() => {
        // Place where necessary cleanups need to be done after navigating to upload

        /**
         * We are having issue from the state when the object are persisted,
         * Once we receive success response with object and redirecting to the different component,
         * we no longer need the same object.
         */
        this.store.dispatch(new CreateCaseSuccess(null))
      })
  }

  private closeDialogAndCleanUp(): void {
    this.dialogRef.close()
    this.showSpinner = false
    this.cdr.markForCheck()
  }

  private handleNewCase(settingsInfo: SettingsInfo): void {
    this.store.dispatch(new CreateCaseError(null))
    this.store.dispatch(new CreateCaseServiceType(this.serviceInfoData))
    this.store
      .pipe(
        select(newCase),
        filter((p) => p instanceof Array && p[0]?.ProjectId > 0),
        map((p) => ({ project: p[0], serviceId: p[1] })),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({
        next: ({ project, serviceId }) => {
          this.xSStore
            .dispatch(new SetVODRSettings(settingsInfo))
            .pipe(take(1))
            .subscribe(() => {
              this.navigateTo(project.ProjectId, false, false, serviceId)
              this.closeDialogAndCleanUp() // Now close the dialog and cleanup only on success.
            })
        },
        error: (err) => {
          // Optionally handle any errors here
          this.displayResponseError()
        }
      })

    this.displayResponseError()
  }

  private removeData(settingsInfoData: SettingsInfo) {
    delete settingsInfoData?.imageConversionOption?.timeZone
    delete settingsInfoData?.imageConversionOption?.csvExcelOption
    delete settingsInfoData?.controlNumberEndorsement
    delete settingsInfoData?.serviceType
    delete settingsInfoData?.editableCustomFields
    delete settingsInfoData?.approvePreProcessPageCostEstimate
    delete settingsInfoData?.productionOptions?.fieldTemplateId
    delete settingsInfoData?.controlNumber_Endorsement?.controlNumberSetting
      .volumeId
    return settingsInfoData
  }

  previousStep(): void {
    this.currentStep =
      this.currentStep > 1 ? this.currentStep - 1 : this.currentStep
  }

  private setupExistingCaseSubscription(): void {
    this.child.existingCaseValue
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((value) => {
        this.existingCase = value
        if (this.settingsForm && this.settingsForm.get('caseName')) {
          this.settingsForm.get('caseName').updateValueAndValidity() // Check if form and control exist
        }
      })

    this.child.selectedCaseIdValue
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribed$))
      .subscribe((value) => {
        this.existingCaseId = value
        if (this.existingCaseId) {
          this.xsStore.dispatch(
            new fromRootActions.FetchExistingCaseData(this.existingCaseId)
          )
        }
      })
  }

  /**
   * Prepares query param for the case list to get from API
   * @param start Page index begin
   * @param end Page index end
   * @param searchTerm Search term
   */
  private prepareParam(start: number, end: number, searchTerm?: string) {
    const userId = +localStorage.getItem('UserId')
    // We don't have to worry about checking search term value here cause we've validated in service class.
    return {
      userId: userId,
      start: start,
      end: end,
      sortBy: this.sortType,
      isDesc: this.sortDesc,
      searchTerm: searchTerm,
      clientIdString: ''
    }
  }

  private uniqueProjectNameValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) {
        return of(null)
      }
      if (!this.existingCase) {
        const isNotUnique = this.caseList?.some(
          (project) =>
            project.ProjectName.toLowerCase() === control.value.toLowerCase()
        )
        return of(isNotUnique).pipe(
          map((result) =>
            result ? { notUniqueName: { value: control.value } } : null
          )
        )
      } else {
        // If existingCase is true, skip the uniqueness validation
        return of(null)
      }
    }
  }

  /**
   * Removes the UI blocker.
   */
  private displayResponseError() {
    this.store
      .select(getErrorMessage)
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        if (res !== null) {
          this.notification.showError('Error creating service request.', true)
          this.logger.logError(res)
          this.currentStep = 2
          this.showSpinner = false
          this.cdr.detectChanges()
        }
      })
  }

  /**
   * Creates ad returns the VODR SettingsInfo object from the user selected values
   */
  private getSettingsFromTheForm(): SettingsInfo {
    const settingsInfo = new SettingsInfo()
    const vodSettings = new VODRSettings()
    this.updateDefaultDataFromServerData(
      settingsInfo,
      vodSettings.addDataToExistingCase,
      vodSettings.overrideSettingsInfo,
      vodSettings.isContinuedAfterControlNumberConflict,
      this.defaultSettingsInfo
    )
    this.getServiceSelectionSettingsFromTheForm(settingsInfo)
    this.getGeneralSettingsFromTheForm(settingsInfo)
    this.getImageConversionSettingsFromTheForm(settingsInfo)
    this.getControlSettingsFromTheForm(settingsInfo)
    this.getPdfSettingsFromTheForm(settingsInfo)
    this.getPrintSettingsFromTheForm(settingsInfo)
    this.getProductionSettingsFromTheForm(settingsInfo)

    return settingsInfo
  }

  private updateDefaultDataFromServerData(
    settingsInfo: SettingsInfo,
    addDataToExistingCase: any,
    overrideSettingsInfo: any,
    isContinuedAfterControlNumberConflict: any,
    serverData: any
  ): void {
    // Update simple fields
    settingsInfo.notifyAfterImgGenComplete =
      serverData?.notifyAfterImgGenComplete
    settingsInfo.generateColorImage = serverData?.generateColorImage
    settingsInfo.defaultTiffColorOption = serverData?.defaultTiffColorOption
    settingsInfo.searchDupOption = serverData?.searchDupOption
    settingsInfo.searchTerm = serverData?.searchTerm
    settingsInfo.timeZone = serverData?.timeZone
    settingsInfo.tzTimeZone = serverData?.tzTimeZone
    settingsInfo.enableNativeFileHandling = serverData?.enableNativeFileHandling
    settingsInfo.serviceRequestType = serverData?.serviceRequestType
    settingsInfo.exportTemplateName = serverData?.exportTemplateName
    settingsInfo.approvePreProcessPage_CostEstimate =
      serverData?.approvePreProcessPage_CostEstimate

    // Update nested objects
    const imageConversion =
      serverData?.imageConversionOption?.imageColorConversion
    settingsInfo.imageConversionOption.imageColorConversion.imageFileType =
      imageConversion?.imageFileType
    settingsInfo.imageConversionOption.imageColorConversion.pdfFiles =
      imageConversion?.pdfFiles
    settingsInfo.imageConversionOption.imageColorConversion.powerpoint =
      imageConversion?.powerpoint

    settingsInfo.imageConversionOption.passwordList =
      serverData?.imageConversionOption?.passwordList
    settingsInfo.imageConversionOption.deduplicationOption =
      serverData?.imageConversionOption?.deduplicationOption
    settingsInfo.imageConversionOption.timeZone =
      serverData?.imageConversionOption?.timeZone
    settingsInfo.imageConversionOption.csV_Excel_option =
      serverData?.imageConversionOption?.csV_Excel_option
    settingsInfo.imageConversionOption.autoGenerateImagesAfterIngestion =
      serverData?.imageConversionOption?.autoGenerateImagesAfterIngestion
    settingsInfo.imageConversionOption.ignoreAutoTiffJobsForMediaProcessingStatus =
      serverData?.imageConversionOption?.ignoreAutoTiffJobsForMediaProcessingStatus

    // Update other fields like controlNumber_Endorsement, printServiceOption, pdfServiceOption
    settingsInfo.controlNumber_Endorsement.sortOrder =
      serverData?.controlNumber_Endorsement?.sortOrder
    settingsInfo.controlNumber_Endorsement.controlNumberSetting.prefix =
      serverData?.controlNumber_Endorsement?.controlNumberSetting?.prefix
    settingsInfo.controlNumber_Endorsement.controlNumberSetting.prefixDelimiter =
      serverData?.controlNumber_Endorsement?.controlNumberSetting?.prefixDelimiter
    settingsInfo.controlNumber_Endorsement.controlNumberSetting.startNumber =
      serverData?.controlNumber_Endorsement?.controlNumberSetting?.startNumber

    // Similarly update other fields based on server data...

    // Update top-level fields
    addDataToExistingCase =
      serverData?.addDataToExistingCase || addDataToExistingCase
    overrideSettingsInfo =
      serverData?.overrideSettingsInfo || overrideSettingsInfo
    isContinuedAfterControlNumberConflict =
      serverData?.isContinuedAfterControlNumberConflict ||
      isContinuedAfterControlNumberConflict
  }

  /**
   * Prepares service selection page related options in the SettingsInfo from the user selected form values.
   * @param settingsInfo VODR Settings
   */
  private getServiceSelectionSettingsFromTheForm(settingsInfo: SettingsInfo) {
    const formValue = this.settingsForm.value

    settingsInfo.caseName = formValue?.caseName || settingsInfo?.caseName
    // settingsInfo.serviceRequestType = formValue?.ServiceRequestType !==
    //   SERVICE_TYPE_CONSTANTS.VODR_ADD_DATA_EXISTING_PROJECT
    //   ? this.getServiceTypeFromTheForm(formValue?.ServiceRequestType)
    //   : (this.settings && !_.isEmpty(this.settings))
    //     ? this.settings.serviceRequestType
    //     : settingsInfo.serviceRequestType;
    settingsInfo.serviceRequestType =
      formValue?.ServiceRequestType || settingsInfo?.serviceRequestType

    // settingsInfo.exportTemplateName = formValue?.ServiceRequestType !==
    //   SERVICE_TYPE_CONSTANTS.VODR_ADD_DATA_EXISTING_PROJECT
    //   ? formValue?.exportTemplateName
    //   : (this.settings && !_.isEmpty(this.settings))
    //     ? this.settings.exportTemplateName
    //     : settingsInfo.exportTemplateName;
    settingsInfo.exportTemplateName =
      formValue?.exportTemplateName || settingsInfo?.exportTemplateName
    settingsInfo.dataRetentionRequest =
      formValue?.dataRetentionType || settingsInfo?.dataRetentionRequest
    settingsInfo.createImage =
      formValue?.createImages != null
        ? formValue?.createImages
        : settingsInfo.createImage

    // ThirdPartyBillingOption handling with fallbacks
    settingsInfo.thirdPartyBillingOption.thirdPartyBillingEnabled =
      formValue?.thirdPartyBilling?.thirdPartyBillingEnabled ||
      settingsInfo?.thirdPartyBillingOption.thirdPartyBillingEnabled
    settingsInfo.thirdPartyBillingOption.company =
      formValue?.thirdPartyBilling?.company ||
      settingsInfo?.thirdPartyBillingOption.company
    settingsInfo.thirdPartyBillingOption.billingAddress =
      formValue?.thirdPartyBilling?.billingAddress ||
      settingsInfo?.thirdPartyBillingOption.billingAddress
    settingsInfo.thirdPartyBillingOption.billingCaseName =
      formValue?.thirdPartyBilling?.billingCaseName ||
      settingsInfo?.thirdPartyBillingOption.billingCaseName
    settingsInfo.thirdPartyBillingOption.contactPerson =
      formValue?.thirdPartyBilling?.contactPerson ||
      settingsInfo?.thirdPartyBillingOption.contactPerson
    settingsInfo.thirdPartyBillingOption.contactPhone =
      formValue?.thirdPartyBilling?.contactPhone ||
      settingsInfo?.thirdPartyBillingOption.contactPhone
    settingsInfo.thirdPartyBillingOption.contactEmail =
      formValue?.thirdPartyBilling?.contactEmail ||
      settingsInfo?.thirdPartyBillingOption.contactEmail
  }

  /**
   * Prepares general options in the SettingsInfo from the user selected form values.
   * @param settingsInfo VODR Settings
   */
  private getGeneralSettingsFromTheForm(settingsInfo: SettingsInfo) {
    const generalFormValue = (<FormGroup>(
      this.settingsForm['controls'].generalSettings
    )).getRawValue()

    settingsInfo.enableDiscoveryExceptionHandling =
      generalFormValue.discoveryExceptionHandling != null
        ? generalFormValue.discoveryExceptionHandling
        : settingsInfo.enableDiscoveryExceptionHandling
    settingsInfo.indexOnlyCase =
      generalFormValue.nativeFileHandling != null
        ? generalFormValue.nativeFileHandling
        : settingsInfo.indexOnlyCase

    settingsInfo.editableCustomFields =
      this.settings && !_.isEmpty(this.settings)
        ? this.settings.editableCustomFields
        : settingsInfo.editableCustomFields
  }

  /**
   * Prepares Image Conversion related options in the SettingsInfo from the user selected form values.
   * @param settingsInfo VODR Settings
   */
  private getImageConversionSettingsFromTheForm(settingsInfo: SettingsInfo) {
    const generalFormValue = (<FormGroup>(
      this.settingsForm['controls'].generalSettings
    )).getRawValue()
    const imageFormValue = (<FormGroup>(
      this.settingsForm['controls'].imageConversionSettings
    )).getRawValue()

    const imageConversionOption = new ImageConversionOption()
    imageConversionOption.deduplicationOption =
      generalFormValue?.deduplicationOption
    imageConversionOption.csV_Excel_option = generalFormValue?.csvExcelHandling
    imageConversionOption.passwordList =
      this.getPasswordsFromTheForm(imageFormValue?.passwordList) ||
      settingsInfo?.imageConversionOption?.passwordList
    imageConversionOption.autoGenerateImagesAfterIngestion =
      generalFormValue?.autoGenerateImagesAfterIngestion ||
      settingsInfo?.imageConversionOption?.autoGenerateImagesAfterIngestion
    imageConversionOption.ignoreAutoTiffJobsForMediaProcessingStatus =
      generalFormValue?.ignoreAutoTiffJobsForMediaProcessingStatus ||
      settingsInfo?.imageConversionOption
        ?.ignoreAutoTiffJobsForMediaProcessingStatus

    const imageColorConversion = new ColorConversion()
    imageColorConversion.imageFileType =
      imageFormValue?.imageColorConversion?.imageFileType ||
      settingsInfo?.imageConversionOption.imageColorConversion?.imageFileType
    imageColorConversion.pdfFiles =
      imageFormValue?.imageColorConversion?.pdfFiles ||
      settingsInfo?.imageConversionOption?.imageColorConversion.pdfFiles
    imageColorConversion.powerpoint =
      imageFormValue?.imageColorConversion?.powerpoint ||
      settingsInfo?.imageConversionOption?.imageColorConversion.powerpoint

    imageConversionOption.imageColorConversion = imageColorConversion
    settingsInfo.tzTimeZone = generalFormValue?.timeZone
    settingsInfo.imageConversionOption = imageConversionOption
  }

  /**
   * Prepares Control Settings and Endorsement related options in the SettingsInfo from the user selected form values.
   * @param settingsInfo VODR Settings
   */
  private getControlSettingsFromTheForm(settingsInfo: SettingsInfo) {
    const formValue = (<FormGroup>(
      this.settingsForm['controls'].controlNumberAndEndorsementSettings
    )).getRawValue()

    const controlNumberEndorsement = new ControlNumberEndorsement()
    controlNumberEndorsement.sortOrder =
      formValue?.sortOrder || settingsInfo?.controlNumber_Endorsement?.sortOrder
    controlNumberEndorsement.exportLocation =
      formValue?.exportLocation ||
      settingsInfo?.controlNumber_Endorsement.exportLocation

    const controlNumberSetting = new ControlNumberSetting()
    controlNumberSetting.prefix =
      formValue?.ControlNumberSetting?.controlNumberPrefix ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting?.prefix
    controlNumberSetting.advancedEndorsementSetting =
      formValue?.advancedEndorsementSetting ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting
        ?.advancedEndorsementSetting
    controlNumberSetting.continueFromPreviousControlNumber =
      formValue?.continueFromPreviousControlNumber ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting
        ?.continueFromPreviousControlNumber
    controlNumberSetting.controlNumberLocation =
      formValue?.ControlNumberSetting?.controlNumberLocation ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting
        .controlNumberLocation
    controlNumberSetting.endorseControlNumber =
      formValue?.ControlNumberSetting?.endorseControlNumber ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting
        ?.endorseControlNumber
    controlNumberSetting.endorseOptionalMessage =
      formValue?.ControlNumberSetting?.endorseOptionalMessage ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting
        ?.endorseOptionalMessage
    controlNumberSetting.messageText =
      formValue?.ControlNumberSetting?.messageText ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting?.messageText
    controlNumberSetting.optionalMessageLocation =
      formValue?.ControlNumberSetting?.messageTextLocation ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting
        ?.optionalMessageLocation
    controlNumberSetting.paddingLength =
      formValue?.paddingLength ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting
        ?.paddingLength
    controlNumberSetting.prefixDelimiter =
      formValue?.ControlNumberSetting?.controlNumberDelimiter ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting
        ?.prefixDelimiter
    controlNumberSetting.prefixDelimiterValue =
      formValue?.ControlNumberSetting?.prefixDelimiterValue ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting
        ?.prefixDelimiterValue
    controlNumberSetting.startNumber =
      parseInt(formValue?.ControlNumberSetting?.controlNumberStartingNumber) ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting?.startNumber
    controlNumberSetting.volumeId =
      formValue?.ControlNumberSetting?.volumeId ||
      settingsInfo?.controlNumber_Endorsement?.controlNumberSetting?.volumeId

    controlNumberEndorsement.controlNumberSetting = controlNumberSetting
    settingsInfo.controlNumber_Endorsement = controlNumberEndorsement
  }

  /**
   * Prepares PDF service related options in the SettingsInfo from the user selected form values.
   * @param settingsInfo VODR Settings
   */
  private getPdfSettingsFromTheForm(settingsInfo: SettingsInfo) {
    const formValue = (<FormGroup>(
      this.settingsForm['controls'].pdfServiceSettings
    )).getRawValue()
    const pdfServiceOption = new PDFServiceOption()
    pdfServiceOption.pdfType =
      formValue?.pdfType || settingsInfo?.pdfServiceOption.pdfType
    pdfServiceOption.pdfFamilyFileHandling =
      formValue?.pdfFamilyFileHandling ||
      settingsInfo?.pdfServiceOption.pdfFamilyFileHandling
    pdfServiceOption.pdfFileNamingConvention =
      formValue?.pdfFileNamingConvention ||
      settingsInfo?.pdfServiceOption.pdfFileNamingConvention

    settingsInfo.pdfServiceOption = pdfServiceOption
  }

  /**
   * Prepares PRINT service related options in the SettingsInfo from the user selected form values.
   * @param settingsInfo VODR Settings
   */
  private getPrintSettingsFromTheForm(settingsInfo: SettingsInfo) {
    const formValue = (<FormGroup>(
      this.settingsForm['controls']?.printServiceSettings
    ))?.getRawValue()

    const printServiceOption = new PrintServiceOption()
    printServiceOption.documentSeparator =
      formValue?.documentSeparator ||
      settingsInfo?.printServiceOption.documentSeparator
    printServiceOption.familyFileHandling =
      formValue?.printFamilyFileHandling ||
      settingsInfo?.printServiceOption.familyFileHandling
    printServiceOption.paperSide =
      formValue?.paperSide || settingsInfo?.printServiceOption.paperSide
    printServiceOption.paperType =
      formValue?.paperType || settingsInfo?.printServiceOption.paperType

    const printBinding = new PrintBinding()
    printBinding.bindingType =
      formValue?.binding ||
      settingsInfo?.printServiceOption.printBinding.bindingType
    printBinding.binderColor =
      formValue?.threeRingBinderColor ||
      settingsInfo?.printServiceOption.printBinding.binderColor
    printBinding.binderSize =
      formValue?.threeRingBinderSize ||
      settingsInfo?.printServiceOption.printBinding.binderSize
    printServiceOption.printBinding = printBinding

    const printSet = new PrintSet()
    printSet.numberOfSetValue =
      formValue?.numberOfSets ||
      settingsInfo?.printServiceOption.printSet.numberOfSetValue
    printSet.printSetOption =
      formValue?.printSet ||
      settingsInfo?.printServiceOption.printSet.printSetOption
    printServiceOption.printSet = printSet

    settingsInfo.printServiceOption = printServiceOption
  }

  /**
   * Prepares Production related options in the SettingsInfo from the user selected form values.
   * @param settingsInfo VODR Settings
   */
  private getProductionSettingsFromTheForm(settingsInfo: SettingsInfo) {
    const formValue = (<FormGroup>(
      this.settingsForm['controls']?.productionSettings
    ))?.getRawValue()

    const productionOptions = new ProductionOptions()
    productionOptions.fieldTemplateID =
      formValue?.fieldTemplateId ||
      settingsInfo?.productionOptions.fieldTemplateId
    productionOptions.savedSearchesForExpressions =
      formValue?.savedSearches ||
      settingsInfo?.productionOptions.savedSearchesForExpressions

    const filterOptions = new FilterOptions()
    filterOptions.excludeProducedDocuments =
      formValue?.excludeProducedDocuments ||
      settingsInfo?.productionOptions.filterOptions.excludeProducedDocuments
    filterOptions.excludeNativeForRedactedDocuments =
      formValue?.excludeNativeForRedactedDocuments ||
      settingsInfo?.productionOptions.filterOptions
        .excludeNativeForRedactedDocuments

    productionOptions.filterOptions = filterOptions
    settingsInfo.productionOptions = productionOptions
  }

  private checkSessionIsExpired() {
    this.route.queryParamMap
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((params) => {
        if (
          params.get('Session') === 'expire' ||
          params.get('session') === 'expire'
        ) {
          this.dialogRef.close() // Close the dialog
        }
      })
  }

  /**
   * Gets the service type value from the form and converts it into the format that can be used to set into SettingsInfo object.
   * @param serviceRequestType ServiceRequestType
   */
  private getServiceTypeFromTheForm(
    serviceRequestType: ServiceRequestType
  ): ServiceType {
    let serviceType = null
    if (
      serviceRequestType &&
      !StringUtils.isNullOrEmpty(serviceRequestType.serviceType)
    ) {
      for (const [key, value] of ServiceTypeDescription.entries()) {
        if (value === serviceRequestType.serviceType) {
          serviceType = key
          break
        }
      }
    }
    return serviceType
  }

  /**
   * Gets the timezone value from the form and converts it into the format that can be used to set into SettingsInfo object.
   * @param timeZone
   */
  private getTimeZoneFromTheForm(timeZone: string): TimeZone {
    if (!StringUtils.isNullOrEmpty(timeZone)) {
      for (const [key, value] of TimeZoneDescription.entries()) {
        if (value === timeZone) {
          return key
        }
      }
    }
    return null
  }

  /**
   * Form contains passwords separated by newline character. This function converts it into an array of password strings.
   * @param formValue
   */
  private getPasswordsFromTheForm(formValue: string): string[] {
    if (StringUtils.isNullOrEmpty(formValue)) {
      return []
    } else {
      return formValue.split('\n')
    }
  }

  private transformData(data: { serviceTypes: any[]; templateTypes: any[] }) {
    // Create a map from templateTypes to store each serviceTypeId by serviceTypeName where templateType is "Project Template"
    const serviceIdMap = new Map<string, number>()
    data.templateTypes.forEach((template) => {
      if (
        template.templateType === 'Project Template' &&
        template.serviceTypeId === 1 && // only showing pdf service in the dropdown in old vod (1 is pdf service)
        data.serviceTypes.some(
          (service) => service.serviceTypeName === template.serviceTypeName
        )
      ) {
        serviceIdMap.set(template.serviceTypeName, template.serviceTypeId)
      }
    })

    const pdfServiceTypeOnly = data.serviceTypes.filter((idMap) =>
      serviceIdMap.has(idMap.serviceTypeName)
    )
    // Enrich serviceTypes with serviceTypeId from the map
    const enrichedServiceTypes = pdfServiceTypeOnly.map((service) => ({
      ...service,
      serviceTypeId: serviceIdMap.get(service.serviceTypeName) // This will add the serviceTypeId where templateType is "Project Template"
    }))

    return { serviceTypes: enrichedServiceTypes }
  }

  /**
   * Validates general settings form.
   * @param fg Form Group - serviceSettingFormGroup > generalSettings
   */
  private validateGeneralSettingsForm: ValidatorFn = (fg: FormGroup) => {
    const validationErrors = {}

    if (Object.keys(validationErrors).length === 0) {
      return null
    } else {
      return validationErrors
    }
  }

  /**
   * Validates control number and endorsement settings form.
   * @param fg Form Group - serviceSettingFormGroup > controlNumberAndEndorsementSettings
   */
  private validateControlNumberAndEndorsementSettings: ValidatorFn = (
    fg: FormGroup
  ) => {
    const validationErrors = {}
    const pattern = /^[^\\\\/:*?"<>|]+$/

    // Check if the controls exist
    const prefixControl = fg.get('controlNumberPrefix')
    const volumeIdControl = fg.get('volumeId')
    const startingNumberControl = fg.get('controlNumberStartingNumber')

    if (!prefixControl || !volumeIdControl || !startingNumberControl) {
      return null // or handle the case appropriately if controls are expected to be always there
    }

    // Proceed with existing validation logic
    const prefix = prefixControl.value
    if (prefix && (prefix.trim() === '' || !pattern.test(prefix))) {
      validationErrors['invalidPrefix'] = true
    }

    let volumeId = volumeIdControl.value
    if (StringUtils.isNullOrEmpty(volumeId)) {
      validationErrors['requiredVolumeId'] = true
    } else {
      volumeId = volumeId.replace(/\.+$/, '')
      if (!pattern.test(volumeId)) {
        validationErrors['invalidVolumeId'] = true
      }
    }

    const startingNumber = startingNumberControl.value
    if (
      !this.settings?.controlNumber_Endorsement?.controlNumberSetting
        ?.continueFromPreviousControlNumber
    ) {
      if (
        startingNumber === undefined ||
        startingNumber === null ||
        startingNumber === '' ||
        isNaN(startingNumber)
      ) {
        validationErrors['requiredStartingNumber'] = true
      } else if (startingNumberControl.hasError('requiredStartingNumber')) {
        startingNumberControl.setErrors(null)
      }
    }

    return Object.keys(validationErrors).length === 0 ? null : validationErrors
  }

  /**
   * Validates production settings form.
   * @param fg Form Group - serviceSettingFormGroup > productionSettings
   */
  private validateProductionSettingsForm: ValidatorFn = (fg: FormGroup) => {
    const validationErrors = {}

    const serviceType = this.settingsForm.get('serviceType').value
    // We need to validate only if not filtering service type or
    // if the service type is 'add to existing case' but the project is not of filtering service type
    // if (this.isNotFilteringService(serviceType)) {
    //   // Check for field template
    //   const fieldTemplate = fg.get('fieldTemplate').value
    //   if (!fieldTemplate) {
    //     validationErrors['requiredFieldTemplate'] = true
    //   }
    // }

    if (Object.keys(validationErrors).length === 0) {
      return null
    } else {
      return validationErrors
    }
  }

  /**
   * Returns true is the currently selected service type is not filtering service i.e. Analyze and Review
   * If the service selection in the form is ADD_DATA_TO_EXISTING_CASE,
   * it determines if the case if filtering service type by checking 'approvePreProcessPage_CostEstimate' property.
   * @param serviceType ServiceRequestType object
   */
  private isNotFilteringService(serviceType: ServiceRequestType): boolean {
    return true
    // return (
    //   (serviceType?.serviceType !==
    //     SERVICE_TYPE_CONSTANTS.VODR_FILTERING_SERVICE &&
    //     serviceType?.serviceType ===
    //     SERVICE_TYPE_CONSTANTS.VODR_ADD_DATA_EXISTING_PROJECT &&
    //     !this.settings?.approvePreProcessPage_CostEstimate) ||
    //   (serviceType?.serviceType &&
    //     serviceType?.serviceType !==
    //     SERVICE_TYPE_CONSTANTS.VODR_FILTERING_SERVICE &&
    //     serviceType?.serviceType !==
    //     SERVICE_TYPE_CONSTANTS.VODR_ADD_DATA_EXISTING_PROJECT)
    // )
  }

  ngOnDestroy(): void {
    this.xSStore.dispatch(new fromRootActions.ClearExistingCaseData())
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  #getFormValueFromPasswords(passwords: string[]): string {
    if (!passwords || passwords.length === 0) {
      return ''
    }
    return passwords.join('\n')
  }

  showConfirmationModal(message: string, title: string): Observable<boolean> {
    const modal = this.modalService.show(ConfirmationDialogComponent, {
      class: 'modal-dialog-centered'
    })
    ;(<ConfirmationDialogComponent>modal.content).showConfirmationModal(
      title,
      message
    )
    return (<ConfirmationDialogComponent>modal.content).onClose
  }
}
