<div class="v-widget-title d-flex justify-content-between">
  <div>Progress</div>
  <div class="input-group input-group-sm w-50">
    <!--    TODO: we'll use this to filter by date range later-->
    <!--    <mat-date-range-input [rangePicker]="picker" class="form-control form-control-sm">-->
    <!--      <input (focus)="picker.open()" matStartDate placeholder="Start date">-->
    <!--      <input (focus)="picker.open()" matEndDate placeholder="End date">-->
    <!--    </mat-date-range-input>-->
    <!--    <div class="input-group-append" (click)="picker.open()">-->
    <!--      <span class="input-group-text">-->
    <!--        <i class="fas fa-calendar-alt"></i>-->
    <!--      </span>-->
    <!--    </div>-->
    <!--    <mat-date-range-picker #picker></mat-date-range-picker>-->
  </div>
</div>
<div class="v-widgets-wrap pt-5">
  <ng-template [ngIf]="!isProgressLoading && data.length < 1">
    <div class="row mb-3 mt-3 default-font-size">
      <div class="col-md-12 text-muted">No Data</div>
    </div>
  </ng-template>
  <ng-template [ngIf]="data?.length > 0">
    <dx-chart
      id="chart"
      [dataSource]="data"
      palette="soft Pastel"
      class="v-widget-max-height"
    >
      <dxi-series
        valueField="reviewedDocCount"
        name="reviewedDate"
      ></dxi-series>
      <dxo-common-series-settings
        argumentField="day"
        type="bar"
        valueField="reviewedDocCount"
        [ignoreEmptyPoints]="true"
        opacity="0.45"
      >
      </dxo-common-series-settings>
      <dxo-series-template nameField="reviewedDocCount"></dxo-series-template>
      <dxo-tooltip
        [enabled]="true"
        location="edge"
        [zIndex]="1"
        [customizeTooltip]="customizeTooltip"
      >
      </dxo-tooltip>
      <dxo-legend
        [visible]="false"
        position="inside"
        horizontalAlignment="center"
        verticalAlignment="top"
      >
      </dxo-legend>
    </dx-chart>
    <!--    TODO: we'll use this for filtering by date range later-->
    <!--    <div class="row mt-2">-->
    <!--      <div class="col-2">-->
    <!--        <i class="fas fa-angle-left v-circle-icon" (click)="randomSeries()"></i>-->
    <!--      </div>-->
    <!--      <div class="col-8 text-center">JUL 20 - JUL 30</div>-->
    <!--      <div class="col-2 text-right">-->
    <!--        <i class="fas fa-angle-right v-circle-icon" (click)="randomSeries()"></i>-->
    <!--      </div>-->
    <!--    </div>-->
  </ng-template>

  <ng-template [ngIf]="isProgressLoading">
    <div class="row">
      <div class="col-md-12">
        <app-content-placeholder
          [wrapperHeight]="70"
          [linesOnly]="true"
          [lineCount]="3"
          class="mb-1 d-block"
          [lineHeight]="8"
        >
        </app-content-placeholder>
      </div>
    </div>
  </ng-template>
</div>
