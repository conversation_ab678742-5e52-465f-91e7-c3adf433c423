import * as _ from 'lodash'
import {
  ProjectSetupInfo,
  ProjectTemplateModel,
  TranscribeAccessKeys,
  TranscribeSupportedFiles
} from '../../models/case-template-settings.model'
import { CaseModel } from '../../models/case.model'
import { CaseActions, CaseActionTypes } from '../actions'

export interface CaseState {
  caseList: CaseModel[]
  lastIndex: number
  isDataPullFinished: boolean
  timeZones: any
  newCase: any
  errorMessage: string
  projectMediaStatus: any
  caseRightListInfo: any
  usersListToInvite: any
  extUsersListToInvite: any
  isInvitationSent: boolean
  projectTemplateSettings: ProjectSetupInfo //CaseTemplateSettings
  projectTemplateList: ProjectTemplateModel[]
  caseInfo: CaseModel
  updateCase: string
  caseInfoEdit: any // ProjectInfoModel
  transcriptStatus: boolean
  supportedFileTypesForTranscribing: TranscribeSupportedFiles[]
  transcribeAccessKeys: TranscribeAccessKeys[]
  imageFileExtensions: string[]
  newCaseTemplate: any
  updateCaseTemplateInfo: string
  isMediaStatusLoading: boolean | undefined
  unIndexMediaSuccessResponse: any
  unIndexMediaErrorResponse: any
  canFetchNewCase: boolean | undefined
}

export const initialCaseState: CaseState = {
  caseList: null,
  lastIndex: 0,
  isDataPullFinished: false,
  timeZones: null,
  newCase: null,
  errorMessage: null,
  projectMediaStatus: null,
  caseRightListInfo: null,
  usersListToInvite: null,
  isInvitationSent: false,
  extUsersListToInvite: null,
  projectTemplateSettings: null,
  projectTemplateList: null,
  caseInfo: null,
  updateCase: '',
  caseInfoEdit: null,
  transcriptStatus: false,
  supportedFileTypesForTranscribing: null,
  transcribeAccessKeys: null,
  imageFileExtensions: null,
  newCaseTemplate: null,
  updateCaseTemplateInfo: '',
  isMediaStatusLoading: undefined,
  unIndexMediaSuccessResponse: undefined,
  unIndexMediaErrorResponse: undefined,
  canFetchNewCase: undefined
}

export function reducer(
  state = initialCaseState,
  action: CaseActions
): CaseState {
  let newState
  switch (action.type) {
    case CaseActionTypes.FetchCasesSuccess: {
      const lastIndex = state.lastIndex
      newState = _.cloneDeep(state)
      // if (state.caseList !== null) {
      //   newState.caseList = state.caseList.concat(action.response);
      // } else {
      newState.caseList = action.response
      // }
      if (newState.caseList.length) {
        newState.lastIndex = newState.caseList.length
      }
      if (
        lastIndex === newState.lastIndex ||
        (lastIndex === 0 && lastIndex !== newState.lastIndex)
      ) {
        newState.isDataPullFinished = true
      }
      return newState
    }

    case CaseActionTypes.ClearCaseList:
      return {
        ...state,
        caseList: null
      }

    case CaseActionTypes.GetTimeZonesSuccessful:
      return {
        ...state,
        timeZones: action.payload.data
      }

    case CaseActionTypes.GetImageFileExtensionsSuccessful:
      return {
        ...state,
        imageFileExtensions: action.payload.data
      }

    case CaseActionTypes.FetchCaseInfoSuccess: {
      newState = _.cloneDeep(state)
      newState.caseInfo = action.response
      return newState
    }

    case CaseActionTypes.FetchCaseInfoEditSuccess: {
      newState = _.cloneDeep(state)
      newState.caseInfoEdit = action.response
      return newState
    }

    //tem,plate related
    case CaseActionTypes.FetchTemplateInfoEditSuccess: {
      newState = _.cloneDeep(state)
      newState.caseInfoEdit = action.response
      return newState
    }

    case CaseActionTypes.ResetCaseInfoEdit: {
      newState = _.cloneDeep(state)
      newState.caseInfoEdit = null
      return newState
    }
    case CaseActionTypes.ResetTemplateInfoEdit: {
      newState = _.cloneDeep(state)
      newState.caseInfoEdit = null
      return newState
    }

    case CaseActionTypes.CreateCaseSuccess:
      newState = _.cloneDeep(state)
      newState.newCase = action.payload
      return newState

    case CaseActionTypes.CreateCaseError:
      return {
        ...state,
        errorMessage: action.payload
      }

    case CaseActionTypes.CreateCaseTemplateSuccess:
      newState = _.cloneDeep(state)
      newState.newCaseTemplate = action.payload
      return newState

    case CaseActionTypes.CreateCaseTemplateError:
      return {
        ...state,
        errorMessage: action.payload
      }

    case CaseActionTypes.UpdateCaseSuccess:
      newState = _.cloneDeep(state)
      newState.updateCase = action.payload
      return newState

    case CaseActionTypes.UpdateCaseError:
      return {
        ...state,
        errorMessage: action.payload
      }

    case CaseActionTypes.UpdateCaseTemplateSuccess:
      newState = _.cloneDeep(state)
      newState.updateCaseTemplateInfo = action.payload
      return newState

    case CaseActionTypes.UpdateCaseTemplateError:
      return {
        ...state,
        errorMessage: action.payload
      }

    case CaseActionTypes.GetProjectMediaStatusSuccessful:
      return {
        ...state,
        projectMediaStatus: action.res.data
      }

    case CaseActionTypes.FetchUsersGroupListSuccess:
      return {
        ...state,
        usersListToInvite: action.res
      }

    case CaseActionTypes.FetchExtUsersGroupListSuccess:
      return {
        ...state,
        extUsersListToInvite: action.res
      }

    case CaseActionTypes.GetProjectRightListSuccessful:
      return {
        ...state,
        caseRightListInfo: action.res?.data
      }

    case CaseActionTypes.ResetProjectRightList:
      return {
        ...state,
        caseRightListInfo: null
      }

    case CaseActionTypes.SendInvitationSuccess:
      return {
        ...state,
        isInvitationSent: action.res
      }

    case CaseActionTypes.ClearStoreProperty:
      newState = _.cloneDeep(state)
      newState[action.propertyName] = initialCaseState[action.propertyName]
      return newState

    case CaseActionTypes.FetchCaseTemplateSettingsSuccess:
      return {
        ...state,
        projectTemplateSettings: action.res
      }

    case CaseActionTypes.GetDefaultProjectTemplateSuccess:
      return {
        ...state,
        projectTemplateSettings: action.res
      }

    case CaseActionTypes.ShowCaseListLoader:
      return {
        ...state,
        isDataPullFinished: !action.isShow
      }

    case CaseActionTypes.FetchProjectTemplatesSuccess:
      return {
        ...state,
        projectTemplateList: action.payload
      }

    case CaseActionTypes.GetTranscriptStatusSuccessful:
      return {
        ...state,
        transcriptStatus: action.res
      }
    case CaseActionTypes.GetSupportedFileTypesForTranscribingSuccessful:
      return {
        ...state,
        supportedFileTypesForTranscribing: action.res
      }
    case CaseActionTypes.GetTranscribeAccessKeysSuccessful:
      return {
        ...state,
        transcribeAccessKeys: action.res
      }
    case CaseActionTypes.FetchUnIndexMediaStatus:
      return {
        ...state,
        isMediaStatusLoading: true
      }
    case CaseActionTypes.FetchUnIndexMediaStatusSuccess:
      return {
        ...state,
        unIndexMediaSuccessResponse: action.unIndexMediaSuccessResponse,
        isMediaStatusLoading: false,
        unIndexMediaErrorResponse: undefined,
        canFetchNewCase: undefined
      }
    case CaseActionTypes.FetchUnIndexMediaStatusFailure:
      return {
        ...state,
        unIndexMediaErrorResponse: action.unIndexMediaErrorResponse,
        isMediaStatusLoading: false,
        unIndexMediaSuccessResponse: undefined,
        canFetchNewCase: undefined
      }
    case CaseActionTypes.SetCanFetchNewCase:
      return {
        ...state,
        canFetchNewCase: action.canFetchNewCase
      }
    default:
      return state
  }
}
