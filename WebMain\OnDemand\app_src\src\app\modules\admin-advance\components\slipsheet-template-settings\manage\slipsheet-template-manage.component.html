<ng-template [ngIf]="isWorking">
  <app-content-placeholder [linesOnly]="true"></app-content-placeholder>
</ng-template>

<ng-template [ngIf]="!isWorking">
  <app-route-breadcrumb> </app-route-breadcrumb>

  <div class="row mb-4">
    <div class="col-md-12">
      <mat-card class="block-shadow pt-0">
        <mat-card-content>
          <div class="row heading align-items-center px-0">
            <div class="col-md-8 section-title">Manage SlipSheet Template</div>
            <div
              class="col-md-4 d-flex justify-content-end"
              [@fadeInX]="selectedKeys?.length"
            >
              <button
                *ngIf="selectedKeys?.length > 0"
                class="btn btn-sm btn-outline-danger mr-2"
                type="button"
                [disabled]=""
                (click)="actionClicked(actionTypes.DELETEAll, null)"
              >
                Delete
                <span class="badge badge-info">
                  {{ selectedKeys?.length }}</span
                >
              </button>
              <button
                class="btn btn-{{ config.themeClient }}-primary"
                (click)="createSlipSheet()"
              >
                Create
              </button>
            </div>
          </div>
          <div class="row default-font-size mt-4">
            <div class="col-md-12">
              <dx-data-grid
                #dxGridCase
                id="gridContainer"
                height="75vh"
                [showRowLines]="true"
                [dataSource]="fieldData"
                [showBorders]="true"
                [allowColumnResizing]="true"
                [columnResizingMode]="'widget'"
                [columnMinWidth]="50"
                [columnAutoWidth]="true"
                keyExpr="slipsheetTemplateId"
                [rowAlternationEnabled]="false"
                [hoverStateEnabled]="true"
                [cellHintEnabled]="true"
                [highlightChanges]="true"
                (onRowPrepared)="onRowPrepared($event)"
                (onSelectionChanged)="onSelectionChanged($event)"
              >
                <dxo-selection
                  [recursive]="true"
                  showCheckBoxesMode="always"
                  [allowSelectAll]="true"
                  mode="multiple"
                ></dxo-selection>

                <dxo-paging
                  [enabled]="fieldData?.length > 50"
                  [pageSize]="50"
                ></dxo-paging>
                <dxo-pager
                  [showPageSizeSelector]="true"
                  [allowedPageSizes]="[50, 200, 500]"
                  [showInfo]="true"
                >
                </dxo-pager>
                <!-- dblClick | click-->
                <dxo-editing
                  mode="cell"
                  [allowUpdating]="true"
                  [startEditAction]="'click'"
                  [allowAdding]="false"
                  [useIcons]="false"
                  [texts]="null"
                >
                </dxo-editing>
                <dxo-header-filter
                  [searchMode]
                  [allowSearch]="true"
                  [visible]="true"
                ></dxo-header-filter>
                <dxi-column
                  [allowEditing]="false"
                  dataField="slipsheetTemplateName"
                  caption="Template Name"
                ></dxi-column>
                <dxi-column
                  [allowEditing]="false"
                  dataField="createdOn"
                  caption="Template Created Date"
                >
                </dxi-column>
                <dxi-column
                  [allowEditing]="false"
                  dataField="updatedOn"
                  caption="Template Modified Date"
                >
                </dxi-column>
                <dxi-column
                  [cssClass]="'text-left'"
                  dataField="slipsheetTemplateId"
                  caption="Action"
                  cellTemplate="fieldActions"
                  [allowHeaderFiltering]="false"
                  [allowSearch]="false"
                  [allowFiltering]="false"
                  [allowSorting]="false"
                  [allowEditing]="false"
                >
                </dxi-column>
                <div
                  class="d-flex justify-content-center px-2 actions"
                  *dxTemplate="let c1 of 'fieldActions'"
                >
                  <span
                    matTooltipPosition="above"
                    matTooltip="Edit"
                    *ngIf="c1.data?.editable"
                    (click)="actionClicked(actionTypes.EDIT, c1)"
                  >
                    <i class="fas fa-edit fa-lg text-secondary mr-4"></i>
                  </span>
                  <span
                    matTooltipPosition="above"
                    *ngIf="c1.data?.editable"
                    matTooltip="Delete"
                    (click)="actionClicked(actionTypes.DELETE, c1)"
                  >
                    <i class="fas fa-trash-alt fa-lg text-danger mr-3"></i>
                  </span>
                </div>

                <dxo-load-panel [enabled]="true"></dxo-load-panel>
                <dxo-scrolling
                  showScrollbar="always"
                  mode="virtual"
                ></dxo-scrolling>
                <!-- or "virtual" | "infinite" | "standard"-->
              </dx-data-grid>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</ng-template>

<ng-template #removeConfirm>
  <div
    class="row mx-0"
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div class="modal-header d-flex col-12" cdkDragHandle>
      <h4 class="modal-title pull-left">Confirm!</h4>
      <button
        type="button"
        class="close pull-right close-confirm"
        [mat-dialog-close]="false"
      >
        <span aria-hidden="true" class="fa fa-times"></span>
      </button>
    </div>
    <div class="modal-body h5 text-center mb-0">
      Are you sure you want to delete?
    </div>
    <div class="modal-footer w-100 text-right">
      <button
        type="button"
        class="btn btn-{{
          config.themeClient
        }}-primary float-right close-confirm"
        [mat-dialog-close]="true"
      >
        YES
      </button>
      <button
        [mat-dialog-close]="false"
        type="button"
        class="btn btn-grey close-confirm"
      >
        NO
      </button>
    </div>
  </div>
</ng-template>
