/**
 * Review set state model.
 */
import {
  DeleteDocumentReviewSetModel,
  ReviewBatchDetailModel,
  ReviewBatchSummary,
  ReviewerAccuracyModel,
  ReviewerPerformanceModel,
  ReviewSetModel,
  ReviewUserModel,
  SummaryModel
} from '@admin-advance/models'
import { CALProfileInfo } from '@admin-advance/models/CAL/cal.model'
import { ReviewLayout } from '@admin-advance/models/layout/layout.model'
import {
  BatchModel,
  BatchRichnessModel,
  CALProgressModel,
  FileConversionCountModel,
  RelevancyProjectionModel,
  ReviewRelevanceModel,
  ReviewSetInfo,
  UserGroup,
  UserGroupTree,
  ViewableTag
} from '@shared/models'
import {
  NavigationMode,
  SavedSearch,
  SourceFolder,
  SourceTag
} from '@shared/models/source.model'
import { Response, TagModel } from '@stores/models'
import { ReviewStatusModel } from '../../models/review-set/review-set.model'

export interface ReviewSetStateModel {
  activeColumnSize: number
  reviewSet: ReviewSetModel[]
  reviewSetSummary: SummaryModel
  reviewSetProgress: ReviewStatusModel[]
  reviewSetReviewers: ReviewStatusModel[]
  reviewSetTagStatus: ReviewStatusModel[]
  reviewSetTagRate: ReviewStatusModel[]

  selectedDashboardReviewSets: number[]
  allReviewUsers: ReviewUserModel[]
  selectedReviewUsers: number[]
  reviewBatchSummaries: ReviewBatchSummary[]
  reviewerPerformanceData: ReviewerPerformanceModel[]
  performanceTimeSpanValue: string
  reviewerAccuracyData: ReviewerAccuracyModel[]
  showLoadingBar: boolean
  /**
   * Navigation mode
   */
  navigationMode: NavigationMode

  /**
   * Datasource of tags
   */
  tags: SourceTag[]

  /**
   * Datasource of saved searches
   */
  savedSearches: SavedSearch[]

  /**
   * Datasource of folders
   */
  folders: SourceFolder[]

  /**
   * Datasource of users and user groups
   */
  userGroupTree: UserGroupTree[]

  /**
   * Datasource of review sets
   */
  reviewSets: ReviewSetInfo[]

  /**
   * Selected review set for editing.
   */
  selectedReviewSet: ReviewSetInfo

  /**
   * Datasource of review set batches
   */
  batches: BatchModel[]

  /**
   * Selected batch for user reassign
   */
  selectedBatch: BatchModel

  /**
   * Datasource of batch richness chart
   */
  batchRichnessChartData: BatchRichnessModel[]

  /**
   * Datasource of relevancy projection chart
   */
  relevancyProjectionChartData: RelevancyProjectionModel[]

  /**
   * Datasource of review relevance chart
   */
  reviewRelevanceChartData: ReviewRelevanceModel[]

  /**
   * Datasource of CAL progress info
   */
  calProgressData: CALProgressModel

  /**
   * Response object when http request are made
   */
  batchOperationResponse: Response

  /**
   * List of selected reviewers in the reviewset
   */
  selectedReviewers: UserGroup[]

  viewableTags: ViewableTag[]

  /**
   * status of sql agent service. This status is required to enable/disable the auto collection.
   */
  isSqlAgentStarted: boolean

  /**
   * List of available review layouts
   */
  reviewLayouts: ReviewLayout[]

  /**
   * Source tag response
   */
  sourceTagErrResponse: string

  /** cal options */
  calOptions: CALProfileInfo

  /** Is reveiw set for Continous Action Learning(CAL) */
  isReviewSetForCAL: boolean

  reviewSetFileDetails: DeleteDocumentReviewSetModel[]

  deleteBatchFileAssociationResponse: boolean

  reviewBatchDetail: ReviewBatchDetailModel

  reviewSetTaggedDocumentTags: TagModel[]

  selectedReviewLayout: ReviewLayout
  fileConversionCountModel: FileConversionCountModel
}

/**
 * Initial values for review set store.
 */
export const initialReviewSetState: ReviewSetStateModel = {
  activeColumnSize: 12,
  reviewSet: [],
  reviewSetSummary: {},
  reviewSetProgress: [],
  reviewSetReviewers: [],
  reviewSetTagStatus: [],
  reviewSetTagRate: [],

  selectedDashboardReviewSets: [],
  selectedReviewUsers: [],
  allReviewUsers: [],
  reviewBatchSummaries: [],
  reviewerPerformanceData: [],
  performanceTimeSpanValue: 'WEEK',
  reviewerAccuracyData: [],
  showLoadingBar: false,

  navigationMode: null,
  tags: [],
  savedSearches: [],
  folders: [],
  userGroupTree: [],
  reviewSets: null,
  selectedReviewSet: null,
  batches: null,
  selectedBatch: null,
  batchRichnessChartData: null,
  reviewRelevanceChartData: null,
  relevancyProjectionChartData: null,
  calProgressData: null,
  batchOperationResponse: null,
  selectedReviewers: null,
  isSqlAgentStarted: true,
  reviewLayouts: null,
  sourceTagErrResponse: null,
  calOptions: null,
  isReviewSetForCAL: false,
  viewableTags: [],
  reviewSetFileDetails: [],
  deleteBatchFileAssociationResponse: null,
  reviewBatchDetail: null,
  reviewSetTaggedDocumentTags: null,
  selectedReviewLayout: null,
  fileConversionCountModel: null
}
