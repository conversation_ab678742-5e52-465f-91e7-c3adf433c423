import { ReviewPanelType } from '@admin-advance/models/layout/layout.const'
import { Injectable } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute } from '@angular/router'
import { ProjectInfo } from '@config/models'
import { ConfigService } from '@config/services/config.service'
import { getProjectInfo } from '@config/store/selectors'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { select, Store } from '@ngrx/store'
import { Store as XSStore } from '@ngxs/store'
import { MultiWindowSelectionService } from '@review/services/multi-window-selection.service'
import { MultiWindowTagService } from '@review/services/multi-window-tag.service'
import { FieldModel, ResponseModel } from '@shared/models'
import {
  SearchResponseModel,
  TempTableResponseModel
} from '@shared/models/search.model'
import { TagRuleDisplayService } from '@shared/services/tag-rule-display.service'
import {
  getSearchPageSize,
  getSearchResponse,
  getSearchTempTableName,
  getSearchTempTables
} from '@shared/store/selectors/search.selector'
import { ResponseStatus } from '@stores/models'
import { CaseSelectors, StartupStateSelector } from '@stores/selectors'
import { JsonConvert } from 'json2typescript'
import * as _ from 'lodash'
import { combineLatest, EMPTY, from, of } from 'rxjs'
import {
  catchError,
  concatMap,
  debounceTime,
  filter,
  mergeMap,
  switchMap,
  take,
  withLatestFrom
} from 'rxjs/operators'
import { GlobalDataService } from 'src/app/services/global-data.service'
import {
  GlobalErrorAction,
  GlobalSuccessAction
} from '../../../../store/actions'
import { hideSingleTagConfirmation } from '../../../application-nav/store/selectors/application-nav.selectors'
import { User } from '../../../auth/models/user.model'
import { LogOut } from '../../../auth/store/actions'
import { getUserDetails } from '../../../auth/store/selectors/access.selectors'
import { DocumentTagMessagePopupComponent } from '../../components/document-tag-message-popup/document-tag-message-popup.component'
import {
  ChunkDocumentModel,
  CodingFieldSavedModel,
  DocumentEmailThread,
  DocumentMetadata,
  DocumentNote,
  DocumentNotesResponseModel,
  DocumentShareUserResponseModel,
  DuplicateDoc,
  FileTaggedRequestModel,
  ParentChild,
  SaveTagRequestModel,
  SaveTagResponseModel,
  TagSavedChangeModel,
  TagSavedModel,
  ViewerSettings
} from '../../models/document.model'
import {
  Export,
  Limit,
  PrintStatusModel,
  Summary
} from '../../models/print.model'
import { DocumentTableUpdateModel } from '../../models/review.model'
import {
  SearchFieldViewerType,
  SearchResultRequestData,
  SearchResultRequestModel
} from '../../models/search.model'
import { DocumentsService } from '../../services/documents.service'
import { MultiWindowExcelService } from '../../services/multi-window-excel.service'
import * as fromSearchActions from '../../store/actions/search.actions'
import {
  ReviewSetStateSelector,
  SetcurrentDocNumber,
  UpdateDocumentTableAction
} from '../../xs-store'
import * as fromDocumentActions from '../actions/documents.actions'
import { DocumentState, ReviewSpinnerType } from '../reducers/documents.reducer'
import {
  getCurrentConversationId,
  getCurrentDocument,
  getCurrentDocumentName,
  getCurrentDocumentTablePage,
  getIsBatchSelection,
  getSelectedDocuments,
  getSelectedDocumentsCount,
  getUnselectedDocuments
} from '../selectors/documents.selectors'
import {
  getProjectTags,
  getProjectTagSettings
} from '../selectors/review-params.selectors'
import {
  getAllSearchResultFileIds,
  getSearchResultDocumentTableData
} from '../selectors/search.selectors'
import {
  UpdatedFileTagColors,
  UpdatedFileTags
} from './../../models/document.model'

/**
 * Effects to handle getting/setting document details, tags, notes, etc.
 * It also handles effects from setting current document, document table page; updating document selection;
 * Getting viewer settings, Fetching fulltext, etc.
 */
@Injectable()
export class DocumentsEffects {
  private jsonConvert: JsonConvert

  constructor(
    private actions$: Actions,
    private service: DocumentsService,
    private store: Store<DocumentState>,
    public configService: ConfigService,
    private matDiolog: MatDialog,
    private multiWindowExcelService: MultiWindowExcelService,
    private multiWindowTagService: MultiWindowTagService,
    private multiWindowSelectionService: MultiWindowSelectionService,
    private tagRuleDisplayService: TagRuleDisplayService,
    public xsstore: XSStore,
    public route: ActivatedRoute
  ) {
    this.jsonConvert = new JsonConvert()
  }

  public get _documentShareToken(): string {
    return this.route.snapshot.queryParams['docShareToken']
  }

  /**
   * Fetches document tags
   */
  fetchDocumentTags$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentTags),
      switchMap((action) => {
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.Tags }
          })
        )
        return this.service
          .fetchDocumentTags$(action.payload.documentTagRequest)
          .pipe(
            switchMap((response: any[]) => {
              try {
                const afterMapWithTagComment = response.map((t) => ({
                  tagId: t.TagId,
                  tagState: t.tagState,
                  comments: t.lastTagComment
                }))
                return [
                  new GlobalSuccessAction(
                    'Document tags fetched successfully.',
                    afterMapWithTagComment,
                    false,
                    false
                  ),
                  fromDocumentActions.setDocumentTags({
                    payload: { documentTags: afterMapWithTagComment }
                  }),
                  fromDocumentActions.setProgressSpinner({
                    payload: { show: false, type: ReviewSpinnerType.Tags }
                  })
                ]
              } catch (e) {
                return [
                  new GlobalErrorAction(
                    new Error('Error de-serializing document tags.'),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) =>
              from([
                new GlobalErrorAction(err, true, true),
                fromDocumentActions.setProgressSpinner({
                  payload: { show: false, type: ReviewSpinnerType.Tags }
                })
              ])
            )
          )
      })
    )
  )

  /**
   * Fetches document notes
   */
  fetchDocumentNotes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentNotes),
      switchMap((action) => {
        if (!action.payload.fileId) {
          return EMPTY
        }
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.Notes }
          })
        )
        return this.service
          .fetchDocumentNotes$(
            action.payload.fileId,
            action.payload.projectId,
            action.payload.uniqueId
          )
          .pipe(
            mergeMap((response: any) => {
              try {
                const documentNotesResponse =
                  this.jsonConvert.deserializeObject(
                    response,
                    DocumentNotesResponseModel
                  )

                if (
                  action?.payload?.isRefreshGrid &&
                  documentNotesResponse?.documentNotes
                ) {
                  const documentNotes: DocumentNote[] =
                    documentNotesResponse.documentNotes
                  const docNotes: string[] = []

                  const getNotes = (documentNotes: DocumentNote[]) => {
                    documentNotes.forEach((note: DocumentNote) => {
                      docNotes.push(note.comment)
                      if (note?.children?.length) {
                        getNotes(note.children)
                      }
                    })
                  }

                  getNotes(documentNotes)
                  const docNotesFieldAndValue: DocumentTableUpdateModel = {
                    fileId: action.payload.fileId,
                    displayFieldName: 'DOCUMENT_NOTES',
                    fieldValue: docNotes?.join(';') || ''
                  }
                  this.xsstore.dispatch(
                    new UpdateDocumentTableAction([docNotesFieldAndValue])
                  )
                }

                return [
                  new GlobalSuccessAction(
                    'Document notes fetched successfully.',
                    documentNotesResponse,
                    false,
                    false
                  ),
                  fromDocumentActions.setDocumentNotes({
                    payload: { documentNotesResponse: documentNotesResponse }
                  }),
                  fromDocumentActions.setProgressSpinner({
                    payload: { show: false, type: ReviewSpinnerType.Notes }
                  })
                ]
              } catch (e) {
                return [
                  new GlobalErrorAction(
                    new Error('Error de-serializing document notes.'),
                    true,
                    true
                  ),
                  fromDocumentActions.setProgressSpinner({
                    payload: { show: false, type: ReviewSpinnerType.Notes }
                  })
                ]
              }
            }),
            catchError((err) =>
              from([
                new GlobalErrorAction(err, true, true),
                fromDocumentActions.setProgressSpinner({
                  payload: { show: false, type: ReviewSpinnerType.Notes }
                })
              ])
            )
          )
      })
    )
  )

  /**
   * Adds a document note
   * After adding is successful, invokes action to fetch document notes so that we have updated store
   */
  addDocumentNote$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.addDocumentNote),
      withLatestFrom(this.store.pipe(select(getCurrentDocument))),
      switchMap(([action, currentDocumentId]) => {
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.Notes }
          })
        )
        return this.service
          .addDocumentNote$(
            action.payload.editDocumentNoteRequest,
            action.payload.fileId,
            action.payload.isReply,
            action.payload.projectId,
            action.payload.docShareToken
          )
          .pipe(
            mergeMap((response: any) => {
              return [
                new GlobalSuccessAction(
                  'Document note added successfully.',
                  null,
                  true,
                  false
                ),
                fromDocumentActions.fetchDocumentNotes({
                  payload: {
                    fileId: currentDocumentId,
                    projectId: action.payload.projectId,
                    uniqueId: '',
                    isRefreshGrid: true
                  }
                })
              ]
            }),
            catchError((err) =>
              from([
                new GlobalErrorAction(err, true, true),
                fromDocumentActions.setProgressSpinner({
                  payload: { show: false, type: ReviewSpinnerType.Notes }
                })
              ])
            )
          )
      })
    )
  )

  /**
   * Updates a document note
   * After updating is successful, invokes action to fetch document notes so that we have updated store
   */
  editDocumentNote$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.editDocumentNote),
      withLatestFrom(this.store.pipe(select(getCurrentDocument))),
      switchMap(([action, currentDocumentId]) => {
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.Notes }
          })
        )
        return this.service
          .editDocumentNote$(
            action.payload.editDocumentNoteRequest,
            action.payload.projectId
          )
          .pipe(
            mergeMap((response: any) => {
              return [
                new GlobalSuccessAction(
                  'Document note updated successfully.',
                  null,
                  true,
                  false
                ),
                fromDocumentActions.fetchDocumentNotes({
                  payload: {
                    fileId: currentDocumentId,
                    projectId: action.payload.projectId,
                    uniqueId: '',
                    isRefreshGrid: true
                  }
                })
              ]
            }),
            catchError((err) =>
              from([
                new GlobalErrorAction(err, true, true),
                fromDocumentActions.setProgressSpinner({
                  payload: { show: false, type: ReviewSpinnerType.Notes }
                })
              ])
            )
          )
      })
    )
  )

  /**
   * Deletes a document note
   * After deleting is successful, invokes action to fetch document notes so that we have updated store
   */
  deleteDocumentNote$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.deleteDocumentNote),
      withLatestFrom(this.store.pipe(select(getCurrentDocument))),
      switchMap(([action, currentDocumentId]) => {
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.Notes }
          })
        )
        return this.service
          .deleteDocumentNote$(
            action.payload.commentId,
            action.payload.projectId
          )
          .pipe(
            mergeMap((response: any) => {
              return [
                new GlobalSuccessAction(
                  'Document note deleted successfully.',
                  null,
                  true,
                  false
                ),
                fromDocumentActions.fetchDocumentNotes({
                  payload: {
                    fileId: currentDocumentId,
                    projectId: action.payload.projectId,
                    uniqueId: '',
                    isRefreshGrid: true
                  }
                })
              ]
            }),
            catchError((err) =>
              from([
                new GlobalErrorAction(err, true, true),
                fromDocumentActions.setProgressSpinner({
                  payload: { show: false, type: ReviewSpinnerType.Notes }
                })
              ])
            )
          )
      })
    )
  )

  /**
   * Fetches the list of users who are able to view the document note based on the accessibility option selected
   */
  fetchDocumentNoteVisibleUsers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentNoteVisibleUsers),
      switchMap((action) =>
        this.service
          .fetchDocumentNoteVisibleUsers$(
            action.payload.projectId,
            action.payload.accessibility
          )
          .pipe(
            mergeMap((response: string[]) => {
              return [
                new GlobalSuccessAction(
                  'Document note visible users list fetched successfully.',
                  response,
                  false,
                  false
                ),
                fromDocumentActions.setDocumentNoteVisibleUsers({
                  payload: { users: response }
                })
              ]
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Edited: Sanh Huynh; Date: April 28, 2020; Ref #23822 - Review: Split details panel into individual panels
   * Fetches document metadata
   * Based on the response type i.e. METADATA
   * Invoke action to set the document details in store accordingly
   */
  fetchDocumentMetadata$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentMetadata),
      switchMap((action) => {
        if (!action.payload.fileId) return EMPTY
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.Metadata }
          })
        )
        return combineLatest([
          this.store.pipe(select(getSearchTempTables)),
          this.xsstore.select(
            ReviewSetStateSelector.overrideWithGroupPermission
          )
        ]).pipe(
          filter(([temptables, overrideGroupSecurity]) => {
            this.store.dispatch(
              fromDocumentActions.setProgressSpinner({
                payload: { show: false, type: ReviewSpinnerType.Metadata }
              })
            )
            return !!temptables
          }),
          switchMap(([temptables, overrideGroupSecurity]) => {
            const venioFieldIds: number[] = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedVenioFields(
                ReviewPanelType.Metadata,
                false
              )
            )
            const customFieldIds: number[] = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedCustomFields(
                ReviewPanelType.Metadata,
                false
              )
            )
            return this.service
              .fetchDocumentMetadata$(
                action.payload.fileId,
                action.payload.projectId,
                this.getDocumentRequestModel(
                  temptables,
                  venioFieldIds,
                  customFieldIds,
                  SearchFieldViewerType.Meta_Detail,
                  overrideGroupSecurity,
                  ReviewPanelType.Metadata
                )
              )
              .pipe(
                switchMap((response: any) => {
                  try {
                    const metadata: DocumentMetadata[] = response.data
                    return [
                      new GlobalSuccessAction(
                        'Document metadata fetched successfully.',
                        metadata,
                        false,
                        false
                      ),
                      fromDocumentActions.setDocumentMetadata({
                        payload: { documentMetadata: metadata }
                      }),
                      fromDocumentActions.setProgressSpinner({
                        payload: {
                          show: false,
                          type: ReviewSpinnerType.Metadata
                        }
                      })
                    ]
                  } catch (e) {
                    return [
                      new GlobalErrorAction(
                        new Error('Error de-serializing document metadata.'),
                        true,
                        true
                      ),
                      fromDocumentActions.setProgressSpinner({
                        payload: {
                          show: false,
                          type: ReviewSpinnerType.Metadata
                        }
                      })
                    ]
                  }
                }),
                catchError((err) =>
                  from([
                    new GlobalErrorAction(err, false, true),
                    fromDocumentActions.setProgressSpinner({
                      payload: {
                        show: false,
                        type: ReviewSpinnerType.Metadata
                      }
                    })
                  ])
                )
              )
          })
        )
      })
    )
  )

  /**
   * Edited: Sanh Huynh; Date: April 28, 2020; Ref #23822 - Review: Split details panel into individual panels
   * Fetches document Parent / Child
   * Based on the response type i.e. PARENT_CHILD
   * Invoke action to set the document details in store accordingly
   */
  fetchDocumentParentChild$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentParentChild),
      switchMap((action) => {
        if (!action.payload.fileId) return EMPTY
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.ParentChild }
          })
        )
        return combineLatest([
          this.store.pipe(select(getSearchTempTables)),
          this.xsstore.select(
            ReviewSetStateSelector.overrideWithGroupPermission
          )
        ]).pipe(
          filter(([temptables, overrideGroupSecurity]) => !!temptables),
          switchMap(([temptables, overrideGroupSecurity]) => {
            const venioFieldIds: number[] = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedVenioFields(
                ReviewPanelType.ParentChild,
                false
              )
            )
            const customFieldIds: number[] = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedCustomFields(
                ReviewPanelType.ParentChild,
                false
              )
            )
            return this.service
              .fetchDocumentFamily$(
                action.payload.fileId,
                action.payload.projectId,
                this.getDocumentRequestModel(
                  temptables,
                  venioFieldIds,
                  customFieldIds,
                  SearchFieldViewerType.List_View,
                  overrideGroupSecurity,
                  ReviewPanelType.ParentChild
                )
              )
              .pipe(
                switchMap((response: any) => {
                  try {
                    const parentChild: ParentChild[] = [response.data]
                    return [
                      new GlobalSuccessAction(
                        'Document parent-child fetched successfully.',
                        parentChild,
                        false,
                        false
                      ),
                      fromDocumentActions.setDocumentParentChild({
                        payload: { parentChild: parentChild }
                      }),
                      fromDocumentActions.setProgressSpinner({
                        payload: {
                          show: false,
                          type: ReviewSpinnerType.ParentChild
                        }
                      })
                    ]
                  } catch (e) {
                    return [
                      new GlobalErrorAction(
                        new Error(
                          'Error de-serializing document parent child.'
                        ),
                        true,
                        true
                      ),
                      fromDocumentActions.setProgressSpinner({
                        payload: {
                          show: false,
                          type: ReviewSpinnerType.ParentChild
                        }
                      })
                    ]
                  }
                }),
                catchError((err) =>
                  from([
                    new GlobalErrorAction(err, false, true),
                    fromDocumentActions.setProgressSpinner({
                      payload: {
                        show: false,
                        type: ReviewSpinnerType.ParentChild
                      }
                    })
                  ])
                )
              )
          })
        )
      })
    )
  )

  /**
   * Edited: Sanh Huynh; Date: April 28, 2020; Ref #23822 - Review: Split details panel into individual panels
   * Fetches document email thread
   * Based on the response type i.e. MESSAGE_THREAD
   * Invoke action to set the document details in store accordingly
   */
  fetchDocumentEmailThread$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentEmailThread),
      switchMap((action) => {
        if (!action.payload.fileId) return EMPTY
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.EmailThread }
          })
        )
        return combineLatest([
          this.store.pipe(select(getSearchTempTables)),
          this.xsstore.select(
            ReviewSetStateSelector.overrideWithGroupPermission
          )
        ]).pipe(
          filter(([temptables, overrideGroupSecurity]) => !!temptables),
          switchMap(([temptables, overrideGroupSecurity]) => {
            const venioFieldIds: number[] = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedVenioFields(
                ReviewPanelType.EmailThread,
                false
              )
            )
            const customFieldIds: number[] = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedCustomFields(
                ReviewPanelType.EmailThread,
                false
              )
            )
            return this.service
              .fetchDocumentEmailThread$(
                action.payload.fileId,
                action.payload.projectId,
                this.getDocumentRequestModel(
                  temptables,
                  venioFieldIds,
                  customFieldIds,
                  SearchFieldViewerType.List_View,
                  overrideGroupSecurity,
                  ReviewPanelType.EmailThread
                )
              )
              .pipe(
                switchMap((response: any) => {
                  try {
                    const emailThread: DocumentEmailThread[] = response.data
                    return [
                      new GlobalSuccessAction(
                        'Document email thread fetched successfully.',
                        emailThread,
                        false,
                        false
                      ),
                      fromDocumentActions.setDocumentEmailThread({
                        payload: { emailThread: emailThread }
                      }),
                      fromDocumentActions.setProgressSpinner({
                        payload: {
                          show: false,
                          type: ReviewSpinnerType.EmailThread
                        }
                      })
                    ]
                  } catch (e) {
                    return [
                      new GlobalErrorAction(
                        new Error(
                          'Error de-serializing document email thread.'
                        ),
                        true,
                        true
                      ),
                      fromDocumentActions.setProgressSpinner({
                        payload: {
                          show: false,
                          type: ReviewSpinnerType.EmailThread
                        }
                      })
                    ]
                  }
                }),
                catchError((err) =>
                  from([
                    new GlobalErrorAction(err, false, true),
                    fromDocumentActions.setProgressSpinner({
                      payload: {
                        show: false,
                        type: ReviewSpinnerType.EmailThread
                      }
                    })
                  ])
                )
              )
          })
        )
      })
    )
  )

  /**
   * Edited: Sanh Huynh; Date: April 28, 2020; Ref #23822 - Review: Split details panel into individual panels
   * Fetches document duplicates
   * Based on the response type i.e. DUPLICATES
   * Invoke action to set the document details in store accordingly
   */
  fetchDocumentDuplicates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentDuplicates),
      switchMap((action) => {
        if (!action.payload.fileId) return EMPTY
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.Duplicates }
          })
        )
        return combineLatest([
          this.store.pipe(select(getSearchTempTables)),
          this.xsstore.select(
            ReviewSetStateSelector.overrideWithGroupPermission
          )
        ]).pipe(
          filter(([temptables, overrideGroupSecurity]) => !!temptables),
          switchMap(([temptables, overrideGroupSecurity]) => {
            const venioFieldIds: number[] = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedVenioFields(
                ReviewPanelType.Duplicates,
                false
              )
            )
            const customFieldIds: number[] = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedCustomFields(
                ReviewPanelType.Duplicates,
                false
              )
            )
            return this.service
              .fetchDocumentDuplicate$(
                action.payload.fileId,
                action.payload.projectId,
                this.getDocumentRequestModel(
                  temptables,
                  venioFieldIds,
                  customFieldIds,
                  SearchFieldViewerType.List_View,
                  overrideGroupSecurity,
                  ReviewPanelType.Duplicates
                )
              )
              .pipe(
                switchMap((response: any) => {
                  try {
                    const duplicateDocs: DuplicateDoc[] = response.data
                    return [
                      new GlobalSuccessAction(
                        'Document duplicates fetched successfully.',
                        duplicateDocs,
                        false,
                        false
                      ),
                      fromDocumentActions.setDocumentDuplicates({
                        payload: { duplicateDocs: duplicateDocs }
                      }),
                      fromDocumentActions.setProgressSpinner({
                        payload: {
                          show: false,
                          type: ReviewSpinnerType.Duplicates
                        }
                      })
                    ]
                  } catch (e) {
                    return [
                      new GlobalErrorAction(
                        new Error('Error de-serializing document duplicates.'),
                        true,
                        true
                      ),
                      fromDocumentActions.setProgressSpinner({
                        payload: {
                          show: false,
                          type: ReviewSpinnerType.Duplicates
                        }
                      })
                    ]
                  }
                }),
                catchError((err) =>
                  from([
                    new GlobalErrorAction(err, false, true),
                    fromDocumentActions.setProgressSpinner({
                      payload: {
                        show: false,
                        type: ReviewSpinnerType.Duplicates
                      }
                    })
                  ])
                )
              )
          })
        )
      })
    )
  )

  /**
   * Adds an array of documents to selected documents array in the store
   * Invokes update document selection action after computing new array of selected documents (old items + new items)
   */
  addToSelectedDocuments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.addToSelectedDocuments),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getSelectedDocuments)),
            this.store.pipe(select(getUnselectedDocuments)),
            this.store.pipe(select(getIsBatchSelection))
          )
        )
      ),
      switchMap(
        ([
          action,
          batchSelectedDocuments,
          batchUnselectedDocuments,
          isBatchSelected
        ]) => {
          const documentIds = action.payload.documentIds
          const actions = []
          let selectedDocuments = []
          if (isBatchSelected) {
            //if all documents from all pages are selected, need to keep track of unselected the unselected list.
            //Selected list need to be updated to reflect the changes in UI.
            //So, remove the documents from unselected list.
            const unselectedDocuments = _.difference(
              batchUnselectedDocuments,
              documentIds
            )
            actions.push(
              fromDocumentActions.setUnSelectedDocuments({
                payload: { unselectedDocuments: unselectedDocuments }
              })
            )
            //now, add the documents to the selected list.
            selectedDocuments = _.union(batchSelectedDocuments, documentIds)
            actions.push(
              fromDocumentActions.setSelectedDocuments({
                payload: { selectedDocuments: selectedDocuments }
              })
            )
          } else {
            //if all documents from all pages are not selected, only keep track of selected documents
            selectedDocuments = _.union(batchSelectedDocuments, documentIds)
            actions.push(
              fromDocumentActions.setSelectedDocuments({
                payload: { selectedDocuments: selectedDocuments }
              })
            )
          }
          // actions.push(
          //   fromDocumentActions.postDocumentSelection({
          //     payload: { isForcedFetch: false }
          //   })
          // )
          actions.push(
            fromDocumentActions.setCurrentDocument({
              payload: {
                documentId: selectedDocuments[selectedDocuments.length - 1],
                resetSelection: true,
                conversationId: null //rowData['__conversationId']
              }
            })
          )
          return actions
        }
      )
    )
  )

  /**
   * Removes an array of documents from the selected documents array in the store
   * Invokes update document selection action after computing new array of selected documents (old items - items to unselect)
   */
  removeFromSelectedDocuments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.removeFromSelectedDocuments),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getSelectedDocuments)),
            this.store.pipe(select(getUnselectedDocuments)),
            this.store.pipe(select(getIsBatchSelection))
          )
        )
      ),
      switchMap(
        ([
          action,
          batchSelectedDocuments,
          batchUnselectedDocuments,
          isBatchSelected
        ]) => {
          const documentIds = action.payload.documentIds
          const actions = []
          let selectedDocuments = []
          if (isBatchSelected) {
            //if all documents from all pages are selected, need to keep track of unselected the unselected list.
            //Selected list need to be updated to reflect the changes in UI.
            //So, remove the documents from unselected list.
            const unselectedDocuments = _.union(
              batchUnselectedDocuments,
              documentIds
            )
            actions.push(
              fromDocumentActions.setUnSelectedDocuments({
                payload: { unselectedDocuments: unselectedDocuments }
              })
            )

            //now, add the documents to the selected list.
            selectedDocuments = batchSelectedDocuments.filter(
              (doc) => !unselectedDocuments.includes(doc)
            )
            actions.push(
              fromDocumentActions.setSelectedDocuments({
                payload: { selectedDocuments: selectedDocuments }
              })
            )
          } else {
            //if all documents from all pages are not selected, only keep track of selected documents
            selectedDocuments = _.difference(
              batchSelectedDocuments,
              documentIds
            )
            actions.push(
              fromDocumentActions.setSelectedDocuments({
                payload: { selectedDocuments: selectedDocuments }
              })
            )
          }
          // actions.push(
          //   fromDocumentActions.postDocumentSelection({
          //     payload: { isForcedFetch: false }
          //   })
          // )
          if (selectedDocuments.length > 0) {
            actions.push(
              fromDocumentActions.setCurrentDocument({
                payload: {
                  documentId: selectedDocuments[selectedDocuments.length - 1],
                  resetSelection: false,
                  conversationId: null
                }
              })
            )
          } else {
            // When no documents are selected, set the document as -1 and it will clear all the review panels.
            actions.push(
              fromDocumentActions.setCurrentDocument({
                payload: {
                  documentId: -1,
                  resetSelection: false,
                  conversationId: null
                }
              })
            )
          }
          return actions
        }
      )
    )
  )

  /**
   * Maintain overall list of selected and unselected file ids irrespective of which page a user is in the document table
   * Actions:
   *  Set selected documents
   *  Set unselected documents
   *  Fetch Document Tags for the selected documents
   */
  updateDocumentSelection$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.postDocumentSelection),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getAllSearchResultFileIds)),
            this.store.pipe(select(getSelectedDocuments)),
            this.store.pipe(select(getUnselectedDocuments)),
            this.store.pipe(select(getIsBatchSelection)),
            this.store.pipe(select(getUserDetails)),
            this.store.pipe(select(getProjectInfo)),
            this.store.pipe(select(getSearchTempTableName)),
            this.store.pipe(select(getSelectedDocumentsCount))
          )
        )
      ),
      switchMap(
        ([
          action,
          fileIds,
          batchSelectedDocuments,
          batchUnselectedDocuments,
          isBatchSelected,
          userDetails,
          projectInfo,
          tempTableName,
          selectedDocumentsCount
        ]) => {
          const actions = []
          this.service.updateDocumentSelection$.next()
          if (selectedDocumentsCount > 0) {
            // Commented to fix the issue that tag state is not set in tag panel when result is send to review from analyze page as Tag panel is not active so applying forced Fetch
            // if (!action.payload.isForcedFetch)
            //   this.multiWindowTagService.fetchDocumentTag$.next({
            //     payload: {
            //       documentTagRequest: {
            //         fileIds: batchSelectedDocuments,
            //         //unSelectedFileIds: unselectedDocuments,
            //         unSelectedFileIds: batchUnselectedDocuments,
            //         isBatchSelected: isBatchSelected,
            //         isNotReviewedOnly: isBatchSelected,
            //         markAsReviewed: false,
            //         projectId: projectInfo.projectId,
            //         tagSettings: null, // Get from store if needed
            //         tags: null, // Get from store if needed
            //         searchTempTableName: tempTableName
            //       }
            //     }
            //   })
            // else
            actions.push(
              fromDocumentActions.fetchDocumentTags({
                payload: {
                  documentTagRequest: {
                    fileIds: batchSelectedDocuments,
                    //unSelectedFileIds: unselectedDocuments,
                    unSelectedFileIds: batchUnselectedDocuments,
                    isBatchSelected: isBatchSelected,
                    isNotReviewedOnly: isBatchSelected,
                    markAsReviewed: false,
                    projectId: projectInfo.projectId,
                    tagSettings: null, // Get from store if needed
                    tags: null, // Get from store if needed
                    searchTempTableName: tempTableName
                  }
                }
              })
            )
          }
          return actions
        }
      )
    )
  )

  /**
   * Inserts document view log
   */
  insertDocumentViewLog$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.insertDocumentViewLog),
      switchMap((action) =>
        this.service
          .insertDocumentViewLog$(
            action.payload.documentId,
            action.payload.projectId,
            action.payload.moduleName
          )
          .pipe(
            mergeMap((response: any) => {
              if (response === 'SUCCESS') {
                return [
                  new GlobalSuccessAction(
                    'Document view log inserted.',
                    null,
                    false,
                    false
                  )
                ]
              } else {
                return [
                  new GlobalErrorAction(
                    new Error('Error inserting document view log.'),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Fetch viewer settings for a document
   */
  fetchViewerSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchViewerSettings),
      filter((action) => !!action.payload.documentId),
      switchMap((action) =>
        this.service
          .fetchViewSettings$(
            action.payload.documentId,
            action.payload.projectId,
            action.payload.userId,
            action.payload.imageSetId,
            action.payload.isExternalUser
          )
          .pipe(
            mergeMap((response: any) => {
              try {
                const viewerSettings = this.jsonConvert.deserializeObject(
                  response,
                  ViewerSettings
                )
                return [
                  new GlobalSuccessAction(
                    'Viewer settings fetched successfully.',
                    response,
                    false,
                    false
                  ),
                  fromDocumentActions.setViewerSettings({
                    payload: { viewerSettings: viewerSettings }
                  })
                ]
              } catch (e) {
                return [
                  new GlobalErrorAction(
                    new Error('Error de-serializing viewer settings.'),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Fetches document fulltext
   */
  fetchFullText = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchFullText),

      switchMap((action) => {
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.ViewerFullText }
          })
        )
        return combineLatest([
          this.store.pipe(select(getSearchTempTables), take(1)),
          this.xsstore.selectOnce(
            ReviewSetStateSelector.overrideWithGroupPermission
          ),
          this.store.pipe(select(getCurrentConversationId), take(1))
        ]).pipe(
          filter(
            ([temptables, overrideGroupSecurity, conversationId]) =>
              !!temptables
          ),
          switchMap(([temptables, overrideGroupSecurity, conversationId]) => {
            const venioFieldIds = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedVenioFields(
                ReviewPanelType.TextViewer,
                false
              )
            )
            const customFieldIds = this.xsstore.selectSnapshot(
              ReviewSetStateSelector.selectedCustomFields(
                ReviewPanelType.TextViewer,
                false
              )
            )
            const fieldRequestData: SearchResultRequestData =
              this.getDocumentRequestModel(
                temptables,
                venioFieldIds,
                customFieldIds,
                SearchFieldViewerType.Fulltext_Viewer,
                overrideGroupSecurity,
                ReviewPanelType.TextViewer
              )
            const requestModel: ChunkDocumentModel = {
              ...action.payload.requestModel,
              fieldRequestData,
              conversationId: conversationId
            }
            return this.service.fetchFullText$(requestModel).pipe(
              switchMap((response: any) => {
                if (response.data === 'SessionOut') {
                  return [new LogOut()] // If session is out, log out. We might need to refresh the session rather than logging out.
                }
                return [
                  new GlobalSuccessAction(
                    'Document fulltext fetched successfully.',
                    response.data,
                    false,
                    false
                  ),
                  fromDocumentActions.setFullText({
                    payload: { fullText: response.data }
                  }),
                  fromDocumentActions.setProgressSpinner({
                    payload: {
                      show: false,
                      type: ReviewSpinnerType.ViewerFullText
                    }
                  })
                ]
              }),
              catchError((err) =>
                from([
                  new GlobalErrorAction(err, true, true),
                  fromDocumentActions.setProgressSpinner({
                    payload: {
                      show: false,
                      type: ReviewSpinnerType.ViewerFullText
                    }
                  })
                ])
              )
            )
          })
        )
      })
    )
  )

  /**
   * Fetches document Syncfusion text
   */
  fetchSyncfusionText = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentSyncfusionText),
      switchMap((action) => {
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: {
              show: true,
              type: ReviewSpinnerType.ViewerSyncfusionText
            }
          })
        )
        return this.service
          .fetchSyncfusionText$(action.payload.requestModel)
          .pipe(
            mergeMap((response: any) => {
              if (response.data === 'SessionOut') {
                return [new LogOut()] // If session is out, log out. We might need to refresh the session rather than logging out.
              }
              return [
                new GlobalSuccessAction(
                  'Document Syncfusion text fetched successfully.',
                  response.data,
                  false,
                  false
                ),
                fromDocumentActions.setDocumentSyncfusionText({
                  payload: { syncfusionText: response.data }
                }),
                fromDocumentActions.setProgressSpinner({
                  payload: {
                    show: false,
                    type: ReviewSpinnerType.ViewerSyncfusionText
                  }
                })
              ]
            }),
            catchError((err) =>
              from([
                new GlobalErrorAction(err, true, true),
                fromDocumentActions.setProgressSpinner({
                  payload: {
                    show: false,
                    type: ReviewSpinnerType.ViewerSyncfusionText
                  }
                })
              ])
            )
          )
      })
    )
  )

  /**
   * Fetches document HTML text (this is near native view)
   */
  fetchDocumentHtmlText$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentHtmlText),
      switchMap((action) => {
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.ViewerNearNative }
          })
        )
        return this.service
          .fetchDocumentHtmlText$(action.payload.requestModel)
          .pipe(
            mergeMap((response: any) => {
              if (response.data === 'SessionOut') {
                return [new LogOut()] // Logout if session is out. We might need to refresh the session rather than logging out.
              }
              return [
                new GlobalSuccessAction(
                  'Document HTML text fetched successfully.',
                  response.data,
                  false,
                  false
                ),
                fromDocumentActions.setHtmlInProgressMessage({
                  payload: { message: '' }
                }),
                fromDocumentActions.setDocumentHtmlText({
                  payload: { htmlText: response.data }
                }),
                fromDocumentActions.setProgressSpinner({
                  payload: {
                    show: false,
                    type: ReviewSpinnerType.ViewerNearNative
                  }
                })
              ]
            }),
            catchError((err) => {
              if (err?.error?.data?.Message == 'HTML_IN_PROGRESS') {
                return from([
                  fromDocumentActions.setHtmlInProgressMessage({
                    payload: { message: err?.error?.data?.Message }
                  }),
                  fromDocumentActions.setProgressSpinner({
                    payload: {
                      show: false,
                      type: ReviewSpinnerType.ViewerNearNative
                    }
                  })
                ])
              } else {
                return from([
                  fromDocumentActions.setHtmlInProgressMessage({
                    payload: { message: '' }
                  }),
                  new GlobalErrorAction(err, true, true),
                  fromDocumentActions.setProgressSpinner({
                    payload: {
                      show: false,
                      type: ReviewSpinnerType.ViewerNearNative
                    }
                  })
                ])
              }
            })
          )
      })
    )
  )

  /**
   * Saves tags
   * Actions:
   *  - Save Tags
   *  - Fetch Document Tags so that out store reflects the updated tags selection
   *  - Move to next document, if it is save and move to next
   */
  saveTags$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.saveTags),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getSelectedDocuments)),
            this.store.pipe(select(getUnselectedDocuments)),
            this.store.pipe(select(getIsBatchSelection)),
            this.store.pipe(select(getUserDetails)),
            this.store.pipe(select(getProjectInfo)),
            this.store.pipe(select(getSearchTempTableName)),
            this.store.pipe(select(getProjectTagSettings)),
            this.store.pipe(select(getProjectTags)),
            this.store.pipe(select(hideSingleTagConfirmation)),
            this.store.pipe(select(getCurrentDocument)),
            this.store.pipe(select(getCurrentDocumentName)),
            this.store.pipe(select(getAllSearchResultFileIds)),
            this.xsstore.select(
              StartupStateSelector.SliceOf('selectedMediaScope')
            ),
            this.xsstore.select(
              ReviewSetStateSelector.SliceOf('selectedReviewSetBatchInfo')
            ),
            this.xsstore.select(
              ReviewSetStateSelector.SliceOf('selectedReviewSetInfo')
            ),
            this.xsstore.select(CaseSelectors.mediaStatus)
          )
        )
      ),
      switchMap(
        ([
          action,
          selectedDocuments,
          unselectedDocuments,
          isBatchSelection,
          userDetails,
          projectInfo,
          searchTempTableName,
          tagSettings,
          projectTags,
          hasSingleTagConfirmation,
          currentDocument,
          currentDocumentName,
          currentPageFileIds,
          selectedMediaList,
          selectedReviewSetBatch,
          selectedReviewSetInfo,
          media
        ]) => {
          this.store.dispatch(
            fromDocumentActions.setProgressSpinner({
              payload: { show: true, type: ReviewSpinnerType.Tags }
            })
          )

          // Create a request model for saving tag
          const request = new SaveTagRequestModel()
          request.projectId = projectInfo.projectId
          request.searchTempTableName = searchTempTableName
          request.fileIds = selectedDocuments
          request.unSelectedFileIds = unselectedDocuments
          request.isBatchSelected = isBatchSelection
          request.tagSettings = _.cloneDeep(tagSettings)
          request.isNotReviewedOnly = false
          request.markAsReviewed = false
          request.medialist = selectedMediaList?.some((m) => m)
            ? selectedMediaList
            : media?.isAllMediaProcessed
            ? media.mediaIds
            : media.processedMediaIds

          request.docShareToken = this._documentShareToken

          request.reviewSetBatchId = selectedReviewSetBatch?.batchId

          if (selectedReviewSetBatch?.reviewSetId > 0) {
            request.tagSettings.propagatePCSet =
              selectedReviewSetInfo?.propagateTagPCSet
            request.tagSettings.includeEmailThread =
              selectedReviewSetInfo?.propagateTagEmailThread
            request.tagSettings.dupTagOption =
              selectedReviewSetInfo?.tagPropagationRule
          }

          // Tags selection is sent by updating `selected` and `selectFlag` properties of the ProjectTag model
          let tempTags = _.cloneDeep(projectTags) // Cloning is necessary and ngrx won't allow mutating state property here
          tempTags = tempTags.filter(
            (tag) =>
              !!tag.parentTagId &&
              action?.payload.documentTags.hasOwnProperty(tag.tagId)
          ) // We don't needs tag groups to be included
          for (const tag of tempTags) {
            tag.selected = tag.selectFlag =
              action.payload.documentTags[tag.tagId].tagState
            tag.comments = action.payload.documentTags[tag.tagId].comments
          }

          request.tags = tempTags

          return this.service.saveDocumentTags$(request).pipe(
            switchMap((result: ResponseModel) => {
              try {
                const response = result.data
                const saveTagResponse: SaveTagResponseModel[] =
                  response?.tagResult

                const actions = []
                // keep latest tag save use for copy
                if (saveTagResponse.length > 0) {
                  if (hasSingleTagConfirmation?.HideSingleTagConfirmation) {
                    this.ShowTagSummary(saveTagResponse, request.tagSettings)
                  }

                  const selectedReviewSetId = this.xsstore.selectSnapshot(
                    ReviewSetStateSelector.SliceOf('selectedReviewSetId')
                  )
                  const fileTaggedRequestModel = new FileTaggedRequestModel()
                  fileTaggedRequestModel.currentPageFileIds = currentPageFileIds
                  fileTaggedRequestModel.reviewSetId = selectedReviewSetId
                  fileTaggedRequestModel.isFileTagged = true
                  this.service
                    .updateFileStatus$(
                      fileTaggedRequestModel,
                      projectInfo.projectId
                    )
                    .pipe(take(1))
                    .subscribe((response: any) => {
                      const updatedTagColorList: UpdatedFileTagColors[] =
                        response?.updatedTagColorList

                      const tagColorFieldAndValue: DocumentTableUpdateModel[] =
                        updatedTagColorList.map((field) => {
                          return {
                            fileId: field.fileId,
                            displayFieldName: '__tagicon',
                            fieldValue: field.tagNameColors
                          }
                        })

                      const updatedTagList: UpdatedFileTags[] =
                        response?.updatedTagList

                      const tagNamesFieldAndValue: DocumentTableUpdateModel[] =
                        updatedTagList.map((field) => {
                          return {
                            fileId: field.fileId,
                            displayFieldName: 'Tag Names',
                            fieldValue: field.tags
                          }
                        })

                      const reviewedFileIds = response?.reviewedFileIds
                      const model: DocumentTableUpdateModel[] =
                        reviewedFileIds.map((fileid) => ({
                          fileId: fileid,
                          displayFieldName: '__isReviewed',
                          fieldValue: 'Yes'
                        }))

                      this.xsstore.dispatch(
                        new UpdateDocumentTableAction([
                          ...tagColorFieldAndValue,
                          ...tagNamesFieldAndValue,
                          ...model
                        ])
                      )
                    })

                  const tagSaved: TagSavedModel = {
                    fileId: currentDocument,
                    fileName: currentDocumentName,
                    tagSavedChange: saveTagResponse.map((x) => {
                      const tag: TagSavedChangeModel = {
                        tagId: x.tagId,
                        tagName: x.tagName,
                        isTagOperation: x.isTagOperation,
                        isExclusive: x.isExclusive,
                        tagGroupName: x.tagGroupName
                      }
                      return tag
                    })
                  }
                  actions.push(
                    fromDocumentActions.setTagSavedLatest({
                      payload: { tagSavedLatest: tagSaved }
                    })
                  )
                }

                actions.push(
                  fromDocumentActions.fetchDocumentTags({
                    payload: {
                      documentTagRequest: {
                        fileIds: selectedDocuments,
                        unSelectedFileIds: unselectedDocuments,
                        isBatchSelected: isBatchSelection,
                        isNotReviewedOnly: isBatchSelection,
                        markAsReviewed: false,
                        projectId: projectInfo.projectId,
                        tagSettings: null, // Get from store if required
                        tags: null, // Get from store if required
                        searchTempTableName: searchTempTableName
                      }
                    }
                  })
                )
                // Edited: Sanh Huynh; Date: April 30, 2020; Ref #23826 - Review: Allow document table to 'collapse' hide
                //  add back and next document in viewer panel
                // If it is save and move to next document case, dispatch action to move to next document
                if (action.payload.navigationType === 'next') {
                  actions.push(fromDocumentActions.moveToNextDocument())
                }
                if (action.payload.navigationType === 'back') {
                  actions.push(fromDocumentActions.moveBackDocument())
                }
                return actions
              } catch (e) {
                return [
                  new GlobalErrorAction(
                    new Error(
                      'Error de-serializing response for save tag(s) request.'
                    ),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) => {
              if (err?.error?.message == 'Tag rule violated') {
                this.tagRuleDisplayService.openConflictedTagRulesViewer(
                  err?.error?.data
                )
                return from([
                  fromDocumentActions.setProgressSpinner({
                    payload: { show: false, type: ReviewSpinnerType.Tags }
                  })
                ])
              } else
                return from([
                  new GlobalErrorAction(err, true, true),
                  fromDocumentActions.setProgressSpinner({
                    payload: { show: false, type: ReviewSpinnerType.Tags }
                  })
                ])
            })
          )
        }
      )
    )
  )

  /**
   * The purpose of this effect is to figure out what document id is to set as current document id
   * This will call setCurrentDocument after figuring out the new document id to set as current
   *
   */
  setCurrentDocumentAuto$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.setCurrentDocumentAuto),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getSelectedDocuments)),
            this.store.pipe(select(getAllSearchResultFileIds)),
            this.store.pipe(select(getSearchResultDocumentTableData))
          )
        )
      ),
      switchMap(
        ([action, selectedDocumentIds, allDocumentIds, searchResult]) => {
          // Unset current document before setting new
          //this.store.dispatch(fromDocumentActions.unsetCurrentDocument())
          const actions = []
          let documentId = action.payload.documentId
          const conversationId = searchResult.fieldValues.find(
            (value) => value?.__FileID === documentId
          )?.__conversationId
          // If document id is passed, end of story, we will set that as the current document id, otherwise:
          if (!documentId) {
            // If there are some selected documents, choose the first one
            // This is needed in cases where user selects all
            if (
              selectedDocumentIds.length > 0 &&
              !action?.payload?.resetSelection
            ) {
              documentId = selectedDocumentIds[selectedDocumentIds.length - 1]
            } else {
              // If there was no selected documents, then, choose the first one out of all documents from the current document table page
              // Do nothing if there is no document
              if (allDocumentIds.length === 0) {
                return []
              }
              documentId = allDocumentIds[0]
              // document name from search result
              //documentName = searchResult.fieldValues[0]['ORIGINAL_FILE_NAME']

              actions.push(
                fromDocumentActions.setSelectedDocuments({
                  payload: { selectedDocuments: [documentId] }
                })
              )
            }
          }

          if (documentId && action?.payload?.resetSelection) {
            actions.push(
              fromDocumentActions.setSelectedDocuments({
                payload: { selectedDocuments: [documentId] }
              })
            )
          }
          actions.push(
            fromDocumentActions.setCurrentDocument({
              payload: {
                documentId: documentId,
                resetSelection: action.payload.resetSelection,
                conversationId: documentId < -1 ? conversationId : null
              }
            })
          )
          return actions
        }
      )
    )
  )

  /**
   * Called after the current document is set in the store.
   * Actions:
   *  - If reset selection is true, then all documents are unselected except for the current document.
   *  - Fetch viewer settings.
   */
  setCurrentDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.setCurrentDocument),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getProjectInfo)),
            this.store.pipe(select(getUserDetails)),
            this.store.pipe(select(getCurrentDocument))
          )
        )
      ),
      switchMap(
        ([action, projectInfo, userDetails, currentDocumentId]: [
          any,
          ProjectInfo,
          User,
          number
        ]) => {
          const actions = []
          if (action.payload.documentId) {
            if (
              (action.payload.documentId > 0 &&
                currentDocumentId !== action.payload.documentId) ||
              action?.payload?.conversationId?.length
            ) {
              this.multiWindowSelectionService.fetchViewerSettings$.next({
                payload: {
                  documentId: action.payload.documentId,
                  projectId: projectInfo.projectId,
                  userId: userDetails.userId,
                  imageSetId: GlobalDataService.imageSetId,
                  isExternalUser:
                    userDetails.userRole.toLowerCase() === 'external'
                }
              })
              actions.push(
                // fromDocumentActions.fetchViewerSettings({
                //   payload: {
                //     documentId: action.payload.documentId,
                //     projectId: projectInfo.projectId,
                //     userId: userDetails.userId,
                //     imageSetId: GlobalDataService.imageSetId,
                //     isExternalUser:
                //       userDetails.userRole.toLowerCase() === 'external'
                //   }
                // }),
                fromDocumentActions.setCurrentDocumentSuccess({
                  payload: {
                    documentId: action.payload.documentId,
                    conversationId:
                      action.payload.documentId < -1
                        ? action?.payload?.conversationId
                        : null,
                    resetSelection: action?.payload?.resetSelection
                  }
                }),
                fromDocumentActions.calculateDocumentNumber({
                  payload: {
                    currentDocumentId: action.payload.documentId
                  }
                })
                // fromDocumentActions.postDocumentSelection({
                //   payload: { isForcedFetch: false }
                // })
              )
            } else if (action.payload.documentId == -1) {
              actions.push(
                fromDocumentActions.setCurrentDocumentSuccess({
                  payload: {
                    documentId: action.payload.documentId,
                    conversationId: action?.payload?.conversationId,
                    resetSelection: action?.payload?.resetSelection
                  }
                })
              )
            }
          }
          actions.push(
            fromDocumentActions.postDocumentSelection({
              payload: { isForcedFetch: false }
            })
          )

          return actions
        }
      )
    )
  )

  /**
   * This effect is called soon after the store is done setting current document table page number
   * Here we are getting search results for the new page table page number
   */
  setCurrentDocumentTablePage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.setCurrentDocumentTablePage),
      concatMap((action) =>
        of(action).pipe(
          debounceTime(1000),
          withLatestFrom(
            this.store.pipe(select(getSearchResponse)),
            this.store.pipe(select(getProjectInfo)),
            this.store.pipe(select(getUserDetails))
          )
        )
      ),
      switchMap(
        ([
          action,
          searchResponse,
          projectInfo,
          userDetails
        ]: //selectedReviewSetId
        [any, SearchResponseModel, ProjectInfo, User]) => {
          const selectedReviewSetId = this.xsstore.selectSnapshot(
            //ReviewSetStateSelector.selectedReviewSetId
            ReviewSetStateSelector.SliceOf('selectedReviewSetId')
          )
          const rid = +this.route.snapshot.queryParams['reviewSetId']
          const venioFieldIds: number[] = this.xsstore.selectSnapshot(
            ReviewSetStateSelector.selectedVenioFields(
              ReviewPanelType.Table,
              false
            )
          )
          const customFieldIds: number[] = this.xsstore.selectSnapshot(
            ReviewSetStateSelector.selectedCustomFields(
              ReviewPanelType.Table,
              false
            )
          )
          const selectedViewType = this.xsstore.selectSnapshot(
            ReviewSetStateSelector.SliceOf('reviewViewType')
          )

          // Get the index of first item of the new document table page
          // This is index considering overall search result, not just the current document table page
          const startItem =
            (action.payload.pageNumber - 1) *
              searchResponse.searchResultIntialParameters.pageSize +
            1

          // incorrect isExternalUser value with external user so must reset here
          let isExternalUser = userDetails.userRole.toLowerCase() === 'external'
          if (localStorage.getItem('DocShareUserRole') != null) {
            isExternalUser =
              localStorage
                .getItem('DocShareUserRole')
                .toString()
                .toLowerCase() !== 'external'
                ? false
                : true
          }

          const selectedLayout = this.xsstore.selectSnapshot(
            ReviewSetStateSelector.selectedLayout
          )

          // reviewLayoutPanelId for sorting field as per sequence order while creating layout
          const reviewLayoutPanelId = selectedLayout?.reviewLayoutPanels?.find(
            (x) => x?.name === ReviewPanelType.Table
          )?.reviewLayoutPanelId

          return [
            fromSearchActions.fetchSearchResults({
              payload: {
                projectId: projectInfo.projectId,
                docReq: {
                  //pageNumber: startItem,
                  pageNumber: action.payload.pageNumber ?? 1,
                  pageSize:
                    searchResponse.searchResultIntialParameters.pageSize ?? 20,
                  viewSession: {
                    computedSearchTempTable:
                      searchResponse.tempTables.computedSearchTempTable,
                    searchResultTempTable:
                      searchResponse.tempTables.searchResultTempTable,
                    viewTypePagingTable:
                      searchResponse.tempTables.viewTypePagingTempTable,
                    viewTypeRecordsTable:
                      searchResponse.tempTables.viewTypeSearchResultTempTable
                  },
                  //When layout that is being loaded is created from default layout, venioFieldsIds will be empty. So, send null to fetch all fields that user have permission to.
                  venioFieldIds:
                    venioFieldIds?.length > 0 ? venioFieldIds : undefined,
                  customFieldIds: customFieldIds,
                  reviewSetId: selectedReviewSetId,
                  isExternalUser: isExternalUser,
                  viewType: selectedViewType,
                  reviewLayoutPanelId: reviewLayoutPanelId || null
                },
                resetSelectionItem: action.payload.resetSelectionItem,
                paginatedDocumentIndex: action?.payload?.paginatedDocumentIndex
              }
            })
          ]
        }
      )
    )
  )

  /**
   * Move to next document table page
   */
  moveToNextDocumentTablePage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.moveToNextDocumentTablePage),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getSearchResponse)),
            this.store.pipe(select(getCurrentDocumentTablePage))
          )
        )
      ),
      mergeMap(([action, searchResponse, currentDocumentTablePageNumber]) => {
        const numberOfPages = Math.ceil(
          searchResponse.searchResultIntialParameters.totalHitCount /
            searchResponse.searchResultIntialParameters.pageSize
        )
        const nextDocumentTablePageNumber = currentDocumentTablePageNumber + 1
        if (
          nextDocumentTablePageNumber <= numberOfPages &&
          nextDocumentTablePageNumber >= 1
        ) {
          // We don't want to navigate out of index
          return [
            fromDocumentActions.setCurrentDocumentTablePage({
              payload: {
                pageNumber: nextDocumentTablePageNumber,
                resetSelectionItem: action.payload.resetSelectionItem
              }
            })
          ]
        }
        return []
      })
    )
  )

  /**
   * Set next document in the document table as current document or move to next page and select the first one
   */
  moveToNextDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.moveToNextDocument),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getAllSearchResultFileIds)),
            this.store.pipe(select(getCurrentDocument)),
            this.store.pipe(select(getCurrentDocumentTablePage)),
            this.store.pipe(select(getSearchPageSize))
          )
        )
      ),
      mergeMap(
        ([
          action,
          documentIds,
          currentDocumentId,
          currentDocumentTablePage,
          searchPageSize
        ]) => {
          if (documentIds.includes(currentDocumentId)) {
            const index = documentIds.indexOf(currentDocumentId)
            if (index === documentIds.length - 1) {
              // If the document is at the last index of current document table page
              // Navigate to next page of the document table
              // NOTE: We are setting the first item in the next page as current document, thus true flag is passed as payload
              return [
                fromDocumentActions.moveToNextDocumentTablePage({
                  payload: { resetSelectionItem: 'first' }
                })
              ]
            } else {
              // If next document isn't the last document on current document table page
              // Dispatch actions to set next document as the current document and update document table selection
              const nextDocumentId = documentIds[index + 1]
              return [
                fromDocumentActions.setSelectedDocuments({
                  payload: { selectedDocuments: [nextDocumentId] }
                }),
                fromDocumentActions.setCurrentDocument({
                  payload: {
                    documentId: nextDocumentId,
                    resetSelection: true,
                    conversationId: null
                  }
                })
              ]
            }
          }
          return []
        }
      )
    )
  )

  /**
   * Edited: Sanh Huynh; Date: April 30, 2020; Ref #23826 - Review: Allow document table to 'collapse' hide
   *  add back button
   *  Move back document table page
   */
  moveBackDocumentTablePage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.moveBackDocumentTablePage),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(this.store.pipe(select(getCurrentDocumentTablePage)))
        )
      ),
      mergeMap(([action, currentDocumentTablePageNumber]) => {
        const backDocumentTablePageNumber = currentDocumentTablePageNumber - 1
        if (backDocumentTablePageNumber > 0) {
          // We don't want to navigate out of index
          return [
            fromDocumentActions.setCurrentDocumentTablePage({
              payload: {
                pageNumber: backDocumentTablePageNumber,
                resetSelectionItem: action.payload.resetSelectionItem
              }
            })
          ]
        }
        return []
      })
    )
  )

  /**
   * Edited: Sanh Huynh; Date: April 30, 2020; Ref #23826 - Review: Allow document table to 'collapse' hide
   *  add back button
   *  Set back document in the document table as current document or move to next page and select the first one
   */
  moveBackDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.moveBackDocument),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getAllSearchResultFileIds)),
            this.store.pipe(select(getCurrentDocument))
          )
        )
      ),
      mergeMap(([action, documentIds, currentDocumentId]) => {
        if (documentIds.includes(currentDocumentId)) {
          const index = documentIds.indexOf(currentDocumentId)
          if (index === 0) {
            // If the document is at the last index of current document table page
            // Navigate to next page of the document table
            // NOTE: We are setting the first item in the next page as current document, thus true flag is passed as payload
            return [
              fromDocumentActions.moveBackDocumentTablePage({
                payload: { resetSelectionItem: 'last' }
              })
            ]
          } else {
            // If next document isn't the last document on current document table page
            // Dispatch actions to set next document as the current document and update document table selection
            const backDocumentId = documentIds[index - 1]
            return [
              fromDocumentActions.setSelectedDocuments({
                payload: { selectedDocuments: [backDocumentId] }
              }),
              fromDocumentActions.setCurrentDocument({
                payload: {
                  documentId: backDocumentId,
                  resetSelection: true,
                  conversationId: null
                }
              })
            ]
          }
        }
        return []
      })
    )
  )

  /**
   * Fetches internal users for document sharing
   */
  fetchDocumentShareUsersInternal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentShareUsersInternal),
      switchMap((action) =>
        this.service
          .fetchDocumentShareUsersInternal$(action.payload.projectId)
          .pipe(
            mergeMap((response: any) => {
              try {
                const userResponseModel = this.jsonConvert.deserializeObject(
                  response,
                  DocumentShareUserResponseModel
                )
                return [
                  new GlobalSuccessAction(
                    'Document share internal users list fetch successfully.',
                    response,
                    false,
                    false
                  ),
                  fromDocumentActions.setDocumentShareUsersInternal({
                    payload: { userModels: userResponseModel.shareUserInfoList }
                  })
                ]
              } catch (e) {
                return [
                  new GlobalErrorAction(
                    new Error(
                      'Error de-serializing document share users response.'
                    ),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Fetches external users for document sharing
   */
  fetchDocumentShareUsersExternal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.fetchDocumentShareUsersExternal),
      switchMap((action) =>
        this.service
          .fetchDocumentShareUsersExternal$(action.payload.projectId)
          .pipe(
            mergeMap((response: any) => {
              try {
                const userResponseModel = this.jsonConvert.deserializeObject(
                  response,
                  DocumentShareUserResponseModel
                )
                return [
                  new GlobalSuccessAction(
                    'Document share external users list fetch successfully.',
                    response,
                    false,
                    false
                  ),
                  fromDocumentActions.setDocumentShareUsersExternal({
                    payload: {
                      userModels: userResponseModel.shareExtUserInfoList
                    }
                  })
                ]
              } catch (e) {
                return [
                  new GlobalErrorAction(
                    new Error(
                      'Error de-serializing document share users response.'
                    ),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Shares document
   */
  shareDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.shareDocuments),
      switchMap((action) =>
        this.service
          .shareDocument$(action.payload.projectId, action.payload.requestModel)
          .pipe(
            mergeMap((response: any) => {
              if (response === 'SUCCESS') {
                return [
                  new GlobalSuccessAction(
                    'Documents shared successfully.',
                    response,
                    true,
                    false
                  )
                ]
              } else {
                return [
                  new GlobalErrorAction(
                    new Error('Error sharing documents. ' + response),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  getCodingFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.getCodingFields),
      switchMap((action) => {
        if (!action.payload.fileId) return EMPTY
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.Coding }
          })
        )
        return this.service
          .fetchDocumentDetails$(
            action.payload.fileId,
            action.payload.type,
            action.payload.projectId
          )
          .pipe(
            mergeMap((response: any) => {
              try {
                // const codingFields = this.jsonConvert.deserializeArray(
                //   response.fieldCodingModel,
                //   DocumentCodingModel
                // )
                const codingFields = response.fieldCodingModel
                return [
                  new GlobalSuccessAction(
                    'Document coding fields fetched successfully.',
                    codingFields,
                    false,
                    false
                  ),
                  fromDocumentActions.setCodingFields({
                    payload: { responseModel: codingFields }
                  }),
                  fromDocumentActions.setProgressSpinner({
                    payload: { show: false, type: ReviewSpinnerType.Coding }
                  })
                ]
              } catch (e) {
                return [
                  new GlobalErrorAction(
                    new Error(
                      'Error de-serializing document coding fields (' +
                        action.payload.type +
                        ').'
                    ),
                    true,
                    true
                  ),
                  fromDocumentActions.setProgressSpinner({
                    payload: { show: false, type: ReviewSpinnerType.Coding }
                  })
                ]
              }
            }),
            catchError((err) =>
              from([
                new GlobalErrorAction(err, true, true),
                fromDocumentActions.setProgressSpinner({
                  payload: { show: false, type: ReviewSpinnerType.Coding }
                })
              ])
            )
          )
      })
    )
  )

  saveCodingFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.saveCodingFields),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getCurrentDocument)),
            this.store.pipe(select(getCurrentDocumentName))
          )
        )
      ),
      switchMap(([action, currentDocument, currentDocumentName]) => {
        this.store.dispatch(
          fromDocumentActions.setProgressSpinner({
            payload: { show: true, type: ReviewSpinnerType.Coding }
          })
        )
        return this.service
          .saveCodingFields$(
            action.payload.saveCodingModel,
            action.payload.projectId
          )
          .pipe(
            mergeMap((response: any) => {
              if (response.error) {
                const navigationType = action.payload.navigationType || ''
                if (action.payload.navigationType != '') {
                  return [
                    fromDocumentActions.setProgressSpinner({
                      payload: { show: false, type: ReviewSpinnerType.Coding }
                    })
                  ]
                } else {
                  return [
                    new GlobalErrorAction(
                      new Error(response.errorMessage),
                      true,
                      false
                    ),
                    fromDocumentActions.setProgressSpinner({
                      payload: { show: false, type: ReviewSpinnerType.Coding }
                    })
                  ]
                }
              } else {
                // const codingSummary = this.jsonConvert.deserializeArray(
                //   response.UpdatedCodingList,
                //   CodingSummary
                // )
                const codingSummary = response.updatedCodingList

                // keep latest coding field save use for copy
                const codingSaved: CodingFieldSavedModel = {
                  fileId: currentDocument,
                  fileName: currentDocumentName,
                  codingFieldSavedChange: codingSummary
                }

                return [
                  fromDocumentActions.saveCodingFieldsSuccess({
                    payload: { updatedCodingModel: codingSummary }
                  }),
                  fromDocumentActions.setCodingSummary({
                    payload: { summaryDetails: codingSummary }
                  }),
                  fromDocumentActions.setCodingFieldSavedLatest({
                    payload: { codingFieldSavedLatest: codingSaved }
                  }),
                  fromDocumentActions.updateDocumentTable({
                    payload: { updatedCodingModel: codingSummary }
                  }),
                  new GlobalSuccessAction(
                    'Coding fields updated successfully.',
                    null,
                    true,
                    false,
                    true
                  ),
                  fromDocumentActions.setProgressSpinner({
                    payload: { show: false, type: ReviewSpinnerType.Coding }
                  })
                ]
              }
            }),
            catchError((err) =>
              from([
                new GlobalErrorAction(err, true, true),
                fromDocumentActions.setProgressSpinner({
                  payload: { show: false, type: ReviewSpinnerType.Coding }
                })
              ])
            )
          )
      })
    )
  )

  /**
   * Fetches Tiff Limit Settings
   */
  getTiffLimitSetting$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.getTiffLimitSetting),
      switchMap((action) =>
        this.service.getTiffLimitSetting$(action.payload.projectId).pipe(
          mergeMap((response: ResponseModel) => {
            try {
              const limitResponseModel = this.jsonConvert.deserializeObject(
                response.data,
                Limit
              )
              return [
                new GlobalSuccessAction(
                  'Tiff Limit Settings fetched successfully.',
                  response.data,
                  false,
                  false
                ),
                fromDocumentActions.setTiffLimitSetting({
                  payload: { limitModels: limitResponseModel }
                })
              ]
            } catch (e) {
              return [
                new GlobalErrorAction(
                  new Error('Error de-serializing Limit Setting response.'),
                  true,
                  true
                )
              ]
            }
          }),
          catchError((err) => from([new GlobalErrorAction(err, true, true)]))
        )
      )
    )
  )

  /**
   * Fetches image document count
   */
  getImageDocumentCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.getImageCount),
      switchMap((action) =>
        this.service
          .getImageDocumentCount$(
            action.payload.projectId,
            action.payload.selectionParameter
          )
          .pipe(
            mergeMap((response: ResponseModel) => {
              return [
                new GlobalSuccessAction(
                  'Document image count fetched successfully.',
                  response.data,
                  false,
                  false
                ),
                fromDocumentActions.setImageCount({
                  payload: { imageCount: response.data }
                })
              ]
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Validate print name
   */
  validatePrintName$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.checkPrintName),
      switchMap((action) =>
        this.service
          .checkPrintName$(
            action.payload.projectId,
            action.payload.printJobName
          )
          .pipe(
            mergeMap((response: ResponseModel) => {
              return [
                new GlobalSuccessAction(
                  'Document image count fetched successfully.',
                  response.data,
                  false,
                  false
                ),
                fromDocumentActions.validatePrintName({
                  payload: { printName: response.data }
                })
              ]
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Fetches export items
   */
  getExport$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.getExport),
      switchMap((action) =>
        this.service
          .getExport$(
            action.payload.projectId,
            action.payload.selectionParameter
          )
          .pipe(
            mergeMap((response: ResponseModel) => {
              try {
                const exportresponseModels = this.jsonConvert.deserializeArray(
                  response.data,
                  Export
                )
                return [
                  new GlobalSuccessAction(
                    'Export list fetched successfully.',
                    response.data,
                    false,
                    false
                  ),
                  fromDocumentActions.setExport({
                    payload: { exportModels: exportresponseModels }
                  })
                ]
              } catch (e) {
                return [
                  new GlobalErrorAction(
                    new Error('Error de-serializing export list response.'),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Fetches SlipSheet Field
   */
  getSlipSheetField$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.getSlipSheetField),
      switchMap((action) =>
        this.service.getSlipSheetField$(action.payload.projectId).pipe(
          mergeMap((response: ResponseModel) => {
            const fieldData: FieldModel[] = this.jsonConvert.deserializeArray(
              response.data,
              FieldModel
            )
            return [
              fromDocumentActions.setSlipSheetField({
                payload: { slipSheetModel: fieldData }
              })
            ]
          }),
          catchError((err) => from([new GlobalErrorAction(err, true, true)]))
        )
      )
    )
  )

  /**
   * Fetches Document Summary For Print
   */
  getSummaryForPrint$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.getSummary),
      switchMap((action) =>
        this.service
          .getSummaryForPrint$(
            action.payload.projectId,
            action.payload.summarySelectionParameter
          )
          .pipe(
            mergeMap((response: ResponseModel) => {
              try {
                const summaryresponseModels =
                  this.jsonConvert.deserializeObject(response.data, Summary)
                return [
                  new GlobalSuccessAction(
                    'Summary list fetched successfully.',
                    response.data,
                    false,
                    false
                  ),
                  fromDocumentActions.setSummary({
                    payload: { summaryModels: summaryresponseModels }
                  })
                ]
              } catch (e) {
                return [
                  new GlobalErrorAction(
                    new Error('Error de-serializing summary list response.'),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Print documents
   */
  printDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.printDocuments),
      switchMap((action) =>
        this.service
          .printDocument$(
            action.payload.projectId,
            action.payload.printToPDFParameter
          )
          .pipe(
            mergeMap((response: ResponseModel) => {
              if (response.data === true) {
                return [
                  new GlobalSuccessAction(
                    'Documents to be printed queued successfully.',
                    response,
                    true,
                    false
                  )
                ]
              } else {
                return [
                  new GlobalErrorAction(
                    new Error('Error printing documents. '),
                    true,
                    true
                  )
                ]
              }
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  /**
   * Fetches Download items
   */
  getDownloadStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.getDownloadStatus),
      switchMap((action) =>
        this.service.getDownloadStatus$(action.payload.projectId).pipe(
          mergeMap((response: ResponseModel) => {
            try {
              const downloadStatusresponseModels =
                this.jsonConvert.deserializeArray(
                  response.data,
                  PrintStatusModel
                )
              return [
                new GlobalSuccessAction(
                  'Donwload Status list fetched successfully.',
                  response.data,
                  false,
                  false
                ),
                fromDocumentActions.setDownloadStatus({
                  payload: {
                    downloadStatusModels: downloadStatusresponseModels
                  }
                })
              ]
            } catch (e) {
              return [
                new GlobalErrorAction(
                  new Error(
                    'Error de-serializing download status list response.'
                  ),
                  true,
                  true
                )
              ]
            }
          }),
          catchError((err) => from([new GlobalErrorAction(err, true, true)]))
        )
      )
    )
  )

  /**
   * Deletes Print items
   */
  deleteDownloadStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.deletePrintDocument),
      switchMap((action) =>
        this.service
          .deletePrintDocument$(
            action.payload.projectId,
            action.payload.printId
          )
          .pipe(
            mergeMap((response: ResponseModel) => {
              return [
                new GlobalSuccessAction(
                  'Document deleted successfully.',
                  null,
                  true,
                  false
                ),
                fromDocumentActions.getDownloadStatus({
                  payload: {
                    projectId: action.payload.projectId
                  }
                })
              ]
            }),
            catchError((err) => from([new GlobalErrorAction(err, true, true)]))
          )
      )
    )
  )

  private async ShowTagSummary(
    saveTagResponse: SaveTagResponseModel[],
    tagSettings: any
  ) {
    await import(
      '../../../review/components/document-tag-message-popup/document-tag-message-popup.module'
    )
    this.matDiolog.open(DocumentTagMessagePopupComponent, {
      data: {
        headerTitle: 'VenioOne OnDemand',
        btnText: 'OK',
        isFromAnalyze: false,
        saveTagResponse: saveTagResponse,
        tagSettings: tagSettings
      }
    })
  }

  getDocumentRequestModel(
    tempTables: TempTableResponseModel,
    venioFieldIds: number[],
    customFieldIds: number[],
    searchFieldViewerType: SearchFieldViewerType,
    overrideGroupSecurity: boolean,
    panelType: ReviewPanelType
  ): SearchResultRequestData {
    // incorrect isExternalUser value with external user so must reset here
    let isExternalUser = false
    if (localStorage.getItem('DocShareUserRole') != null) {
      isExternalUser =
        localStorage.getItem('DocShareUserRole').toString().toLowerCase() !==
        'external'
          ? false
          : true
    }

    const selectedLayout = this.xsstore.selectSnapshot(
      ReviewSetStateSelector.selectedLayout
    )

    // reviewLayoutPanelId for sorting field as per sequence order while creating layout
    const reviewLayoutPanelId = selectedLayout?.reviewLayoutPanels?.find(
      (x) => x?.name === panelType
    )?.reviewLayoutPanelId

    return {
      pageNumber: 0,
      pageSize: -1,
      viewSession: {
        computedSearchTempTable: tempTables.computedSearchTempTable,
        searchResultTempTable: tempTables.searchResultTempTable
      },
      //When layout that is being loaded is created from default layout, venioFieldsIds will be empty. So, send null to fetch all fields that user have permission to.
      venioFieldIds: venioFieldIds?.length > 0 ? venioFieldIds : null,
      customFieldIds: customFieldIds,
      overrideGroupSecurity: overrideGroupSecurity,
      searchFieldViewerType: searchFieldViewerType,
      isExternalUser: isExternalUser,
      reviewLayoutPanelId: reviewLayoutPanelId || null,
      documentShareToken: this._documentShareToken
    }
  }

  /**
   * Download CSV File for Review Document
   */
  downloadCSVReviewDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.downloadCSVReviewDocument),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getSearchResponse)),
            this.store.pipe(select(getProjectInfo)),
            this.store.pipe(select(getUserDetails)),
            this.store.pipe(select(getIsBatchSelection)),
            this.store.pipe(select(getSelectedDocuments)),
            this.store.pipe(select(getUnselectedDocuments))
          )
        )
      ),
      switchMap(
        ([
          action,
          searchResponse,
          projectInfo,
          userDetails,
          isBatchSelected,
          selectedDocuments,
          unSelectedDocuments
        ]: [
          any,
          SearchResponseModel,
          ProjectInfo,
          User,
          boolean,
          number[],
          number[]
        ]) => {
          // incorrect isExternalUser value with external user so must reset here
          let isExternalUser = userDetails.userRole.toLowerCase() === 'external'
          if (localStorage.getItem('DocShareUserRole') != null) {
            isExternalUser =
              localStorage
                .getItem('DocShareUserRole')
                .toString()
                .toLowerCase() !== 'external'
                ? false
                : true
          }

          const selectedViewType = this.xsstore.selectSnapshot(
            ReviewSetStateSelector.SliceOf('reviewViewType')
          )
          // Create search result request model
          const searchResultsRequest: SearchResultRequestModel = {
            computedSearchTempTable:
              searchResponse.tempTables.computedSearchTempTable,
            dsid: String(projectInfo.dsid),
            guid: searchResponse.tempTables.searchGuid,
            isExternalUser: isExternalUser,
            pageNumber: action.payload.pageNumber,
            pageSize: String(action.payload.pageSize),
            projectId: String(projectInfo.projectId),
            searchResultTempTable:
              searchResponse.tempTables.searchResultTempTable,
            sessionId: null,
            userId: String(userDetails.userId),
            totalDocuments: action.payload.totalDocuments,
            columnData: action.payload.columnData,
            isBatchSelected: isBatchSelected,
            selectedFileIds: !isBatchSelected ? selectedDocuments : [],
            unSelectedFileIds: isBatchSelected ? unSelectedDocuments : [],
            viewType: selectedViewType
          }

          return [
            fromSearchActions.searchDownloadCSVReviewDocument({
              payload: {
                resultRequest: searchResultsRequest
              }
            })
          ]
        }
      )
    )
  )

  getPDFAnnotations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.getPdfAnnotations),
      switchMap((action) =>
        this.service
          .loadPdfAnnotation$(action.payload.projectId, action.payload.fileID)
          .pipe(
            mergeMap((response: ResponseModel) => {
              //parse the response string to xml to load the annotation in PDF
              let xmlDoc
              if (response.status == ResponseStatus.SUCCESS) {
                if (response.data != null && response.data != '') {
                  const parser = new DOMParser()
                  xmlDoc = parser.parseFromString(response.data, 'text/xml')
                }
              }
              return [
                fromDocumentActions.setPdfAnnotations({
                  payload: {
                    xfdfString: xmlDoc
                  }
                })
              ]
            })
          )
      )
    )
  )

  getSyncfusionPDFAnnotations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.getSyncfusionAnnotations),
      switchMap((action) =>
        this.service
          .fetchSyncfusionAnnotations$(
            action.payload.projectId,
            action.payload.fileId
          )
          .pipe(
            mergeMap((response: any) => {
              return [
                fromDocumentActions.setSyncfusionPdfAnnotations({
                  payload: {
                    annotationJson: response
                  }
                })
              ]
            })
          )
      )
    )
  )

  /***
   * calculate document number of currently selected document
   * this will be triggered while selecting row from doc table or navigating from doc navigation option
   */
  calculateDocumentNumber$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.calculateDocumentNumber),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getAllSearchResultFileIds)),
            this.store.pipe(select(getCurrentDocumentTablePage)),
            this.store.pipe(select(getSearchPageSize))
          )
        )
      ),
      mergeMap(
        ([action, documentIds, currentDocumentTablePage, searchPageSize]) => {
          const index = documentIds.indexOf(+action?.payload?.currentDocumentId)
          const docNumber =
            ((currentDocumentTablePage || 1) - 1) * searchPageSize + (index + 1)
          this.xsstore.dispatch(new SetcurrentDocNumber(docNumber))
          return []
        }
      )
    )
  )

  /**
   * navigate to first or last document
   */
  moveToFirstOrLastDocumentTablePage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.moveToFirstOrLastDocumentTablePage),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getSearchResponse)),
            this.store.pipe(select(getCurrentDocumentTablePage)),
            this.store.pipe(select(getAllSearchResultFileIds)),
            this.store.pipe(select(getCurrentDocument))
          )
        )
      ),
      mergeMap(
        ([
          action,
          searchResponse,
          currentDocumentTablePageNumber,
          documentIds,
          currentDocumentId
        ]) => {
          const numberOfPages = Math.ceil(
            searchResponse.searchResultIntialParameters.totalHitCount /
              searchResponse.searchResultIntialParameters.pageSize
          )
          const documentIndex = documentIds?.indexOf(currentDocumentId)
          // || (action?.payload?.action === 'last' && currentDocumentTablePageNumber === numberOfPages)

          if (
            action?.payload?.action === 'first' &&
            currentDocumentTablePageNumber === 1
          ) {
            if (documentIndex === 0) {
              // We don't want to navigate out of index
              return []
            }
            const nextDocumentId = documentIds[0]
            return [
              fromDocumentActions.setSelectedDocuments({
                payload: { selectedDocuments: [nextDocumentId] }
              }),
              fromDocumentActions.setCurrentDocument({
                payload: {
                  documentId: nextDocumentId,
                  resetSelection: true,
                  conversationId: null
                }
              })
            ]
          }

          if (
            action?.payload?.action === 'last' &&
            currentDocumentTablePageNumber === numberOfPages
          ) {
            const lastDocIndex = documentIds?.length - 1
            if (documentIndex === lastDocIndex) {
              // We don't want to navigate out of index
              return []
            }
            const nextDocumentId = documentIds[lastDocIndex]
            return [
              fromDocumentActions.setSelectedDocuments({
                payload: { selectedDocuments: [nextDocumentId] }
              }),
              fromDocumentActions.setCurrentDocument({
                payload: {
                  documentId: nextDocumentId,
                  resetSelection: true,
                  conversationId: null
                }
              })
            ]
          }
          return [
            fromDocumentActions.setCurrentDocumentTablePage({
              payload: {
                pageNumber:
                  action?.payload?.action === 'first' ? 1 : numberOfPages,
                resetSelectionItem: action?.payload?.action
              }
            })
          ]
        }
      )
    )
  )

  /**
   * navigates to selected/enterned document number
   * calculates the pagenumber, document index of that pagenumber
   * and loads page if necessary or just navigates document
   */
  navigateDocumentFromNumber$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromDocumentActions.navigateDocumentFromNumber),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.store.pipe(select(getSearchResponse)),
            this.store.pipe(select(getSearchPageSize)),
            this.store.pipe(select(getAllSearchResultFileIds)),
            this.store.pipe(select(getCurrentDocumentTablePage))
          )
        )
      ),
      mergeMap(
        ([
          action,
          searchResponse,
          pageSize,
          documentIds,
          currentDocumentTablePage
        ]) => {
          /***
           *  gets valid document number
           * if payload docNumber is less than or equal to 0 then returns 1
           * if payload docNumber is greater than total doc then returns last doc number*/
          const documentNumber =
            (action?.payload?.documentNumber || 0) <= 0
              ? 1
              : action.payload.documentNumber >
                searchResponse.searchResultIntialParameters.totalHitCount
              ? searchResponse.searchResultIntialParameters.totalHitCount
              : action.payload.documentNumber

          const pageNumber = Math.ceil(documentNumber / pageSize)

          // calculates document index of specific pageNumber
          const paginatedDocIndex =
            documentNumber - pageSize * (pageNumber - 1) - 1

          if (pageNumber === currentDocumentTablePage) {
            // searched document lies on currently loaded table/list then just select doc from current page
            const nextDocumentId = documentIds[paginatedDocIndex]
            return [
              fromDocumentActions.setSelectedDocuments({
                payload: { selectedDocuments: [nextDocumentId] }
              }),
              fromDocumentActions.setCurrentDocument({
                payload: {
                  documentId: nextDocumentId,
                  resetSelection: true,
                  conversationId: null
                }
              })
            ]
          }

          // load pageNumber and select specific document

          return [
            fromDocumentActions.setCurrentDocumentTablePage({
              payload: {
                pageNumber: pageNumber,
                resetSelectionItem: 'nth_term',
                paginatedDocumentIndex: paginatedDocIndex
              }
            })
          ]
        }
      )
    )
  )
}
