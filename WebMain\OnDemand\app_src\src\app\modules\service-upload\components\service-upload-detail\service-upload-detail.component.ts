import { CaseModel } from '@admin-advance/models'
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { Store } from '@ngxs/store'
import * as fromRootActions from '@stores/actions'
import * as fromCaseStateModels from '@stores/models'
import { SERVICE_TYPE_CONSTANTS } from '@stores/models'
import { CaseSelectors } from '@stores/selectors'
import { Observable, Subject } from 'rxjs'
import { takeUntil } from 'rxjs/operators'

@Component({
  selector: 'app-service-upload-detail',
  templateUrl: './service-upload-detail.component.html',
  styleUrls: ['./service-upload-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ServiceUploadDetailComponent implements OnInit {
  @Input() settingsForm: FormGroup

  @Input() timeZones$: Observable<fromCaseStateModels.TimeZone[]>

  @Input() exportTemplates: Observable<
    fromCaseStateModels.ExportFieldTemplate[]
  >

  @Input() serviceTypeList: any[]

  @Input() openAllAccordions = false

  @Input() existingCaseId: number

  @Input() caseList: CaseModel[]

  @Output() existingCaseValue = new EventEmitter<boolean>()

  @Output() selectedCaseIdValue = new EventEmitter<number>()

  @Input() isExistingCase: boolean

  @Input() isCaseCreationFlow: boolean

  private unsubscribed$ = new Subject<void>()

  public generalSettingsOpen = true

  public imageConversionOpen = false

  public overrideSettings = false

  deduplicationOptions = [
    { displayName: 'None', value: 2 },
    { displayName: 'Custodian Level', value: 1 },
    { displayName: 'Global', value: 0 }
  ]

  csvExcelOptions = [
    { displayName: 'Placeholder Only', value: 0 },
    { displayName: 'Cap to 100 Pages, then Placeholder', value: 1 },
    { displayName: 'Process all Pages', value: 2 }
  ]

  exceptionHandlingOptions = [
    { displayName: 'Notify me and allow file repair', value: true },
    {
      displayName: 'Do not notify me, complete the project and report',
      value: false
    }
  ]

  sortOrderOptions = [
    { displayName: 'Original Discovery Order', value: 'RELATIVE_FILE_PATH' },
    { displayName: 'Sort By Date – Oldest to Newest', value: 'GROUP_DATE_ASC' },
    { displayName: 'Sort By Date – Newest to Oldest', value: 'GROUP_DATE_DESC' }
  ]

  prefixDelimiterOptions = [
    { displayName: 'None', value: 0 },
    { displayName: 'Space', value: 1 },
    { displayName: 'Dash(-)', value: 2 },
    { displayName: 'Underscore(_)', value: 3 },
    { displayName: 'Period(.)', value: 4 }
  ]

  controlNumberLocationOptions = [
    { displayName: 'TL', value: 0 },
    { displayName: 'TC', value: 1 },
    { displayName: 'TR', value: 2 },
    { displayName: 'LL', value: 3 },
    { displayName: 'LC', value: 4 },
    { displayName: 'LR', value: 5 }
  ]

  existingCase = false

  showCaseNameInput = true

  selectedCaseId: number

  constructor(
    private store: Store,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes?.isExistingCase?.currentValue) {
      this.existingCase = changes?.isExistingCase?.currentValue
    }
    if (changes?.existingCaseId?.currentValue) {
      this.fetchExistingCaseData(changes?.existingCaseId?.currentValue)
      this.existingCase = true
      this.showCaseNameInput = false
    }

    if (changes?.isCaseCreationFlow?.currentValue !== undefined) {
      const isCaseCreationflow = !changes?.isCaseCreationFlow?.currentValue
      this.disableServiceTypeCaseNameInput(isCaseCreationflow)
    }

    if (changes?.caseList?.currentValue?.length > 0) {
      this.filterCaseListBaseOnServiceType(changes?.caseList?.currentValue)
    }

    // if (changes?.serviceTypeList?.currentValue?.length > 0) {
    //   const serviceTypelist = _.cloneDeep(
    //     changes?.serviceTypeList?.currentValue
    //   )
    //   serviceTypelist.push(this.EXISTING_CASE_SERVICE_TYPE)
    //   this.serviceTypeList = serviceTypelist
    // }
    this.existingCaseEmit(this.existingCase)
    if (!this.existingCase) {
      this.updateFormControls(true)
    }

    const settingsFormControl = changes?.settingsForm?.currentValue
    if (settingsFormControl) {
      this.settingsForm = settingsFormControl
    }
    const overrideSettingControl =
      changes?.settingsForm?.currentValue?.get('overrideSetting')
    if (overrideSettingControl) {
      this.overrideSettings = overrideSettingControl.value
      this.updateFormControls(this.overrideSettings)
    }
  }

  private filterCaseListBaseOnServiceType(caseList: CaseModel[]): void {
    this.caseList = caseList.filter(
      ({ IsServiceTypeCase, ServiceType }) =>
        IsServiceTypeCase &&
        ServiceType?.trim()?.toLowerCase() === 'pdf service'
    )
  }

  private disableServiceTypeCaseNameInput(isUploadFlow: boolean) {
    const serviceRequestTypeControl =
      this.settingsForm.get('ServiceRequestType')
    if (serviceRequestTypeControl) {
      isUploadFlow
        ? serviceRequestTypeControl.disable()
        : serviceRequestTypeControl.enable()
    }

    // Disable/enable selectedCase form control
    const selectedCaseControl = this.settingsForm.get('selectedCase')
    if (selectedCaseControl) {
      isUploadFlow
        ? selectedCaseControl.disable()
        : selectedCaseControl.enable()
    }
  }

  public selectedCaseChanged(caseId: number): void {
    this.updateOverrideSettingsOption(false)
    this.fetchExistingCaseData(caseId)
  }

  fetchExistingCaseData(existingCaseId: number) {
    this.selectedCaseId = existingCaseId
    this.selectedCaseIdEmit(existingCaseId)
  }

  /**
   * Updates override settings in the form on checkbox change.
   * @param overrideSettings
   */
  updateOverrideSettingsOption(event: any) {
    this.overrideSettings = event
    this.settingsForm.patchValue({
      overrideSetting: event
    })
    this.updateFormControls(event)
  }

  private updateFormControls(overrideSettings: boolean) {
    const formGroups = [
      'generalSettings',
      'imageConversionSettings',
      'controlNumberAndEndorsementSettings',
      'productionSettings',
      'pdfServiceSettings'
    ]

    // always enable controls for new cases
    if (!this.existingCase) overrideSettings = true

    formGroups.forEach((groupName) => {
      const formGroup = this.settingsForm.get(groupName)
      if (formGroup) {
        overrideSettings ? formGroup.enable() : formGroup.disable()
      }
    })

    // to disable certain control even if overrideSettings is true
    if (overrideSettings) {
      const controlNumberCtrl = this.settingsForm.get(
        'controlNumberAndEndorsementSettings.ControlNumberSetting'
      )
      // disable control number related fields that should remain disabled unless endorse option is checked
      if (!controlNumberCtrl?.get('endorseControlNumber')?.value) {
        controlNumberCtrl?.get('controlNumberLocation')?.disable()
      }
      if (!controlNumberCtrl?.get('endorseOptionalMessage')?.value) {
        controlNumberCtrl?.get('messageText')?.disable()
        controlNumberCtrl?.get('messageTextLocation')?.disable()
      }
    }
  }

  private updateDefaultValues(data: any) {
    this.settingsForm.patchValue({
      exportTemplateName: data.exportTemplateName,
      approvePreProcessPage_CostEstimate:
        data.approvePreProcessPage_CostEstimate,
      generalSettings: {
        deduplicationOption: data.imageConversionOption.deduplicationOption,
        timeZone: data.tzTimeZone,
        csvExcelHandling: data.imageConversionOption.csV_Excel_option,
        discoveryExceptionHandling: data.enableDiscoveryExceptionHandling,
        passwords: '',
        autoGenerateImagesAfterIngestion:
          data.imageConversionOption.autoGenerateImagesAfterIngestion,
        ignoreAutoTiffJobsForMediaProcessingStatus:
          data.imageConversionOption.ignoreAutoTiffJobsForMediaProcessingStatus
      },
      imageConversionSettings: {
        imageColorConversion: {
          imageFileType:
            data.imageConversionOption.imageColorConversion.imageFileType,
          pdfFiles: data.imageConversionOption.imageColorConversion.pdfFiles,
          powerpoint: data.imageConversionOption.imageColorConversion.powerpoint
        },
        passwordList: this.#getFormValueFromPasswords(
          data.imageConversionOption.passwordList
        )
      },
      controlNumberAndEndorsementSettings: {
        sortOrder: data.controlNumber_Endorsement.sortOrder,
        exportLocation: data.controlNumber_Endorsement.exportLocation,
        ControlNumberSetting: {
          controlNumberPrefix:
            data.controlNumber_Endorsement.controlNumberSetting.prefix,
          controlNumberDelimiter:
            data.controlNumber_Endorsement.controlNumberSetting.prefixDelimiter,
          controlNumberStartingNumber:
            data.controlNumber_Endorsement.controlNumberSetting.startNumber,
          endorseControlNumber:
            data.controlNumber_Endorsement.controlNumberSetting
              .endorseControlNumber,
          controlNumberLocation:
            data.controlNumber_Endorsement.controlNumberSetting
              .controlNumberLocation,
          endorseOptionalMessage:
            data.controlNumber_Endorsement.controlNumberSetting
              .endorseOptionalMessage,
          messageText:
            data.controlNumber_Endorsement.controlNumberSetting.messageText,
          messageTextLocation:
            data.controlNumber_Endorsement.controlNumberSetting
              .optionalMessageLocation,
          volumeId:
            data.controlNumber_Endorsement.controlNumberSetting.volumnId,
          paddingLength:
            data.controlNumber_Endorsement.controlNumberSetting.paddingLength,
          continueFromPreviousControlNumber:
            data.controlNumber_Endorsement.controlNumberSetting
              .continueFromPreviousControlNumber,
          advancedEndorsementSetting:
            data.controlNumber_Endorsement.controlNumberSetting
              .advancedEndorsementSetting,
          prefixDelimiterValue:
            data.controlNumber_Endorsement.controlNumberSetting
              .prefixDelimiterValue
        }
      },
      productionSettings: {
        fieldTemplateId: data.productionOptions?.fieldTemplateID,
        filterOptions: {
          excludeProducedDocuments:
            data.productionOptions.filterOptions.excludeProducedDocuments,
          excludeNativeForRedactedDocuments:
            data.productionOptions.filterOptions
              .excludeNativeForRedactedDocuments
        },
        savedSearchesForExpressions:
          data.productionOptions.savedSearchesForExpressions
      },
      pdfServiceSettings: {
        pdfType: data.pdfServiceOption.pdfType,
        pdfFamilyFileHandling: data.pdfServiceOption.pdfFamilyFileHandling,
        pdfFileNamingConvention: data.pdfServiceOption.pdfFileNamingConvention
      },
      thirdPartyBillingOption: {
        thirdPartyBillingEnabled:
          data.thirdPartyBillingOption.thirdPartyBillingEnabled,
        company: data.thirdPartyBillingOption.company,
        billingAddress: data.thirdPartyBillingOption.billingAddress,
        billingCaseName: data.thirdPartyBillingOption.billingCaseName,
        contactPerson: data.thirdPartyBillingOption.contactPerson,
        contactPhone: data.thirdPartyBillingOption.contactPhone,
        contactEmail: data.thirdPartyBillingOption.contactEmail
      },
      webURL: data.webURL,
      clientMatterNo: '',
      createImage: data.createImage,
      dataRetentionRequest: data.dataRetentionRequest,
      editableCustomFieldList: data.editableCustomFieldList,
      productionSourceId: data.productionSourceId,
      enableDiscoveryExceptionHandling: data.enableDiscoveryExceptionHandling,
      autoQueueForEntityExtraction: data.autoQueueForEntityExtraction
    })

    if (this.existingCase) {
      this.settingsForm.patchValue({
        caseName: data?.caseName,
        ServiceRequestType:
          SERVICE_TYPE_CONSTANTS.VODR_ADD_DATA_EXISTING_PROJECT,
        serviceRequestTypeExisting: data.serviceRequestType
      })
    } else {
      this.settingsForm.patchValue({
        ServiceRequestType: data.serviceRequestType
      })
    }
  }

  private existingCaseEmit(value: boolean) {
    this.existingCaseValue.emit(value)
  }

  private selectedCaseIdEmit(value: number | undefined) {
    this.selectedCaseIdValue.emit(value)
  }

  onServiceRequestSelection(event: any) {
    if (
      event?.serviceTypeName !==
      SERVICE_TYPE_CONSTANTS.VODR_ADD_DATA_EXISTING_PROJECT
    ) {
      this.selectedCaseId = -1
      this.settingsForm.patchValue({
        selectedCase: undefined,
        caseName: ''
      })
      this.selectedCaseIdEmit(undefined)
      this.store.dispatch(
        new fromRootActions.FetchServiceTypeDefaultData(event?.serviceTypeName)
      )
      this.updateOverrideSettingsOption(false) //uncheck override settings checkbox when service type is changed
      this.store
        .select(CaseSelectors.serviceTypeDefaultData)
        .pipe(takeUntil(this.unsubscribed$))
        .subscribe((res) => {
          if (res) {
            this.updateDefaultValues(res)
          }
        })
      this.existingCase = false
      this.showCaseNameInput = true
    } else {
      this.existingCase = true
      this.showCaseNameInput = false
      this.settingsForm.patchValue({
        caseName: ''
      })
    }
    this.existingCaseEmit(this.existingCase)
  }

  // Checks if the form is valid and filled
  public isValidFormFilled(): boolean {
    const caseName = this.settingsForm.get('caseName')?.value
    const serviceType = this.settingsForm.get('ServiceRequestType')?.value
    const selectedCase = this.settingsForm.get('selectedCase')?.value
    return (
      (caseName &&
        serviceType !== undefined &&
        serviceType !== null &&
        serviceType !== -1) ||
      (this.existingCase && selectedCase && selectedCase !== -1)
    )
  }

  #getFormValueFromPasswords(passwords: string[]): string {
    if (!passwords || passwords.length === 0) {
      return ''
    }
    return passwords.join('\n')
  }

  ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
